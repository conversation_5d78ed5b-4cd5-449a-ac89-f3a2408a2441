# Shared Infrastructure Outputs
# 這些輸出可以被其他 terraform 配置引用

output "project_id" {
  description = "GCP Project ID"
  value       = var.project_id
}

output "region" {
  description = "GCP Region"
  value       = var.region
}

output "environment" {
  description = "Environment"
  value       = var.environment
}

# BigQuery
output "bigquery_dataset_id" {
  description = "BigQuery dataset ID"
  value       = google_bigquery_dataset.integrated_event.dataset_id
}

output "bigquery_dataset_location" {
  description = "BigQuery dataset location"
  value       = google_bigquery_dataset.integrated_event.location
}

# Service Account
output "service_account_email" {
  description = "Service account email for integrated event services"
  value       = google_service_account.integrated_event_sa.email
}

output "service_account_name" {
  description = "Service account name for integrated event services"
  value       = google_service_account.integrated_event_sa.name
}

# Firestore
output "firestore_database_name" {
  description = "Firestore database name"
  value       = google_firestore_database.integrated_event.name
}

# Pub/Sub
output "data_processing_topic" {
  description = "Pub/Sub topic for data processing events"
  value       = google_pubsub_topic.data_processing_events.name
}

output "error_notifications_topic" {
  description = "Pub/Sub topic for error notifications"
  value       = google_pubsub_topic.error_notifications.name
}

# Cloud Storage
output "staging_bucket_name" {
  description = "Staging data bucket name"
  value       = google_storage_bucket.staging_data.name
}

output "platform_dashboard_bucket_name" {
  description = "Platform dashboard bucket name"
  value       = google_storage_bucket.platform_dashboard.name
}

output "platform_dashboard_bucket_url" {
  description = "Platform dashboard bucket URL for web access"
  value       = "https://storage.googleapis.com/${google_storage_bucket.platform_dashboard.name}"
}

# Monitoring
output "notification_channel_id" {
  description = "Monitoring notification channel ID"
  value       = google_monitoring_notification_channel.email.id
}

# ====== Artifact Registry ======
output "artifact_registry_repository_url" {
  description = "URL of the Artifact Registry repository for application images"
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.apps_repository.repository_id}"
}
