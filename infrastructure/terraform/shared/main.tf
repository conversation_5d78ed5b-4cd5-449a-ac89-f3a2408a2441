# Shared Infrastructure for Integrated Event Platform
# 共用基礎設施，所有服務都會使用的資源

terraform {
  required_version = ">= 1.12.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
  }

  # 使用 Terraform Cloud 或 GCS backend
  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

locals {
  common_labels = {
    project     = "integrated-event"
    environment = var.environment
    managed-by  = "terraform"
  }
}

# ====== BigQuery Datasets ======
resource "google_bigquery_dataset" "integrated_event" {
  # 原本 event_test dataset 的 location 是 US
  # 為了跟 event_prod 的 location 一致，所以重新創建 event_staging (location 在 asia-east1)
  dataset_id                  = var.environment == "prod" ? "event_prod" : "event_test"
  friendly_name               = "Integrated Event Dataset"
  description                 = "統合事件資料集，整合多種來源的使用者行為資料"
  location                    = var.bigquery_location
  default_table_expiration_ms = var.table_expiration_ms

  labels = local.common_labels
}

# ====== BigQuery Table: integrated_event (shared) ======
resource "google_bigquery_table" "integrated_event" {
  dataset_id = google_bigquery_dataset.integrated_event.dataset_id
  table_id   = "integrated_event"

  description = "整合事件資料表 - 統合多種來源的使用者行為資料 (共用)"

  labels = local.common_labels

  time_partitioning {
    type  = "DAY"
    field = "event_time"
  }

  clustering = ["partner_source", "ec_id"]

  schema = file("${path.module}/../schema/integrated_event.json")

  depends_on = [google_bigquery_dataset.integrated_event]
}

# ====== Service Accounts ======
resource "google_service_account" "integrated_event_sa" {
  account_id   = "integrated-event-${var.environment}"
  display_name = "Integrated Event Service Account (${var.environment})"
  description  = "Service account for integrated event platform services"
}

# ====== BigQuery Dataset Access (separate resources) ======
resource "google_bigquery_dataset_access" "owner_team_data" {
  project    = var.project_id
  dataset_id = google_bigquery_dataset.integrated_event.dataset_id
  role       = "OWNER"

  group_by_email = var.bigquery_admin_email # <EMAIL> 是群組信箱
}

resource "google_bigquery_dataset_access" "writer_runtime_sa" {
  project    = var.project_id
  dataset_id = google_bigquery_dataset.integrated_event.dataset_id
  role       = "WRITER"

  user_by_email = google_service_account.integrated_event_sa.email
}

resource "google_bigquery_dataset_access" "writer_bq_transfer" {
  count      = 0 # 已存在於 BigQuery，暫不由 Terraform 管理
  project    = var.project_id
  dataset_id = google_bigquery_dataset.integrated_event.dataset_id
  role       = "WRITER"

  user_by_email = "<EMAIL>"
}

resource "google_bigquery_dataset_access" "writer_kubeflow_user" {
  count      = 0 # 已存在於 BigQuery，暫不由 Terraform 管理
  project    = var.project_id
  dataset_id = google_bigquery_dataset.integrated_event.dataset_id
  role       = "WRITER"

  user_by_email = "<EMAIL>"
}

# ====== Additional IAM Roles for Runtime Service Account ======
resource "google_project_iam_member" "integrated_event_sa_additional_roles" {
  for_each = toset([
    "roles/cloudfunctions.invoker",
    "roles/run.invoker",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/datastore.user",
    "roles/bigquery.dataEditor",
    "roles/bigquery.jobUser",
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.integrated_event_sa.email}"
}

# ====== Firestore Database ======
resource "google_firestore_database" "integrated_event" {
  project     = var.project_id
  name        = "integrated-event-${var.environment}"
  location_id = var.firestore_location
  type        = "FIRESTORE_NATIVE"

  depends_on = [google_project_service.firestore]
}

# ====== Enable Required APIs ======
resource "google_project_service" "required_apis" {
  for_each = toset([
    "bigquery.googleapis.com",
    "cloudfunctions.googleapis.com",
    "run.googleapis.com",
    "firestore.googleapis.com",
    "cloudtasks.googleapis.com",
    "pubsub.googleapis.com",
    "cloudscheduler.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "cloudtrace.googleapis.com",
    "cloudbuild.googleapis.com",
    "artifactregistry.googleapis.com",
    "secretmanager.googleapis.com",
  ])

  project = var.project_id
  service = each.value

  disable_dependent_services = false
}

resource "google_project_service" "firestore" {
  project = var.project_id
  service = "firestore.googleapis.com"

  disable_dependent_services = false
}

# ====== Pub/Sub Topics (共用) ======
resource "google_pubsub_topic" "data_processing_events" {
  name = "integrated-event-processing-${var.environment}"

  labels = local.common_labels
}

resource "google_pubsub_topic" "error_notifications" {
  name = "integrated-event-errors-${var.environment}"

  labels = local.common_labels
}

# ====== Secret Manager (共用設定) ======
resource "google_secret_manager_secret" "database_url" {
  secret_id = "integrated-event-database-url-${var.environment}"

  replication {
    auto {}
  }

  labels = local.common_labels
}

# ====== Monitoring ======
resource "google_monitoring_notification_channel" "email" {
  display_name = "Integrated Event Team Email"
  type         = "email"

  labels = {
    email_address = var.monitoring_email
  }
}

# ====== Cloud Storage Bucket (for staging data) ======
resource "google_storage_bucket" "staging_data" {
  name     = "integrated-event-staging-${var.environment}-${var.project_id}"
  location = var.region

  uniform_bucket_level_access = true

  labels = local.common_labels

  lifecycle_rule {
    condition {
      age = 7 # 7 days
    }
    action {
      type = "Delete"
    }
  }
}

# ====== Cloud Storage Bucket (for platform dashboard & reports) ======
resource "google_storage_bucket" "platform_dashboard" {
  name     = "integrated-event-platform-${var.environment}-${var.project_id}"
  location = var.region

  uniform_bucket_level_access = true

  labels = merge(local.common_labels, {
    purpose = "dashboard-reports"
  })

  # CORS configuration for web access
  cors {
    origin          = ["*"]
    method          = ["GET", "HEAD"]
    response_header = ["*"]
    max_age_seconds = 3600
  }

  # Website configuration for static hosting
  website {
    main_page_suffix = "index.html"
    not_found_page   = "404.html"
  }

  # Lifecycle management for cost optimization
  lifecycle_rule {
    condition {
      age            = 90 # 90 days for archive files
      matches_prefix = ["legacy-event-sync/dashboard/archive/", "*/reports/archive/"]
    }
    action {
      type = "Delete"
    }
  }

  lifecycle_rule {
    condition {
      age            = 30 # 30 days for old data files
      matches_prefix = ["*/data/history/"]
    }
    action {
      type = "Delete"
    }
  }
}

# ====== IAM for staging bucket ======
resource "google_storage_bucket_iam_member" "staging_data_writer" {
  bucket = google_storage_bucket.staging_data.name
  role   = "roles/storage.objectCreator"
  member = "serviceAccount:${google_service_account.integrated_event_sa.email}"
}

resource "google_storage_bucket_iam_member" "staging_data_reader" {
  bucket = google_storage_bucket.staging_data.name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.integrated_event_sa.email}"
}

# ====== IAM for platform dashboard bucket ======
resource "google_storage_bucket_iam_member" "platform_dashboard_writer" {
  bucket = google_storage_bucket.platform_dashboard.name
  role   = "roles/storage.objectCreator"
  member = "serviceAccount:${google_service_account.integrated_event_sa.email}"
}

resource "google_storage_bucket_iam_member" "platform_dashboard_admin" {
  bucket = google_storage_bucket.platform_dashboard.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.integrated_event_sa.email}"
}

# Public read access for dashboard files
resource "google_storage_bucket_iam_member" "platform_dashboard_public_read" {
  bucket = google_storage_bucket.platform_dashboard.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

# ====== Artifact Registry (for Docker images) ======
resource "google_artifact_registry_repository" "apps_repository" {
  provider      = google-beta
  project       = var.project_id
  location      = var.region
  repository_id = "integrated-event-apps"
  description   = "Docker repository for Integrated Event Platform applications"
  format        = "DOCKER"

  labels = local.common_labels
}
