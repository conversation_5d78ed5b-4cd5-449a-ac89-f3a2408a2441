# Integrated Event Platform

> 企業級整合事件資料平台 - 統合多種使用者資料來源，支援進階分析和受眾分群

[![CI Status](https://github.com/Tagtoo/integrated-event/workflows/CI/badge.svg)](https://github.com/Tagtoo/integrated-event/actions)
[![Security Scan](https://github.com/Tagtoo/integrated-event/workflows/Security%20Scan/badge.svg)](https://github.com/Tagtoo/integrated-event/actions)
[![License](https://img.shields.io/badge/license-Proprietary-red.svg)](LICENSE)

## 🤖 AI 協作開發

本專案支援 AI coding agent 協作開發。請參考 [AI 協作開發守則](.ai-rules.md) 了解開發規範和最佳實踐。

## 專案概述

這是一個基於 Google Cloud Platform 的企業級資料整合平台，使用 mono repo 架構整合多個子系統，提供統一的使用者行為資料介面。

### 核心功能

- **資料統一**: 整合線下發票、CRM、合作夥伴與既有追蹤資料
- **即時處理**: 支援大量資料的即時處理和分析
- **彈性擴展**: 模組化設計支援新資料來源快速接入
- **團隊協作**: Mono repo 支援多人並行開發

### 技術架構

#### 資料流架構

```
原始資料來源 → Firestore (Staging) → ETL Pipeline → BigQuery (integrated_event)
     ↓              ↓                     ↓                    ↓
線下發票資料    原始資料保存         資料清理轉換        統一分析介面
CRM 資料       彈性 Schema        Cloud Functions     受眾分群分析
合作夥伴資料    即時寫入          錯誤處理重試         使用者行為追蹤
既有追蹤資料    成本效益          監控告警            報表和 Dashboard
```

#### 完整資料流程圖

以下流程圖展示了如何整合多種資料來源到統一的分析平台：

```mermaid
flowchart TD
    A1["發票資料<br/>(包含 user_id)"] -.-> B1["User Mapping Table<br/>(查詢獲取 tagtoo_permanent_id)"]
    A2["零售線下轉換事件<br/>(包含 user_id)"] -.-> B1
    A3["VM5 下載資料<br/>(包含 vm5_user_id)"] -.-> B1
    A4["線上廣告數據<br/>(包含 data_partner_user_id)"] --> B1
    A5["現有 event_prod 資料<br/>(包含 permanent_id)"] --> C2["目前資料 Processing Layer"]

    B1 -.-> C1["第三方資料 Processing Layer<br/>(清理/標準化/轉換)"]
    B1 --> C2["目前資料 Processing Layer<br/>(清理/標準化/轉換)"]

    C1 -.-> D["Integrated Event Table<br/>(BigQuery)"]
    C2 --> D

    D --> E1["Data Analytics & Reporting"]
    D --> E2["受眾分群分析"]
    D --> E3["使用者行為追蹤"]

    classDef existing fill:#00aaFF,stroke:#0080CC,color:#fff
    classDef non-existing fill:#f9f9f9,stroke:#2E86C1,stroke-dasharray:3
    classDef processing fill:#52C41A,stroke:#389E0D,color:#fff
    classDef output fill:#FA8C16,stroke:#D46B08,color:#fff

    class A4,A5,B1,C2,D existing
    class A1,A2,A3,C1 non-existing
    class C1,C2 processing
    class E1,E2,E3 output
```

### 技術棧選擇

**儲存層**:

- **Firestore**: 原始資料 staging area (彈性 schema, 成本效益)
- **BigQuery**: 最終整合資料 (強大分析能力, 與現有系統整合)

**運算層**:

- **Cloud Functions**: ETL 處理、定時同步
- **Cloud Run**: 複雜業務邏輯、API 服務
- **BigQuery Scheduler**: 定時資料處理任務

**協調層**:

- **Cloud Tasks**: 任務佇列和調度
- **Pub/Sub**: 事件驅動架構
- **Cloud Scheduler**: 定時觸發

### 架構設計原則

#### 混合式 Infrastructure 管理

我們採用**混合式基礎設施管理**架構，平衡獨立性與資源共享：

**共用基礎設施** (`infrastructure/terraform/shared/`):

- VPC 網路和安全群組
- IAM 角色和服務帳戶
- BigQuery datasets 和共用表格
- Pub/Sub topics (跨服務通信)
- Secret Manager 和 KMS
- 監控和告警系統

**服務特定基礎設施** (各 `apps/*/terraform/`):

- Cloud Functions/Cloud Run 服務
- 服務特定的 BigQuery 表格
- 服務特定的 Firestore collections
- Cloud Scheduler 任務
- 服務特定的監控指標

**優勢**:

- **獨立部署**: 每個團隊可以獨立部署自己的服務
- **故障隔離**: 一個服務的問題不會影響其他服務
- **權限分離**: 可以給不同團隊不同的權限範圍
- **資源共享**: 避免重複建立共用資源
- **成本效益**: 共用資源降低總體成本

#### Terraform 資源共享機制

**避免重複定義**的解決方案：

1. **Remote State 引用**：

   ```hcl
   # 在 apps/your-app/terraform/main.tf 中
   data "terraform_remote_state" "shared" {
     backend = "gcs"
     config = {
       bucket = "tagtoo-tracking-terraform-state"
       prefix = "integrated-event/shared"
     }
   }

   # 使用共用資源
   resource "google_cloud_run_service" "your_service" {
     # ...
     service_account_email = data.terraform_remote_state.shared.outputs.service_account_email
   }
   ```

2. **可重用 Modules**：

   ```hcl
   # 使用共用模組
   module "cloud_function" {
     source = "../../../infrastructure/terraform/modules/cloud-function"
     # ...
   }
   ```

3. **Data Sources 查詢**：
   ```hcl
   # 查詢已存在的資源
   data "google_bigquery_dataset" "integrated_event" {
     dataset_id = "event_test"
     project    = "tagtoo-tracking"
   }
   ```

**實際運作流程**：

1. DevOps 團隊先部署 `infrastructure/terraform/shared/`
2. 各專案團隊引用共用資源的 outputs 來建立自己的服務
3. 沒有資源重複定義，只有引用關係

## 📊 Schema 管理

### 簡化設計理念

本專案採用 **簡化的 Schema 管理方案**，專為 4 人小團隊設計：

- **🎯 集中管理**: 所有 schema 檔案統一放在 `/terraform/schema/`
- **🤝 協作友善**: 透過 Git commit 標籤進行團隊協作
- **⚡ 快速操作**: 提供簡單的 Make 指令管理 schema

### 快速使用

```bash
# 檢視 schema 狀態
make schema-status

# 更新開發環境 schema
make schema-update-dev

# 比較本地與 BigQuery 的 schema 差異
make schema-diff-dev

# 驗證 schema 格式
make schema-validate
```

### Schema 變更流程

```bash
# 1. 修改 schema 檔案
vim terraform/schema/integrated_event.json

# 2. 更新開發環境測試
make schema-update-dev

# 3. 提交變更 (必須加 [SCHEMA] 標籤)
git add terraform/schema/integrated_event.json
git commit -m "[SCHEMA] feat: 新增 location 和 raw_json 欄位"

# 4. 其他同事看到標籤後同步
git pull
make schema-update-dev
```

> 📚 **詳細說明**: 請參考 [Schema 管理文檔](terraform/schema/README.md)

## 快速開始

### 先決條件

- Docker Desktop
- Google Cloud CLI (`gcloud`)
- Terraform >= 1.7.0
- Python 3.11+
- Node.js 18+ (for some tools)

### 本地開發環境設定

```bash
# 1. 複製專案
git clone https://github.com/Tagtoo/integrated-event.git
cd integrated-event

# 2. 設定開發環境
make dev-setup

# 3. 啟動本地服務
make dev-up

# 4. 運行測試
make test
```

### 快速部署

```bash
# 部署到開發環境
make deploy-dev

# 部署到生產環境
make deploy-prod
```

### 🚀 建立新的子專案/服務

本專案提供了自動化工具來快速建立新的整合服務，支援三種不同的部署類型。每個新服務都會包含完整的開發環境配置、測試框架、部署配置等。

## 🎯 支援的部署類型

| 部署類型           | 適用場景                               | 架構組件                                     |
| ------------------ | -------------------------------------- | -------------------------------------------- |
| **cloud-run**      | 長時間背景任務、複雜業務邏輯、API 服務 | Cloud Run v2 + Cloud Tasks + Cloud Scheduler |
| **cloud-function** | 短時間事件驅動任務、定時處理           | Cloud Functions + Cloud Scheduler            |
| **bigquery-only**  | 純 SQL 資料處理任務                    | BigQuery + Cloud Scheduler                   |

## 🚀 建立新服務

### 方法一：使用配置檔案 (推薦)

1. **建立配置檔案** (例如 `my-service-config.yml`):

```yaml
service:
  name: "Shopify Webhook"
  description: "整合 Shopify webhook 資料到 integrated_event 平台"
  deployment_type: "cloud-run" # cloud-run, cloud-function, bigquery-only
  author: "Tagtoo Data Team"
  email: "<EMAIL>"

# 技術配置 (選填)
technical:
  python_version: "3.11"
  dependencies:
    - "flask>=2.3.0"
    - "google-cloud-firestore>=2.11.0"

# Cloud Run 配置 (當 deployment_type=cloud-run 時)
cloud_run:
  cpu: "1000m"
  memory: "512Mi"
  min_instances: 0
  max_instances: 10
```

2. **執行建立命令**:

```bash
python3 scripts/create-service.py --config my-service-config.yml
```

### 方法二：使用 Makefile 命令

```bash
# 建立新服務 (預設為 cloud-run 類型)
make create-service SERVICE_NAME="Your Service Name" DESCRIPTION="Service description"

# 範例
make create-service SERVICE_NAME="Shopify Webhook" DESCRIPTION="Shopify webhook integration service"
```

### 方法三：直接使用 Python 腳本

```bash
# 基本使用
python3 scripts/create-service.py --name "Service Name" --description "Description"

# 完整參數
python3 scripts/create-service.py \
  --name "My Service" \
  --description "My service description" \
  --author "Your Name" \
  --email "<EMAIL>"
```

## 🔄 CI/CD 自動整合

新建立的服務會**自動**被納入 CI/CD 流程，無需手動設定：

### ✅ 自動偵測機制

- GitHub Actions 會自動偵測 `apps/` 目錄下的新服務
- 當服務檔案有變更時，會自動觸發相應的測試和部署流程

### 🚀 部署流程

- **推送到 `main` 分支**: 自動部署到生產環境
- **推送到其他分支**: 觸發測試和開發環境部署
- **Pull Request**: 執行完整測試套件

### 📋 必要條件

新服務需要包含以下檔案才能正確整合：

- `Makefile`: 包含 `test` 目標
- `terraform/`: Terraform 配置檔案
- 適當的目錄結構 (由模板自動建立)

### 🔍 監控和追蹤

- 在 GitHub Actions Summary 中查看 Terraform Plan 輸出
- 自動監控部署狀態和錯誤
- 整合的測試報告和 artifact

# 使用配置檔案

python3 scripts/create-service.py --config service-config.yml

````

#### 🎯 建立後的下一步

新服務建立後，腳本會自動：

- ✅ 複製完整的服務模板 (`apps/_template/`)
- ✅ 替換所有模板變數 (`{{SERVICE_NAME}}` 等)
- ✅ 建立 Docker 配置 (`Dockerfile`, `docker-compose.yml`)
- ✅ 建立 Terraform 基礎設施配置
- ✅ 建立完整的測試框架 (`pytest` + 覆蓋率)
- ✅ 生成 GitHub Actions CI/CD 配置
- ✅ 更新根目錄 Makefile 添加專用命令

**立即開始開發** ([使用容器化開發環境][memory:142975430710102188]]):

```bash
# 進入新服務目錄
cd apps/your-service-name

# 設定環境變數
cp .env.example .env
# 編輯 .env 填入實際配置

# 啟動開發環境 (完整容器化)
docker-compose up -d --build

# 實作你的業務邏輯
# 編輯 src/main.py - 主要包含 process_event() 方法

# 撰寫測試
# 編輯 tests/test_main.py

# 在容器內運行測試
make test-your-service-name

# 查看服務日誌
docker-compose logs -f your-service-name

# 部署到開發環境
make deploy-your-service-name-dev
````

#### 🔍 檢視和管理服務

```bash
# 列出所有可用的服務
make list-services

# 檢視特定服務的結構
tree apps/your-service-name

# 檢視服務模板結構 (參考用)
tree apps/_template
```

#### 🏗️ 服務模板包含的功能

每個新服務都基於 `apps/_template/` 模板，包含：

**📁 完整的項目結構**:

```
your-service-name/
├── src/
│   └── main.py              # 主要業務邏輯
├── tests/
│   └── test_main.py         # 測試檔案
├── terraform/
│   ├── main.tf              # 基礎設施配置
│   ├── variables.tf         # 變數定義
│   └── schema/              # BigQuery schema
├── docker-compose.yml       # 本地開發環境
├── Dockerfile               # 容器配置
├── requirements.txt         # Python 依賴
├── pyproject.toml          # 專案配置
├── pytest.ini             # 測試配置
├── .env.example            # 環境變數範例
├── .flake8                 # 代碼風格配置
└── README.md               # 完整的服務文檔
```

**🔧 開箱即用的功能**:

- Flask 應用框架 (健康檢查、webhook 端點)
- Firestore 集成 (資料暫存)
- BigQuery 集成 (資料分析)
- Docker 多階段建構
- 完整的測試套件 (單元測試、整合測試)
- CI/CD 管道 (GitHub Actions)
- Terraform 基礎設施管理
- 監控和日誌設定

**📋 需要自行實作的部分**:

- `src/main.py` 中的 `process_event()` 方法 (核心業務邏輯)
- 針對特定資料來源的驗證和轉換邏輯
- 相應的測試用例
- Terraform 變數檔案的實際配置值

## 專案結構

```
integrated-event/
├── apps/                          # 應用服務
│   ├── legacy-event-sync/        # 同步舊資料服務
│   │   ├── src/                  # 應用程式碼
│   │   ├── tests/                # 服務特定測試
│   │   ├── terraform/            # 服務特定基礎設施
│   │   ├── Dockerfile
│   │   └── requirements.txt
│   ├── reurl/                    # ReURL 資料合作夥伴
│   │   ├── fetch-link-metadata/  # 連結元數據抓取
│   │   ├── task-controller/      # 任務控制服務
│   │   └── terraform/            # ReURL 專案基礎設施
│   ├── shopify-webhook/         # Shopify Webhook 整合
│   │   ├── src/                  # 應用程式碼
│   │   ├── tests/                # 服務特定測試
│   │   └── terraform/            # 服務特定基礎設施
│   ├── invoice-crawler/          # 發票資料爬蟲 (Phase 3)
│   │   └── terraform/
│   ├── crm-connector/            # CRM 連接器 (Phase 3)
│   │   └── terraform/
│   └── partner-data-sync/        # 合作夥伴資料同步 (Phase 3)
│       └── terraform/
├── libs/                         # 共用函式庫
│   ├── common/                   # 共用工具和設定
│   ├── bigquery-utils/          # BigQuery 相關工具
│   ├── firestore-utils/         # Firestore 相關工具
│   └── auth-utils/              # 認證相關工具
├── infrastructure/              # 共用基礎設施
│   └── terraform/
│       ├── shared/              # 共用基礎設施 (VPC, IAM, BigQuery datasets)
│       └── modules/             # 可重用 Terraform 模組
├── pipelines/                   # CI/CD 管道
│   ├── .github/workflows/       # GitHub Actions
│   └── scripts/                 # 部署腳本
├── docs/                        # 技術文件
├── tests/                       # 整合測試
└── tools/                       # 開發工具
```

## BigQuery Schema 設計

### integrated_event Table Schema

基於現有 `tagtoo-tracking.event_prod.tagtoo_event` table，設計的整合資料表結構：

```python
schema = [
    # 核心識別欄位
    bigquery.SchemaField("permanent", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("partner_id", "STRING"),
    bigquery.SchemaField("ec_id", "INTEGER"),
    bigquery.SchemaField("partner_source", "STRING", mode="REQUIRED"),

    # 事件資訊
    bigquery.SchemaField("event", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("event_time", "TIMESTAMP", mode="REQUIRED"),
    bigquery.SchemaField("create_time", "TIMESTAMP", mode="REQUIRED"),

    # 交易資訊
    bigquery.SchemaField("value", "FLOAT"),
    bigquery.SchemaField("currency", "STRING"),
    bigquery.SchemaField("order_id", "STRING"),

    # 頁面資訊
    bigquery.SchemaField("link", "STRING"),
    bigquery.SchemaField("page_title", "STRING"),

    # 使用者資訊（擴展版本）
    bigquery.SchemaField("user", "RECORD", fields=[
        bigquery.SchemaField("em", "STRING"),
        bigquery.SchemaField("ph", "STRING"),
        bigquery.SchemaField("partner_user_id", "STRING"),
        bigquery.SchemaField("device_id", "STRING"),        # 未來擴展
        bigquery.SchemaField("invoice_carrier_id", "STRING"), # 載具 ID
        # 保留原始 tagtoo_event 的其他 user 欄位
        bigquery.SchemaField("un", "STRING"),  # username
        bigquery.SchemaField("gd", "STRING"),  # gender
        bigquery.SchemaField("db", "STRING"),  # date of birth
        bigquery.SchemaField("fbp", "STRING"), # Facebook pixel
        bigquery.SchemaField("fbc", "STRING"), # Facebook click
        bigquery.SchemaField("ga", "STRING"),  # Google Analytics
        bigquery.SchemaField("gid", "STRING"), # Google ID
        bigquery.SchemaField("lmid", "STRING"), # Line ID
    ]),

    # 商品資訊
    bigquery.SchemaField("items", "RECORD", mode="REPEATED", fields=[
        bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("name", "STRING"),
        bigquery.SchemaField("description", "STRING"),
        bigquery.SchemaField("price", "FLOAT"),
        bigquery.SchemaField("quantity", "FLOAT"),
        bigquery.SchemaField("availability", "STRING"),
    ]),

    # 技術追蹤欄位
    bigquery.SchemaField("source_metadata", "RECORD", fields=[
        bigquery.SchemaField("ingestion_time", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("source_system", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("source_version", "STRING"),
        bigquery.SchemaField("data_quality_score", "FLOAT"),
    ]),

    # 原始資料保存（Debug 用）
    bigquery.SchemaField("raw_data", "JSON"),

    # 位置資訊（選擇性保留）
    bigquery.SchemaField("location", "RECORD", fields=[
        bigquery.SchemaField("country_code", "STRING"),
        bigquery.SchemaField("region_name", "STRING"),
        bigquery.SchemaField("city_name", "STRING"),
    ]),
]

# 分區和叢集設定
PARTITION BY DATE(event_time)
CLUSTER BY partner_source, ec_id
```

## 開發 Checklist

### Phase 1: 基礎建設 📋

#### Milestone 1.1: 專案架構建立 (Week 1)

- [ ] **專案初始化**

  - [x] 建立 mono repo 目錄結構
  - [x] 撰寫專案規劃和完整 README.md
  - [x] 整合目錄結構（ReURL 合併）
  - [x] 分析現有 event_prod schema
  - [x] 設計 integrated_event table schema
  - [ ] 設定 `.gitignore` 和基本配置檔案
  - [ ] 建立 LICENSE 和 CONTRIBUTING.md

- [ ] **開發環境標準化**

  - [ ] 建立 `.devcontainer` 配置
  - [ ] 設定 `docker-compose.yml` 本地環境
  - [ ] 建立各服務的 `Dockerfile`
  - [ ] 設定 `Makefile` 常用命令
  - [ ] 建立 Python 虛擬環境管理

- [ ] **共用函式庫框架**

  - [ ] 建立 `libs/common` 基礎工具
  - [ ] 建立 `libs/bigquery-utils` BigQuery 工具
  - [ ] 建立 `libs/firestore-utils` Firestore 工具
  - [ ] 建立 `libs/auth-utils` 認證工具
  - [ ] 設定套件依賴管理

- [ ] **代碼品質工具**
  - [ ] 設定 `pre-commit-config.yaml`
  - [ ] 配置 `black`, `flake8`, `mypy`
  - [ ] 設定 `pytest` 測試框架
  - [ ] 建立代碼覆蓋率檢查
  - [ ] 設定安全掃描工具

#### Milestone 1.2: CI/CD Pipeline (Week 2-3)

- [ ] **GitHub Actions 設定**

  - [ ] 建立 `.github/workflows/ci.yml`
  - [ ] 建立 `.github/workflows/cd-dev.yml`
  - [ ] 建立 `.github/workflows/cd-staging.yml`
  - [ ] 建立 `.github/workflows/cd-prod.yml`
  - [ ] 建立 `.github/workflows/security-scan.yml`

- [ ] **變更檢測機制**

  - [ ] 實作 path-based triggers
  - [ ] 建立服務相依性檢測
  - [ ] 設定條件式部署邏輯
  - [ ] 建立變更影響分析
  - [ ] 測試變更檢測功能

- [ ] **多環境部署流程**

  - [ ] 設定環境變數管理
  - [ ] 建立部署腳本 (`scripts/deploy.sh`)
  - [ ] 設定回滾機制
  - [ ] 建立部署驗證檢查
  - [ ] 測試完整部署流程

- [ ] **測試整合**
  - [ ] 整合單元測試到 CI
  - [ ] 設定整合測試環境
  - [ ] 建立端到端測試
  - [ ] 設定效能測試
  - [ ] 建立測試報告

#### Milestone 1.3: 基礎設施即代碼 (Week 4-5)

- [ ] **Terraform 架構重構**

  - [ ] 遷移現有 Terraform 到 `infrastructure/terraform/`
  - [ ] 建立可重用模組 (`modules/`)
  - [ ] 設定多環境配置 (`environments/`)
  - [ ] 建立變數和輸出管理
  - [ ] 驗證 Terraform 配置

- [ ] **BigQuery 資料結構**

  - [ ] 建立 `integrated_event` table schema
  - [ ] 設定資料分區和叢集策略
  - [ ] 建立資料品質視圖
  - [ ] 設定資料保留政策
  - [ ] 測試資料寫入和查詢

- [ ] **GCP 權限和安全**

  - [ ] 設定服務帳戶和 IAM 角色
  - [ ] 配置 Workload Identity
  - [ ] 設定 Secret Manager
  - [ ] 建立網路安全規則
  - [ ] 驗證權限設定

- [ ] **監控和告警系統**
  - [ ] 設定 Cloud Monitoring
  - [ ] 建立自定義指標
  - [ ] 設定告警規則
  - [ ] 建立監控 Dashboard
  - [ ] 測試告警機制

#### Milestone 1.4: 現有服務遷移 (Week 6)

- [ ] **服務遷移到新架構**

  - [ ] 遷移 ReURL 服務到 `apps/reurl/`
  - [ ] 更新 Dockerfile 和依賴
  - [ ] 更新部署配置
  - [ ] 驗證服務功能
  - [ ] 整合測試

- [ ] **Shopify Scheduler 遷移**

  - [ ] 遷移 `shopify-scheduler.tf` 配置
  - [ ] 更新 BigQuery 查詢邏輯
  - [ ] 測試排程執行
  - [ ] 驗證資料處理結果
  - [ ] 建立監控和告警

- [ ] **整合測試**
  - [ ] 測試所有遷移服務
  - [ ] 驗證服務間通信
  - [ ] 檢查資料完整性
  - [ ] 效能基準測試
  - [ ] 用戶驗收測試

### Phase 2: 核心功能開發 🚀

#### Milestone 2.1: Legacy Event Sync (Week 7-8)

- [x] **Legacy Event Sync 服務開發** - ✅ **已部署至生產環境**
  - [x] 服務已上線: `legacy-event-sync-prod`
  - [x] Cloud Scheduler 已配置 (每 4 小時執行)
  - [x] 安全配置已完成 (移除公開存取)
  - [x] 監控和告警已設定
  - 🔄 **待驗證**: 首次完整同步執行
  - 📊 **目標**: 同步 2.8 億筆 tagtoo_event 資料
  - 📝 _詳細狀態請參考 [apps/legacy-event-sync/docs/SYSTEM_STATUS.md](apps/legacy-event-sync/docs/SYSTEM_STATUS.md)_

#### Milestone 2.2: 資料品質和驗證 (Week 9)

- [ ] **資料驗證規則**

  - [ ] 建立資料格式驗證
  - [ ] 實作業務規則檢查
  - [ ] 建立資料完整性驗證
  - [ ] 設定資料範圍檢查
  - [ ] 實作重複資料檢測

- [ ] **資料品質檢查**

  - [ ] 建立資料品質評分
  - [ ] 實作資料異常檢測
  - [ ] 建立資料血緣追蹤
  - [ ] 設定資料新鮮度檢查
  - [ ] 建立品質報告

- [ ] **資料修復機制**
  - [ ] 建立資料修復工具
  - [ ] 實作資料補償邏輯
  - [ ] 設定人工審核流程
  - [ ] 建立資料回滾機制
  - [ ] 測試修復功能

#### Milestone 2.3: 端到端測試 (Week 10)

- [ ] **測試環境建立**

  - [ ] 建立完整測試環境
  - [ ] 準備測試資料集
  - [ ] 設定測試用服務帳戶
  - [ ] 建立測試資料清理機制
  - [ ] 驗證測試環境

- [ ] **完整流程測試**

  - [ ] 資料讀取測試
  - [ ] 資料轉換測試
  - [ ] 資料寫入測試
  - [ ] 錯誤處理測試
  - [ ] 效能負載測試

- [ ] **用戶驗收測試**
  - [ ] 準備 UAT 環境
  - [ ] 建立測試案例
  - [ ] 執行用戶測試
  - [ ] 收集用戶反饋
  - [ ] 修復發現問題

### Phase 3: 擴展和優化 🔄

#### Milestone 3.1: 新資料來源接入

- [ ] **發票資料爬蟲** (`apps/invoice-crawler/`)

  - [ ] 分析發票資料格式
  - [ ] 建立資料擷取邏輯
  - [ ] 實作資料清理和標準化
  - [ ] 整合到 ETL pipeline
  - [ ] 測試和驗證

- [ ] **CRM 連接器** (`apps/crm-connector/`)

  - [ ] 分析 CRM API 和資料格式
  - [ ] 建立資料同步邏輯
  - [ ] 實作增量更新機制
  - [ ] 整合到 integrated_event
  - [ ] 測試和驗證

- [ ] **合作夥伴資料同步** (`apps/partner-data-sync/`)
  - [ ] 建立合作夥伴資料接入介面
  - [ ] 實作資料對應和清理
  - [ ] 建立資料品質檢查
  - [ ] 整合到主要資料流
  - [ ] 測試和驗證

#### Milestone 3.2: 進階功能

- [ ] **即時資料處理能力**

  - [ ] 建立 Streaming 資料管道
  - [ ] 實作即時資料驗證
  - [ ] 建立即時告警機制
  - [ ] 優化處理延遲
  - [ ] 測試即時功能

- [ ] **資料湖架構整合**
  - [ ] 設計資料湖架構
  - [ ] 建立原始資料儲存層
  - [ ] 實作資料生命週期管理
  - [ ] 整合資料處理管道
  - [ ] 測試資料湖功能

### Phase 4: 生產化和維運 🚀

#### Milestone 4.1: 生產部署準備

- [ ] **生產環境配置**

  - [ ] 建立生產 Terraform 配置
  - [ ] 設定生產環境權限
  - [ ] 配置生產監控和告警
  - [ ] 建立生產資料備份
  - [ ] 驗證生產環境

- [ ] **災難恢復計劃**

  - [ ] 建立災難恢復流程
  - [ ] 設定資料備份策略
  - [ ] 建立服務恢復程序
  - [ ] 測試災難恢復
  - [ ] 文件化恢復程序

- [ ] **安全合規檢查**
  - [ ] 執行安全滲透測試
  - [ ] 驗證資料加密
  - [ ] 檢查存取控制
  - [ ] 審查合規要求
  - [ ] 修復安全問題

#### Milestone 4.2: 監控和維運

- [ ] **監控 Dashboard**

  - [ ] 建立業務指標監控
  - [ ] 建立技術指標監控
  - [ ] 設定告警規則
  - [ ] 建立效能監控
  - [ ] 測試監控功能

- [ ] **維運手冊**
  - [ ] 撰寫操作手冊
  - [ ] 建立故障排除指南
  - [ ] 文件化維運程序
  - [ ] 建立事件響應流程
  - [ ] 訓練維運團隊

## 常用命令

```bash
# 開發環境
make dev-setup          # 設定開發環境
make dev-up             # 啟動本地服務
make dev-down           # 停止本地服務
make dev-clean          # 清理本地環境

# 測試
make test               # 運行所有測試
make test-unit          # 運行單元測試
make test-integration   # 運行整合測試
make test-e2e           # 運行端到端測試
make lint               # 代碼檢查

# 部署
make build              # 建置所有服務
make deploy-dev         # 部署到開發環境
make deploy-staging     # 部署到測試環境
make deploy-prod        # 部署到生產環境

# 維運
make logs               # 查看服務日誌
make status             # 檢查服務狀態
make monitor            # 開啟監控 Dashboard
make backup             # 資料備份
```

## 環境配置

### 開發環境

- **GCP Project**: `tagtoo-tracking`
- **BigQuery Dataset**: `event_test`
- **Service Account**: `<EMAIL>`

### 生產環境

- **GCP Project**: `tagtoo-tracking`
- **BigQuery Dataset**: `event_prod`
- **Service Account**: `<EMAIL>`

## 開發規範和守則

### 🏗️ 專案結構規範

每個 `apps/` 下的子專案必須遵循以下標準結構：

```
your-app/
├── src/                    # 應用程式碼（必要）
├── tests/                  # 測試程式碼（必要）
├── terraform/              # 服務特定基礎設施（DevOps 負責）
├── Dockerfile              # 容器配置（如需要）
├── requirements.txt        # Python 依賴（如適用）
└── README.md               # 服務文件（必要）
```

### 🤝 團隊協作分工

#### 專案負責人（各服務團隊）負責：

- **應用程式碼**: `src/` 目錄下的所有業務邏輯
- **測試程式碼**: `tests/` 目錄下的所有測試
- **服務文件**: `README.md` 的維護和更新
- **依賴管理**: `requirements.txt` 或其他依賴檔案
- **服務特定基礎設施**: `apps/your-app/terraform/` 下的資源配置
  - Cloud Run/Cloud Functions
  - 服務特定的 BigQuery 表格
  - 服務特定的 Firestore collections
  - Cloud Scheduler 任務
  - 服務特定的監控和告警

#### DevOps 團隊負責：

- **跨服務共用基礎設施**: `infrastructure/terraform/shared/` 的管理
  - VPC 網路和安全群組
  - 共用的 IAM 角色和服務帳戶
  - 共用的 BigQuery datasets
  - 跨服務的 Pub/Sub topics
  - Secret Manager 和 KMS
  - 整體監控 Dashboard
- **CI/CD 管道**: 整體 pipeline 的維護
- **Terraform Modules**: `infrastructure/terraform/modules/` 可重用模組

### 🔧 CI/CD 整合規範

#### 必須實作的 Make 指令：

每個子專案都必須支援以下指令，CI 會自動調用：

```bash
make test               # 運行所有測試（必要）
make lint               # 代碼檢查（建議）
make build              # 建置服務（如需要）
make deploy-dev         # 部署到開發環境（必要）
make deploy-prod        # 部署到生產環境（必要）
```

#### 範例 Makefile：

```makefile
# 在你的 apps/your-app/ 目錄下建立 Makefile

.PHONY: test lint build deploy-dev deploy-prod

test:
	# 實作你的測試命令，例如：
	pytest tests/ -v
	# 或其他測試框架

lint:
	# 實作代碼檢查，例如：
	flake8 src/
	black --check src/

build:
	# 實作建置邏輯，例如：
	docker build -t your-app .

deploy-dev:
	# 實作開發環境部署
	cd terraform && terraform apply -var="environment=dev" -auto-approve

deploy-prod:
	# 實作生產環境部署
	cd terraform && terraform apply -var="environment=prod" -auto-approve
```

### 🚀 CI 觸發機制

CI 使用 **path-based triggers**，只有當特定目錄有變更時才會觸發：

- **變更 `apps/your-app/src/`** → 觸發該服務的測試和部署
- **變更 `apps/your-app/tests/`** → 觸發該服務的測試
- **變更 `infrastructure/terraform/shared/`** → 觸發所有服務的整合測試
- **變更 `libs/`** → 觸發使用該 lib 的服務測試

### 📏 代碼品質標準

#### Python 專案：

- **格式化**: 使用 `black` 或 `autopep8`
- **Linting**: 使用 `flake8` 或 `pylint`
- **測試覆蓋率**: > 80%
- **型別檢查**: 建議使用 `mypy`

#### 其他語言：

- 遵循該語言的業界最佳實踐
- 確保 `make test` 可以正常運行

### 🔧 開發工具鏈與自動化

#### Terraform 與 Pre-commit 的關係

**重要觀念**：Terraform 與 Pre-commit 是**兩個獨立的工具**，各司其職：

**🔧 Terraform** (核心基礎設施工具):

```bash
# Terraform 原生命令 (推薦優先使用)
terraform fmt -recursive          # 格式化所有 .tf 檔案
terraform validate                # 語法和配置驗證
terraform plan                   # 執行計劃檢查
terraform apply                  # 部署基礎設施

# Makefile 包裝 (團隊標準化)
make tf-fmt                      # 格式化
make tf-validate                 # 驗證
make tf-plan                     # 計劃檢查
```

**⚡ Pre-commit** (自動化檢查框架):

- **本質**: 在 git commit 前自動執行檢查工具的框架
- **目的**: 自動化執行 terraform 命令，而**不是替代**它們
- **配置**: 採用責任分離設計
  - 根目錄：共用基礎設施 + 全域一致性檢查
  - 各服務：服務特定的詳細檢查

**設計理念**：您完全可以不使用 pre-commit，直接使用 terraform 原生命令 + Makefile

#### 開發工作流選擇

您可以根據偏好選擇任一工作流，它們在功能上是等價的：

**🚀 選項 1: 純 Terraform 原生** (最直接的方式)

```bash
cd apps/your-service/terraform
terraform fmt -recursive
terraform validate
terraform plan -var="environment=dev"
git add . && git commit -m "your commit message"
```

**📋 選項 2: Makefile 標準化** (團隊推薦)

```bash
make tf-fmt
make tf-validate
make tf-plan-dev
git add . && git commit -m "your commit message"
```

**🤖 選項 3: Pre-commit 自動化** (適合新手)

```bash
# pre-commit 會在 commit 時自動執行上述檢查
git add .
git commit -m "your commit message"  # 自動觸發所有檢查
```

**🔄 CI/CD 環境** (保證一致性):

```bash
# GitHub Actions 使用原生命令確保環境一致性
terraform fmt -check -recursive
terraform validate
terraform plan
```

### 🔍 服務健康檢查

每個服務都必須提供：

- **健康檢查端點**: `/health` 或類似
- **版本資訊端點**: `/version` 或類似
- **指標端點**: `/metrics` 或類似（建議）

### Git 工作流程

1. **分支策略**

   - `main`: 生產環境，受保護
   - `develop`: 開發整合分支
   - `feature/*`: 功能開發分支
   - `hotfix/*`: 緊急修復分支

2. **Commit 規範** (Conventional Commits)

   ```
   <type>(<scope>): <description>

   [optional body]

   [optional footer(s)]
   ```

   Types: feat, fix, docs, style, refactor, test, chore
   Scopes: 對應的服務或模組名稱，例如：

   - `feat(legacy-sync): add retry mechanism`
   - `fix(shopify-webhook): handle malformed data`
   - `docs(reurl): update API documentation`

### 🛡️ 安全和合規

- **敏感資料**: 使用 Secret Manager，不可 commit 到 repo
- **權限最小化**: 每個服務只能存取必要的資源
- **定期掃描**: CI 會自動執行安全掃描

### 提交訊息格式

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

範例:

```
feat(legacy-sync): add incremental data sync capability

- Implement incremental sync logic
- Add data validation checks
- Optimize performance for large datasets

Closes #123
```

## 監控和可觀測性

### 監控指標

1. **業務指標**

   - 每日處理資料量
   - 資料品質分數
   - 使用者活躍度
   - 資料新鮮度

2. **技術指標**
   - 服務可用性
   - 回應時間
   - 錯誤率
   - 資源使用率

### 告警策略

1. **嚴重告警** (立即響應)

   - 服務完全不可用
   - 資料遺失
   - 安全事件

2. **警告告警** (工作時間響應)
   - 效能下降
   - 資料品質問題
   - 資源使用異常

## 支援和聯絡

- **技術問題**: 建立 GitHub Issue
- **技術文件**: `/docs` 目錄
- **API 文件**: https://integrated-event.tagtoo.com/docs

## 授權

此專案為 Tagtoo 內部專案，請遵循公司相關政策。

---

**最後更新**: 2025-07-09
**版本**: v0.1.0
**狀態**: 開發中 🚧
