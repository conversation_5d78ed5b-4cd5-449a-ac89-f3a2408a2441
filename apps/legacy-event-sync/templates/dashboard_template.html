<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Legacy Event Sync 儀表板</title>
    <link rel="stylesheet" href="https://storage.googleapis.com/integrated-event-platform-prod-tagtoo-tracking/shared-assets/css/common.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header .subtitle { color: #7f8c8d; font-size: 14px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { margin: 0 0 10px 0; color: #34495e; font-size: 16px; }
        .big-number { font-size: 32px; font-weight: bold; margin: 10px 0; }
        .cost-card .big-number { color: #e74c3c; }
        .progress-card .big-number { color: #27ae60; }
        .events-card .big-number { color: #3498db; }
        .time-card .big-number { color: #9b59b6; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #27ae60, #2ecc71); transition: width 0.3s ease; }
        .charts-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .chart-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
        .details-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .detail-item { padding: 10px 0; border-bottom: 1px solid #ecf0f1; }
        .detail-label { font-weight: bold; color: #34495e; }
        .detail-value { color: #7f8c8d; }
        @media (max-width: 768px) {
            .charts-container { grid-template-columns: 1fr; }
            .details-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Legacy Event Sync 即時儀表板</h1>
            <div class="subtitle">
                最後更新: {{ last_updated }} |
                模式: {{ sync_mode }} |
                狀態: <span class="status-indicator status-{{ status_class }}"></span>{{ status_text }}
            </div>
        </div>

        <div class="metrics-grid">
            <div class="card cost-card">
                <h3>💰 BigQuery 成本</h3>
                <p class="big-number">${{ cost_data.total_cost_usd }}</p>
                <small>掃描量: {{ cost_data.tb_processed }} TB</small><br>
                <small>成本等級: {{ cost_data.cost_level }}</small>
            </div>

            <div class="card events-card">
                <h3>📊 事件統計</h3>
                <p class="big-number">{{ events_data.total_events }}</p>
                <small>成功: {{ events_data.synced_events }} | 失敗: {{ events_data.error_events }}</small>
            </div>

            <div class="card progress-card">
                <h3>⏳ 同步進度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
                </div>
                <small>{{ current_segment }}/{{ total_segments }} 分段</small>
            </div>

            <div class="card time-card">
                <h3>⏱️ 執行時間</h3>
                <p class="big-number">{{ execution_time.total_minutes }}m</p>
                <small>開始: {{ time_range.start_time }}</small><br>
                <small>結束: {{ time_range.end_time }}</small>
            </div>
        </div>

        <div class="charts-container">
            <div class="chart-card">
                <h3>📈 成本趨勢</h3>
                <canvas id="costTrendChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-card">
                <h3>🚀 處理效率</h3>
                <canvas id="throughputChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="details-section">
            <h3>📋 詳細資訊</h3>
            <div class="details-grid">
                <div>
                    <div class="detail-item">
                        <div class="detail-label">來源表格</div>
                        <div class="detail-value">{{ config.source_table }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">目標表格</div>
                        <div class="detail-value">{{ config.target_table }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">批次大小</div>
                        <div class="detail-value">{{ config.batch_size }} 筆</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">分段間隔</div>
                        <div class="detail-value">{{ config.split_interval_minutes }} 分鐘</div>
                    </div>
                </div>
                <div>
                    <div class="detail-item">
                        <div class="detail-label">去重複模式</div>
                        <div class="detail-value">{{ "啟用" if config.enable_deduplication else "停用" }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">執行模式</div>
                        <div class="detail-value">{{ sync_mode_desc }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">平行處理</div>
                        <div class="detail-value">{{ config.max_workers }} 個工作執行緒</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">記憶體限制</div>
                        <div class="detail-value">{{ config.memory_limit_gb }} GB</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化圖表
        const costCtx = document.getElementById('costTrendChart').getContext('2d');
        const throughputCtx = document.getElementById('throughputChart').getContext('2d');

        // 成本趨勢圖
        new Chart(costCtx, {
            type: 'line',
            data: {
                labels: {{ chart_data.cost_labels | safe }},
                datasets: [{
                    label: '累積成本 (USD)',
                    data: {{ chart_data.cost_values | safe }},
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // 處理效率圖
        new Chart(throughputCtx, {
            type: 'bar',
            data: {
                labels: {{ chart_data.throughput_labels | safe }},
                datasets: [{
                    label: '每分鐘處理事件數',
                    data: {{ chart_data.throughput_values | safe }},
                    backgroundColor: '#3498db'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // 自動重新整理 (每30秒)
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
