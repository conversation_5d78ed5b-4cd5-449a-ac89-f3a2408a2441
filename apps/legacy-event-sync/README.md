# Legacy Event Sync Service

> 此服務用於將現有 BigQuery table `tagtoo-tracking.event_prod.tagtoo_event` 資料同步到整合事件表格 `tagtoo-tracking.event_prod.integrated_event` BigQuery table。

## 📋 專案概述

此服務負責定期（預設每日）將 `tagtoo-tracking.event_prod.tagtoo_event` 的資料轉換並同步到 `tagtoo-tracking.event_prod.integrated_event` BigQuery table。

服務採用 **Cloud Run + Cloud Tasks** 分散式架構：

- **Coordinator (`/start-sync`)**: 由 Cloud Scheduler 觸發，負責計算需同步的時間區間，並為每個區間建立一個 Cloud Task。
- **Worker (`/process-segment`)**: 由 Cloud Task 觸發，負責處理單一時間區間的資料同步。
- **Monitor (`/sync-status`)**: 提供即時的同步狀態監控與 BigQuery 成本分析。

## 🏗️ 系統架構

### 整體運作流程

```mermaid
graph TD
    A["📊 來源資料<br/>tagtoo_event"] --> B["⏰ Cloud Scheduler<br/>每 60 分鐘觸發"]
    B --> C["🚀 Cloud Run<br/>Coordinator"]
    C --> D["📋 計算時間區段<br/>每 5 分鐘一段"]
    D --> E["📤 建立 Cloud Tasks<br/>分散式處理"]
    E --> F["⚡ Worker Endpoints<br/>並行處理"]
    F --> G["🔄 資料轉換<br/>標準化格式"]
    G --> H["🎯 MERGE 寫入<br/>去重複邏輯"]
    H --> I["📈 目標資料<br/>integrated_event"]

    J["📊 監控 API"] --> K["🔍 同步率驗證"]
    K --> L["📈 健康狀態回報"]

    C --> J
    I --> K

    style A fill:#e1f5fe
    style I fill:#e8f5e8
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style F fill:#fff8e1
```

### 核心組件說明

#### 1. **協調器 (Coordinator)**

- **功能**: 計算需要同步的時間範圍，分割成小段，建立 Cloud Tasks
- **輸入**: 可選的開始/結束時間，預設處理前 2 小時的資料
- **輸出**: Cloud Tasks 任務數量和執行狀態
- **特色**:
  - 自動標準化到整點邊界確保冪等性
  - 智能檢測時間空隙並發出警告
  - 支援手動指定時間範圍進行補齊

#### 2. **工作者 (Worker)**

- **功能**: 處理單一時間段的資料同步
- **處理邏輯**: 串流讀取 → 批次轉換 → 記憶體去重 → MERGE 寫入
- **效能特色**:
  - 串流處理避免記憶體溢出
  - 批次大小可配置 (預設 5000 筆)
  - 記憶體內去重減少 BigQuery 負荷
  - 進度監控每 5 分鐘報告狀態

#### 3. **監控系統 (Monitor)**

- **同步率驗證**: 比較來源與目標資料的完整性
- **成本分析**: 即時計算 BigQuery 查詢成本
- **健康檢查**: 綜合評估系統狀態
- **警告機制**: 自動偵測異常情況並提供優化建議

### 🎯 主要功能

- ✅ **定期同步**: 透過 Cloud Scheduler 定期觸發 (每 60 分鐘)
- ✅ **分散式處理**: 使用 Cloud Tasks 進行分段同步，提高穩定性和效能
- ✅ **智能去重**: 支援記憶體內去重和 BigQuery MERGE 操作
- ✅ **資料轉換**: 將舊格式轉換為新的統一格式
- ✅ **智能過濾**: 可配置排除特定事件類型 (如 focus 滑鼠追蹤事件)，大幅減少處理量
- ✅ **成本控制**: 內建 BigQuery 成本監控和優化建議
- ✅ **錯誤處理**: 具備重試機制和詳細錯誤日誌
- ✅ **效能優化**: Phase 1 優化提供 2-3 倍處理速度提升
- ✅ **監控完善**: 同步狀態查詢、健康檢查、成本分析
- ✅ **Feature Toggle**: 支援動態開關各種優化功能

## 🚀 **目前部署狀態 (Production)**

### ✅ **基礎設施狀態：**

- **Cloud Run 服務**: `legacy-event-sync-prod` - 運行正常
- **Cloud Scheduler**: 每小時執行一次 (`0 * * * *`) - 已啟用，處理 2 小時前的資料以避免 BigQuery streaming buffer 問題
- **Cloud Tasks 佇列**: `legacy-event-sync-segments-prod` - 運行中
- **認證配置**: 已移除公開存取，使用 Service Account 認證

### 📋 **資料同步狀況：**

- **Partner Source**: `legacy-tagtoo-event`
- **來源表格**: `tagtoo-tracking.event_prod.tagtoo_event` (28,336 萬筆記錄，持續更新中)
- **目標表格**: `tagtoo-tracking.event_prod.integrated_event`
- **同步狀態**: ✅ 服務正常運行，已成功同步 123 萬筆記錄
- **同步策略**: 每小時處理前 2 小時的資料，避免 BigQuery streaming buffer 延遲問題
- **處理效能**: 210+ 筆/秒 (Phase 1 優化後)

### 🔧 **Phase 1 優化效果：**

1. ✅ **簡化 MERGE 操作**: 移除複雜的 SQL 去重邏輯，效能提升 2-3 倍
2. ✅ **記憶體內去重**: 在 Python 端處理重複資料，減少 BigQuery 運算負荷
3. ✅ **串流處理**: 避免大批次資料造成的記憶體問題
4. ✅ **動態批次管理**: 根據記憶體使用情況調整批次大小
5. ✅ **成本監控**: 即時計算查詢成本並提供優化建議

### 📈 **監控端點：**

- 健康檢查: `/health` (需要認證)
- 同步狀態: `/sync-status` (需要認證)
- 手動觸發: `/start-sync` (POST, 需要認證)
- 分段處理: `/process-segment` (POST, Cloud Tasks 內部使用)

## 🔧 技術架構詳解

### 資料流向與處理邏輯

#### 1. **時間分段策略**

```python
# 配置參數
MINUTES_PER_SEGMENT = 5        # 每個分段處理 5 分鐘資料
SCHEDULER_INTERVAL_MINUTES = 60 # 每 60 分鐘觸發一次
BATCH_SIZE = 5000              # 每批次處理 5000 筆資料
```

#### 2. **資料轉換對應**

- **來源格式**: `tagtoo_event` (舊版事件結構)
- **目標格式**: `integrated_event` (統一事件結構)
- **主要轉換**:
  - JSON 欄位解析和重新結構化
  - 時間格式標準化 (ISO 8601)
  - 空值處理和預設值設定
  - 巢狀物件展開和重組

#### 3. **去重複邏輯**

```python
# 複合主鍵去重 (可透過 ENABLE_DEDUPLICATION 控制)
去重複鍵 = [
    "permanent",      # 使用者永久 ID
    "ec_id",         # 電商平台 ID
    "event_time",    # 事件時間
    "event",         # 事件類型
    "partner_source", # 來源標識
    "link",          # 頁面連結
    "value",         # 事件價值
    "currency",      # 貨幣
    "order_id"       # 訂單編號
]
```

#### 4. **效能優化機制**

- **記憶體管理**: 串流處理 + 批次清理
- **BigQuery 優化**: 簡化 MERGE 語法，移除複雜子查詢
- **並行處理**: Cloud Tasks 分散式執行
- **成本控制**: 查詢前估算成本，超過閾值警告

### Feature Toggle 系統

系統支援透過環境變數控制各種功能：

```bash
# 核心功能開關
ENABLE_DEDUPLICATION=false     # 去重複邏輯 (建議關閉以提升效能)
USE_OPTIMIZED_MERGE=true       # Phase 1 優化 (建議開啟)

# 🆕 同步模式控制 (經濟模式 vs 快速模式)
USE_POLARS=true               # 經濟模式開關 (建議啟用)
                              # true: 經濟模式 - 使用 polars 本地處理，大幅降低成本
                              # false: 快速模式 - 使用 BigQuery MERGE，處理速度較快但成本較高

# 事件過濾配置
EXCLUDED_EVENT_TYPES=["focus"] # 排除的事件類型 (JSON 陣列格式)
                               # focus: 滑鼠追蹤事件，約占 50% 資料量
                               # 可設定多個: ["focus", "scroll"]

# Streaming Buffer 延遲控制
STREAMING_BUFFER_OFFSET_HOURS=1 # 同步時間偏移（小時）
                                # 1: 基於真實監控的最佳化設定（預設）
                                # 1.5: 較保守設定，適合延遲不穩定環境
                                # 0.5: 積極設定，僅適合延遲極穩定情況

# 重疊同步機制（避免資料遺漏）
SYNC_OVERLAP_MINUTES=15         # 同步窗口重疊時間（分鐘）
                                # 15: 標準重疊，覆蓋典型 Streaming Buffer 延遲（預設）
                                # 30: 保守重疊，適合延遲不穩定環境
                                # 0: 停用重疊（不建議，可能遺漏資料）

# 效能調整
BATCH_SIZE=5000                # 批次大小
MINUTES_PER_SEGMENT=5          # 分段時間長度
MAX_CONCURRENT_SEGMENTS=20     # 最大並行分段數

# 資源配置
MEMORY_MB=4096                 # 記憶體限制
CPU_COUNT=4                    # CPU 核心數
```

## 🎯 同步模式架構

### 經濟模式 vs 快速模式

系統提供兩種同步模式，通過 `USE_POLARS` 環境變數控制：

#### 🟢 經濟模式 (`USE_POLARS=true`)

**特色：**

- 🔸 使用 polars 進行本地資料處理
- 🔸 避免昂貴的 BigQuery MERGE 操作
- 🔸 大幅降低 BigQuery 查詢成本
- 🔸 提供相同的冪等性保證

**工作流程：**

```mermaid
graph LR
    A[讀取 BigQuery] --> B[polars DataFrame]
    B --> C[本地資料清理]
    C --> D[冪等性檢查]
    D --> E[JSON 批次寫入]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#fff8e1
```

**成本效益：**

- 📊 **成本節省**: 約 200 倍（$11.26 → $0.055 USD/小時）
- 📊 **處理邏輯**: 本地 polars 處理 + BigQuery JSON 插入
- 📊 **適用場景**: 成本敏感、大量資料處理

#### 🔴 快速模式 (`USE_POLARS=false`)

**特色：**

- 🔸 使用 BigQuery MERGE 操作
- 🔸 完全在雲端處理，減少本地運算
- 🔸 較高的 BigQuery 查詢成本
- 🔸 傳統的資料處理方式

**工作流程：**

```mermaid
graph LR
    A[讀取 BigQuery] --> B[資料轉換]
    B --> C[建立臨時表]
    C --> D[執行 MERGE]
    D --> E[清理臨時表]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#fff8e1
    style D fill:#ffebee
    style E fill:#f3e5f5
```

**成本結構：**

- 📊 **主要成本**: BigQuery MERGE 操作
- 📊 **處理邏輯**: 雲端運算為主
- 📊 **適用場景**: 速度優先、小量資料處理

### 🛡️ 智能降級機制

系統提供完整的錯誤處理和降級策略：

```python
# 自動降級邏輯
try:
    if USE_POLARS:
        return process_batch_economical(events)  # 嘗試經濟模式
except Exception as e:
    logger.warning(f"經濟模式失敗，自動降級到快速模式: {e}")
    return process_batch_fast(events)           # 降級到快速模式
```

**降級觸發條件：**

- polars 處理異常
- 記憶體不足錯誤
- 本地資料處理失敗
- 冪等性檢查錯誤

### 💰 成本分析與監控

系統內建完整的成本追踪功能：

```bash
# 查看成本分析
curl "http://localhost:8080/bigquery-costs?analysis_type=detailed"

# 檢視模式效能對比
curl "http://localhost:8080/sync-status" | jq '.bigquery_analysis.by_processing_mode'
```

**成本追踪項目：**

- 📈 即時查詢成本估算
- 📈 兩種模式成本對比
- 📈 節省效果計算
- 📈 處理效率指標
- 📈 最佳化建議

**典型成本節省範例：**

```json
{
  "cost_comparison": {
    "estimated_fast_mode_cost_usd": 0.015,
    "economical_mode_cost_usd": 0.0001,
    "cost_savings_usd": 0.0149,
    "savings_percentage": 99.3,
    "cost_efficiency_ratio": 150.0
  },
  "efficiency_metrics": {
    "records_per_second": 185.2,
    "cost_per_thousand_records": 0.0002,
    "processing_mode_advantage": "99% 成本節省"
  }
}
```

### ⚙️ 模式選擇建議

**選擇經濟模式的情況：**

- ✅ 成本控制是主要考量
- ✅ 處理大量歷史資料
- ✅ 批次處理非即時需求
- ✅ 有足夠的運算資源

**選擇快速模式的情況：**

- ✅ 處理速度是首要需求
- ✅ 小量資料即時同步
- ✅ 雲端運算資源充足
- ✅ BigQuery 成本不敏感

**預設建議：** 啟用經濟模式 (`USE_POLARS=true`)，獲得最佳成本效益。

## 🗂️ 資料欄位對應（摘錄）

來源 `tagtoo_event` ➜ 目標 `integrated_event` 主要欄位對照：

| 來源欄位                     | 目標欄位                | 說明                            |
| ---------------------------- | ----------------------- | ------------------------------- |
| `permanent`                  | `permanent`             | 使用者永久 ID                   |
| `ec_id`                      | `ec_id`                 | 電商平台 ID                     |
| `event_time`                 | `event_time`            | 事件發生時間 (ISO)              |
| `event.name`                 | `event`                 | 事件名稱                        |
| `event.value`                | `value`                 | 事件價值                        |
| `event.currency`             | `currency`              | 貨幣                            |
| `event.custom_data.order_id` | `order_id`              | 訂單編號                        |
| `event.items[]`              | `items[]`               | 商品陣列                        |
| `location.country_code`      | `location.country_code` | 國碼                            |
| `user.em`                    | `user.em`               | 加密電子郵件                    |
| `user.ph`                    | `user.ph`               | 加密電話                        |
| `link`                       | `link`                  | 頁面連結 (空值轉為空字串)       |
| `N/A`                        | `partner_source`        | 固定值: `"legacy-tagtoo-event"` |
| `N/A`                        | `create_time`           | 同步建立時間 (UTC)              |

完整的欄位對應與設計原則請參閱 [`docs/DEVELOPMENT.md`](docs/DEVELOPMENT.md)。

## 🚀 快速開始

### 本地開發

```bash
# 進入服務目錄
cd apps/legacy-event-sync

# 設定認證（一次性）
make auth-setup

# 啟動開發環境（使用真實 BigQuery + 測試表格）
make dev-up

# 執行測試
make test

# 測試同步功能
make sync-manual

# 查看服務日誌
make dev-logs

# 停止服務
make dev-down
```

### 測試驗證

```bash
# 執行完整測試套件
make test

# 單元測試
make test-unit

# 整合測試（需要真實 BigQuery 連接）
make test-integration

# 檢查測試環境
make test-env-check
```

### 同步狀態監控

```bash
# 檢查今天的同步狀態
make check-sync-status

# 檢查特定日期 (需要 DATE 參數)
make check-sync-status-date DATE=2024-01-15

# 只查看成本分析
make check-sync-status-cost
make check-sync-status-date-cost DATE=2024-01-15
```

## 🚀 部署

### 手動觸發部署 (GitHub Actions)

現在支援直接從 GitHub Actions 手動觸發部署，無需修改代碼：

1. **前往 GitHub Actions**: [https://github.com/Tagtoo/integrated-event/actions](https://github.com/Tagtoo/integrated-event/actions)
2. **選擇 Workflow**: 點擊 "Apps CI/CD Pipeline"
3. **手動觸發**: 點擊 "Run workflow" 按鈕
4. **配置選項**:
   - **Service**: 輸入 `legacy-event-sync`（或留空部署所有服務）
   - **Environment**: 選擇 `prod` 或 `dev`
5. **開始部署**: 點擊 "Run workflow"

### 其他部署方法

```bash
# 本地部署到開發環境
make deploy-dev

# 本地部署到生產環境（需要手動確認）
make deploy-prod

# 空提交觸發 CI/CD
git commit --allow-empty -m "[deploy] 觸發 legacy-event-sync 部署"
git push origin main
```

### 本地測試 CI/CD (使用 Act)

```bash
# 測試特定 job
act push --env-file apps/legacy-event-sync/act.env -j detect-changes

# 測試完整 workflow
act push --env-file apps/legacy-event-sync/act.env
```

詳細的部署說明請參閱 [`docs/DEPLOYMENT.md`](docs/DEPLOYMENT.md)。

## 📊 API 端點

### 健康檢查

```bash
# 檢查服務健康狀態
curl http://localhost:8080/health
```

### Streaming Buffer 狀況檢查 (新功能)

```bash
# 檢查 BigQuery Streaming Buffer 狀況
curl http://localhost:8080/streaming-buffer-status

# 不包含建議的簡化版本
curl "http://localhost:8080/streaming-buffer-status?include_recommendations=false"
```

**回應範例：**

```json
{
  "timestamp": "2025-07-23T03:09:24.137297",
  "streaming_buffer": {
    "has_streaming_buffer": true,
    "estimated_size_mb": 147.1,
    "estimated_rows": 167226,
    "delay_minutes": 9.5,
    "current_offset_minutes": 120,
    "is_safe": true,
    "recommendation": "當前偏移充足"
  },
  "quick_diagnosis": {
    "status_level": "excellent",
    "status_message": "延遲極低，適合即時處理",
    "delay_assessment": "當前延遲 9.5 分鐘"
  },
  "recommendations": [
    {
      "type": "optimization",
      "priority": "medium",
      "message": "當前延遲只有 9.5 分鐘，可考慮縮短同步偏移時間",
      "suggested_action": "可嘗試將偏移時間從 120 分鐘縮短至 60 分鐘"
    }
  ]
}
```

### 同步狀態查詢

```bash
# 查詢今天的同步狀態
curl http://localhost:8080/sync-status

# 查詢特定日期的同步狀態
curl "http://localhost:8080/sync-status?date=2024-01-15"
```

**回應範例：**

```json
{
  "date": "2024-01-15",
  "status": "healthy",
  "last_sync_time": "2024-01-15T10:30:00",
  "source_events_count": 12500,
  "target_events_count": 12500,
  "sync_coverage_percent": 100.0,
  "latest_data_timestamp": "2024-01-15T10:25:00",
  "warnings": [],
  "table_info": {
    "source_table": "tagtoo-tracking.event_prod.tagtoo_event",
    "target_table": "tagtoo-tracking.event_test.integrated_event"
  },
  "filtering_config": {
    "excluded_event_types": ["focus"],
    "filtering_enabled": true,
    "description": "已過濾 1 種事件類型"
  },
  "bigquery_analysis": {
    "total_cost_usd": 0.0125,
    "total_tb_processed": 0.0025,
    "total_execution_time_seconds": 3.2,
    "source_query_stats": {
      "bytes_processed": **********,
      "tb_processed": 0.00125,
      "estimated_cost_usd": 0.00625,
      "execution_time_seconds": 1.8
    },
    "target_query_stats": {
      "bytes_processed": **********,
      "tb_processed": 0.00125,
      "estimated_cost_usd": 0.00625,
      "execution_time_seconds": 1.4
    },
    "optimization_suggestions": [
      "Consider adding date partitioning filters to reduce data scan"
    ]
  },
  "streaming_buffer_info": {
    "has_streaming_buffer": true,
    "delay_minutes": 9.5,
    "estimated_size_mb": 147.1,
    "is_safe": true,
    "recommendation": "當前偏移充足"
  }
}
```

**狀態說明：**

- `healthy`: 同步正常，覆蓋率 ≥ 95%，最近 6 小時內有同步，查詢成本 < $0.50
- `warning`: 同步覆蓋率 < 95% 或超過 6 小時未同步或查詢成本過高
- `error`: 發生錯誤無法取得狀態

**過濾配置說明：**

- `excluded_event_types`: 被排除的事件類型清單
- `filtering_enabled`: 是否啟用事件過濾
- `description`: 過濾配置的文字描述

**Streaming Buffer 資訊說明：**

- `has_streaming_buffer`: 是否偵測到 Streaming Buffer
- `delay_minutes`: 當前 Streaming Buffer 延遲時間（分鐘）
- `estimated_size_mb`: Streaming Buffer 預估大小
- `is_safe`: 當前偏移時間是否足夠安全
- `recommendation`: 基於當前狀況的建議

**BigQuery 成本分析說明：**

- `total_cost_usd`: 本次狀態查詢的總預估成本（美元）
- `total_tb_processed`: 總掃描資料量（TB）
- `total_execution_time_seconds`: 總查詢執行時間（秒）
- `optimization_suggestions`: 自動產生的查詢優化建議
- `source_query_stats` / `target_query_stats`: 個別查詢的詳細統計資訊

**成本控制警告：**

- 查詢成本 > $0.10: 顯示 moderate cost warning
- 查詢成本 > $0.50: 狀態變為 warning，顯示 high cost warning
- 執行時間 > 30 秒: 顯示 slow query warning

### 手動觸發同步

```bash
# 觸發手動同步（時間範圍可選）
curl -X POST http://localhost:8080/start-sync \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-01-15T00:00:00", "end_date": "2024-01-15T05:00:00"}'
```

### 回補遺漏時段同步 (新功能)

```bash
# 回補指定時間範圍的遺漏資料
curl -X POST http://localhost:8080/backfill-sync \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-01-15T00:00:00", "end_date": "2024-01-15T05:00:00"}'
```

**回補功能特色：**

- 🔸 **安全限制**: 單次最多回補 72 小時資料
- 🔸 **時間驗證**: 自動驗證時間範圍合理性
- 🔸 **操作標記**: 回應中包含 `operation_type: "backfill"` 標記
- 🔸 **進度追蹤**: 提供回補範圍和預估時間資訊

**回應範例：**

```json
{
  "status": "success",
  "operation_type": "backfill",
  "total_segments": 12,
  "tasks_created": 12,
  "backfill_range": {
    "start_date": "2024-01-15T00:00:00",
    "end_date": "2024-01-15T05:00:00",
    "hours_covered": 5.0
  }
}
```

### BigQuery 成本分析端點 (新功能)

```bash
# 快速成本分析
curl "http://localhost:8080/bigquery-costs"

# 詳細成本分析（包含實際查詢）
curl "http://localhost:8080/bigquery-costs?analysis_type=detailed"

# 特定日期成本分析
curl "http://localhost:8080/bigquery-costs?date=2024-01-15&analysis_type=detailed"
```

**功能特色：**

- 🔸 **即時成本估算**: 提供當前查詢的成本預估
- 🔸 **模式對比分析**: 比較經濟模式與快速模式的成本差異
- 🔸 **最佳化建議**: 自動生成查詢最佳化建議
- 🔸 **成本控制**: 內建成本閾值警告機制

**詳細回應範例：**

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "analysis_date": "2024-01-15",
  "analysis_type": "detailed",
  "cost_tracking_enabled": true,
  "detailed_analysis": {
    "sync_status": {
      "source_events_count": 125000,
      "target_events_count": 124800,
      "sync_coverage_percent": 99.8
    },
    "cost_breakdown": {
      "by_processing_mode": {
        "economical": {
          "count": 18,
          "total_cost_usd": 0.0045,
          "avg_cost_usd": 0.0003,
          "total_savings_usd": 0.265,
          "percentage_of_operations": 85.7
        },
        "fast": {
          "count": 3,
          "total_cost_usd": 0.089,
          "avg_cost_usd": 0.0297,
          "percentage_of_operations": 14.3
        }
      },
      "real_time_costs": {
        "total_cost_usd": 0.0935,
        "cost_efficiency_score": 92.5
      }
    }
  },
  "optimization_recommendations": [
    "🎯 使用分區表 (PARTITION BY DATE) 減少資料掃描範圍",
    "🔍 避免 SELECT * 查詢，只選取必要欄位",
    "⏰ 在測試環境使用 LIMIT 限制查詢範圍"
  ]
}
```

### 分段處理端點 (內部使用)

```bash
# 處理單一時間分段（通常由 Cloud Tasks 調用）
curl -X POST http://localhost:8080/process-segment \
  -H "Content-Type: application/json" \
  -d '{"start_time": "2024-01-15T10:00:00", "end_time": "2024-01-15T10:05:00"}'
```

## 🛠️ 開發工具

### 手動同步腳本

#### ⚠️ **重要時區說明**

- **所有時間參數都使用台灣時間 (UTC+8)**
- **腳本內部會自動轉換為 UTC 進行查詢**
- **請務必確認您輸入的是台灣時間！**

#### 🚀 推薦使用方式 (Normal Mode)

```bash
# 推薦：Normal Mode - 平行處理 + 低成本 + 可靠冪等性
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  -e USE_POLARS=true \
  -e ENABLE_DASHBOARD=false \
  -e ENABLE_DEDUPLICATION=false \
  -e ENABLE_COST_TRACKING=true \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode normal \
  --split-interval-minutes 60 \
  --verbose \
  --production
```

#### 💰 成本估算 (Dry Run)

```bash
# 先估算成本（不實際執行同步）
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode normal \
  --split-interval-minutes 60 \
  --dry_run \
  --verbose
```

#### ⚡ 其他模式選擇

```bash
# Economical Mode - 最低成本，順序處理
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode economical \
  --split-interval-minutes 60 \
  --verbose \
  --production

# Fast Mode - 最高效能，BigQuery MERGE，成本較高
docker-compose exec -T \
  -e USE_POLARS=false \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode fast \
  --split-interval-minutes 60 \
  --verbose \
  --production
```

#### 📊 參數說明

| 參數                       | 說明                | 建議值                            |
| -------------------------- | ------------------- | --------------------------------- |
| `--start`                  | 開始時間 (台灣時間) | `"2025-07-04 22:00:00"`           |
| `--end`                    | 結束時間 (台灣時間) | `"2025-07-05 00:00:00"`           |
| `--mode`                   | 同步模式            | `normal` (推薦)                   |
| `--split-interval-minutes` | 時間切片 (分鐘)     | `60` (大量資料) / `15` (小量資料) |
| `BATCH_SIZE`               | 批次大小            | `15000` (最佳效能)                |
| `--verbose`                | 詳細日誌            | 建議開啟                          |
| `--production`             | 生產環境模式        | **必須指定**                      |

### 同步率驗證腳本

#### ⚠️ **重要時區說明**

- **驗證腳本的時間參數同樣使用台灣時間 (UTC+8)**
- **必須與手動同步腳本的時間範圍一致**
- **驗證結果會顯示台灣時間**

#### 🔍 基本驗證

```bash
# 基本同步率驗證（台灣時間輸入）
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 11:00:00" \
  --end "2025-07-04 18:00:00" \
  --production \
  --sample_size 0
```

#### 📈 詳細分析

```bash
# 完整資料驗證（不抽樣）
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 0 \
  --verbose

# 抽樣驗證（適合大量資料）
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 1000 \
  --verbose

# 啟用去重邏輯驗證
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --enable_deduplication \
  --sample_size 0
```

#### 🕒 時區轉換說明

**重要：時間轉換原理**

```
輸入時間 (台灣時間): 2025-07-04 22:00:00
            ↓ (腳本內部轉換)
BigQuery 查詢時間 (UTC): 2025-07-04 14:00:00
            ↓ (查詢完成)
顯示時間 (台灣時間): 2025-07-04 22:00:00
```

**確認時區設定**

```bash
# 檢查系統時區設定
docker-compose exec -T legacy-event-sync date
docker-compose exec -T legacy-event-sync python3 -c "import datetime; print(datetime.datetime.now())"
```

#### 📋 驗證參數說明

| 參數                     | 說明                | 建議值                     |
| ------------------------ | ------------------- | -------------------------- |
| `--start`                | 開始時間 (台灣時間) | 與同步腳本一致             |
| `--end`                  | 結束時間 (台灣時間) | 與同步腳本一致             |
| `--sample_size`          | 抽樣數量            | `0` (完整) / `1000` (抽樣) |
| `--production`           | 生產環境模式        | **必須指定**               |
| `--enable_deduplication` | 去重邏輯驗證        | 可選                       |
| `--verbose`              | 詳細輸出            | 建議開啟                   |

#### ⚠️ 注意事項

1. **時間一致性**：驗證腳本的時間範圍必須與同步腳本完全一致
2. **環境變數**：驗證腳本會自動讀取 `EXCLUDED_EVENT_TYPES` 環境變數
3. **資料延遲**：BigQuery Streaming Buffer 可能有 1-2 分鐘延遲
4. **成本控制**：大範圍驗證建議使用 `--sample_size` 限制查詢量

### Streaming Buffer 監控腳本

```bash
# 檢查當前 Streaming Buffer 狀況
docker-compose exec -T legacy-event-sync python scripts/check_streaming_buffer.py

# 分析過去 24 小時的延遲趨勢
docker-compose exec -T legacy-event-sync python scripts/check_streaming_buffer.py --analyze-trends

# 獲取智能同步偏移建議
docker-compose exec -T legacy-event-sync python scripts/check_streaming_buffer.py --recommend-offset

# 完整分析（包含趨勢和建議）
docker-compose exec -T legacy-event-sync python scripts/check_streaming_buffer.py --analyze-trends --recommend-offset --hours 48
```

### 資料庫 Schema 比較

```bash
# 比較本地和 BigQuery 的 Schema 一致性
make check-schema
```

## 📚 相關文件

- 📖 **[手動同步操作指南](docs/MANUAL_SYNC_GUIDE.md)** - 完整的手動同步腳本使用說明（⚠️ 包含重要時區說明）
- [`SYNC_MODES.md`](SYNC_MODES.md) - 三種同步模式詳細比較
- [`docs/DEVELOPMENT.md`](docs/DEVELOPMENT.md) - 詳細開發指南和欄位對應
- [`docs/DEPLOYMENT.md`](docs/DEPLOYMENT.md) - 部署流程和配置說明
- [`docs/AUTHENTICATION.md`](docs/AUTHENTICATION.md) - 認證設定指南
- [`docs/TESTING.md`](docs/TESTING.md) - 測試策略和方法
- [`docs/PERFORMANCE_OPTIMIZATION_PLAN.md`](docs/PERFORMANCE_OPTIMIZATION_PLAN.md) - 效能優化計畫
- [`docs/PHASE1_DEPLOYMENT_GUIDE.md`](docs/PHASE1_DEPLOYMENT_GUIDE.md) - Phase 1 優化部署指南

## 🚨 疑難排解

### 常見問題

1. **同步率低於預期**

   - 檢查來源資料是否正常更新
   - 驗證時間範圍設定是否正確
   - 查看錯誤日誌確認是否有處理失敗

2. **BigQuery 成本過高**

   - 使用 `/sync-status` API 查看成本分析
   - 考慮調整查詢時間範圍
   - 檢查是否需要新增分區過濾條件

3. **記憶體使用過高**

   - 調降 `BATCH_SIZE` 參數
   - 確認去重複邏輯是否過於複雜
   - 監控 Cloud Run 記憶體使用率

4. **同步延遲問題**

   - 檢查 Cloud Tasks 佇列狀況
   - 確認 Cloud Scheduler 觸發正常
   - 驗證 BigQuery streaming buffer 延遲

5. **經濟模式處理失敗** 🆕

   - 檢查 polars 套件是否正確安裝
   - 驗證記憶體是否足夠支持 DataFrame 操作
   - 查看是否有資料格式不相容問題
   - 確認 BigQuery JSON 寫入權限

6. **模式切換異常** 🆕

   - 驗證 `USE_POLARS` 環境變數設定
   - 檢查降級機制是否正常運作
   - 確認兩種模式的功能等價性
   - 監控成本追踪是否正常記錄

7. **冪等性檢查緩慢** 🆕

   - 檢查 BigQuery 查詢效能
   - 考慮調整時間範圍窗口大小
   - 驗證複合主鍵索引狀況
   - 監控 BigQuery 配額使用情況

### 監控指令

```bash
# 檢查服務狀態
make health-check

# 查看詳細日誌
make dev-logs

# 進入容器除錯
make dev-shell

# 檢查認證狀態
make auth-check
```

## 📈 效能指標

### 目標效能

- **處理速度**: 210+ 筆/秒 (Phase 1 優化後)
- **同步率**: 高流量時段 90%+，低流量時段 99%+
- **錯誤率**: < 1%
- **查詢成本**: 平均 < $0.10 per status check

### 資源使用

- **CPU**: 4 核心 (可調整)
- **記憶體**: 4GB (可調整)
- **網路**: 主要為 BigQuery API 調用
- **儲存**: 臨時表和日誌

## 🔐 安全性

- **認證**: 使用 Google Cloud Service Account
- **授權**: 最小權限原則 (BigQuery Data Editor + Job User)
- **網路**: Cloud Run 內部通訊，外部 API 需要認證
- **資料**: 所有傳輸使用 HTTPS，敏感資料加密處理

---

**總結**: `legacy-event-sync` 是一個高效能、可監控、可擴展的資料同步系統，為 Tagtoo 事件資料的整合提供了可靠的基礎架構。透過分散式處理和智能優化，確保資料同步的準確性和效能。
