# Legacy Event Sync - 手動同步操作指南

> 🕒 **重要提醒：所有時間參數都使用台灣時間 (UTC+8)，腳本會自動轉換為 UTC 進行查詢**

## 📋 目錄

- [🚀 快速開始](#-快速開始)
- [⚠️ 時區說明](#️-時區說明)
- [📊 同步模式選擇](#-同步模式選擇)
- [🔍 驗證指南](#-驗證指南)
- [💡 最佳實踐](#-最佳實踐)
- [🐛 常見問題](#-常見問題)

---

## 🚀 快速開始

### 標準操作流程

```bash
# 1. 估算成本 (Dry Run)
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode normal \
  --split-interval-minutes 60 \
  --dry_run \
  --verbose

# 2. 實際同步
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  -e USE_POLARS=true \
  -e ENABLE_DASHBOARD=false \
  -e ENABLE_DEDUPLICATION=false \
  -e ENABLE_COST_TRACKING=true \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode normal \
  --split-interval-minutes 60 \
  --verbose \
  --production

# 3. 驗證同步結果
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 0
```

---

## ⚠️ 時區說明

### 🕒 重要概念

**所有腳本的時間輸入都必須是台灣時間 (UTC+8)**

#### 時間轉換流程

```
👤 使用者輸入    腳本內部處理      BigQuery 查詢     結果顯示
台灣時間        →  UTC 轉換    →   UTC 查詢     →   台灣時間
22:00:00          14:00:00        14:00:00        22:00:00
(UTC+8)           (UTC+0)         (UTC+0)         (UTC+8)
```

#### 常見時區錯誤

❌ **錯誤示例**

```bash
# 錯誤：直接輸入 UTC 時間
--start "2025-07-04 14:00:00"  # 這是 UTC 時間，會被錯誤轉換
```

✅ **正確示例**

```bash
# 正確：輸入台灣時間
--start "2025-07-04 22:00:00"  # 這是台灣時間，會正確轉換為 UTC 14:00
```

#### 驗證時區設定

```bash
# 檢查容器時區
docker-compose exec -T legacy-event-sync date

# 檢查 Python 時區處理
docker-compose exec -T legacy-event-sync python3 -c "
import datetime
from zoneinfo import ZoneInfo
print('系統時間:', datetime.datetime.now())
print('台灣時間:', datetime.datetime.now(ZoneInfo('Asia/Taipei')))
print('UTC 時間:', datetime.datetime.now(ZoneInfo('UTC')))
"
```

---

## 📊 同步模式選擇

### 🥇 Normal Mode (推薦)

**最佳平衡：速度 + 成本 + 可靠性**

```bash
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  -e USE_POLARS=true \
  -e ENABLE_DASHBOARD=false \
  -e ENABLE_DEDUPLICATION=false \
  -e ENABLE_COST_TRACKING=true \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode normal \
  --split-interval-minutes 60 \
  --verbose \
  --production
```

**特性**：

- ✅ 平行處理 (速度快)
- ✅ Polars 本地處理 (成本低)
- ✅ 完全冪等性 (可重複執行)
- ✅ 統計計數已修復

### 💰 Economical Mode

**最低成本，適合測試**

```bash
docker-compose exec -T \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode economical \
  --split-interval-minutes 60 \
  --verbose \
  --production
```

**特性**：

- 💰 最低成本
- 🐌 順序處理 (較慢)
- ✅ 冪等性保護
- 🧪 適合小範圍測試

### ⚡ Fast Mode

**最高效能，成本較高**

```bash
docker-compose exec -T \
  -e USE_POLARS=false \
  -e BATCH_SIZE=15000 \
  legacy-event-sync python scripts/manual_sync.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --mode fast \
  --split-interval-minutes 60 \
  --verbose \
  --production
```

**特性**：

- ⚡ 最快速度
- 💸 成本最高 (BigQuery MERGE)
- ✅ 完全冪等性
- 🎯 適合關鍵時段

### 📊 模式比較

| 特性         | Economical           | Normal               | Fast           |
| ------------ | -------------------- | -------------------- | -------------- |
| **處理方式** | 順序                 | 平行                 | 平行           |
| **主要操作** | Polars + JSON Insert | Polars + JSON Insert | BigQuery MERGE |
| **相對成本** | 1x                   | 1x                   | ~100x          |
| **相對速度** | 1x                   | 2-3x                 | 3-4x           |
| **冪等性**   | ✅                   | ✅                   | ✅             |
| **適用場景** | 測試、小量資料       | **日常使用**         | 關鍵時段       |

---

## 🔍 驗證指南

### 🎯 基本驗證

```bash
# 快速驗證（適用於大部分情況）
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 0
```

### 📈 詳細驗證

```bash
# 完整驗證（包含詳細統計）
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 0 \
  --verbose

# 去重邏輯驗證
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --enable_deduplication \
  --sample_size 0
```

### 🔍 抽樣驗證

適用於大量資料的成本控制：

```bash
# 抽樣 1000 筆進行驗證
docker-compose exec -T legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "2025-07-04 22:00:00" \
  --end "2025-07-05 00:00:00" \
  --production \
  --sample_size 1000 \
  --verbose
```

### 📊 驗證結果解讀

**正常結果示例**：

```
日期時間      原始資料數      去重後數量      實際同步數 原始同步率 真實同步率
----------------------------------------------------------------------------------
 07/04 22:00         647,546         637,897         637,897     98.51%    100.00%
 07/04 23:00         711,288         700,423         700,423     98.47%    100.00%
```

**關鍵指標**：

- **真實同步率**: 應該接近 100%
- **原始同步率**: 通常 98-99% (正常的去重效果)
- **實際同步數**: 應該與去重後數量一致

---

## 💡 最佳實踐

### 🕐 時間範圍規劃

#### 小量資料 (< 100萬筆/小時)

```bash
--split-interval-minutes 15
BATCH_SIZE=15000
```

#### 大量資料 (> 100萬筆/小時)

```bash
--split-interval-minutes 60
BATCH_SIZE=15000
```

#### 超大量資料 (> 500萬筆/小時)

```bash
--split-interval-minutes 60
BATCH_SIZE=20000
```

### 🚀 效能最佳化

#### 環境變數配置

```bash
# 標準配置 (推薦)
-e BATCH_SIZE=15000              # 最佳批次大小
-e USE_POLARS=true               # 啟用 Polars 高效處理
-e ENABLE_DASHBOARD=false        # 停用儀表板節省資源
-e ENABLE_DEDUPLICATION=false    # 已有冪等性檢查，可停用
-e ENABLE_COST_TRACKING=true     # 開啟成本追踪
```

#### 批次大小調整原則

| 資料量/小時 | BATCH_SIZE | split-interval-minutes |
| ----------- | ---------- | ---------------------- |
| < 50萬      | 10000      | 15                     |
| 50-200萬    | 15000      | 30                     |
| 200-500萬   | 15000      | 60                     |
| > 500萬     | 20000      | 60                     |

### 🔒 安全實踐

#### 1. 務必先執行 Dry Run

```bash
# 永遠先估算成本
--dry_run
```

#### 2. 分階段執行大範圍同步

```bash
# 不要一次同步超過 24 小時的資料
# 建議分成 4-8 小時的時段
```

#### 3. 驗證後再繼續

```bash
# 每個時段同步後都執行驗證
# 確認無誤再繼續下一時段
```

---

## 🐛 常見問題

### ❓ 同步統計顯示 0 筆

**問題**：`✅ 成功同步: 0 筆`，但實際有資料寫入

**解決方案**：
此問題已在最新版本中修復。如果仍遇到，請檢查：

```bash
# 確認使用最新版本
git log --oneline -5

# 應該看到：
# [fix](sync-counter): 修復 manual_sync.py 中的統計計數 Bug
```

### ❓ 時區轉換錯誤

**問題**：查詢時間範圍錯誤

**解決方案**：

```bash
# 確認輸入台灣時間，不是 UTC
--start "2025-07-04 22:00:00"  # 台灣時間 22:00
# 而不是
--start "2025-07-04 14:00:00"  # UTC 時間 14:00
```

### ❓ BATCH_SIZE 不生效

**問題**：指定 `--batch_size 15000` 但日誌顯示 5000

**解決方案**：

```bash
# 使用環境變數方式
-e BATCH_SIZE=15000

# 而不是命令行參數
--batch_size 15000  # 這個會被環境變數覆蓋
```

### ❓ 冪等性問題

**問題**：重複執行產生重複記錄

**解決方案**：

- Normal/Economical Mode 使用冪等性檢查
- Fast Mode 使用 BigQuery MERGE
- 如果遇到問題，等待 1-2 分鐘後重試（Streaming Buffer 延遲）

### ❓ 成本過高

**問題**：同步成本超出預期

**解決方案**：

```bash
# 1. 使用 Normal Mode 而非 Fast Mode
--mode normal

# 2. 啟用成本追踪
-e ENABLE_COST_TRACKING=true

# 3. 先執行 dry run 估算
--dry_run
```

### ❓ 驗證失敗

**問題**：驗證腳本顯示同步率低

**可能原因與解決方案**：

1. **時間範圍不一致**

   ```bash
   # 確保驗證時間與同步時間完全一致
   ```

2. **Streaming Buffer 延遲**

   ```bash
   # 等待 1-2 分鐘後重新驗證
   ```

3. **事件過濾差異**
   ```bash
   # 確認 EXCLUDED_EVENT_TYPES 環境變數一致
   ```

---

## 📞 支援資訊

### 📋 檢查清單

執行前確認：

- [ ] 時間使用台灣時間格式
- [ ] 先執行 dry run 估算成本
- [ ] 選擇適當的同步模式
- [ ] 設定正確的環境變數
- [ ] 同步後執行驗證

### 🔍 日誌分析

**關鍵日誌位置**：

```bash
# 檢視同步日誌
docker-compose logs legacy-event-sync

# 檢視即時日誌
docker-compose logs -f legacy-event-sync
```

**關鍵指標**：

- 處理速度：`事件/分鐘`
- 成本追踪：`$X.XX USD`
- 統計計數：`✅ 成功同步: X,XXX 筆`
- 冪等性：`冪等性檢查後無新記錄需寫入`

---

_最後更新: 2025-08-01_
