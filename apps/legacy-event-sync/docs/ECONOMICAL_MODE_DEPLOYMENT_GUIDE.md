# 經濟模式部署與監控指南

## 📋 概述

本指南提供經濟模式功能的完整部署流程、監控配置和最佳實踐建議。經濟模式通過使用 polars 本地處理替代昂貴的 BigQuery MERGE 操作，實現約 200 倍的成本節省。

## 🚀 段階性部署策略

### Phase 1: 開發環境驗證

#### 1.1 環境準備

```bash
# 確保開發環境已啟用經濟模式
export USE_POLARS=true
export ENABLE_COST_TRACKING=true

# 啟動開發環境
cd apps/legacy-event-sync
make dev-up

# 驗證配置
curl http://localhost:8080/health | jq '.economical_mode_enabled'
```

#### 1.2 功能驗證

```bash
# 執行完整測試套件
make test

# 特定測試經濟模式核心功能
docker-compose exec -T legacy-event-sync pytest tests/test_economical_mode.py -v

# 手動同步測試（小範圍）
curl -X POST http://localhost:8080/start-sync \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-01-15T10:00:00", "end_date": "2024-01-15T11:00:00"}'
```

#### 1.3 成本效益驗證

```bash
# 檢查成本追踪是否正常
curl "http://localhost:8080/bigquery-costs?analysis_type=detailed" | jq '.detailed_analysis.cost_breakdown.by_processing_mode'

# 驗證模式切換
export USE_POLARS=false  # 臨時切換到快速模式
# 重新啟動服務並測試
export USE_POLARS=true   # 切回經濟模式
```

### Phase 2: 測試環境部署

#### 2.1 配置更新

```yaml
# terraform/environments/dev/main.tf
locals {
  environment_variables = {
    # 經濟模式核心配置
    USE_POLARS              = "true"
    ENABLE_COST_TRACKING    = "true"

    # 效能調整
    BATCH_SIZE              = "5000"
    MEMORY_MB              = "4096"
    CPU_COUNT              = "4"

    # 監控增強
    LOG_LEVEL              = "INFO"
    ENABLE_DEBUG_LOGGING   = "false"
  }
}
```

#### 2.2 部署執行

```bash
# 部署到測試環境
make deploy-dev

# 驗證部署狀態
gcloud run services describe legacy-event-sync-dev --region=asia-east1

# 檢查環境變數
gcloud run services describe legacy-event-sync-dev --region=asia-east1 \
  --format="value(spec.template.spec.template.spec.containers[0].env[?name=='USE_POLARS'].value)"
```

#### 2.3 測試環境驗證

```bash
# 健康檢查
curl https://legacy-event-sync-dev-[HASH]-de.a.run.app/health

# 同步狀態檢查
curl "https://legacy-event-sync-dev-[HASH]-de.a.run.app/sync-status" | jq '.bigquery_analysis'

# 成本分析檢查
curl "https://legacy-event-sync-dev-[HASH]-de.a.run.app/bigquery-costs" | jq '.cost_tracking_enabled'
```

### Phase 3: 生產環境漸進式部署

#### 3.1 預部署檢查清單

- [ ] 開發環境測試通過
- [ ] 測試環境穩定運行 7 天以上
- [ ] 成本節省效果確認（> 100 倍）
- [ ] 降級機制測試通過
- [ ] 監控告警配置完成
- [ ] 回滾計畫準備就緒

#### 3.2 生產環境配置

```yaml
# terraform/environments/prod/main.tf
locals {
  environment_variables = {
    # 經濟模式生產配置
    USE_POLARS              = "true"
    ENABLE_COST_TRACKING    = "true"

    # 保守的效能設定
    BATCH_SIZE              = "3000"    # 較保守的批次大小
    MEMORY_MB              = "8192"     # 增加記憶體緩衝
    CPU_COUNT              = "4"

    # 生產環境監控
    LOG_LEVEL              = "INFO"
    ENABLE_DEBUG_LOGGING   = "false"

    # 錯誤處理增強
    MAX_RETRY_ATTEMPTS     = "3"
    FALLBACK_MODE_ENABLED  = "true"
  }
}
```

#### 3.3 分階段啟用策略

**階段 3.1: 觀察模式**

```bash
# 先部署但暫時不啟用經濟模式
export USE_POLARS=false
make deploy-prod

# 觀察系統穩定性 24-48 小時
```

**階段 3.2: 小流量測試**

```bash
# 啟用經濟模式，但限制處理範圍
export USE_POLARS=true
export BATCH_SIZE=1000  # 小批次測試

# 監控 6-12 小時，確認穩定性
```

**階段 3.3: 全量啟用**

```bash
# 恢復正常批次大小
export BATCH_SIZE=3000

# 持續監控 72 小時
```

## 📊 監控配置

### Cloud Monitoring 告警設定

#### 3.1 核心指標監控

```yaml
# monitoring/economical_mode_alerts.yaml
alerts:
  - name: "經濟模式降級頻率過高"
    condition: |
      rate(economical_mode_fallback_count[5m]) > 0.1
    duration: 5m
    severity: WARNING
    description: "經濟模式降級到快速模式的頻率過高"

  - name: "成本節省效果異常"
    condition: |
      avg_over_time(cost_savings_percentage[10m]) < 50
    duration: 10m
    severity: WARNING
    description: "成本節省效果低於預期"

  - name: "polars 處理錯誤率高"
    condition: |
      rate(polars_processing_errors[5m]) > 0.05
    duration: 5m
    severity: CRITICAL
    description: "polars 資料處理錯誤率過高"

  - name: "記憶體使用量異常"
    condition: |
      avg_over_time(memory_usage_percent[5m]) > 85
    duration: 10m
    severity: WARNING
    description: "記憶體使用率持續過高"
```

#### 3.2 成本監控指標

```yaml
cost_monitoring:
  - metric: "bigquery_cost_per_hour"
    threshold: 0.1 # $0.10 USD/hour
    severity: WARNING

  - metric: "cost_efficiency_ratio"
    threshold: 50 # 低於 50 倍節省
    severity: WARNING

  - metric: "processing_mode_distribution"
    economical_percentage: 85 # 經濟模式使用率 > 85%
    severity: INFO
```

### Dashboard 配置

#### 3.3 經濟模式監控面板

```json
{
  "dashboard": "Legacy Event Sync - 經濟模式監控",
  "panels": [
    {
      "title": "處理模式分布",
      "type": "pie_chart",
      "query": "sum by (processing_mode) (processing_mode_count)",
      "description": "經濟模式 vs 快速模式使用分布"
    },
    {
      "title": "成本節省趨勢",
      "type": "time_series",
      "query": "cost_savings_usd_total",
      "description": "累積成本節省金額"
    },
    {
      "title": "效能對比",
      "type": "bar_chart",
      "metrics": [
        "economical_mode_records_per_second",
        "fast_mode_records_per_second"
      ],
      "description": "兩種模式的處理速度對比"
    },
    {
      "title": "錯誤率監控",
      "type": "time_series",
      "query": "rate(processing_errors_total[5m]) by (mode)",
      "description": "各模式錯誤率趨勢"
    },
    {
      "title": "記憶體使用",
      "type": "time_series",
      "query": "memory_usage_mb",
      "description": "經濟模式記憶體使用趨勢"
    }
  ]
}
```

### 日誌監控

#### 3.4 關鍵日誌模式

```bash
# 經濟模式成功處理
"🟢 [Economical] 批次 .* 開始經濟模式處理"

# 降級機制觸發
"⚠️ 批次 .* 經濟模式失敗，自動降級到快速模式"

# 成本節省記錄
"💰 批次 .* 成本節省: \\$[0-9.]+ USD \\([0-9.]+%\\)"

# polars 處理錯誤
"❌ 經濟模式批次 .* 處理失敗"

# 冪等性檢查完成
"✅ 冪等性檢查完成：.* 筆輸入，.* 筆重複，.* 筆新記錄"
```

## 🔧 故障排除手冊

### 常見問題診斷

#### 4.1 經濟模式頻繁降級

**症狀：** 日誌中出現大量降級警告

```
⚠️ 批次 XXX 經濟模式失敗，自動降級到快速模式: polars 處理失敗
```

**診斷步驟：**

1. **檢查記憶體使用**

   ```bash
   # 查看 Cloud Run 記憶體指標
   gcloud monitoring metrics list --filter="metric.type:run.googleapis.com/container/memory/utilizations"
   ```

2. **驗證 polars 套件**

   ```bash
   # 進入容器檢查套件
   docker-compose exec legacy-event-sync python -c "import polars as pl; print(pl.__version__)"
   ```

3. **檢查資料格式**
   ```bash
   # 驗證最近處理的事件格式
   curl "http://localhost:8080/sync-status" | jq '.source_events_count'
   ```

**解決方案：**

- 調降 `BATCH_SIZE` 參數
- 增加記憶體配置
- 檢查資料品質問題

#### 4.2 成本節省效果不如預期

**症狀：** 成本節省率 < 100 倍

**診斷步驟：**

1. **檢查模式分布**

   ```bash
   curl "http://localhost:8080/bigquery-costs" | jq '.detailed_analysis.cost_breakdown.by_processing_mode'
   ```

2. **分析降級頻率**

   ```bash
   # 查看日誌中的降級次數
   gcloud logging read "resource.type=cloud_run_revision AND textPayload:降級到快速模式" --limit=50
   ```

3. **驗證配置**
   ```bash
   # 確認環境變數設定
   gcloud run services describe SERVICE_NAME --format="value(spec.template.spec.template.spec.containers[0].env)"
   ```

#### 4.3 冪等性檢查緩慢

**症狀：** 批次處理時間異常長

**診斷步驟：**

1. **查看 BigQuery 查詢效能**

   ```bash
   # 分析查詢執行時間
   curl "http://localhost:8080/sync-status" | jq '.bigquery_analysis.total_execution_time_seconds'
   ```

2. **檢查查詢範圍**
   ```bash
   # 確認時間窗口設定合理
   echo "檢查 ±5 秒查詢範圍是否適當"
   ```

**解決方案：**

- 調整冪等性檢查的時間窗口
- 優化 BigQuery 表格分區
- 增加查詢暫存

## 📈 效能調優建議

### 5.1 記憶體最佳化

```bash
# 基於實際使用情況調整記憶體配置
MEMORY_USAGE_BASELINE = "2GB"
MEMORY_PEAK_MULTIPLIER = "2.5"
RECOMMENDED_MEMORY = MEMORY_USAGE_BASELINE * MEMORY_PEAK_MULTIPLIER

# 生產環境建議配置
MEMORY_MB=8192  # 8GB，提供充足緩衝
CPU_COUNT=4     # 4核心CPU
BATCH_SIZE=3000 # 保守的批次大小
```

### 5.2 成本最佳化

```bash
# 成本控制參數
COST_WARNING_THRESHOLD=0.05    # $0.05 USD
COST_CRITICAL_THRESHOLD=0.20   # $0.20 USD

# 查詢最佳化
IDEMPOTENCY_WINDOW_SECONDS=5   # ±5 秒查詢窗口
ENABLE_QUERY_CACHE=true        # 啟用查詢暫存
```

### 5.3 並行處理最佳化

```bash
# Cloud Run 實例配置
MIN_INSTANCES=1          # 最小實例數
MAX_INSTANCES=20         # 最大實例數
MAX_CONCURRENT_REQUESTS=50 # 每實例併發請求

# Cloud Tasks 配置
MAX_DISPATCHES_PER_SECOND=10  # 每秒最大分派數
MAX_CONCURRENT_DISPATCHES=50  # 最大併發分派數
```

## 🔄 回滾計畫

### 緊急回滾程序

#### 6.1 快速回滾（< 5 分鐘）

```bash
# 立即切換回快速模式
gcloud run services update legacy-event-sync-prod \
  --update-env-vars USE_POLARS=false \
  --region=asia-east1

# 驗證切換成功
curl https://SERVICE_URL/health | jq '.economical_mode_enabled'
```

#### 6.2 完整回滾（< 30 分鐘）

```bash
# 回滾到前一個穩定版本
git checkout PREVIOUS_STABLE_TAG
make deploy-prod

# 驗證服務恢復正常
make check-sync-status
```

### 回滾決策矩陣

| 問題嚴重程度 | 降級頻率 | 錯誤率 | 建議動作 |
| ------------ | -------- | ------ | -------- |
| 低           | < 5%     | < 1%   | 監控觀察 |
| 中           | 5-20%    | 1-5%   | 調整參數 |
| 高           | 20-50%   | 5-10%  | 快速回滾 |
| 嚴重         | > 50%    | > 10%  | 完整回滾 |

## 📋 部署檢查清單

### 部署前檢查

- [ ] 所有測試通過（單元、整合、效能）
- [ ] 成本節省效果驗證（> 100 倍）
- [ ] 降級機制測試通過
- [ ] 監控告警配置完成
- [ ] 文件更新完成
- [ ] 回滾計畫準備就緒

### 部署後驗證

- [ ] 健康檢查端點正常
- [ ] 經濟模式正確啟用
- [ ] 成本追踪功能正常
- [ ] 同步狀態 API 正常
- [ ] 監控指標數據收集正常
- [ ] 日誌輸出格式正確

### 穩定性監控（72小時）

- [ ] 經濟模式使用率 > 80%
- [ ] 成本節省率 > 100 倍
- [ ] 錯誤率 < 1%
- [ ] 記憶體使用率 < 80%
- [ ] 處理延遲無明顯增加
- [ ] 同步率維持 > 98%

---

**總結：** 本指南提供了經濟模式的完整部署流程，從開發驗證到生產部署的每個階段都有詳細說明。通過段階性部署、完善監控和及時回滾機制，確保經濟模式的安全穩定部署，同時實現顯著的成本節省效果。
