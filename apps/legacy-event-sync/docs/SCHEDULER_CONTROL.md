# Cloud Scheduler 控制指南

## 概述

Legacy Event Sync 服務使用 Cloud Scheduler 來定期觸發資料同步作業。本指南說明如何暫時停止或重新啟用自動同步功能。

## 🚫 自動停止設定

**重要：** 在 GitHub Actions 部署時，系統會自動設定 `enable_scheduler = false` 來暫時停止 Cloud Scheduler。這是為了：

1. **避免部署期間的衝突** - 防止在部署過程中觸發同步作業
2. **確保部署穩定性** - 避免新舊版本同時運行造成的問題
3. **減少不必要的成本** - 在部署期間避免額外的 BigQuery 查詢

### 重新啟用 Scheduler

部署完成後，如果需要重新啟用自動同步，請使用以下方法之一：

#### 方法 1: 手動重新啟用（推薦）

```bash
# 進入服務目錄
cd apps/legacy-event-sync/terraform

# 編輯 terraform.tfvars 檔案，設定 enable_scheduler = true
echo "enable_scheduler = true" >> terraform.tfvars

# 部署變更
make deploy-prod
```

#### 方法 2: 使用 gcloud CLI

```bash
make scheduler-start
```

## 控制方法

### 方法 1: 使用 Terraform 變數控制（推薦）

這是最安全和可追蹤的方法，適合長期停止或重新啟用。

#### 停止 Scheduler

1. **編輯 terraform.tfvars 檔案**

   ```bash
   cd apps/legacy-event-sync/terraform
   ```

2. **設定 enable_scheduler = false**

   ```hcl
   # 在 terraform.tfvars 中
   enable_scheduler = false
   ```

3. **部署變更**
   ```bash
   make deploy-prod
   ```

#### 重新啟用 Scheduler

1. **編輯 terraform.tfvars 檔案**

   ```hcl
   # 在 terraform.tfvars 中
   enable_scheduler = true
   ```

2. **部署變更**
   ```bash
   make deploy-prod
   ```

### 方法 2: 使用 gcloud CLI 直接控制

適合快速暫時停止，但變更不會被 Terraform 追蹤。

#### 停止 Scheduler

```bash
make scheduler-stop
```

#### 重新啟用 Scheduler

```bash
make scheduler-start
```

#### 檢查 Scheduler 狀態

```bash
make scheduler-status
```

## 注意事項

### ⚠️ 重要提醒

1. **停止 Scheduler 不會影響手動同步**

   - 您仍然可以透過 API 手動觸發同步
   - 使用 `curl -X POST http://localhost:8080/start-sync` 進行手動同步

2. **資料不會丟失**

   - 停止 scheduler 只是暫停自動觸發
   - 已同步的資料會保留在 BigQuery 中

3. **成本影響**
   - 停止 scheduler 可以減少不必要的查詢成本
   - 但需要手動監控資料同步狀態

### 🔍 監控建議

停止 scheduler 後，建議定期檢查：

1. **檢查同步狀態**

   ```bash
   make check-sync-status
   ```

2. **檢查最新資料時間戳**

   ```bash
   curl http://localhost:8080/sync-status | jq '.latest_data_timestamp'
   ```

3. **手動觸發同步（如需要）**
   ```bash
   curl -X POST http://localhost:8080/start-sync \
     -H "Content-Type: application/json" \
     -d '{"start_date": "2024-01-01T00:00:00", "end_date": "2024-01-01T23:59:59"}'
   ```

## 故障排除

### 常見問題

1. **Terraform 部署失敗**

   - 檢查 `terraform.tfvars` 檔案格式
   - 確認 `enable_scheduler` 變數設定正確

2. **gcloud 命令失敗**

   - 確認已安裝 gcloud CLI
   - 檢查認證狀態：`gcloud auth list`
   - 確認專案設定：`gcloud config get-value project`

3. **Scheduler 狀態異常**
   - 使用 `make scheduler-status` 檢查詳細狀態
   - 檢查 Cloud Console 中的 Scheduler 頁面

### 緊急情況

如果需要立即停止所有同步活動：

1. **緊急停止**

   ```bash
   make scheduler-stop
   ```

2. **檢查 Cloud Run 服務狀態**

   ```bash
   gcloud run services describe legacy-event-sync-prod --region=us-central1
   ```

3. **必要時可以暫時停止 Cloud Run 服務**
   ```bash
   gcloud run services update legacy-event-sync-prod --region=us-central1 --no-traffic
   ```

## 最佳實踐

1. **使用 Terraform 變數控制**

   - 保持基礎設施變更的可追蹤性
   - 確保團隊成員了解當前狀態

2. **記錄停止原因和時間**

   - 在團隊通訊工具中記錄
   - 設定提醒重新啟用的時間

3. **定期檢查資料同步狀態**

   - 即使停止自動同步，也要確保資料完整性
   - 考慮設定監控告警

4. **測試重新啟用流程**
   - 在非生產環境測試停止/啟用流程
   - 確保團隊熟悉操作步驟
