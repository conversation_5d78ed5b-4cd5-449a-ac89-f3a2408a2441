# Legacy Event Sync 開發環境
# 使用真實 BigQuery + 測試表格進行開發

services:
  # 主要應用服務
  legacy-event-sync:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    # 直接使用 appuser 執行，遵循 Dockerfile 的設計
    # 開發環境使用 --reload 實現熱重載
    command:
      [
        "gunicorn",
        "--bind",
        "0.0.0.0:8080",
        "--workers",
        "4",
        "--timeout",
        "1800",
        "--reload",
        "src.main:app",
      ]
    environment:
      - PROJECT_ID=tagtoo-tracking
      - ENVIRONMENT=dev
      - DEBUG=true
      - LOG_LEVEL=INFO
      - PORT=8080
      # BigQuery 設定 - 使用真實 BigQuery
      - BIGQUERY_DATASET=event_test
      - SECRET_KEY=a-very-secret-key-for-dev
      # Google Cloud 認證設定 - 使用 ADC
      - GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json
      - GOOGLE_CLOUD_PROJECT=tagtoo-tracking

      # 效能優化開關
      - USE_OPTIMIZED_MERGE=true
      # 分時段處理配置 - 針對平行化處理優化
      - BATCH_SIZE=5000
      - MINUTES_PER_SEGMENT=15 # 更細緻的分段，充分利用平行化處理
      - MAX_CONCURRENT_SEGMENTS=8 # 提高並行度，加速同步處理
      - MEMORY_MB=8192 # 增加記憶體限制以支援大規模處理
      - CPU_COUNT=4
      # 🚀 經濟模式優化設定
      - USE_POLARS=true
      - ENABLE_PARALLEL_PROCESSING=true # 🚀 啟用普通模式：平行處理
      - ENABLE_COST_TRACKING=true
      - ENABLE_DASHBOARD=false # 🚀 停用儀表板以提升效能
      - PLATFORM_DASHBOARD_BUCKET=integrated-event-platform-prod-tagtoo-tracking
      # BigQuery 連線優化
      - BIGQUERY_MAX_RETRY_DELAY=60
      - BIGQUERY_TOTAL_TIMEOUT=3600
      # 開發環境表格名稱 - 使用測試表格
      - SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
      - TARGET_TABLE=tagtoo-tracking.event_test.integrated_event
    mem_limit: 6g # 增加記憶體限制以支援大規模資料處理
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./scripts:/app/scripts
      - ./templates:/app/templates # 🆕 儀表板範本掛載
      - ./shared-assets:/app/shared-assets # 🆕 共用資產掛載
      - ./config.py:/app/config.py
      - ./pytest.ini:/app/pytest.ini
      - ./.env:/app/.env
      # Google Cloud ADC 檔案掛載
      - ~/.config/gcloud/application_default_credentials.json:/app/adc.json:ro
      # Schema 檔案掛載 - 解決上層目錄存取問題
      - ../../infrastructure/terraform/schema:/app/terraform/schema:ro
    restart: unless-stopped
    networks:
      - legacy-event-sync-network

networks:
  legacy-event-sync-network:
    driver: bridge
