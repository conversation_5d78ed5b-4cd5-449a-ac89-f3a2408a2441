#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧同步率驗證腳本

🎯 目的：
驗證 legacy event 同步至 integrated_event 的正確性與完整性。
此腳本提供多種分析模式，包括：
1.  整體同步率分析：從宏觀角度查看同步狀況。
2.  分時/分事件類型分析：細分維度，找出特定條件下的問題。
3.  資料抽樣驗證：隨機抽取資料，逐一比對欄位，確保資料轉換邏輯正確。
4.  單一事件除錯：針對特定一筆資料進行深入調查。

用法：
-   預設 (驗證過去 24 小時，抽樣 10 筆):
    $ python3 scripts/validate_smart_sync_rate.py

-   指定時間範圍 (台灣時區 UTC+8):
    $ python3 scripts/validate_smart_sync_rate.py --start '2025-07-15 08:00:00' --end '2025-07-16 15:00:00'

-   調整抽樣數量 (設為 0 可跳過抽樣):
    $ python3 scripts/validate_smart_sync_rate.py --sample_size 20

-   單一事件除錯模式 (時間為台灣時區 UTC+8):
    $ python3 scripts/validate_smart_sync_rate.py --debug_permanent '...' --debug_time 'YYYY-MM-DD HH:MM:SS'
"""

import sys
import pytz
import os
import argparse
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging
import unicodedata
import pytz
import json
from typing import Dict, Any

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_display_width(text):
    """計算字串在終端中的顯示寬度，處理 CJK 字元"""
    width = 0
    for char in text:
        if unicodedata.east_asian_width(char) in ('F', 'W', 'A'):
            width += 2
        else:
            width += 1
    return width

def format_row(columns, widths):
    """格式化單行，根據顯示寬度對齊"""
    formatted_cols = []
    for i, col in enumerate(columns):
        col_str = str(col)
        width = widths[i]
        display_width = get_display_width(col_str)
        padding = width - display_width

        if isinstance(width, str) and width.startswith('<'):
            formatted_cols.append(col_str + ' ' * padding)
        else:
            formatted_cols.append(' ' * padding + col_str)

    return ' '.join(formatted_cols)

class SmartSyncRateValidator:
    def check_fully_duplicate_rows(self, sample):
        """
        檢查同主鍵下（permanent, event_time, ec_id, event.name, link, value, currency, order_id, partner_source）所有欄位完全相同的重複資料數量。
        回傳 (total, fully_duplicate_count)
        """
        event_time = sample['event_time'].astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
        permanent = sample['permanent']
        event = sample.get('event', {})
        event_name = event.get('name') if isinstance(event, dict) else None
        ec_id = sample.get('ec_id', None)
        link = sample.get('link', None)
        value = event.get('value') if isinstance(event, dict) else None
        currency = event.get('currency') if isinstance(event, dict) else None
        order_id = None
        if isinstance(event.get('custom_data'), dict):
            order_id = event['custom_data'].get('order_id')
        partner_source = sample.get('partner_source', 'legacy-tagtoo-event')

        ec_id_str = str(ec_id) if ec_id is not None else None

        query = f"""
        SELECT * FROM `{self.source_table}`
        WHERE permanent = @permanent
          AND event_time = @event_time
          AND (CAST(ec_id AS STRING) = @ec_id OR (ec_id IS NULL AND @ec_id IS NULL))
          AND (event.name = @event_name OR (event.name IS NULL AND @event_name IS NULL))
          AND (link = @link OR (link IS NULL AND @link IS NULL))
          AND (CAST(event.value AS STRING) = @value OR (event.value IS NULL AND @value IS NULL))
          AND (event.currency = @currency OR (event.currency IS NULL AND @currency IS NULL))
          AND ((event.custom_data.order_id = @order_id) OR (event.custom_data.order_id IS NULL AND @order_id IS NULL))
          AND (@partner_source = 'legacy-tagtoo-event')
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("permanent", "STRING", permanent),
                bigquery.ScalarQueryParameter("event_time", "TIMESTAMP", event_time),
                bigquery.ScalarQueryParameter("ec_id", "STRING", ec_id_str),
                bigquery.ScalarQueryParameter("event_name", "STRING", event_name),
                bigquery.ScalarQueryParameter("link", "STRING", link),
                bigquery.ScalarQueryParameter("value", "STRING", str(value) if value is not None else None),
                bigquery.ScalarQueryParameter("currency", "STRING", currency),
                bigquery.ScalarQueryParameter("order_id", "STRING", order_id),
                bigquery.ScalarQueryParameter("partner_source", "STRING", partner_source),
            ]
        )
        results = list(self.bigquery_client.query(query, job_config=job_config).result())
        total = len(results)
        seen = set()
        for row in results:
            row_dict = dict(row.items())
            row_repr = json.dumps({k: v for k, v in row_dict.items() if k not in ('row_id', 'insert_time', 'create_time')}, sort_keys=True, default=str)
            seen.add(row_repr)
        fully_duplicate_count = total - len(seen)
        return total, fully_duplicate_count
    def check_source_by_permanent_event_time(self, sample):
        """
        查詢同 permanent + event_time 下所有資料，顯示 event.name、ec_id、link、event.value、event.currency、order_id 分布。
        """
        print("\n🔎 [permanent+event_time 分布] tagtoo_event 所有資料：")
        event_time = sample['event_time'].astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
        permanent = sample['permanent']
        query = f"""
        SELECT event.name AS event_name, ec_id, link, event.value, event.currency, event.custom_data.order_id
        FROM `{self.source_table}`
        WHERE permanent = @permanent
          AND event_time = @event_time
        ORDER BY event_name, ec_id, link
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("permanent", "STRING", permanent),
                bigquery.ScalarQueryParameter("event_time", "TIMESTAMP", event_time),
            ]
        )
        results = list(self.bigquery_client.query(query, job_config=job_config).result())
        print(f"  - 查詢結果: {len(results)} 筆")
        for i, row in enumerate(results):
            print(f"    [第{i+1}筆] event_name={row['event_name']} | ec_id={row['ec_id']} | link={row['link']} | value={row['event_value'] if 'event_value' in row else row.get('event.value')} | currency={row['event_currency'] if 'event_currency' in row else row.get('event.currency')} | order_id={row['order_id']}")

    """智慧同步率驗證器"""

    def __init__(self, production: bool = False):
        self.bigquery_client = bigquery.Client(location="asia-east1")
        self.source_table = "tagtoo-tracking.event_prod.tagtoo_event"
        if production:
            self.target_table = "tagtoo-tracking.event_prod.integrated_event"
            print("⚠️  已啟用 --production，將驗證正式表 event_prod.integrated_event")
        else:
            self.target_table = "tagtoo-tracking.event_test.integrated_event"
            print("（預設）將驗證測試表 event_test.integrated_event。如需驗證正式表請加上 --production 參數")
        self.display_timezone = pytz.timezone("Asia/Taipei")
        self.display_timezone_str = "Asia/Taipei"

        # 解析排除的事件類型（與主程式保持一致）
        self.excluded_event_types = self._parse_excluded_event_types()

        # 💰 追踪總費用
        self.total_cost_tracker = {
            "total_cost_usd": 0.0,
            "total_gb_processed": 0.0,
            "total_execution_time": 0.0,
            "query_count": 0,
            "operations": []
        }

    def _parse_excluded_event_types(self):
        """
        解析需要排除的事件類型清單（與主程式邏輯一致）。
        環境變數 EXCLUDED_EVENT_TYPES 應為 JSON 格式的字串陣列。
        """
        import os
        import json

        excluded_str = os.environ.get("EXCLUDED_EVENT_TYPES", '["focus"]')
        try:
            excluded_list = json.loads(excluded_str)
            if isinstance(excluded_list, list):
                print(f"📋 已載入事件過濾配置: {excluded_list}")
                return excluded_list
            else:
                print(f"⚠️  EXCLUDED_EVENT_TYPES 不是陣列格式，使用預設值: {excluded_str}")
                return ["focus"]
        except json.JSONDecodeError as e:
            print(f"⚠️  無法解析 EXCLUDED_EVENT_TYPES: {e}，使用預設值")
            return ["focus"]

    def _get_time_range_filter(self, start_utc: datetime, end_utc: datetime, time_col: str = "event_time") -> str:
        """根據 UTC 時間範圍產生過濾條件"""
        return f"{time_col} >= @start_utc AND {time_col} < @end_utc"

    def _get_event_filter_condition(self) -> str:
        """建構事件類型過濾條件（與主程式邏輯一致）"""
        if not self.excluded_event_types:
            return ""

        excluded_list = [f"'{event_type}'" for event_type in self.excluded_event_types]
        excluded_str = ", ".join(excluded_list)
        return f" AND event.name NOT IN ({excluded_str})"

    def run_all_analyses(self, start_utc: datetime, end_utc: datetime, sample_size: int, enable_deduplication: bool = True):
        """執行所有分析並顯示總費用"""
        start_local = start_utc.astimezone(self.display_timezone)
        end_local = end_utc.astimezone(self.display_timezone)

        logger.info(f"🔍 開始驗證，時區範圍 ({self.display_timezone_str}): {start_local.strftime('%Y-%m-%d %H:%M:%S')} -> {end_local.strftime('%Y-%m-%d %H:%M:%S')}")

        # 💰 重置費用追踪
        self.total_cost_tracker = {
            "total_cost_usd": 0.0,
            "total_gb_processed": 0.0,
            "total_execution_time": 0.0,
            "query_count": 0,
            "operations": []
        }

        # 1. 整體同步率分析
        self.overall_sync_analysis(start_utc, end_utc, enable_deduplication)

        # 2. 各事件類型分析
        self.event_type_analysis(start_utc, end_utc, enable_deduplication)

        # 3. 每小時同步率分析
        self.hourly_sync_rate_analysis(start_utc, end_utc, enable_deduplication)

        # 4. 每小時延遲分析
        self.hourly_lag_analysis(start_utc, end_utc)

        # 5. 資料抽樣驗證
        if sample_size > 0:
            self.data_sampling_validation(start_utc, end_utc, sample_size, enable_deduplication)

        # 💰 顯示總費用摘要
        self._print_total_cost_summary()

    def _compare_rows(self, source, target):
        """比較來源和目標 row 的所有欄位"""
        mismatched_fields = []

        # Helper to get nested properties
        def get_value(data, path):
            keys = path.split('.')
            val = data
            for key in keys:
                if val is None: return None
                val = val.get(key)
            return val

        # Helper to normalize and compare item lists
        def compare_items(source_items, target_items):
            if source_items is None and target_items is None:
                return True
            if source_items is None or target_items is None:
                return False

            # 只比對兩邊共同擁有的 key/value，忽略 availability/description 這種 schema-only 差異
            def normalize(items):
                normed = set()
                for d in items:
                    # 移除 availability/description
                    filtered = {k: v for k, v in d.items() if k not in ("availability", "description")}
                    normed.add(frozenset(filtered.items()))
                return normed
            return normalize(source_items) == normalize(target_items)

        # Map source path to target path and a transformation function
        comparison_map = [
            ("event.name", "event", None),
            ("link", "link", None),
            ("event.value", "value", None),
            ("event.currency", "currency", None),
            ("event.custom_data.order_id", "order_id", None),
            ("user.em", "user.em", None),
            ("user.ph", "user.ph", None),
            ("location.country_code", "location.country_code", None),
            ("location.region_name", "location.region_name", None),
            ("location.city_name", "location.city_name", None),
            ("event.items", "items", compare_items),
        ]

        for source_path, target_path, compare_func in comparison_map:
            source_val = get_value(source, source_path)
            target_val = get_value(target, target_path)

            if compare_func:
                is_match = compare_func(source_val, target_val)
            else:
                is_match = (source_val == target_val) or (source_val is None and target_val is None)

            if not is_match:
                mismatched_fields.append((source_path, source_val, target_val))

        return mismatched_fields

    def overall_sync_analysis(self, start_utc: datetime, end_utc: datetime, enable_deduplication: bool = True):
        """
        整體同步率分析：顯示原始同步率與主鍵去重後的真實同步率，包含成本估算。
        """
        time_filter = self._get_time_range_filter(start_utc, end_utc)
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", end_utc),
            ]
        )

        # 基本查詢：原始總數和實際同步數（加入事件過濾）
        event_filter = self._get_event_filter_condition()
        source_total_query = f"SELECT COUNT(*) as count FROM `{self.source_table}` WHERE {time_filter}{event_filter}"
        target_actual_query = f"SELECT COUNT(*) as count FROM `{self.target_table}` WHERE {time_filter} AND partner_source = 'legacy-tagtoo-event'"

        # 💰 成本估算
        source_cost = self._estimate_query_cost(source_total_query, job_config)
        target_cost = self._estimate_query_cost(target_actual_query, job_config)

        # 執行基本查詢
        start_time = datetime.utcnow()
        source_total = list(self.bigquery_client.query(source_total_query, job_config=job_config).result())[0].count
        target_actual = list(self.bigquery_client.query(target_actual_query, job_config=job_config).result())[0].count
        basic_execution_time = (datetime.utcnow() - start_time).total_seconds()

        # 根據 enable_deduplication 動態選擇主鍵
        if enable_deduplication:
            partition_fields = """
                permanent, ec_id, event_time, event.name, IFNULL(link, ''),
                CAST(IFNULL(event.value, -999) AS STRING), IFNULL(event.currency, 'NULL'),
                IFNULL(event.custom_data.order_id, 'NULL')
            """
            dedup_status_msg = "啟用去重 (9欄位)"
        else:
            partition_fields = "permanent, ec_id, event_time, event.name"
            dedup_status_msg = "停用去重 (5欄位)"

        # 主鍵去重後的預期同步數（加入事件過濾）
        source_unique_query = f"""
            SELECT COUNT(*) as count FROM (
                SELECT ROW_NUMBER() OVER (
                    PARTITION BY {partition_fields}
                    ORDER BY event_time
                ) as rn
                FROM `{self.source_table}` WHERE {time_filter}{event_filter}
            ) WHERE rn = 1
        """

        # 💰 去重查詢成本估算
        dedup_cost = self._estimate_query_cost(source_unique_query, job_config)

        # 執行去重查詢
        start_time = datetime.utcnow()
        source_unique = list(self.bigquery_client.query(source_unique_query, job_config=job_config).result())[0].count
        dedup_execution_time = (datetime.utcnow() - start_time).total_seconds()

        # 計算同步率
        raw_sync_rate = target_actual / source_total * 100 if source_total > 0 else 0
        effective_sync_rate = target_actual / source_unique * 100 if source_unique > 0 else 0

        # 顯示過濾資訊
        filter_info = ""
        if self.excluded_event_types:
            filter_info = f" (已過濾: {', '.join(self.excluded_event_types)})"

        print(f"🎯 整體智慧同步率驗證 ({dedup_status_msg}){filter_info}")
        print("=" * 50)
        print(f"📊 原始資料總數: {source_total:,}")
        print(f"🎯 去重後預期數量: {source_unique:,}")
        print(f"✅ 實際同步數量: {target_actual:,}")
        print()
        print(f"📈 原始同步率: {raw_sync_rate:.2f}%")
        print(f"🎯 真實同步率(主鍵去重): {effective_sync_rate:.2f}%")

        if self.excluded_event_types:
            print(f"🚫 事件過濾: 已排除 {self.excluded_event_types} 類型事件")

        print("💡 完全重複率查詢已移除，僅於同步率異常時建議人工查詢完全重複資料。")

        # 💰 總成本分析
        total_cost = source_cost.get("estimated_cost_usd", 0) + target_cost.get("estimated_cost_usd", 0) + dedup_cost.get("estimated_cost_usd", 0)
        total_gb = source_cost.get("gb_processed", 0) + target_cost.get("gb_processed", 0) + dedup_cost.get("gb_processed", 0)
        total_execution_time = basic_execution_time + dedup_execution_time

        print(f"\n💰 BigQuery 成本分析 - 整體同步率驗證")
        print(f"   💵 總成本: ${total_cost:.4f} USD ({total_gb:.3f} GB 掃描)")
        print(f"   ⏱️ 總執行時間: {total_execution_time:.2f} 秒")
        print(f"   📊 查詢明細:")
        print(f"      - 原始資料查詢: ${source_cost.get('estimated_cost_usd', 0):.4f} USD")
        print(f"      - 目標資料查詢: ${target_cost.get('estimated_cost_usd', 0):.4f} USD")
        print(f"      - 去重分析查詢: ${dedup_cost.get('estimated_cost_usd', 0):.4f} USD")

        if total_cost > 1.0:
            print(f"   🔴 高成本警告: 建議縮小時間範圍或優化查詢")
        elif total_cost > 0.1:
            print(f"   �� 中等成本提醒: 注意查詢頻率")

        self._track_cost("整體同步率分析", source_cost, basic_execution_time)
        self._track_cost("整體同步率分析", target_cost, basic_execution_time)
        self._track_cost("整體同步率分析", dedup_cost, dedup_execution_time)

    def event_type_analysis(self, start_utc: datetime, end_utc: datetime, enable_deduplication: bool = True):
        """
        事件類型分析：根據 enable_deduplication 參數動態切換去重邏輯，包含成本分析。
        """
        time_filter = self._get_time_range_filter(start_utc, end_utc)
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", end_utc),
            ]
        )

        # 根據 enable_deduplication 動態選擇主鍵
        if enable_deduplication:
            partition_fields = """
                permanent, ec_id, event_time, event.name, IFNULL(link, ''),
                CAST(IFNULL(event.value, -999) AS STRING), IFNULL(event.currency, 'NULL'),
                IFNULL(event.custom_data.order_id, 'NULL')
            """
        else:
            partition_fields = "permanent, ec_id, event_time, event.name"

        # 建構事件過濾條件
        event_filter = self._get_event_filter_condition()

        query = f"""
        WITH source_with_row_num AS (
            SELECT
                event.name as event_name,
                ROW_NUMBER() OVER (
                    PARTITION BY {partition_fields}
                    ORDER BY event_time
                ) as rn
            FROM `{self.source_table}`
            WHERE {time_filter}{event_filter}
        ),
        source_stats AS (
            SELECT
                event_name,
                COUNT(*) as source_total,
                SUM(IF(rn = 1, 1, 0)) as source_unique
            FROM source_with_row_num
            GROUP BY event_name
        ),
        target_stats AS (
          SELECT event as event_name, COUNT(*) as target_count
          FROM `{self.target_table}` WHERE {time_filter} AND partner_source = 'legacy-tagtoo-event'
          GROUP BY event_name
        )
        SELECT
          COALESCE(s.event_name, t.event_name) as event,
          IFNULL(s.source_total, 0) as source_total,
          IFNULL(s.source_unique, 0) as source_unique,
          IFNULL(t.target_count, 0) as target_count,
          ROUND(IFNULL(t.target_count, 0) / NULLIF(s.source_total, 0) * 100, 2) as raw_sync_rate,
          ROUND(IFNULL(t.target_count, 0) / NULLIF(s.source_unique, 0) * 100, 2) as effective_sync_rate
        FROM source_stats s FULL OUTER JOIN target_stats t ON s.event_name = t.event_name
        ORDER BY source_total DESC
        """

        # 💰 成本估算
        cost_estimate = self._estimate_query_cost(query, job_config)

        # 執行查詢
        start_time = datetime.utcnow()
        details = [dict(row.items()) for row in self.bigquery_client.query(query, job_config=job_config).result()]
        execution_time = (datetime.utcnow() - start_time).total_seconds()

        headers = ["事件類型", "原始總數", "去重後", "同步數", "原始同步率", "真實同步率"]
        widths = [15, 15, 15, 15, 10, 10]

        # 顯示過濾資訊
        filter_info = ""
        if self.excluded_event_types:
            filter_info = f" (已過濾: {', '.join(self.excluded_event_types)})"

        print(f"\n📋 各事件類型分析{filter_info}:")

        # 💰 顯示成本信息
        self._print_cost_summary("事件類型分析", cost_estimate, execution_time)

        print(format_row(headers, widths))
        print("-" * (sum(widths) + len(widths) -1))
        for detail in details:
            event = detail.get('event', 'N/A') or 'N/A'

            # 安全處理可能為 None 的數值
            raw_sync_rate = detail.get('raw_sync_rate')
            effective_sync_rate = detail.get('effective_sync_rate')

            row = [
                event,
                f"{detail.get('source_total', 0):,}",
                f"{detail.get('source_unique', 0):,}",
                f"{detail.get('target_count', 0):,}",
                f"{raw_sync_rate:.2f}%" if raw_sync_rate is not None else "N/A",
                f"{effective_sync_rate:.2f}%" if effective_sync_rate is not None else "N/A"
            ]
            print(format_row(row, widths))

        self._track_cost("事件類型分析", cost_estimate, execution_time)

    def hourly_sync_rate_analysis(self, start_utc: datetime, end_utc: datetime, enable_deduplication: bool = True):
        """
        每小時同步率分析：顯示原始同步率和真實同步率，並包含 BigQuery 成本分析。
        """
        time_filter = self._get_time_range_filter(start_utc, end_utc)
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", end_utc),
            ]
        )

        # 建構事件過濾條件
        event_filter = self._get_event_filter_condition()

        # 🎯 無論是否啟用去重，都計算原始和真實同步率
        query = f"""
        WITH hours_in_range AS (
            SELECT hour_timestamp
            FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(@start_utc, TIMESTAMP_SUB(@end_utc, INTERVAL 1 MICROSECOND), INTERVAL 1 HOUR)) as hour_timestamp
        ),
        hourly_source_stats AS (
            SELECT
                TIMESTAMP_TRUNC(event_time, HOUR) as hour_bucket,
                COUNT(*) as source_total_count,
                COUNT(DISTINCT CONCAT(
                    permanent, '|',
                    CAST(ec_id AS STRING), '|',
                    CAST(TIMESTAMP_SECONDS(UNIX_SECONDS(event_time)) AS STRING), '|',
                    event.name, '|',
                    IFNULL(link, ''), '|',
                    CAST(IFNULL(event.value, -999) AS STRING), '|',
                    IFNULL(event.currency, 'NULL'), '|',
                    IFNULL(event.custom_data.order_id, 'NULL')
                )) as source_unique_count
            FROM `{self.source_table}`
            WHERE {time_filter}{event_filter}
            GROUP BY hour_bucket
        ),
        hourly_target_stats AS (
            SELECT TIMESTAMP_TRUNC(event_time, HOUR) as hour_bucket, COUNT(*) as target_count
            FROM `{self.target_table}` WHERE {time_filter} AND partner_source = 'legacy-tagtoo-event'
            GROUP BY hour_bucket
        )
        SELECT
            FORMAT_TIMESTAMP('%m/%d %H:00', h.hour_timestamp, '{self.display_timezone_str}') as hour_display,
            IFNULL(s.source_total_count, 0) as source_total,
            IFNULL(s.source_unique_count, 0) as source_unique,
            IFNULL(t.target_count, 0) as target_actual,
            ROUND(IFNULL(t.target_count, 0) / NULLIF(s.source_total_count, 0) * 100, 2) as raw_sync_rate,
            ROUND(IFNULL(t.target_count, 0) / NULLIF(s.source_unique_count, 0) * 100, 2) as true_sync_rate
        FROM hours_in_range h
        LEFT JOIN hourly_source_stats s ON TIMESTAMP_TRUNC(h.hour_timestamp, HOUR) = s.hour_bucket
        LEFT JOIN hourly_target_stats t ON TIMESTAMP_TRUNC(h.hour_timestamp, HOUR) = t.hour_bucket
        ORDER BY h.hour_timestamp
        """

        # 💰 BigQuery 成本估算
        cost_estimate = self._estimate_query_cost(query, job_config)

        # 執行查詢
        start_time = datetime.utcnow()
        hourly_details = [dict(row.items()) for row in self.bigquery_client.query(query, job_config=job_config).result()]
        execution_time = (datetime.utcnow() - start_time).total_seconds()

        dedup_status = "啟用去重邏輯" if enable_deduplication else "停用去重邏輯"
        filter_info = ""
        if self.excluded_event_types:
            filter_info = f", 已過濾: {', '.join(self.excluded_event_types)}"
        print(f"\n🕒 每小時同步率分析 ({dedup_status}, 時區: {self.display_timezone_str}{filter_info}):")

        # 💰 顯示成本信息
        self._print_cost_summary("每小時同步率分析", cost_estimate, execution_time)

        # 🎯 總是顯示完整的表格（包含原始和真實同步率）
        headers = ["日期時間", "原始資料數", "去重後數量", "實際同步數", "原始同步率", "真實同步率"]
        widths = [12, 15, 15, 15, 10, 10]

        print(format_row(headers, widths))
        print("-" * (sum(widths) + len(widths) -1))

        for hour_detail in hourly_details:
            raw_sync_rate = hour_detail.get('raw_sync_rate')
            true_sync_rate = hour_detail.get('true_sync_rate')
            row = [
                hour_detail.get('hour_display', 'N/A'),
                f"{hour_detail.get('source_total', 0):,}",
                f"{hour_detail.get('source_unique', 0):,}",
                f"{hour_detail.get('target_actual', 0):,}",
                f"{raw_sync_rate:.2f}%" if raw_sync_rate is not None else "N/A",
                f"{true_sync_rate:.2f}%" if true_sync_rate is not None else "N/A"
            ]
            print(format_row(row, widths))

        self._track_cost("每小時同步率分析", cost_estimate, execution_time)

    def hourly_lag_analysis(self, start_utc: datetime, end_utc: datetime):
        """每小時延遲分析，包含成本追踪"""
        time_filter = self._get_time_range_filter(start_utc, end_utc, 'event_time')
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", end_utc),
            ]
        )

        query = f"""
        WITH hours_in_range AS (
            SELECT hour_timestamp
            FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(@start_utc, TIMESTAMP_SUB(@end_utc, INTERVAL 1 MICROSECOND), INTERVAL 1 HOUR)) as hour_timestamp
        ),
        hourly_stats AS (
            SELECT
                TIMESTAMP_TRUNC(event_time, HOUR) as hour_bucket,
                ROUND(AVG(TIMESTAMP_DIFF(create_time, event_time, SECOND)), 2) as avg_lag_seconds,
                MIN(TIMESTAMP_DIFF(create_time, event_time, SECOND)) as min_lag_seconds,
                MAX(TIMESTAMP_DIFF(create_time, event_time, SECOND)) as max_lag_seconds,
                APPROX_QUANTILES(TIMESTAMP_DIFF(create_time, event_time, SECOND), 100)[OFFSET(50)] as p50_lag_seconds,
                APPROX_QUANTILES(TIMESTAMP_DIFF(create_time, event_time, SECOND), 100)[OFFSET(95)] as p95_lag_seconds,
                APPROX_QUANTILES(TIMESTAMP_DIFF(create_time, event_time, SECOND), 100)[OFFSET(99)] as p99_lag_seconds
            FROM `{self.target_table}`
            WHERE {time_filter} AND partner_source = 'legacy-tagtoo-event'
            GROUP BY TIMESTAMP_TRUNC(event_time, HOUR)
        )
        SELECT
            FORMAT_TIMESTAMP('%m/%d %H:00', h.hour_timestamp, '{self.display_timezone_str}') as hour_display,
            s.avg_lag_seconds,
            s.min_lag_seconds,
            s.max_lag_seconds,
            s.p50_lag_seconds,
            s.p95_lag_seconds,
            s.p99_lag_seconds
        FROM hours_in_range h
        LEFT JOIN hourly_stats s ON TIMESTAMP_TRUNC(h.hour_timestamp, HOUR) = s.hour_bucket
        ORDER BY h.hour_timestamp
        """

        # 💰 成本估算
        cost_estimate = self._estimate_query_cost(query, job_config)

        # 執行查詢
        start_time = datetime.utcnow()
        delay_details = [dict(row.items()) for row in self.bigquery_client.query(query, job_config=job_config).result()]
        execution_time = (datetime.utcnow() - start_time).total_seconds()

        print(f"\n⏱️ 每小時同步延遲分析 (秒, 時區: {self.display_timezone_str}):")

        # 💰 顯示成本信息
        self._print_cost_summary("每小時延遲分析", cost_estimate, execution_time)

        headers = ["日期時間", "平均延遲", "最小延遲", "最大延遲", "P50", "P95", "P99"]
        widths = [12, 10, 10, 10, 8, 8, 8]
        print(format_row(headers, widths))
        print("-" * (sum(widths) + len(widths) - 1))
        for detail in delay_details:
            row = [
                detail.get('hour_display', 'N/A'),
                f"{detail.get('avg_lag_seconds', 0):>7,.2f}" if detail.get('avg_lag_seconds') is not None else "    N/A",
                f"{detail.get('min_lag_seconds', 0):>7,}" if detail.get('min_lag_seconds') is not None else "    N/A",
                f"{detail.get('max_lag_seconds', 0):>7,}" if detail.get('max_lag_seconds') is not None else "    N/A",
                f"{detail.get('p50_lag_seconds', 0):>5}" if detail.get('p50_lag_seconds') is not None else "  N/A",
                f"{detail.get('p95_lag_seconds', 0):>5}" if detail.get('p95_lag_seconds') is not None else "  N/A",
                f"{detail.get('p99_lag_seconds', 0):>5}" if detail.get('p99_lag_seconds') is not None else "  N/A"
            ]
            print(format_row(row, widths))

        self._track_cost("每小時延遲分析", cost_estimate, execution_time)

    def debug_single_event(self, permanent: str, event_time_str: str):
        """查詢並印出單一事件在來源和目標表的詳細資料，並自動比對所有欄位。"""
        print("\n🕵️  單一事件除錯模式 (精確時間比對)")
        print("=" * 50)

        try:
            # 將輸入的台灣時間轉換為 UTC
            taiwan_tz = pytz.timezone('Asia/Taipei')
            event_time_taiwan = taiwan_tz.localize(datetime.fromisoformat(event_time_str.replace('Z', '')))
            event_time_utc = event_time_taiwan.astimezone(pytz.utc)
            logger.info(f"🕐 除錯時間轉換: {event_time_str} (台灣) -> {event_time_utc.strftime('%Y-%m-%d %H:%M:%S')} (UTC)")
        except ValueError:
            logger.error("除錯用的時間格式錯誤，請使用台灣時區 'YYYY-MM-DD HH:MM:SS'")
            return

        print(f"  - Permanent ID: {permanent}")
        print(f"  - Event Time (UTC): {event_time_utc}")

        # 步驟 1: 查詢來源表中精確匹配的記錄
        exact_source_query = f"""
        SELECT * FROM `{self.source_table}`
        WHERE permanent = @permanent
          AND event_time = @event_time
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("permanent", "STRING", permanent),
                bigquery.ScalarQueryParameter("event_time", "TIMESTAMP", event_time_utc),
            ]
        )

        exact_source_results = list(self.bigquery_client.query(exact_source_query, job_config=job_config).result())

        # 步驟 2: 查詢目標表中精確匹配的記錄
        exact_target_query = f"""
        SELECT * FROM `{self.target_table}`
        WHERE permanent = @permanent
          AND event_time = @event_time
          AND partner_source = 'legacy-tagtoo-event'
        """
        exact_target_results = list(self.bigquery_client.query(exact_target_query, job_config=job_config).result())

        print(f"\n📊 精確時間匹配結果:")
        print(f"  - 源表記錄數: {len(exact_source_results)}")
        print(f"  - 目標表記錄數: {len(exact_target_results)}")

        # 狀況判斷
        if len(exact_source_results) == 0:
            print("\n❌ 問題診斷: 源表中沒有該時間的記錄")
            print("  💡 可能原因: 輸入的時間不正確")
            return

        if len(exact_target_results) == 0:
            print("\n❌ 問題診斷: 資料同步遺漏")
            print("  💡 結論: 該記錄存在於源表但完全沒有同步到目標表")
            print("\n--- 源表記錄詳情 ---")
            for i, source in enumerate(exact_source_results):
                source_dict = dict(source.items())
                event_name = source_dict.get('event', {}).get('name') if source_dict.get('event') else 'None'
                print(f"  記錄 {i+1}:")
                print(f"    - event.name: {event_name}")
                print(f"    - link: {source_dict.get('link', 'None')}")
                print(f"    - ec_id: {source_dict.get('ec_id', 'None')}")
            return

        # 狀況 3: 都有記錄，進行欄位比對
        print("\n✅ 資料同步狀況: 正常 (兩邊都有記錄)")
        print("\n📋 進行詳細欄位比對...")

        # 比對每一組對應記錄
        for i, source in enumerate(exact_source_results):
            source_dict = dict(source.items())
            print(f"\n--- 源記錄 {i+1} 比對結果 ---")

            # 找出最匹配的目標記錄 (based on event name)
            source_event_name = source_dict.get('event', {}).get('name') if source_dict.get('event') else None

            matching_targets = []
            for target in exact_target_results:
                target_dict = dict(target.items())
                if target_dict.get('event') == source_event_name:
                    matching_targets.append(target)

            if not matching_targets:
                print(f"  ❌ 沒有找到 event.name='{source_event_name}' 的對應目標記錄")
                print(f"  📋 目標表中的事件類型: {[dict(t.items()).get('event') for t in exact_target_results]}")
                continue

            # 使用第一個匹配的目標記錄進行比對
            target = matching_targets[0]
            target_dict = dict(target.items())

            print(f"  ✅ 找到對應的目標記錄 (event.name='{source_event_name}')")

            # 進行詳細欄位比對
            mismatched_fields = self._compare_rows(source_dict, target_dict)

            if not mismatched_fields:
                print(f"  🎉 所有欄位完全匹配!")
            else:
                print(f"  ⚠️  發現 {len(mismatched_fields)} 個欄位不匹配:")
                for field, source_val, target_val in mismatched_fields:
                    print(f"    - {field}: [源] {source_val} | [標] {target_val}")

        print("\n--- 除錯總結 ---")
        if len(exact_source_results) == len(exact_target_results):
            print(f"  📊 記錄數量: 源表 {len(exact_source_results)} 筆 = 目標表 {len(exact_target_results)} 筆 ✅")
        else:
            print(f"  📊 記錄數量: 源表 {len(exact_source_results)} 筆 ≠ 目標表 {len(exact_target_results)} 筆 ⚠️")
            print(f"  � 可能原因: MERGE 條件過於嚴格，導致部分記錄沒有插入")

    def check_source_duplicates(self, sample):
        """
        檢查 tagtoo_event 是否有多筆主鍵（permanent, event_time, ec_id, event.name, link）相同的事件，並列出所有內容。
        並額外查詢同 permanent+event_time 下所有資料分布，協助判斷主鍵衝突。
        """
        print("\n🔎 [主鍵分布檢查] tagtoo_event 是否有多筆主鍵(permanent, event_time, ec_id, event.name, link)相同的事件：")
        event_time = sample['event_time'].astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
        permanent = sample['permanent']
        event = sample.get('event', {})
        event_name = event.get('name') if isinstance(event, dict) else None
        ec_id = sample.get('ec_id', None)
        link = sample.get('link', None)

        ec_id_str = str(ec_id) if ec_id is not None else None

        query = f"""
        SELECT * FROM `{self.source_table}`
        WHERE permanent = @permanent
          AND event_time = @event_time
          AND (CAST(ec_id AS STRING) = @ec_id OR (ec_id IS NULL AND @ec_id IS NULL))
          AND (event.name = @event_name OR (event.name IS NULL AND @event_name IS NULL))
          AND (link = @link OR (link IS NULL AND @link IS NULL))
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("permanent", "STRING", permanent),
                bigquery.ScalarQueryParameter("event_time", "TIMESTAMP", event_time),
                bigquery.ScalarQueryParameter("ec_id", "STRING", ec_id_str),
                bigquery.ScalarQueryParameter("event_name", "STRING", event_name),
                bigquery.ScalarQueryParameter("link", "STRING", link),
            ]
        )
        results = list(self.bigquery_client.query(query, job_config=job_config).result())
        print(f"  - 查詢結果: {len(results)} 筆")
        for i, row in enumerate(results):
            print(f"    [源表第{i+1}筆] {dict(row.items())}")

        # 額外查詢同 permanent+event_time 下所有資料分布
        self.check_source_by_permanent_event_time(sample)

    def data_sampling_validation(self, start_utc: datetime, end_utc: datetime, sample_size: int, enable_deduplication: bool = True):
        """抽樣比對來源和目標表的資料內容（支援去重邏輯 toggle），並統計完全重複合併比例，包含成本分析"""
        dedup_status = "啟用去重邏輯" if enable_deduplication else "停用去重邏輯"
        filter_info = ""
        if self.excluded_event_types:
            filter_info = f", 已過濾: {', '.join(self.excluded_event_types)}"

        print(f"\n🔬 資料抽樣驗證 (抽樣數: {sample_size})")
        print(f"🎛️  {dedup_status}{filter_info}")
        print("=" * 50)

        # 1. 先找出目標表中有資料的確切時間範圍
        time_range_query = f"""
            SELECT MIN(event_time) as min_synced_time, MAX(event_time) as max_synced_time
            FROM `{self.target_table}`
            WHERE event_time >= @start_utc AND event_time < @end_utc
              AND partner_source = 'legacy-tagtoo-event'
        """
        job_config_time_range = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", end_utc),
            ]
        )

        # 💰 時間範圍查詢成本估算
        time_range_cost = self._estimate_query_cost(time_range_query, job_config_time_range)

        start_time = datetime.utcnow()
        time_range_result = list(self.bigquery_client.query(time_range_query, job_config=job_config_time_range).result())
        time_range_execution_time = (datetime.utcnow() - start_time).total_seconds()

        if not time_range_result or not time_range_result[0].min_synced_time:
            print("  ⚠️ 在目標表中未找到任何已同步的資料，跳過抽樣驗證。")
            return

        actual_start_utc = time_range_result[0].min_synced_time
        actual_end_utc = time_range_result[0].max_synced_time

        print(f"  ℹ️ 實際抽樣時間範圍 (UTC): {actual_start_utc.strftime('%Y-%m-%d %H:%M:%S')} -> {actual_end_utc.strftime('%Y-%m-%d %H:%M:%S')}")

        # 2. 🔄 根據去重設定調整抽樣邏輯
        job_config_sampling = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_utc", "TIMESTAMP", actual_start_utc),
                bigquery.ScalarQueryParameter("end_utc", "TIMESTAMP", actual_end_utc + timedelta(seconds=1)),
                bigquery.ScalarQueryParameter("sample_size", "INT64", sample_size),
            ]
        )

        # 建構事件過濾條件
        event_filter = self._get_event_filter_condition()

        # 選擇抽樣查詢
        if enable_deduplication:
            # 🎯 使用完整去重邏輯的抽樣（9欄位主鍵）
            query = f"""
            WITH deduplicated_source AS (
                SELECT *
                FROM (
                    SELECT *,
                        ROW_NUMBER() OVER (
                            PARTITION BY
                                permanent,
                                ec_id,
                                event_time,
                                event.name,
                                IFNULL(link, ''),
                                CAST(IFNULL(event.value, 'NULL') AS STRING),
                                IFNULL(event.currency, 'NULL'),
                                IFNULL(event.custom_data.order_id, 'NULL'),
                                'legacy-tagtoo-event'
                            ORDER BY event_time
                        ) as row_num
                    FROM `{self.source_table}`
                    WHERE event_time >= @start_utc AND event_time < @end_utc{event_filter}
                )
                WHERE row_num = 1
            ),
            source_samples AS (
                SELECT *,
                       FARM_FINGERPRINT(CONCAT(CAST(event_time AS STRING), permanent, IFNULL(link, ''))) as sample_id
                FROM deduplicated_source
                ORDER BY sample_id
                LIMIT @sample_size
            )
            SELECT
                s AS source_row,
                t AS target_row
            FROM source_samples s
            LEFT JOIN `{self.target_table}` t
            ON s.permanent = t.permanent
               AND s.ec_id = t.ec_id
               AND s.event_time = t.event_time
               AND s.event.name = t.event
               AND IFNULL(s.link, '') = IFNULL(t.link, '')
               AND CAST(IFNULL(s.event.value, 'NULL') AS STRING) = CAST(IFNULL(t.value, 'NULL') AS STRING)
               AND IFNULL(s.event.currency, 'NULL') = IFNULL(t.currency, 'NULL')
               AND IFNULL(s.event.custom_data.order_id, 'NULL') = IFNULL(t.order_id, 'NULL')
               AND t.partner_source = 'legacy-tagtoo-event'
            ORDER BY s.permanent, s.event_time
            """
        else:
            # 不去重時也將 link 納入主鍵
            query = f"""
            WITH source_samples AS (
                SELECT *,
                       FARM_FINGERPRINT(CONCAT(CAST(event_time AS STRING), permanent, IFNULL(link, ''))) as sample_id
                FROM `{self.source_table}`
                WHERE event_time >= @start_utc AND event_time < @end_utc{event_filter}
                ORDER BY sample_id
                LIMIT @sample_size
            )
            SELECT
                s AS source_row,
                t AS target_row
            FROM source_samples s
            LEFT JOIN `{self.target_table}` t
            ON s.permanent = t.permanent
               AND s.ec_id = t.ec_id
               AND s.event_time = t.event_time
               AND s.event.name = t.event
               AND IFNULL(s.link, '') = IFNULL(t.link, '')
               AND t.partner_source = 'legacy-tagtoo-event'
            ORDER BY s.permanent, s.event_time
            """

        # 💰 抽樣查詢成本估算
        sampling_cost = self._estimate_query_cost(query, job_config_sampling)

        # 💰 總成本分析
        total_cost = time_range_cost.get("estimated_cost_usd", 0) + sampling_cost.get("estimated_cost_usd", 0)
        total_gb = time_range_cost.get("gb_processed", 0) + sampling_cost.get("gb_processed", 0)

        print(f"\n💰 BigQuery 成本分析 - 資料抽樣驗證")
        print(f"   💵 總成本: ${total_cost:.4f} USD ({total_gb:.3f} GB 掃描)")
        print(f"   ⏱️ 時間範圍查詢: {time_range_execution_time:.2f} 秒")

        try:
            # 執行抽樣查詢
            start_time = datetime.utcnow()
            results = self.bigquery_client.query(query, job_config=job_config_sampling).result()
            sampling_execution_time = (datetime.utcnow() - start_time).total_seconds()

            print(f"   ⏱️ 抽樣查詢執行: {sampling_execution_time:.2f} 秒")

            if sampling_cost.get("estimated_cost_usd", 0) > 0.1:
                print(f"   🟡 抽樣查詢成本較高: ${sampling_cost.get('estimated_cost_usd', 0):.4f} USD")

            passed_count = 0
            data_missing_count = 0
            field_mismatch_count = 0
            fully_duplicate_count = 0
            total_duplicates_checked = 0
            failed_details = {
                'data_missing': [],
                'field_mismatch': []
            }

            for i, row in enumerate(results):
                source = row.source_row
                target = row.target_row
                sample_time_local = source['event_time'].astimezone(self.display_timezone)
                print(f"\n--- 樣本 {i+1} (permanent: {source['permanent']}, time: {sample_time_local}) ---")

                # 檢查完全重複合併
                total, fully_dup = self.check_fully_duplicate_rows(source)
                if total > 1 and fully_dup > 0:
                    print(f"  ℹ️ 完全重複合併: {fully_dup} 筆 (同主鍵下所有欄位完全相同)")
                    fully_duplicate_count += fully_dup
                    total_duplicates_checked += total

                if target is None:
                    print("  ❌ 類型: 資料同步遺漏")
                    print("  💡 說明: 源表有記錄，但目標表完全沒有對應資料")
                    data_missing_count += 1
                    failed_details['data_missing'].append({
                        'permanent': source['permanent'],
                        'event_time': sample_time_local,
                        'event_name': source.get('event', {}).get('name') if source.get('event') else 'None',
                        'link': source.get('link', 'None')
                    })
                    continue

                mismatched_fields = self._compare_rows(source, target)
                if mismatched_fields:
                    print("  ⚠️ 類型: 欄位不匹配")
                    print("  💡 說明: 資料已同步，但部分欄位值不一致")
                    for field_path, source_val, target_val in mismatched_fields:
                        print(f"     - {field_path}: 源='{source_val}' vs 目標='{target_val}'")
                    field_mismatch_count += 1
                    failed_details['field_mismatch'].append({
                        'permanent': source['permanent'],
                        'event_time': sample_time_local,
                        'mismatched_fields': mismatched_fields
                    })
                else:
                    print("  ✅ 類型: 同步正常")
                    print("  💡 說明: 所有欄位完全匹配")
                    passed_count += 1

            print("\n" + "=" * 60)
            print("📊 抽樣驗證詳細分類結果")
            print("=" * 60)
            print(f"  ✅ 同步正常: {passed_count} 筆")
            print(f"  ❌ 資料同步遺漏: {data_missing_count} 筆")
            print(f"  ⚠️  欄位不匹配: {field_mismatch_count} 筆")
            print(f"  📊 總計: {passed_count + data_missing_count + field_mismatch_count} 筆")
            if fully_duplicate_count > 0:
                duplicate_percentage = (fully_duplicate_count / total_duplicates_checked) * 100 if total_duplicates_checked > 0 else 0
                print(f"  ℹ️ 完全重複合併: {fully_duplicate_count} 筆 (佔抽樣 {duplicate_percentage:.2f}%)")

            print("\n💡 結論說明：")
            if passed_count == sample_size:
                print("   🎉 所有抽樣資料同步狀況良好！")
            elif data_missing_count > 0:
                print(f"   ⚠️ 發現 {data_missing_count} 筆資料遺漏，建議檢查同步流程")
            elif field_mismatch_count > 0:
                print(f"   ⚠️ 發現 {field_mismatch_count} 筆欄位不匹配，建議檢查轉換邏輯")

            print("   📋 此次抽樣使用與 MERGE 操作相同的去重邏輯")

            if field_mismatch_count > 0:
                print("\n🔎 自動檢查所有欄位不匹配樣本的 MERGE 主鍵重複情況：")
                for idx, item in enumerate(failed_details['field_mismatch']):
                    print(f"\n--- 樣本 {idx+1} (permanent: {item['permanent']}, time: {item['event_time']}) ---")
                    self.check_source_duplicates(item)

        except Exception as e:
            logger.error(f"執行抽樣驗證時出錯: {e}", exc_info=True)
            # 設定預設值以避免 UnboundLocalError
            sampling_execution_time = 0

        # 💰 追踪費用
        self._track_cost("資料抽樣驗證-時間範圍", time_range_cost, time_range_execution_time)
        self._track_cost("資料抽樣驗證-抽樣查詢", sampling_cost, sampling_execution_time)

    def _estimate_query_cost(self, query: str, job_config: bigquery.QueryJobConfig) -> Dict[str, Any]:
        """估算 BigQuery 查詢成本"""
        try:
            # 建立 dry run 配置
            dry_run_config = bigquery.QueryJobConfig()

            # 複製必要參數
            if job_config.query_parameters:
                dry_run_config.query_parameters = job_config.query_parameters
            if job_config.use_legacy_sql is not None:
                dry_run_config.use_legacy_sql = job_config.use_legacy_sql

            # 設定 dry run 選項
            dry_run_config.dry_run = True
            dry_run_config.use_query_cache = False

            # 執行 dry run
            job = self.bigquery_client.query(query, job_config=dry_run_config)

            # 計算成本 (官方 BigQuery 定價 $6.25 per TiB)
            # Reference: https://cloud.google.com/bigquery/pricing
            bytes_processed = job.total_bytes_processed or 0
            tb_processed = bytes_processed / (1024**4) if bytes_processed else 0
            estimated_cost_usd = tb_processed * 6.25  # $6.25 per TiB (官方定價)

            return {
                "bytes_processed": bytes_processed,
                "gb_processed": round(bytes_processed / (1024**3), 3),
                "tb_processed": round(tb_processed, 6),
                "estimated_cost_usd": round(estimated_cost_usd, 4),
                "slot_ms": job.slot_millis if job.slot_millis else 0
            }
        except Exception as e:
            logger.warning(f"成本估算失敗: {e}")
            return {
                "bytes_processed": 0,
                "gb_processed": 0.0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "slot_ms": 0,
                "error": str(e)
            }

    def _print_cost_summary(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float = None):
        """印出查詢成本摘要"""
        cost_usd = cost_estimate.get("estimated_cost_usd", 0)
        gb_processed = cost_estimate.get("gb_processed", 0)

        # 成本警告顏色
        if cost_usd > 1.0:
            cost_icon = "🔴"
            cost_level = "高成本"
        elif cost_usd > 0.1:
            cost_icon = "🟡"
            cost_level = "中等成本"
        else:
            cost_icon = "🟢"
            cost_level = "低成本"

        print(f"\n💰 BigQuery 成本分析 - {operation_name}")
        print(f"   {cost_icon} {cost_level}: ${cost_usd:.4f} USD ({gb_processed:.3f} GB 掃描)")

        if execution_time:
            print(f"   ⏱️ 執行時間: {execution_time:.2f} 秒")

        if cost_estimate.get("error"):
            print(f"   ⚠️ 成本估算警告: {cost_estimate['error']}")

        # 成本建議
        if cost_usd > 0.5:
            print(f"   💡 建議: 考慮縮小時間範圍或加入更精確的過濾條件")
        elif gb_processed > 50:
            print(f"   💡 建議: 查詢掃描了大量資料 ({gb_processed:.1f} GB)，建議優化")

    def _track_cost(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float = None):
        """追踪單次操作的費用"""
        cost_usd = cost_estimate.get("estimated_cost_usd", 0)
        gb_processed = cost_estimate.get("gb_processed", 0)

        self.total_cost_tracker["total_cost_usd"] += cost_usd
        self.total_cost_tracker["total_gb_processed"] += gb_processed
        self.total_cost_tracker["query_count"] += 1

        if execution_time:
            self.total_cost_tracker["total_execution_time"] += execution_time

        self.total_cost_tracker["operations"].append({
            "name": operation_name,
            "cost_usd": cost_usd,
            "gb_processed": gb_processed,
            "execution_time": execution_time or 0
        })

    def _print_total_cost_summary(self):
        """顯示總費用摘要"""
        tracker = self.total_cost_tracker

        print(f"\n" + "=" * 80)
        print(f"💰 BigQuery 總費用摘要")
        print(f"=" * 80)
        print(f"💵 總成本: ${tracker['total_cost_usd']:.4f} USD")
        print(f"📊 總掃描量: {tracker['total_gb_processed']:.3f} GB")
        print(f"⏱️ 總執行時間: {tracker['total_execution_time']:.2f} 秒")
        print(f"🔢 查詢次數: {tracker['query_count']} 次")

        # 成本等級評估
        if tracker['total_cost_usd'] > 5.0:
            cost_level = "🔴 極高成本"
            recommendation = "強烈建議縮小時間範圍或優化查詢邏輯"
        elif tracker['total_cost_usd'] > 1.0:
            cost_level = "🟠 高成本"
            recommendation = "建議考慮縮小時間範圍"
        elif tracker['total_cost_usd'] > 0.1:
            cost_level = "🟡 中等成本"
            recommendation = "注意查詢頻率，避免過度使用"
        else:
            cost_level = "🟢 低成本"
            recommendation = "成本在合理範圍內"

        print(f"📈 成本等級: {cost_level}")
        print(f"💡 建議: {recommendation}")

        # 操作明細
        print(f"\n📋 各操作費用明細:")
        for i, op in enumerate(tracker['operations'], 1):
            print(f"   {i:2d}. {op['name']:<25} ${op['cost_usd']:>8.4f} USD ({op['gb_processed']:>6.3f} GB)")

        # 估算月度成本（假設每日執行一次）
        monthly_cost = tracker['total_cost_usd'] * 30
        print(f"\n📅 預估月度成本: ${monthly_cost:.2f} USD (假設每日執行一次)")

        if monthly_cost > 100:
            print(f"   ⚠️ 月度成本較高，建議審查查詢頻率和範圍")
        elif monthly_cost > 30:
            print(f"   💡 月度成本適中，建議定期監控")

        print(f"=" * 80)

def main():
    """主程式"""
    parser = argparse.ArgumentParser(description=f"智慧同步率驗證腳本。預設驗證過去 24 小時。")
    parser.add_argument('--start', help=f"開始時間 (台灣時區 UTC+8)，格式: 'YYYY-MM-DD HH:MM:SS'")
    parser.add_argument('--end', help=f"結束時間 (台灣時區 UTC+8)，格式: 'YYYY-MM-DD HH:MM:SS'")
    parser.add_argument('--sample_size', type=int, default=10, help="資料抽樣驗證的樣本數，設為 0 可跳過。")
    parser.add_argument('--enable_deduplication', action='store_true', help="🎛️ 啟用去重邏輯驗證 (預設停用去重邏輯)")
    parser.add_argument('--debug_permanent', help="[除錯用] 指定要查詢的 permanent ID")
    parser.add_argument('--debug_time', help="[除錯用] 指定要查詢的 event_time (台灣時區 UTC+8)，格式: 'YYYY-MM-DD HH:MM:SS'")

    parser.add_argument('--production', action='store_true', help='驗證正式表 event_prod.integrated_event（預設為 event_test.integrated_event）')
    args = parser.parse_args()

    validator = SmartSyncRateValidator(production=args.production)

    if args.debug_permanent and args.debug_time:
        validator.debug_single_event(args.debug_permanent, args.debug_time)
        return

    tz = validator.display_timezone

    if args.start and args.end:
        try:
            # 將輸入的台灣時間 (UTC+8) 轉換為 UTC datetime 對象
            taiwan_tz = pytz.timezone('Asia/Taipei')
            start_taiwan = taiwan_tz.localize(datetime.fromisoformat(args.start))
            end_taiwan = taiwan_tz.localize(datetime.fromisoformat(args.end))

            # 轉換為 UTC
            start_utc = start_taiwan.astimezone(pytz.utc)
            end_utc = end_taiwan.astimezone(pytz.utc)

            logger.info(f"🕐 時間轉換: {args.start} (台灣) -> {start_utc.strftime('%Y-%m-%d %H:%M:%S')} (UTC)")
            logger.info(f"🕐 時間轉換: {args.end} (台灣) -> {end_utc.strftime('%Y-%m-%d %H:%M:%S')} (UTC)")
        except ValueError:
            logger.error("時間格式錯誤，請使用 'YYYY-MM-DD HH:MM:SS' 格式 (台灣時區 UTC+8)。")
            sys.exit(1)
    else:
        # 預設：檢查過去 24 小時（UTC 時間）
        end_utc = datetime.now(pytz.utc)
        start_utc = end_utc - timedelta(hours=24)

    # 🎛️ Feature Toggle: 去重邏輯驗證設定
    enable_deduplication = args.enable_deduplication

    # 🔍 重要說明：即使停用驗證腳本的去重邏輯，實際同步過程仍有以下去重機制：
    # 1. BigQuery MERGE 操作的自然去重
    # 2. Python 記憶體去重 (當 USE_OPTIMIZED_MERGE=true 時)
    # 3. 主鍵衝突避免
    if not enable_deduplication:
        logger.info("📝 注意：驗證腳本停用複雜去重邏輯，但實際同步仍有基本去重機制")

    validator.run_all_analyses(start_utc, end_utc, args.sample_size, enable_deduplication)

if __name__ == "__main__":
    main()
