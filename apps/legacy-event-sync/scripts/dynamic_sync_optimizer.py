#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
動態同步優化器 - 根據資料範圍自動調整最佳同步參數

此模組會根據同步的時間範圍、預估資料量和系統資源，
動態計算最佳的切分策略和平行處理參數。

功能特色：
- 🧠 智能時間切分：根據資料量動態調整切分粒度
- ⚡ 平行度優化：根據系統資源和資料特性調整 worker 數量
- 💰 成本控制：平衡處理速度和 BigQuery 成本
- 📊 效能預測：預估同步時間和資源使用
"""

import math
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class DynamicSyncOptimizer:
    """動態同步參數優化器"""

    def __init__(self):
        # 基準參數（基於歷史測試數據）
        self.base_events_per_hour = 3500  # 平均每小時事件數
        self.base_processing_rate = 60    # 每分鐘處理事件數（經濟模式）
        self.max_workers_limit = 8        # 系統最大 worker 數量
        self.min_segment_minutes = 5      # 最小切分粒度（分鐘）
        self.max_segment_minutes = 60     # 最大切分粒度（分鐘）

        # 成本控制參數
        self.cost_per_tb = 6.25          # BigQuery 查詢成本 USD/TB
        self.bytes_per_hour = 251415832  # 每小時掃描的 bytes（基於測試）

    def calculate_optimal_config(self,
                               start_time_str: str,
                               end_time_str: str,
                               sync_mode: str = "economical",
                               target_cost_limit: float = 10.0) -> Dict[str, Any]:
        """
        計算最佳同步配置

        Args:
            start_time_str: 開始時間
            end_time_str: 結束時間
            sync_mode: 同步模式 ('economical' 或 'fast')
            target_cost_limit: 目標成本上限 (USD)

        Returns:
            最佳配置字典
        """
        # 解析時間範圍
        start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
        total_hours = (end_time - start_time).total_seconds() / 3600

        # 預估資料量
        estimated_events = int(total_hours * self.base_events_per_hour)
        estimated_cost = self._estimate_cost(total_hours)

        # 根據資料量和時間範圍計算最佳參數
        optimal_config = self._calculate_segmentation_strategy(
            total_hours, estimated_events, sync_mode, target_cost_limit
        )

        # 添加預估資訊
        optimal_config.update({
            "estimated_events": estimated_events,
            "estimated_cost_usd": estimated_cost,
            "total_hours": total_hours,
            "estimated_duration_minutes": self._estimate_duration(
                estimated_events, optimal_config["max_workers"],
                optimal_config["split_interval_minutes"]
            )
        })

        return optimal_config

    def _calculate_segmentation_strategy(self,
                                       total_hours: float,
                                       estimated_events: int,
                                       sync_mode: str,
                                       cost_limit: float) -> Dict[str, Any]:
        """計算分段策略"""

        # 根據資料量決定切分粒度
        if total_hours <= 1:
            # 1小時以內：細粒度切分，充分利用平行化
            split_minutes = max(self.min_segment_minutes, int(total_hours * 60 / 8))
            max_workers = min(6, max(2, int(total_hours * 60 / split_minutes)))
        elif total_hours <= 24:
            # 1天以內：中等粒度切分
            split_minutes = 15
            max_workers = min(8, max(4, int(total_hours / 2)))
        elif total_hours <= 168:  # 1週
            # 1週以內：平衡切分
            split_minutes = 30
            max_workers = min(6, max(4, int(math.sqrt(total_hours))))
        else:
            # 超過1週：較大切分，避免過多分段
            split_minutes = 60
            max_workers = 4

        # 根據同步模式調整
        if sync_mode == "fast":
            # 快速模式：可以使用更多 worker
            max_workers = min(self.max_workers_limit, max_workers + 2)
        else:
            # 經濟模式：保守一些，避免記憶體壓力
            max_workers = min(6, max_workers)

        # 成本控制調整
        estimated_cost = self._estimate_cost(total_hours)
        if estimated_cost > cost_limit:
            # 如果預估成本超標，增加切分粒度減少並行度
            split_minutes = min(60, split_minutes * 2)
            max_workers = max(2, max_workers - 1)

        # 確保參數在合理範圍內
        split_minutes = max(self.min_segment_minutes,
                          min(self.max_segment_minutes, split_minutes))
        max_workers = max(1, min(self.max_workers_limit, max_workers))

        # 計算總分段數
        total_segments = math.ceil(total_hours * 60 / split_minutes)

        return {
            "split_interval_minutes": split_minutes,
            "max_workers": max_workers,
            "total_segments": total_segments,
            "parallel_efficiency": min(1.0, max_workers / total_segments),
            "optimization_strategy": self._get_strategy_description(
                total_hours, split_minutes, max_workers
            )
        }

    def _estimate_cost(self, total_hours):
        """預估 BigQuery 成本"""
        total_bytes = total_hours * self.bytes_per_hour
        total_tb = total_bytes / (1024**4)
        return total_tb * self.cost_per_tb

    def _estimate_duration(self, events: int, workers: int, split_minutes: int) -> float:
        """預估執行時間（分鐘）"""
        # 基於經驗公式：考慮平行效率和處理速度
        parallel_efficiency = min(1.0, workers / 4)  # 4個worker時效率最佳
        effective_rate = self.base_processing_rate * workers * parallel_efficiency

        # 加上分段切換的開銷
        segment_overhead = (events / (split_minutes * self.base_events_per_hour)) * 0.5

        base_duration = events / effective_rate
        return base_duration + segment_overhead

    def _get_strategy_description(self, hours: float, split_min: int, workers: int) -> str:
        """獲取策略描述"""
        if hours <= 1:
            return f"🚀 短時間高並行策略：{split_min}分鐘切分，{workers}個worker並行處理"
        elif hours <= 24:
            return f"⚡ 中等時間平衡策略：{split_min}分鐘切分，{workers}個worker穩定處理"
        elif hours <= 168:
            return f"🔄 長時間穩定策略：{split_min}分鐘切分，{workers}個worker持續處理"
        else:
            return f"🛡️ 大規模保守策略：{split_min}分鐘切分，{workers}個worker安全處理"

    def print_optimization_report(self, config: Dict[str, Any],
                                start_time_str: str, end_time_str: str):
        """打印優化報告"""
        print("\n" + "="*60)
        print("🧠 動態同步優化報告")
        print("="*60)
        print(f"📅 時間範圍: {start_time_str} ~ {end_time_str}")
        print(f"⏱️  總時長: {config['total_hours']:.1f} 小時")
        print(f"📊 預估事件數: {config['estimated_events']:,} 筆")
        print(f"💰 預估成本: ${config['estimated_cost_usd']:.4f} USD")
        print(f"🕐 預估執行時間: {config['estimated_duration_minutes']:.1f} 分鐘")
        print()
        print("🔧 最佳化參數:")
        print(f"   ⚡ 切分間隔: {config['split_interval_minutes']} 分鐘")
        print(f"   👥 並行 Workers: {config['max_workers']} 個")
        print(f"   📋 總分段數: {config['total_segments']} 個")
        print(f"   📈 並行效率: {config['parallel_efficiency']:.1%}")
        print()
        print(f"💡 策略說明: {config['optimization_strategy']}")
        print("="*60)


def optimize_sync_config(start_time_str: str,
                        end_time_str: str,
                        sync_mode: str = "economical",
                        cost_limit: float = 10.0,
                        show_report: bool = True) -> Dict[str, Any]:
    """
    便捷函數：獲取最佳同步配置

    Args:
        start_time_str: 開始時間 "YYYY-MM-DD HH:MM:SS"
        end_time_str: 結束時間 "YYYY-MM-DD HH:MM:SS"
        sync_mode: 同步模式
        cost_limit: 成本上限
        show_report: 是否顯示報告

    Returns:
        最佳配置字典
    """
    optimizer = DynamicSyncOptimizer()
    config = optimizer.calculate_optimal_config(
        start_time_str, end_time_str, sync_mode, cost_limit
    )

    if show_report:
        optimizer.print_optimization_report(config, start_time_str, end_time_str)

    return config


if __name__ == "__main__":
    # 測試不同時間範圍的優化效果
    test_cases = [
        ("2025-07-01 00:00:00", "2025-07-01 01:00:00", "1小時測試"),
        ("2025-07-01 00:00:00", "2025-07-01 12:00:00", "12小時測試"),
        ("2025-07-01 00:00:00", "2025-07-02 00:00:00", "1天測試"),
        ("2025-07-01 00:00:00", "2025-07-08 00:00:00", "1週測試"),
        ("2025-07-01 00:00:00", "2025-07-22 00:00:00", "21天測試"),
    ]

    for start, end, desc in test_cases:
        print(f"\n🧪 {desc}")
        config = optimize_sync_config(start, end, show_report=True)
