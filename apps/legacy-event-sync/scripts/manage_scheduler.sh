#!/bin/bash
# Legacy Event Sync - Scheduler 管理腳本
# 用於快速控制 Cloud Scheduler 的啟用/禁用狀態

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 預設值
PROJECT_ID="tagtoo-tracking"
REGION="asia-east1"
ENVIRONMENT="prod"
SERVICE_NAME="legacy-event-sync"

# 顯示幫助資訊
show_help() {
    echo "Legacy Event Sync - Scheduler 管理腳本"
    echo ""
    echo "用法: $0 [命令] [選項]"
    echo ""
    echo "命令:"
    echo "  status     - 檢查 scheduler 狀態"
    echo "  stop       - 停止 scheduler"
    echo "  start      - 啟用 scheduler"
    echo "  toggle     - 切換 scheduler 狀態"
    echo "  terraform  - 使用 Terraform 控制 scheduler"
    echo ""
    echo "選項:"
    echo "  -e, --environment ENV  設定環境 (預設: prod)"
    echo "  -p, --project PROJECT  設定專案 ID (預設: tagtoo-tracking)"
    echo "  -r, --region REGION    設定地區 (預設: asia-east1)"
    echo "  -h, --help             顯示此幫助資訊"
    echo ""
    echo "範例:"
    echo "  $0 status                    # 檢查狀態"
    echo "  $0 stop                      # 停止 scheduler"
    echo "  $0 start                     # 啟用 scheduler"
    echo "  $0 terraform enable          # 使用 Terraform 啟用"
    echo "  $0 terraform disable         # 使用 Terraform 禁用"
}

# 解析命令列參數
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -p|--project)
                PROJECT_ID="$2"
                shift 2
                ;;
            -r|--region)
                REGION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                COMMAND="$1"
                shift
                ;;
        esac
    done
}

# 檢查 gcloud 是否可用
check_gcloud() {
    if ! command -v gcloud >/dev/null 2>&1; then
        echo -e "${RED}❌ gcloud CLI 未安裝或不在 PATH 中${NC}"
        echo "請安裝 Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
}

# 檢查認證狀態
check_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo -e "${RED}❌ 未找到有效的 Google Cloud 認證${NC}"
        echo "請執行: gcloud auth login"
        exit 1
    fi
}

# 取得 scheduler 名稱
get_scheduler_name() {
    echo "${SERVICE_NAME}-${ENVIRONMENT}"
}

# 檢查 scheduler 狀態
check_status() {
    local scheduler_name=$(get_scheduler_name)

    echo -e "${BLUE}🔍 檢查 Cloud Scheduler 狀態...${NC}"
    echo "專案: $PROJECT_ID"
    echo "地區: $REGION"
    echo "Scheduler: $scheduler_name"
    echo ""

    if gcloud scheduler jobs describe "$scheduler_name" \
        --project="$PROJECT_ID" \
        --region="$REGION" \
        --format="table(name,state,lastAttemptTime,nextRunTime)" 2>/dev/null; then
        echo ""
        echo -e "${GREEN}✅ Scheduler 存在且可存取${NC}"
    else
        echo -e "${YELLOW}⚠️  Scheduler 不存在或無法存取${NC}"
        echo "可能的原因："
        echo "  - Scheduler 尚未創建"
        echo "  - 權限不足"
        echo "  - 地區或專案設定錯誤"
    fi
}

# 停止 scheduler
stop_scheduler() {
    local scheduler_name=$(get_scheduler_name)

    echo -e "${YELLOW}🔄 停止 Cloud Scheduler...${NC}"
    echo "Scheduler: $scheduler_name"
    echo ""

    if gcloud scheduler jobs pause "$scheduler_name" \
        --project="$PROJECT_ID" \
        --region="$REGION"; then
        echo -e "${GREEN}✅ Scheduler 已停止${NC}"
    else
        echo -e "${RED}❌ 停止失敗${NC}"
        exit 1
    fi
}

# 啟用 scheduler
start_scheduler() {
    local scheduler_name=$(get_scheduler_name)

    echo -e "${YELLOW}🔄 啟用 Cloud Scheduler...${NC}"
    echo "Scheduler: $scheduler_name"
    echo ""

    if gcloud scheduler jobs resume "$scheduler_name" \
        --project="$PROJECT_ID" \
        --region="$REGION"; then
        echo -e "${GREEN}✅ Scheduler 已啟用${NC}"
    else
        echo -e "${RED}❌ 啟用失敗${NC}"
        exit 1
    fi
}

# 切換 scheduler 狀態
toggle_scheduler() {
    local scheduler_name=$(get_scheduler_name)

    echo -e "${BLUE}🔄 檢查當前狀態並切換...${NC}"

    # 檢查當前狀態
    local state=$(gcloud scheduler jobs describe "$scheduler_name" \
        --project="$PROJECT_ID" \
        --region="$REGION" \
        --format="value(state)" 2>/dev/null || echo "UNKNOWN")

    case "$state" in
        "ENABLED")
            echo "當前狀態: 已啟用"
            echo -e "${YELLOW}切換為: 停止${NC}"
            stop_scheduler
            ;;
        "PAUSED"|"DISABLED")
            echo "當前狀態: 已停止"
            echo -e "${YELLOW}切換為: 啟用${NC}"
            start_scheduler
            ;;
        *)
            echo -e "${RED}❌ 無法確定當前狀態${NC}"
            exit 1
            ;;
    esac
}

# 使用 Terraform 控制 scheduler
terraform_control() {
    local action="$1"

    if [ -z "$action" ]; then
        echo -e "${RED}❌ 請指定 Terraform 動作 (enable/disable)${NC}"
        echo "用法: $0 terraform [enable|disable]"
        exit 1
    fi

    echo -e "${BLUE}🔧 使用 Terraform 控制 scheduler...${NC}"
    echo "動作: $action"
    echo ""

    # 檢查是否在正確的目錄
    if [ ! -f "terraform/main.tf" ]; then
        echo -e "${RED}❌ 請在 legacy-event-sync 目錄中執行此命令${NC}"
        exit 1
    fi

    case "$action" in
        "enable")
            echo "設定 enable_scheduler = true"
            cd terraform
            terraform apply -var="enable_scheduler=true" -var="environment=$ENVIRONMENT" -auto-approve
            ;;
        "disable")
            echo "設定 enable_scheduler = false"
            cd terraform
            terraform apply -var="enable_scheduler=false" -var="environment=$ENVIRONMENT" -auto-approve
            ;;
        *)
            echo -e "${RED}❌ 無效的動作: $action${NC}"
            echo "有效的動作: enable, disable"
            exit 1
            ;;
    esac
}

# 主函數
main() {
    parse_args "$@"

    # 檢查必要工具
    check_gcloud
    check_auth

    case "$COMMAND" in
        "status")
            check_status
            ;;
        "stop")
            stop_scheduler
            ;;
        "start")
            start_scheduler
            ;;
        "toggle")
            toggle_scheduler
            ;;
        "terraform")
            terraform_control "$2"
            ;;
        "")
            echo -e "${RED}❌ 請指定命令${NC}"
            echo ""
            show_help
            exit 1
            ;;
        *)
            echo -e "${RED}❌ 無效的命令: $COMMAND${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"
