#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legacy Event Sync - 手動同步腳本 (重構版)

此腳本提供兩種同步模式，確保資料轉換、錯誤處理邏輯與正式服務完全一致。

🔧 同步模式：
- 🟢 經濟模式 (預設)：使用本地 polars 處理 + 批次寫入，成本低但較慢
- 🔴 快速模式：使用 BigQuery MERGE，成本高但快速

功能特色：
- 🔄 直接共用正式同步邏輯，確保資料一致性
- 🧪 支援 dry-run 模式，僅查詢不寫入
- 💰 BigQuery 費用估算功能
- 📊 即時顯示同步進度與批次處理狀況
- ⚙️  靈活的 CLI 參數設定

使用範例：
  # 經濟模式同步 (預設，成本低)
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 23:59:59"

  # 快速模式同步 (成本高，需手動開啟)
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 23:59:59" --mode fast

  # 測試模式（不寫入）
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --dry_run

成本對比 (1小時資料)：
- 經濟模式：~$0.055 USD (查詢成本)
- 快速模式：~$11.26 USD (包含 MERGE 成本)
"""

import argparse
import json
import logging
import os
import sys
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

import polars as pl
import pytz
from google.cloud import bigquery

def get_worker_id() -> str:
    """獲取當前 worker 的識別標記"""
    thread_name = threading.current_thread().name
    if thread_name.startswith('ThreadPoolExecutor'):
        # 提取線程編號，例如 "ThreadPoolExecutor-0_1" → "Worker-1"
        parts = thread_name.split('_')
        if len(parts) > 1:
            return f"[Worker-{parts[-1]}]"
    # 如果是主線程
    if thread_name == 'MainThread':
        return "[Main]"
    # 其他情況使用線程 ID
    return f"[Worker-{threading.get_ident() % 1000}]"

# --- 本地模組匯入 ---
# 將 src 目錄加入 Python 路徑，以便直接匯入 main.py 的類別
current_dir = os.path.dirname(__file__)
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from main import LegacyEventSyncProcessor, _parse_excluded_event_types
    from gcs_dashboard_uploader import create_dashboard_uploader
    # 從環境變數或預設值取得設定
    BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "5000"))
    PROJECT_ID = os.environ.get("PROJECT_ID", "tagtoo-tracking")
    # 安全起見：優先從環境變數讀取目標表格，若無則使用測試表格作為預設值
    TARGET_TABLE_DEFAULT = os.environ.get("TARGET_TABLE", f"{PROJECT_ID}.event_test.integrated_event")
    SOURCE_TABLE_DEFAULT = os.environ.get("SOURCE_TABLE", f"{PROJECT_ID}.event_prod.tagtoo_event")

    # 🎛️ 導入事件過濾配置，與main.py保持一致
    EXCLUDED_EVENT_TYPES = _parse_excluded_event_types()

except ImportError as e:
    print(f"❌ 無法匯入 LegacyEventSyncProcessor: {e}")
    print("請確認此腳本位於 scripts/ 目錄下，且 src/main.py 存在")
    print(f"當前查找路徑: {src_dir}")
    sys.exit(1)

# --- 日誌設定 ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)


class ManualSyncRunner:
    """手動同步執行器，負責協調整個同步過程"""

    def __init__(self,
                 project_id: str,
                 source_table: str,
                 target_table: str,
                 batch_size: int = BATCH_SIZE,
                 enable_deduplication: bool = False,
                 auto_patch_legacy: bool = True,
                 split_interval_minutes: int = 60,
                 sync_mode: str = "economical",
                 memory_limit_gb: int = 8):
        """
        初始化手動同步執行器

        Args:
            project_id: GCP 專案 ID
            source_table: 來源表格完整路徑 (如: project.dataset.table)
            target_table: 目標表格完整路徑
            batch_size: 每批次處理的事件數量
            enable_deduplication: 是否啟用去重複邏輯 (預設: False)
            auto_patch_legacy: 是否自動修正早於 2025-07-22 的來源/目標表格 (預設: True)
            sync_mode: 同步模式 ('economical' 或 'fast')
            memory_limit_gb: 記憶體限制 GB (僅經濟模式)
        """
        self.project_id = project_id
        self.batch_size = batch_size
        self.display_timezone = pytz.timezone("Asia/Taipei")
        self.source_table = source_table
        self.target_table = target_table
        self.auto_patch_legacy = auto_patch_legacy
        self.enable_deduplication = enable_deduplication
        self.split_interval_minutes = split_interval_minutes
        self.sync_mode = sync_mode
        self.memory_limit_gb = memory_limit_gb

        # 🎛️ Feature Toggle: 冪等性檢查控制（與 main.py 一致）
        # 預設關閉冪等性檢查以節省成本（約64%），可透過環境變數啟用
        self.enable_idempotency_check = os.environ.get("ENABLE_IDEMPOTENCY_CHECK", "false").lower() in ("true", "1", "yes")

        # 🎛️ 初始化事件過濾配置，與main.py保持一致
        self.excluded_event_types = EXCLUDED_EVENT_TYPES

        # 初始化 BigQuery 客戶端
        self.bq_client = bigquery.Client(project=project_id)

        # 僅在快速模式下初始化 processor
        if sync_mode == "fast":
            self.processor = LegacyEventSyncProcessor(
                source_table=self.source_table,
                target_table=self.target_table,
                task_queue_name=None,
                worker_url=None,
                enable_deduplication=enable_deduplication
            )
        else:
            self.processor = None

        # 📊 初始化 GCS 儀表板上傳器
        self.dashboard_uploader = None
        self._enable_dashboard = os.environ.get("ENABLE_DASHBOARD", "true").lower() in ("true", "1", "yes")
        self._dashboard_bucket = os.environ.get("PLATFORM_DASHBOARD_BUCKET", "integrated-event-platform-prod-tagtoo-tracking")

        if self._enable_dashboard:
            try:
                self.dashboard_uploader = create_dashboard_uploader(
                    bucket_name=self._dashboard_bucket,
                    app_name="legacy-event-sync"
                )
                logger.info(f"📊 GCS 儀表板已啟用，bucket: {self._dashboard_bucket}")
            except Exception as e:
                logger.warning(f"⚠️ 儀表板上傳器初始化失敗: {e}")
                self._enable_dashboard = False

        if not self._enable_dashboard:
            logger.info("📊 GCS 儀表板已停用")

        logger.info(f"🚀 初始化手動同步執行器")
        logger.info(f"   同步模式: {'🟢 經濟模式' if sync_mode == 'economical' else '🔴 快速模式'}")
        logger.info(f"   來源表格: {self.source_table}")
        logger.info(f"   目標表格: {self.target_table}")
        logger.info(f"   批次大小: {batch_size:,} 筆")
        logger.info(f"   去重複邏輯: {'啟用' if enable_deduplication else '停用'}")
        logger.info(f"   冪等性檢查: {'啟用' if self.enable_idempotency_check else '停用 (成本優化)'}")
        logger.info(f"   切分區間: {split_interval_minutes} 分鐘")

        # 🎛️ 顯示事件過濾配置
        if self.excluded_event_types:
            logger.info(f"   事件過濾: 排除 {self.excluded_event_types} 類型事件")
        else:
            logger.info(f"   事件過濾: 未設定過濾條件")

        if sync_mode == "economical":
            logger.info(f"   批次寫入大小: {batch_size:,} 筆")
            logger.info(f"   記憶體限制: {memory_limit_gb} GB")

    def parse_time_input(self, time_str: str) -> datetime:
        """
        解析時間輸入字串，轉換為 UTC 時間

        Args:
            time_str: 時間字串，格式為 'YYYY-MM-DD HH:MM:SS' (Asia/Taipei 時區)

        Returns:
            datetime: UTC 時間

        Raises:
            ValueError: 當時間格式不正確時
        """
        try:
            # 👉 解析為 Asia/Taipei 時區
            local_time = self.display_timezone.localize(
                datetime.fromisoformat(time_str)
            )

            # 👉 轉換為 UTC 時間
            utc_time = local_time.astimezone(pytz.utc)

            logger.debug(f"時間轉換: {time_str} (Asia/Taipei) -> {utc_time} (UTC)")
            return utc_time

        except ValueError as e:
            logger.error(f"❌ 時間格式錯誤: {time_str}")
            logger.error("請使用格式: 'YYYY-MM-DD HH:MM:SS' (Asia/Taipei 時區)")
            raise ValueError(f"時間格式錯誤: {e}")

    def _auto_patch_legacy_tables(self, start_utc: datetime, end_utc: datetime):
        """
        自動修正 legacy event 的來源/目標表格
        若同步區間早於 2025-07-22 00:00:00（台灣時間），自動 patch 表格名稱
        """
        if self.auto_patch_legacy:
            tw_cutoff = self.display_timezone.localize(datetime(2025, 7, 22, 0, 0, 0))
            utc_cutoff = tw_cutoff.astimezone(pytz.utc)
            # 若結束時間早於 cutoff，或開始時間早於 cutoff
            if end_utc <= utc_cutoff or start_utc < utc_cutoff:
                # 預設 legacy event 的來源表
                legacy_source = f"{self.project_id}.event_prod.tagtoo_event"
                legacy_target = f"{self.project_id}.event_prod.integrated_event"
                logger.info("🕰️  偵測到同步區間早於 2025-07-22，將自動切換 legacy event 表格:")
                logger.info(f"   來源表格: {legacy_source}")
                logger.info(f"   目標表格: {legacy_target}")
                self.source_table = legacy_source
                self.target_table = legacy_target
                # 重新建立 processor
                self.processor = LegacyEventSyncProcessor(
                    source_table=self.source_table,
                    target_table=self.target_table,
                    task_queue_name=None,
                    worker_url=None,
                    enable_deduplication=self.enable_deduplication
                )



    def run_sync(
        self,
        start_time_str: str,
        end_time_str: str,
        dry_run: bool = False,
        max_workers: int = None
    ) -> Dict[str, Any]:
        """
        執行手動同步作業

        Args:
            start_time_str: 開始時間字串 (Asia/Taipei)
            end_time_str: 結束時間字串 (Asia/Taipei)
            dry_run: 是否只測試不實際寫入（自動進行費用估算）
            max_workers: 平行處理的最大 worker 數量

        Returns:
            同步結果摘要
        """

        # 🚀 動態設定 max_workers（優先級：參數 > 環境變數 > CPU數量 > 預設值）
        if max_workers is None:
            # 讀取環境變數配置
            max_workers = int(os.environ.get("MAX_CONCURRENT_SEGMENTS",
                             os.environ.get("CPU_COUNT",
                             os.cpu_count() or 4)))

        # 🔧 資源限制檢查
        available_cpus = os.cpu_count() or 4
        memory_gb = self.memory_limit_gb

        # 根據記憶體限制調整 worker 數量（每個 worker 建議至少 2GB）
        memory_based_limit = max(1, memory_gb // 2)
        max_workers = min(max_workers, available_cpus, memory_based_limit)

        logger.info(f"🚀 平行處理配置：max_workers={max_workers} (CPU: {available_cpus}, 記憶體: {memory_gb}GB)")

        # 🕐 解析時間參數
        try:
            start_utc = self.parse_time_input(start_time_str)
            end_utc = self.parse_time_input(end_time_str)
        except ValueError:
            return {"status": "error", "message": "時間格式錯誤"}

        # 🔧 自動修正 legacy 表格 (如果需要)
        self._auto_patch_legacy_tables(start_utc, end_utc)

        # ⏱️ 顯示同步資訊
        start_local = start_utc.astimezone(self.display_timezone)
        end_local = end_utc.astimezone(self.display_timezone)

        mode_desc = "🧪 測試模式 (不寫入)" if dry_run else "🔄 正式同步"
        logger.info(f"{mode_desc} - 時間範圍 (Asia/Taipei):")
        logger.info(f"   開始: {start_local.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"   結束: {end_local.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"   來源表格: {self.source_table}")
        logger.info(f"   目標表格: {self.target_table}")

        # 💰 費用估算將由各分段獨立進行（移除初始估算）

        # 分段切分
        segs = []
        seg = start_utc
        delta = timedelta(minutes=self.split_interval_minutes)
        while seg < end_utc:
            next_seg = min(seg + delta, end_utc)
            segs.append((seg, next_seg))
            seg = next_seg

        try:
            # 🔍 調試輸出：確認 sync_mode 值
            logger.info(f"🔧 調試資訊：sync_mode = '{self.sync_mode}'")

            # 🔄 根據模式選擇同步邏輯
            if self.sync_mode == "fast":
                # 快速模式：使用原有的 MERGE 邏輯
                import concurrent.futures
                mode_desc = "🧪 Dry-run 模式" if dry_run else "🚀 快速同步模式"
                main_id = get_worker_id()
                logger.info(f"{main_id} {mode_desc}：分段平行處理，每段 {self.split_interval_minutes} 分鐘，max_workers={max_workers}")
                logger.info(f"{main_id} 📋 將處理 {len(segs)} 個時間段")

                results = []
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_seg = {executor.submit(self._sync_segment_unified, seg_pair, dry_run): seg_pair for seg_pair in segs}
                    for future in concurrent.futures.as_completed(future_to_seg):
                        res = future.result()
                        results.append(res)
            else:
                # 經濟模式：使用 polars 本地處理
                mode_desc = "🧪 Dry-run 模式" if dry_run else "🟢 經濟同步模式"
                main_id = get_worker_id()
                logger.info(f"{main_id} {mode_desc}：使用 polars 本地處理，每段 {self.split_interval_minutes} 分鐘")
                logger.info(f"{main_id} 📋 將處理 {len(segs)} 個時間段")

                results = []
                for seg_pair in segs:
                    res = self._sync_segment_economical(seg_pair, dry_run)
                    results.append(res)
            # 📊 匯總所有分段的結果
            logger.info("📊 正在匯總各分段結果...")
            total_events = sum(r.get("total_events", 0) for r in results)
            synced_events = sum(r.get("synced_events", 0) for r in results)
            error_events = sum(r.get("error_events", 0) for r in results)
            batches_processed = sum(r.get("batches_processed", 0) for r in results)

            # 累加所有分段的費用估算
            total_cost_usd = 0.0
            total_tb_processed = 0.0
            valid_cost_segments = 0

            # 🔍 調試：費用累加追蹤
            logger.debug(f"💰 開始累加 {len(results)} 個分段的費用...")

            for i, r in enumerate(results):
                ce = r.get("cost_estimate")
                segment_id = r.get("segment_id", f"segment-{i}")

                if ce and not ce.get("error"):
                    cost_usd = ce.get("estimated_cost_usd", 0.0)
                    tb_processed = ce.get("tb_processed", 0.0)
                    total_cost_usd += cost_usd
                    total_tb_processed += tb_processed
                    valid_cost_segments += 1
                    logger.debug(f"💰 分段 {segment_id}: +${cost_usd:.4f} USD (+{tb_processed:.6f} TB)")
                else:
                    logger.debug(f"💰 分段 {segment_id}: 無有效費用資訊 (ce={ce})")

            logger.debug(f"💰 費用累加完成: {valid_cost_segments}/{len(results)} 個分段有效，總費用 ${total_cost_usd:.4f} USD ({total_tb_processed:.6f} TB)")

            # 決定狀態
            if dry_run:
                status = "dry_run_success" if error_events == 0 else "partial_success"
            else:
                status = ("success" if error_events == 0 else ("partial_success" if error_events < total_events else "error"))

            result = {
                "status": status,
                "total_events": total_events,
                "synced_events": synced_events,
                "error_events": error_events,
                "batches_processed": batches_processed,
            }

            # 加入費用資訊 (即使成本為0也要記錄)
            if total_cost_usd >= 0 or total_tb_processed >= 0:
                result["cost_estimate"] = {
                    "estimated_cost_usd": total_cost_usd,
                    "tb_processed": total_tb_processed,
                    "error": False  # 明確標示沒有錯誤
                }

            # 📊 更新儀表板
            self._update_dashboard_after_sync(result, start_time_str, end_time_str, dry_run, len(segs))

            # ✅ 顯示最終摘要
            self._display_sync_summary(result, dry_run, start_time_str, end_time_str)
            return result

        except Exception as e:
            logger.error(f"❌ 同步過程發生錯誤: {e}", exc_info=True)
            return {
                "status": "error",
                "message": str(e),
                "cost_estimate": {
                    "estimated_cost_usd": 0.0,
                    "tb_processed": 0.0,
                    "error": "同步過程發生錯誤"
                }
            }

    def _sync_segment_unified(self, seg_pair, dry_run: bool = False):
        """統一的分段處理方法，支援 dry_run 和實際同步"""
        seg, next_seg = seg_pair
        segment_id = f"{seg.strftime('%H:%M')}-{next_seg.strftime('%H:%M')}"
        mode_desc = "[Dry-run]" if dry_run else "[Sync]"

        try:
            worker_id = get_worker_id()
            logger.info(f"🔎 {worker_id} {mode_desc} 處理區間 {segment_id}: {seg.strftime('%Y-%m-%d %H:%M:%S')} ~ {next_seg.strftime('%Y-%m-%d %H:%M:%S')}")

            # 使用統一的 sync_time_segment 方法，透過 dry_run 參數控制行為
            result = self.processor.sync_time_segment(seg, next_seg, dry_run=dry_run)

            # 💰 費用估算處理（🐛 修復：使用 main.py 返回的實際費用）
            cost_estimate = result.get("cost_estimate")
            if dry_run and not cost_estimate:
                # Dry-run 模式且 main.py 沒有返回費用時，才使用本地估算
                cost_estimate = self._estimate_segment_cost(seg, next_seg)

            # 🔍 調試：費用計算追蹤
            if cost_estimate:
                cost_usd = cost_estimate.get("estimated_cost_usd", 0)
                tb_processed = cost_estimate.get("tb_processed", 0)
                logger.debug(f"💰 {worker_id} {mode_desc} 分段 {segment_id} 費用: ${cost_usd:.4f} USD ({tb_processed:.6f} TB)")
            else:
                logger.debug(f"💰 {worker_id} {mode_desc} 分段 {segment_id} 無費用資訊")

            # 標準化結果格式 (🐛 修復：使用正確的鍵名)
            total_events = result.get("total_events", result.get("synced_events", 0) + result.get("error_events", 0))
            synced_events = result.get("synced_events", 0)
            error_events = result.get("error_events", 0)
            batches_processed = result.get("batches_processed", 1)

            # 顯示完成訊息（包含費用資訊）
            if cost_estimate and not cost_estimate.get("error"):
                cost_usd = cost_estimate.get("estimated_cost_usd", 0)
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: {total_events:,} 筆事件 (${cost_usd:.4f} USD)")
            else:
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: {total_events:,} 筆事件")

            return {
                "status": "success" if error_events == 0 else "partial_success",
                "total_events": total_events,
                "synced_events": synced_events,
                "error_events": error_events,
                "batches_processed": batches_processed,
                "segment_id": segment_id,
                "cost_estimate": cost_estimate
            }
        except Exception as e:
            worker_id = get_worker_id()
            logger.error(f"❌ {worker_id} {mode_desc} 區間 {segment_id} 失敗: {e}")
            return {
                "status": "error",
                "total_events": 0,
                "synced_events": 0,
                "error_events": 0,
                "batches_processed": 0,
                "segment_id": segment_id
            }

    def _sync_segment_economical(self, seg_pair, dry_run: bool = False):
        """經濟模式：使用 polars 本地處理 + 批次寫入"""
        seg, next_seg = seg_pair
        segment_id = f"{seg.strftime('%H:%M')}-{next_seg.strftime('%H:%M')}"
        mode_desc = "[Dry-run]" if dry_run else "[Economical]"

        try:
            worker_id = get_worker_id()
            logger.info(f"🔎 {worker_id} {mode_desc} 處理區間 {segment_id}: {seg.strftime('%Y-%m-%d %H:%M:%S')} ~ {next_seg.strftime('%Y-%m-%d %H:%M:%S')}")

            # 1. 查詢資料，添加事件過濾條件（與main.py的get_events_to_sync保持一致）
            # 建構事件類型過濾條件
            event_filter_condition = ""
            if self.excluded_event_types:
                excluded_list = [f"'{event_type}'" for event_type in self.excluded_event_types]
                excluded_str = ", ".join(excluded_list)
                event_filter_condition = f" AND event.name NOT IN ({excluded_str})"
                logger.info(f"🎛️ {mode_desc} 過濾事件類型: {self.excluded_event_types}")

            # 🎯 欄位選擇優化：只選取轉換所需的核心欄位，預期節省 70% 掃描成本 (27GB → 8.6GB)
            query = f"""
                SELECT
                    permanent,
                    ec_id,
                    event_time,
                    link,
                    event.name as event_name,
                    event.value as event_value,
                    event.currency as event_currency,
                    event.custom_data.order_id as order_id,
                    event.items as event_items,
                    user.em as user_em,
                    user.ph as user_ph,
                    location.country_code as location_country_code,
                    location.region_name as location_region_name,
                    location.city_name as location_city_name
                FROM `{self.source_table}`
                WHERE event_time >= @start_time AND event_time < @end_time{event_filter_condition}
                ORDER BY event_time
            """

            # 🚨 動態成本限制：根據時間範圍調整
            time_diff_minutes = (next_seg - seg).total_seconds() / 60
            if time_diff_minutes <= 60:  # ≤1小時
                max_bytes = 50 * 1024**3  # 50GB (SELECT * 需要)
            elif time_diff_minutes <= 360:  # ≤6小時
                max_bytes = 100 * 1024**3  # 100GB
            else:
                max_bytes = 200 * 1024**3  # 200GB

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", seg),
                    bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", next_seg),
                ],
                use_query_cache=True,  # 啟用查詢快取以節省成本
                maximum_bytes_billed=max_bytes  # 動態成本限制
            )

            # 執行查詢
            query_job = self.bq_client.query(query, job_config=job_config)
            results = query_job.result()

            # 轉換為 polars DataFrame
            # 🎯 先轉換為字典列表，然後使用明確的 schema
            raw_data = [dict(row) for row in results]

            if raw_data:
                # 使用寬鬆的 schema 推斷，避免類型衝突
                try:
                    df = pl.DataFrame(raw_data, infer_schema_length=len(raw_data))
                except Exception as e:
                    logger.warning(f"⚠️ polars DataFrame 建立失敗，使用預設推斷: {e}")
                    # 檢查問題值
                    if "07258924" in str(e):
                        logger.error(f"🎯 發現問題值 '07258924' 在原始 BigQuery 資料中")
                        for i, row_data in enumerate(raw_data[:5]):  # 檢查前5筆
                            for field_name, value in row_data.items():
                                if str(value) == "07258924":
                                    logger.error(f"   問題值位於第 {i+1} 筆資料的 '{field_name}' 欄位")
                                    logger.error(f"   值: {value} (類型: {type(value).__name__})")

                    # 回退到預設推斷
                    df = pl.DataFrame(raw_data)
            else:
                df = pl.DataFrame()
            total_events = len(df)

            if total_events == 0:
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: 0 筆事件")
                return {
                    "status": "success",
                    "total_events": 0,
                    "synced_events": 0,
                    "error_events": 0,
                    "batches_processed": 0,
                    "segment_id": segment_id
                }

            logger.info(f"📊 {mode_desc} 查詢到 {total_events:,} 筆事件")

            if dry_run:
                # Dry-run 模式：只計算費用
                cost_estimate = self._estimate_query_cost(query_job)
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: {total_events:,} 筆事件 (${cost_estimate['estimated_cost_usd']:.4f} USD)")
                return {
                    "status": "success",
                    "total_events": total_events,
                    "synced_events": 0,
                    "error_events": 0,
                    "batches_processed": 0,
                    "segment_id": segment_id,
                    "cost_estimate": cost_estimate
                }

            # 2. 本地資料處理
            logger.info(f"🔄 {mode_desc} 開始本地資料處理...")

            # 資料轉換（模擬 LegacyEventSyncProcessor 的邏輯）
            processed_df = self._process_events_locally(df)

            # 3. 批次寫入
            logger.info(f"📝 {mode_desc} 開始批次寫入...")
            batches_processed, synced_events, error_events = self._batch_write_to_bigquery(
                processed_df, dry_run
            )

            # 計算查詢成本
            cost_estimate = self._estimate_query_cost(query_job)

            if cost_estimate and not cost_estimate.get("error"):
                cost_usd = cost_estimate.get("estimated_cost_usd", 0)
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: {total_events:,} 筆事件，成功 {synced_events:,} 筆 (${cost_usd:.4f} USD)")
            else:
                logger.info(f"✅ {worker_id} {mode_desc} [分段完成] 區間 {segment_id}: {total_events:,} 筆事件，成功 {synced_events:,} 筆")

            return {
                "status": "success" if error_events == 0 else "partial_success",
                "total_events": total_events,
                "synced_events": synced_events,
                "error_events": error_events,
                "batches_processed": batches_processed,
                "segment_id": segment_id,
                "cost_estimate": cost_estimate
            }

        except Exception as e:
            worker_id = get_worker_id()
            logger.error(f"❌ {worker_id} {mode_desc} 區間 {segment_id} 失敗: {e}")
            return {
                "status": "error",
                "total_events": 0,
                "synced_events": 0,
                "error_events": 0,
                "batches_processed": 0,
                "segment_id": segment_id
            }

    def _process_events_locally(self, df: pl.DataFrame) -> pl.DataFrame:
        """本地處理事件資料"""
        try:
            # 直接使用 polars 處理，避免 pandas 轉換問題
            logger.info(f"🔄 本地處理 {len(df):,} 筆事件...")

            # 建立處理器實例以使用轉換邏輯
            if not hasattr(self, '_temp_processor'):
                from main import LegacyEventSyncProcessor
                self._temp_processor = LegacyEventSyncProcessor(
                    source_table=self.source_table,
                    target_table=self.target_table,
                    task_queue_name=None,
                    worker_url=None,
                    enable_deduplication=self.enable_deduplication
                )

            # 逐行處理事件並轉換
            transformed_rows = []
            for i, row_data in enumerate(df.iter_rows(named=True)):
                try:
                    # 建立模擬 BigQuery Row 的物件
                    class MockRow:
                        def __init__(self, data):
                            self._data = data
                        def get(self, key, default=None):
                            return self._data.get(key, default)

                    # 🔍 Debug: 檢查轉換前的原始資料
                    if i < 3:  # 只檢查前3筆避免日誌過多
                        logger.debug(f"   - 第{i+1}筆 row_data 結構: {list(row_data.keys())}")
                        logger.debug(f"   - 第{i+1}筆 event: {row_data.get('event')} (type: {type(row_data.get('event'))})")
                        event_data = row_data.get('event', {})
                        if event_data and isinstance(event_data, dict):
                            items_data = event_data.get('items', [])
                            logger.debug(f"   - 轉換前第{i+1}筆 items: {items_data} (type: {type(items_data)})")
                        elif isinstance(event_data, str):
                            logger.debug(f"   - 轉換前第{i+1}筆 event 是字串，長度: {len(event_data)}")

                    mock_row = MockRow(row_data)

                    # 使用現有的 transform_event_data 方法
                    transformed_row = self._temp_processor.transform_event_data(mock_row)

                    # 🔍 Debug: 檢查轉換後的資料
                    if i < 3:  # 只檢查前3筆避免日誌過多
                        transformed_items = transformed_row.get('items', [])
                        logger.debug(f"   - 轉換後第{i+1}筆 items: {transformed_items} (type: {type(transformed_items)})")

                    transformed_rows.append(transformed_row)

                except Exception as e:
                    logger.debug(f"⚠️  轉換單筆事件失敗: {e}")
                    continue

            if not transformed_rows:
                logger.warning("⚠️  沒有成功轉換的事件")
                return pl.DataFrame()

            logger.info(f"✅ 成功轉換 {len(transformed_rows):,} 筆事件")

            # 🔍 Debug: 檢查轉換後的資料中 items 欄位
            if transformed_rows:
                first_row = transformed_rows[0]
                logger.debug(f"   - 第一筆轉換後資料 items: {first_row.get('items', [])} (type: {type(first_row.get('items', []))})")
                if first_row.get('items'):
                    logger.debug(f"   - items 內容: {first_row['items']}")

            # 直接用轉換後的資料建立 polars DataFrame
            # 🎯 使用明確的 schema 定義以避免類型推斷錯誤
            schema = {
                "permanent": pl.Utf8,  # 強制為字串，避免數字/字串混合問題
                "ec_id": pl.Int64,
                "partner_source": pl.Utf8,
                "event_time": pl.Utf8,
                "create_time": pl.Utf8,
                "link": pl.Utf8,
                "event": pl.Utf8,
                "value": pl.Float64,
                "currency": pl.Utf8,
                "order_id": pl.Utf8,
                "items": pl.Object,  # 複雜物件類型
                "user": pl.Object,   # 複雜物件類型
                "partner_id": pl.Utf8,
                "page": pl.Utf8,
                "location": pl.Object,  # 複雜物件類型
                "raw_json": pl.Utf8
            }

            try:
                processed_df = pl.DataFrame(transformed_rows, schema=schema)
                logger.debug(f"✅ 使用明確 schema 成功建立 DataFrame")
            except Exception as e:
                logger.error(f"❌ 明確 schema 失敗: {e}")
                logger.error(f"   錯誤詳情: {type(e).__name__}: {str(e)}")

                # 🔍 詳細分析資料類型問題
                if transformed_rows:
                    logger.error(f"   總共 {len(transformed_rows)} 筆資料")

                    # 檢查前幾筆資料的類型
                    for i, row in enumerate(transformed_rows[:3]):
                        logger.error(f"   第 {i+1} 筆資料:")
                        for field_name, expected_type in schema.items():
                            actual_value = row.get(field_name)
                            actual_type = type(actual_value).__name__
                            logger.error(f"     {field_name}: {actual_value} (類型: {actual_type}, 期望: {expected_type})")

                    # 檢查是否有特定值 "07258924"
                    problem_value = "07258924"
                    for i, row in enumerate(transformed_rows):
                        for field_name, value in row.items():
                            if str(value) == problem_value:
                                logger.error(f"   🎯 找到問題值 '{problem_value}' 在第 {i+1} 筆資料的 '{field_name}' 欄位")
                                logger.error(f"       值類型: {type(value).__name__}")
                                logger.error(f"       期望類型: {schema.get(field_name, 'Unknown')}")

                # 回退方案：使用完整長度推斷
                try:
                    processed_df = pl.DataFrame(
                        transformed_rows,
                        infer_schema_length=len(transformed_rows)  # 使用完整長度推斷
                    )
                    logger.debug(f"✅ 回退方案成功")
                except Exception as e2:
                    logger.error(f"❌ 回退方案也失敗: {e2}")
                    # 最後的回退：不指定 schema
                    processed_df = pl.DataFrame(transformed_rows)

            # 🔍 Debug: 檢查 DataFrame 建立後的 items 欄位
            if len(processed_df) > 0:
                first_row_after = processed_df.row(0, named=True)
                logger.debug(f"   - DataFrame 第一筆 items: {first_row_after.get('items', [])} (type: {type(first_row_after.get('items', []))})")

            # 去重複邏輯（如果啟用）
            if self.enable_deduplication:
                # 使用轉換後的欄位進行去重
                processed_df = processed_df.unique(subset=["permanent", "event_time"], maintain_order=False)
                logger.info(f"🔄 去重複後剩餘 {len(processed_df):,} 筆事件")

            return processed_df

        except Exception as e:
            logger.error(f"❌ 本地資料處理失敗: {e}")
            raise

    def _batch_write_to_bigquery(self, df: pl.DataFrame, dry_run: bool = False):
        """批次寫入 BigQuery"""
        try:
            total_rows = len(df)
            batch_size = self.batch_size
            batches_processed = 0
            synced_events = 0
            error_events = 0

            # 分批處理
            for i in range(0, total_rows, batch_size):
                batch_df = df.slice(i, batch_size)
                batches_processed += 1

                if dry_run:
                    # Dry-run 模式：只計算數量
                    synced_events += len(batch_df)
                    continue

                try:
                    # 💡 經濟模式核心：本地 polars 處理 + 簡化寫入
                    # 重點是避免 BigQuery MERGE 操作，降低成本

                    # 先將 polars DataFrame 清理為簡單格式
                    logger.debug(f"📊 批次 {batches_processed} 經濟模式處理:")
                    logger.debug(f"   - 原始 polars 形狀: {batch_df.shape}")

                    # 清理 polars DataFrame 中的複雜類型
                    clean_df = batch_df.clone()

                    # 💡 修正 items 欄位處理 - 確保與快速模式一致
                    if 'items' in clean_df.columns:
                        def fix_items_field(x):
                            # 如果已經是正確的 list，直接返回
                            if isinstance(x, list):
                                return x
                            # 如果是 numpy array，轉為 list
                            elif hasattr(x, 'tolist'):
                                try:
                                    return x.tolist() if len(x) > 0 else []
                                except:
                                    return []
                            # 如果是 None 或其他類型，返回空 list
                            else:
                                return []

                        clean_df = clean_df.with_columns([
                            pl.col('items').map_elements(fix_items_field, return_dtype=pl.Object)
                        ])
                        logger.debug("   - 已修正 items 欄位處理邏輯（保持與快速模式一致）")

                    # 確保結構化欄位有正確的預設值
                    for col in ['user', 'location', 'page', 'raw_json']:
                        if col in clean_df.columns:
                            clean_df = clean_df.with_columns([
                                pl.col(col).map_elements(
                                    lambda x: x if isinstance(x, dict) else None,
                                    return_dtype=pl.Object
                                )
                            ])

                    # 💡 真正的經濟模式：polars → JSON 字典，避免 pandas 轉換問題
                    logger.debug(f"📊 批次 {batches_processed} 經濟模式（純 polars）:")
                    logger.debug(f"   - polars DataFrame 形狀: {clean_df.shape}")
                    logger.debug(f"   - 欄位數量: {len(clean_df.columns)}")
                    logger.debug(f"   - 欄位順序: {list(clean_df.columns)}")

                    # 直接從 polars 轉為 Python 字典列表
                    batch_records = clean_df.to_dicts()

                    # 檢查轉換後的資料格式
                    if batch_records:
                        first_record = batch_records[0]
                        logger.debug(f"   - 第一筆記錄欄位: {list(first_record.keys())}")

                        # 檢查關鍵欄位的資料類型
                        for key in ['partner_id', 'ec_id', 'event_time', 'items']:
                            if key in first_record:
                                val = first_record[key]
                                logger.debug(f"   - {key}: {val} (type: {type(val)})")

                        # 🔍 特別檢查 items 欄位內容
                        items_val = first_record.get('items', [])
                        if items_val and len(items_val) > 0:
                            logger.debug(f"   - items 詳細內容: {items_val}")
                        elif items_val == []:
                            logger.debug(f"   - ⚠️ items 為空陣列")
                        else:
                            logger.debug(f"   - items 為 None 或其他: {items_val}")

                    logger.debug(f"   - 轉換為 {len(batch_records)} 筆 JSON 記錄")

                    # 💰 純經濟模式：直接 JSON 寫入，避免所有 pandas/Arrow 轉換問題

                    # 💰 經濟模式 + 冪等性保證：先檢查重複，再寫入新記錄（可選）
                    if self.enable_idempotency_check:
                        logger.debug(f"🔍 批次 {batches_processed}: 執行冪等性檢查")
                        new_records = self._filter_duplicate_records(batch_records)
                        logger.debug(f"📊 批次 {batches_processed}: 冪等性檢查完成，{len(batch_records)} → {len(new_records)} 筆")
                    else:
                        logger.debug(f"⚡ 批次 {batches_processed}: 跳過冪等性檢查（成本優化模式）")
                        new_records = batch_records

                    if not new_records:
                        logger.debug(f"📝 批次 {batches_processed}: 所有記錄已存在，跳過寫入")
                        # 🐛 修復：沒有新記錄時，synced_events 不應該增加
                        # synced_events += 0  # 冪等性：沒有新記錄同步
                    else:
                        table_ref = self.bq_client.get_table(self.target_table)
                        errors = self.bq_client.insert_rows_json(table_ref, new_records)

                        if errors:
                            logger.error(f"❌ 批次 {batches_processed} BigQuery JSON 寫入錯誤:")
                            for error in errors[:3]:  # 只顯示前 3 個錯誤
                                logger.error(f"   - {error}")
                            error_events += len(new_records)  # 🐛 修復：錯誤的應該是新記錄數，不是總批次數
                        else:
                            synced_events += len(new_records)  # 🐛 修復：成功的應該是新記錄數，不是總批次數
                            skipped_count = len(batch_records) - len(new_records)
                            logger.debug(f"📝 批次 {batches_processed}: 純經濟模式成功寫入 {len(new_records):,} 筆，跳過重複 {skipped_count} 筆 (JSON)")

                except Exception as e:
                    logger.error(f"❌ 批次 {batches_processed} 寫入失敗: {e}")
                    logger.error(f"   - 錯誤類型: {type(e).__name__}")
                    if hasattr(e, 'args') and e.args:
                        logger.error(f"   - 詳細信息: {e.args[0]}")
                    error_events += len(batch_df)  # 💡 這裡保持原樣，因為是整個批次失敗

            return batches_processed, synced_events, error_events

        except Exception as e:
            logger.error(f"❌ 批次寫入失敗: {e}")
            raise

    def _filter_duplicate_records(self, records: list) -> list:
        """
        過濾已存在的重複記錄，實現與快速模式相同的冪等性邏輯

        使用與 MERGE 操作相同的 8 欄位複合主鍵：
        - permanent, ec_id, event_time, event
        - partner_source, link, value, currency, order_id
        """
        if not records:
            return []

        try:
            logger.debug(f"🔍 開始冪等性檢查，輸入 {len(records)} 筆記錄")

            # 🔧 改進：統一時間格式處理函數
            def normalize_event_time(event_time_value):
                """統一時間格式處理，確保一致性"""
                if event_time_value is None:
                    return None

                try:
                    event_time_str = str(event_time_value)
                    # 移除時區信息
                    if '+' in event_time_str:
                        event_time_str = event_time_str.split('+')[0]
                    # 統一格式：移除 T 分隔符，確保使用空格
                    event_time_str = event_time_str.replace('T', ' ')
                    # 去掉微秒部分，只保留到秒
                    if '.' in event_time_str:
                        event_time_str = event_time_str.split('.')[0]
                    return event_time_str
                except Exception as e:
                    logger.warning(f"時間格式處理異常: {event_time_value}, 錯誤: {e}")
                    return str(event_time_value) if event_time_value is not None else None

            # 🔧 改進：統一key生成函數
            def generate_record_key(record_data):
                """生成統一的記錄key"""
                link_key = record_data.get("link") or ""
                value_key = record_data.get("value")
                event_time_normalized = normalize_event_time(record_data.get("event_time"))

                return (
                    record_data.get("permanent"),
                    record_data.get("ec_id"),
                    event_time_normalized,
                    record_data.get("event"),
                    record_data.get("partner_source"),
                    link_key,
                    value_key,
                    record_data.get("currency"),
                    record_data.get("order_id")
                )

            # 建立輸入記錄的 key 映射
            input_keys = {}
            for i, record in enumerate(records):
                try:
                    record_key = generate_record_key(record)
                    input_keys[record_key] = record

                    # Debug: 顯示前3筆記錄的 key
                    if i < 3:
                        logger.debug(f"   - 輸入記錄 {i+1} key: {record_key}")
                except Exception as e:
                    logger.warning(f"生成輸入記錄 key 失敗: {record}, 錯誤: {e}")
                    continue

            # 🔧 改進：擴大查詢範圍，確保不遺漏記錄
            first_record = records[0]
            base_time = normalize_event_time(first_record.get("event_time"))
            if not base_time:
                logger.warning("無法獲取基準時間，跳過冪等性檢查")
                return records

            # 查詢範圍擴大到10秒，確保覆蓋所有可能的記錄
            check_query = f"""
            SELECT permanent, ec_id, event_time, event, partner_source,
                   IFNULL(link, '') as link, value, currency, order_id
            FROM `{self.target_table}`
            WHERE partner_source = 'legacy-tagtoo-event'
              AND event_time >= TIMESTAMP_SUB(TIMESTAMP('{base_time}'), INTERVAL 5 SECOND)
              AND event_time <= TIMESTAMP_ADD(TIMESTAMP('{base_time}'), INTERVAL 5 SECOND)
            """

            logger.debug(f"🔍 查詢已存在記錄，時間範圍: {base_time} ± 5秒")
            existing_results = list(self.bq_client.query(check_query).result())
            logger.debug(f"🔍 查詢到 {len(existing_results)} 筆已存在記錄")

            # 建立已存在記錄的 key set
            existing_keys = set()
            for i, row in enumerate(existing_results):
                try:
                    existing_key = generate_record_key(row)
                    existing_keys.add(existing_key)

                    # Debug: 顯示前3筆已存在記錄的 key
                    if i < 3:
                        logger.debug(f"   - 已存在記錄 {i+1} key: {existing_key}")
                except Exception as e:
                    logger.warning(f"生成已存在記錄 key 失敗: {dict(row)}, 錯誤: {e}")
                    continue

            # 過濾出新記錄
            new_records = []
            duplicate_count = 0

            for record_key, record in input_keys.items():
                if record_key not in existing_keys:
                    new_records.append(record)
                else:
                    duplicate_count += 1
                    logger.debug(f"   - 發現重複記錄: {record_key}")

            logger.debug(f"✅ 冪等性檢查完成：{len(records)} 筆輸入，{duplicate_count} 筆重複，{len(new_records)} 筆新記錄")
            logger.debug(f"   - 重複率: {(duplicate_count / len(records) * 100):.1f}%")

            return new_records

        except Exception as e:
            logger.error(f"❌ 冪等性檢查失敗: {e}")
            logger.error(f"   - 錯誤類型: {type(e).__name__}")
            import traceback
            logger.error(f"   - 詳細堆疊: {traceback.format_exc()}")
            logger.warning("使用降級策略：跳過重複檢查，直接寫入所有記錄")
            # 降級策略：如果檢查失敗，返回所有記錄（保持原有行為）
            return records

    def _estimate_query_cost(self, query_job) -> Dict[str, Any]:
        """估算查詢成本"""
        try:
            # 取得查詢統計資訊
            bytes_processed = query_job.total_bytes_processed or 0
            tb_processed = bytes_processed / (1024**4) if bytes_processed else 0
            estimated_cost_usd = tb_processed * 6.25  # $6.25 per TiB (官方定價)

            return {
                "bytes_processed": bytes_processed,
                "tb_processed": tb_processed,
                "estimated_cost_usd": estimated_cost_usd
            }
        except Exception as e:
            logger.warning(f"⚠️  查詢成本估算失敗: {e}")
            return {
                "bytes_processed": 0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "error": str(e)
            }

    def _estimate_segment_cost(self, start_utc: datetime, end_utc: datetime) -> Dict[str, Any]:
        """
        估算單一分段的 BigQuery 查詢費用

        Args:
            start_utc: 分段開始時間 (UTC)
            end_utc: 分段結束時間 (UTC)

        Returns:
            費用估算資訊字典
        """
        try:
            # 📊 建立查詢設定（與實際查詢相同）
            query = f"""
                SELECT *
                FROM `{self.source_table}`
                WHERE event_time >= @start_time AND event_time < @end_time
                ORDER BY event_time
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_utc),
                    bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_utc),
                ],
                use_query_cache=True,  # 啟用查詢快取以節省成本
                maximum_bytes_billed=1024**3  # 1GB 查詢限制，防止意外高成本
            )

            # 🧮 呼叫處理器的費用估算功能
            cost_info = self.processor.estimate_query_cost(query, job_config)

            return cost_info

        except Exception as e:
            logger.warning(f"⚠️  分段費用估算失敗: {e}")
            return {
                "bytes_processed": 0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "error": str(e)
            }

    def _display_sync_summary(self, result: Dict[str, Any], dry_run: bool, start_time_str: str = None, end_time_str: str = None):
        """
        顯示同步結果摘要

        Args:
            result: 同步結果字典
            dry_run: 是否為測試模式
            start_time_str: 開始時間字串（台灣時間）
            end_time_str: 結束時間字串（台灣時間）
        """
        status = result.get("status", "unknown")
        total_events = result.get("total_events", 0)
        synced_events = result.get("synced_events", 0)
        error_events = result.get("error_events", 0)
        batches = result.get("batches_processed", 0)

        logger.info("=" * 60)

        if dry_run:
            logger.info("🧪 Dry-run 模式完成")
        else:
            logger.info("🔄 手動同步完成")

        # 顯示時間範圍
        if start_time_str and end_time_str:
            logger.info(f"⏰ 處理時段: {start_time_str} ~ {end_time_str} (台灣時間)")

        if dry_run:
            logger.info(f"📊 查詢到事件總數: {total_events:,} 筆")
            logger.info(f"📦 批次數量: {batches:,} 個")
            logger.info("💡 如需實際同步，請移除 --dry_run 參數")
        else:
            logger.info(f"📊 處理事件總數: {total_events:,} 筆")
            logger.info(f"✅ 成功同步: {synced_events:,} 筆")
            if error_events > 0:
                logger.info(f"❌ 錯誤事件: {error_events:,} 筆")
            logger.info(f"📦 批次數量: {batches:,} 個")

            # 🎯 狀態說明
            if status == "success":
                logger.info("🎉 同步狀態: 完全成功")
            elif status == "partial_success":
                logger.info("⚠️  同步狀態: 部分成功 (有錯誤事件)")
            elif status == "error":
                logger.info("❌ 同步狀態: 失敗")

        # 💰 費用資訊
        cost_estimate = result.get("cost_estimate")
        if cost_estimate and not cost_estimate.get("error"):
            logger.info(f"💰 BigQuery 費用: ${cost_estimate['estimated_cost_usd']:.4f} USD ({cost_estimate['tb_processed']:.6f} TB)")
            bytes_processed = cost_estimate.get('bytes_processed', 0)
            gb_processed = bytes_processed / (1024**3) if bytes_processed else 0
            logger.info(f"   📊 查詢掃描量: {bytes_processed:,} bytes ({gb_processed:.3f} GB)")

            # 顯示成本等級
            cost_usd = cost_estimate['estimated_cost_usd']
            if cost_usd < 0.01:
                cost_level = "🟢 極低成本"
            elif cost_usd < 0.1:
                cost_level = "🟡 低成本"
            elif cost_usd < 1.0:
                cost_level = "🟠 中等成本"
            else:
                cost_level = "🔴 高成本"
            logger.info(f"   📈 成本等級: {cost_level}")
        else:
            logger.info("💰 BigQuery 費用: 無法估算")

        # 📊 詳細成本追踪摘要 (僅在 verbose mode 顯示)
        if hasattr(self.processor, 'cost_tracker') and self.processor.cost_tracker:
            cost_summary = self.processor.cost_tracker.get_summary()
            if cost_summary:
                logger.info("📊 詳細成本追踪摘要:")
                logger.info(f"   總成本: ${cost_summary['cost_summary']['total_cost_usd']:.4f} USD")
                logger.info(f"   總掃描量: {cost_summary['cost_summary']['total_tb_processed']:.6f} TB")
                logger.info(f"   總執行時間: {cost_summary['cost_summary']['total_execution_time_seconds']:.2f} 秒")
                logger.info(f"   操作次數: {cost_summary['session_info']['total_operations']} 次")

                # 顯示 MERGE 操作統計
                merge_stats = cost_summary['cost_breakdown']['by_operation'].get('merge_operation', {})
                if merge_stats:
                    logger.info("🔗 MERGE 操作統計:")
                    logger.info(f"   MERGE 次數: {merge_stats['count']} 次")
                    logger.info(f"   MERGE 總成本: ${merge_stats['total_cost_usd']:.4f} USD")
                    logger.info(f"   MERGE 平均成本: ${merge_stats['avg_cost_usd']:.4f} USD")
                    logger.info(f"   MERGE 總執行時間: {merge_stats['total_execution_time']:.2f} 秒")

                # 顯示查詢操作統計
                query_stats = cost_summary['cost_breakdown']['by_operation'].get('get_events_to_sync', {})
                if query_stats:
                    logger.info("🔍 查詢操作統計:")
                    logger.info(f"   查詢次數: {query_stats['count']} 次")
                    logger.info(f"   查詢總成本: ${query_stats['total_cost_usd']:.4f} USD")
                    logger.info(f"   查詢平均成本: ${query_stats['avg_cost_usd']:.4f} USD")
                    logger.info(f"   查詢總執行時間: {query_stats['total_execution_time']:.2f} 秒")

        logger.info("=" * 60)

    def _update_dashboard_after_sync(self,
                                   sync_result: Dict[str, Any],
                                   start_time_str: str,
                                   end_time_str: str,
                                   dry_run: bool,
                                   total_segments: int):
        """
        同步完成後更新儀表板

        Args:
            sync_result: 同步結果
            start_time_str: 開始時間字串（台灣時間）
            end_time_str: 結束時間字串（台灣時間）
            dry_run: 是否為測試模式
            total_segments: 總分段數
        """
        # 🧪 Dry-run 模式時跳過儀表板更新
        if dry_run:
            logger.info("🧪 Dry-run 模式：跳過儀表板更新")
            return

        if not self._enable_dashboard or not self.dashboard_uploader:
            return

        try:
            # 準備詳細的儀表板資料
            dashboard_data = {
                **sync_result,
                "sync_mode": self.sync_mode,
                "start_time_str": start_time_str,
                "end_time_str": end_time_str,
                "dry_run": dry_run,
                "execution_time_seconds": 0,  # 可以從實際執行時間計算
                "total_segments": total_segments,
                "current_segment": total_segments,  # 手動同步完成時即為最後一段
                "config": {
                    "source_table": self.source_table,
                    "target_table": self.target_table,
                    "batch_size": self.batch_size,
                    "split_interval_minutes": self.split_interval_minutes,
                    "enable_deduplication": self.enable_deduplication,
                    "max_workers": 4,  # 手動同步的預設值
                    "memory_limit_gb": self.memory_limit_gb
                }
            }

            # 上傳儀表板
            dashboard_url = self.dashboard_uploader.upload_dashboard(dashboard_data)
            logger.info(f"📊 儀表板已更新：{dashboard_url}")

        except Exception as e:
            # 儀表板更新失敗不應影響主要功能
            logger.warning(f"⚠️ 儀表板更新失敗: {e}")


def main():
    """主程式入口點"""

    parser = argparse.ArgumentParser(
        description="Legacy Event Sync - 手動同步腳本 (重構版)",
        epilog="""
範例用法:
  # 一般同步
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 23:59:59"

  # 測試模式 (不寫入，自動進行費用估算)
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --dry_run

注意: 所有時間參數均以 Asia/Taipei (UTC+8) 時區為準。
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        '--split-interval-minutes',
        type=int,
        default=60,
        help="每次查詢的切分區間（分鐘），預設 60 (每小時分段)。建議 15~60 視資料量調整"
    )

    parser.add_argument(
        '--start',
        required=True,
        help="開始時間 (Asia/Taipei)，格式: 'YYYY-MM-DD HH:MM:SS'"
    )
    parser.add_argument(
        '--end',
        required=True,
        help="結束時間 (Asia/Taipei)，格式: 'YYYY-MM-DD HH:MM:SS'"
    )
    parser.add_argument(
        '--batch_size',
        type=int,
        default=BATCH_SIZE,  # 使用環境變數 BATCH_SIZE 或預設值 5000
        help=f"每批次處理的事件數量 (預設: {BATCH_SIZE}，可測試不同值：5000/7500/10000)"
    )
    parser.add_argument(
        '--no-auto-patch-legacy',
        action='store_true',
        help="停用自動 legacy event 表格修正（預設啟用，僅限進階用途）"
    )
    parser.add_argument(
        '--project_id',
        default=PROJECT_ID,
        help=f"GCP 專案 ID (預設: {PROJECT_ID})"
    )
    parser.add_argument(
        '--source_table',
        default=SOURCE_TABLE_DEFAULT,
        help=f"來源資料表 (預設: {SOURCE_TABLE_DEFAULT})"
    )
    parser.add_argument(
        '--production',
        action='store_true',
        help='寫入正式表 event_prod.integrated_event（預設為 event_test.integrated_event）'
    )
    parser.add_argument(
        '--target_table',
        default=None,
        help="目標資料表（預設依 --production 決定，除非手動指定）"
    )
    parser.add_argument(
        '--dry_run',
        action='store_true',
        help="測試模式：僅查詢資料與顯示內容，不實際寫入"
    )
    parser.add_argument(
        '--no-estimate-cost',
        action='store_true',
        help="停用 BigQuery 查詢費用估算 (預設啟用)"
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help="顯示詳細日誌訊息"
    )
    parser.add_argument(
        '--enable_deduplication',
        action='store_true',
        help="🎛️ 啟用去重複邏輯 (預設停用去重複)"
    )
    parser.add_argument(
        '--mode',
        choices=['economical', 'normal', 'fast'],
        default='economical',
        help="🔄 同步模式：economical (依序處理，成本低)、normal (平行處理，成本低) 或 fast (平行+MERGE，成本高)"
    )

    # 🔧 從環境變數計算記憶體限制 (MEMORY_MB → GB)
    default_memory_gb = int(os.environ.get("MEMORY_MB", "8192")) // 1024

    parser.add_argument(
        '--memory-limit-gb',
        type=int,
        default=default_memory_gb,
        help=f"💾 記憶體限制 GB (預設: {default_memory_gb}GB，從環境變數 MEMORY_MB 自動計算)"
    )

    parser.add_argument(
        '--max-workers',
        type=int,
        default=None,
        help="🚀 平行處理的最大 worker 數量 (預設: 從環境變數 MAX_CONCURRENT_SEGMENTS 或 CPU 數量自動決定)"
    )

    args = parser.parse_args()

    # ⚠️ 模式說明與警告
    if args.mode == "fast":
        logger.warning("⚠️  您選擇了快速模式，成本較高！")
        logger.warning("   1小時資料約需 $11.26 USD")
        logger.warning("   1個月資料約需 $8,111 USD")
        logger.warning("   建議使用 normal 模式來平衡速度與成本")
    elif args.mode == "normal":
        logger.info("🚀 您選擇了 Normal 模式：平行處理 + 低成本")
        logger.info("   效能：比 economical 快 40-60%")
        logger.info("   成本：與 economical 相同，約 $0.055/小時")
        logger.info("   適合：大量資料處理的最佳性價比選擇")

    # 🔧 設定日誌等級
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 🏗️ 建構完整表格路徑
    source_table_full = args.source_table
    if args.target_table:
        target_table_full = args.target_table
        logger.info(f"⚠️  已手動指定 target_table，將寫入 {target_table_full}")
    else:
        if args.production:
            target_table_full = f"{args.project_id}.event_prod.integrated_event"
            logger.info("⚠️  已啟用 --production，將寫入正式表 event_prod.integrated_event")
        else:
            target_table_full = f"{args.project_id}.event_test.integrated_event"
            logger.info("（預設）將寫入測試表 event_test.integrated_event。如需寫入正式表請加上 --production 參數")

    # 🧪 Dry-run 模式時停用儀表板更新
    if args.dry_run:
        os.environ["ENABLE_DASHBOARD"] = "false"
        logger.info("🧪 Dry-run 模式：已停用儀表板更新")

    # 🚀 Normal 模式自動優化設定
    if args.mode == "normal":
        os.environ["USE_POLARS"] = "true"          # 啟用 polars 模式
        os.environ["ENABLE_DASHBOARD"] = "false"   # 停用儀表板以提升效能
        logger.info("🚀 Normal 模式設定：平行處理 + Polars 去重複")
        logger.info("   環境變數：USE_POLARS=true, ENABLE_DASHBOARD=false")
    elif args.mode == "fast":
        os.environ["USE_POLARS"] = "false"         # 停用 polars，使用 BigQuery MERGE
        logger.info("🔴 Fast 模式設定：平行處理 + BigQuery MERGE 去重複")
        logger.info("   環境變數：USE_POLARS=false")
    elif args.mode == "economical":
        logger.info("🟢 Economical 模式設定：依序處理 + Polars 去重複")

    # 🚀 建立並執行同步器
    import time
    try:
        # 🔄 模式轉換：normal 和 fast 都需要平行處理能力
        internal_sync_mode = "fast" if args.mode in ["normal", "fast"] else "economical"

        # 🔧 調試輸出：確認模式轉換
        logger.info(f"🔧 模式轉換：args.mode='{args.mode}' → internal_sync_mode='{internal_sync_mode}'")

        runner = ManualSyncRunner(
            project_id=args.project_id,
            source_table=source_table_full,
            target_table=target_table_full,
            batch_size=args.batch_size,
            enable_deduplication=args.enable_deduplication,  # 🎛️ Feature Toggle
            auto_patch_legacy=not args.no_auto_patch_legacy,
            split_interval_minutes=args.split_interval_minutes,
            sync_mode=internal_sync_mode,
            memory_limit_gb=args.memory_limit_gb
        )

        start_time = time.time()
        result = runner.run_sync(
            start_time_str=args.start,
            end_time_str=args.end,
            dry_run=args.dry_run,
            max_workers=args.max_workers
        )
        elapsed = time.time() - start_time
        logger.info(f"⏱️ 同步腳本總執行時間: {elapsed:.2f} 秒 ({elapsed/60:.2f} 分鐘)")

        # 🎉 顯示完成訊息
        if result.get("status") in ["success", "dry_run_success"]:
            logger.info("🎉 同步作業完成！")
            if not args.dry_run:
                logger.info("✅ 資料已成功寫入目標表格")
            else:
                logger.info("🧪 Dry-run 模式完成，未實際寫入資料")
        elif result.get("status") == "partial_success":
            logger.info("⚠️  同步作業部分完成（有錯誤事件）")
        else:
            logger.info("❌ 同步作業失敗")

        # 📊 根據結果設定退出碼
        if result.get("status") in ["success", "dry_run_success"]:
            sys.exit(0)
        elif result.get("status") == "partial_success":
            sys.exit(1)  # 部分成功
        else:
            sys.exit(2)  # 完全失敗

    except KeyboardInterrupt:
        logger.info("\n👋 使用者中斷同步作業")
        sys.exit(130)
    except Exception as e:
        logger.error(f"❌ 程式執行失敗: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
