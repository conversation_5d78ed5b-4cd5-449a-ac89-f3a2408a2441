#!/usr/bin/env python3
"""
並行同步執行器 - 解決多實例衝突問題
支援真正的並行化處理，避免資源競爭
"""

import os
import sys
import logging
import argparse
import multiprocessing as mp
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
import pytz
import uuid
import json
import time

# 添加 src 目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from manual_sync import ManualSyncRunner
except ImportError as e:
    print(f"❌ 無法匯入 ManualSyncRunner: {e}")
    sys.exit(1)

# 日誌設定
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)


class ParallelSyncRunner:
    """並行同步執行器 - 避免資源衝突的設計"""

    def __init__(self,
                 project_id: str = "tagtoo-tracking",
                 source_table: str = None,
                 target_table: str = None,
                 max_parallel_jobs: int = 3,
                 split_interval_minutes: int = 30,
                 sync_mode: str = "economical"):
        """
        初始化並行同步執行器

        Args:
            project_id: GCP 專案 ID
            source_table: 來源表格
            target_table: 目標表格
            max_parallel_jobs: 最大並行任務數
            split_interval_minutes: 時間切分間隔
            sync_mode: 同步模式
        """
        self.project_id = project_id
        self.source_table = source_table or f"{project_id}.event_prod.tagtoo_event"
        self.target_table = target_table or f"{project_id}.event_prod.integrated_event"
        self.max_parallel_jobs = max_parallel_jobs
        self.split_interval_minutes = split_interval_minutes
        self.sync_mode = sync_mode
        self.timezone = pytz.timezone("Asia/Taipei")

        # 為每個並行任務生成唯一 ID
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        logger.info(f"🚀 並行同步執行器已初始化")
        logger.info(f"   📋 會話 ID: {self.session_id}")
        logger.info(f"   🔧 最大並行數: {max_parallel_jobs}")
        logger.info(f"   ⏱️ 切分間隔: {split_interval_minutes} 分鐘")
        logger.info(f"   🎯 同步模式: {sync_mode}")

    def run_parallel_sync(self,
                         time_ranges: List[Tuple[str, str]],
                         dry_run: bool = False) -> Dict[str, Any]:
        """
        執行並行同步

        Args:
            time_ranges: 時間範圍列表 [(start, end), ...]
            dry_run: 是否為測試模式

        Returns:
            同步結果統計
        """
        logger.info(f"🎯 開始並行同步 - {len(time_ranges)} 個時間範圍")

        # 準備任務參數
        tasks = []
        for i, (start_time, end_time) in enumerate(time_ranges):
            task_id = f"{self.session_id}_batch_{i+1:02d}"
            task_params = {
                'task_id': task_id,
                'start_time': start_time,
                'end_time': end_time,
                'project_id': self.project_id,
                'source_table': self.source_table,
                'target_table': self.target_table,
                'split_interval_minutes': self.split_interval_minutes,
                'sync_mode': self.sync_mode,
                'dry_run': dry_run,
                'disable_dashboard': True  # 關鍵：停用 Dashboard 避免衝突
            }
            tasks.append(task_params)

        # 使用進程池執行並行同步
        start_time = time.time()
        results = []

        with mp.Pool(processes=min(self.max_parallel_jobs, len(tasks))) as pool:
            logger.info(f"🔄 啟動 {min(self.max_parallel_jobs, len(tasks))} 個並行進程")

            # 提交所有任務
            async_results = []
            for task in tasks:
                async_result = pool.apply_async(_run_single_sync_task, (task,))
                async_results.append((task['task_id'], async_result))

            # 收集結果
            for task_id, async_result in async_results:
                try:
                    result = async_result.get(timeout=7200)  # 2小時超時
                    result['task_id'] = task_id
                    results.append(result)
                    logger.info(f"✅ 任務 {task_id} 完成")
                except Exception as e:
                    logger.error(f"❌ 任務 {task_id} 失敗: {e}")
                    results.append({
                        'task_id': task_id,
                        'status': 'error',
                        'error': str(e)
                    })

        execution_time = time.time() - start_time

        # 統計總結果
        summary = self._aggregate_results(results, execution_time)

        # 生成統一的 Dashboard（避免衝突）
        if not dry_run:
            self._upload_unified_dashboard(summary)

        return summary

    def _aggregate_results(self, results: List[Dict], execution_time: float) -> Dict[str, Any]:
        """聚合所有任務的結果"""
        total_events = sum(r.get('total_events', 0) for r in results)
        synced_events = sum(r.get('synced_events', 0) for r in results)
        error_events = sum(r.get('error_events', 0) for r in results)
        total_cost = sum(r.get('cost_estimate', {}).get('estimated_cost_usd', 0) for r in results)

        successful_tasks = len([r for r in results if r.get('status') == 'success'])
        failed_tasks = len([r for r in results if r.get('status') == 'error'])

        summary = {
            'session_id': self.session_id,
            'status': 'success' if failed_tasks == 0 else 'partial_success',
            'execution_time_seconds': execution_time,
            'total_tasks': len(results),
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'total_events': total_events,
            'synced_events': synced_events,
            'error_events': error_events,
            'sync_rate': (synced_events / total_events * 100) if total_events > 0 else 0,
            'total_cost_usd': total_cost,
            'task_results': results,
            'config': {
                'max_parallel_jobs': self.max_parallel_jobs,
                'split_interval_minutes': self.split_interval_minutes,
                'sync_mode': self.sync_mode,
                'source_table': self.source_table,
                'target_table': self.target_table
            }
        }

        return summary

    def _upload_unified_dashboard(self, summary: Dict[str, Any]):
        """上傳統一的 Dashboard，避免多實例衝突"""
        try:
            # 這裡可以實作統一的 Dashboard 上傳邏輯
            # 使用 session_id 作為唯一標識符
            logger.info(f"📊 統一 Dashboard 上傳功能待實作")
            logger.info(f"   會話 ID: {summary['session_id']}")
            logger.info(f"   總成本: ${summary['total_cost_usd']:.4f} USD")
            logger.info(f"   同步率: {summary['sync_rate']:.2f}%")
        except Exception as e:
            logger.warning(f"⚠️ Dashboard 上傳失敗: {e}")


def _run_single_sync_task(task_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    執行單一同步任務（在獨立進程中運行）

    Args:
        task_params: 任務參數

    Returns:
        同步結果
    """
    task_id = task_params['task_id']

    try:
        # 設定進程級別的日誌
        logger = logging.getLogger(f"Task-{task_id}")
        logger.info(f"🚀 開始執行任務: {task_params['start_time']} ~ {task_params['end_time']}")

        # 建立 ManualSyncRunner 實例（停用 Dashboard）
        os.environ['ENABLE_DASHBOARD'] = 'false'  # 關鍵：停用 Dashboard

        runner = ManualSyncRunner(
            project_id=task_params['project_id'],
            source_table=task_params['source_table'],
            target_table=task_params['target_table'],
            split_interval_minutes=task_params['split_interval_minutes'],
            sync_mode=task_params['sync_mode']
        )

        # 執行同步
        result = runner.run_sync(
            start_time_str=task_params['start_time'],
            end_time_str=task_params['end_time'],
            dry_run=task_params['dry_run']
        )

        logger.info(f"✅ 任務 {task_id} 完成: {result.get('synced_events', 0)} 筆事件")
        return result

    except Exception as e:
        logger.error(f"❌ 任務 {task_id} 執行失敗: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'total_events': 0,
            'synced_events': 0,
            'error_events': 0
        }


def main():
    """主程式入口"""
    parser = argparse.ArgumentParser(description="並行同步執行器")
    parser.add_argument('--time-ranges', required=True,
                       help='時間範圍 JSON 格式: [["2025-07-01 04:00:00", "2025-07-08 00:00:00"], ...]')
    parser.add_argument('--max-parallel-jobs', type=int, default=3,
                       help='最大並行任務數 (預設: 3)')
    parser.add_argument('--split-interval-minutes', type=int, default=30,
                       help='時間切分間隔分鐘數 (預設: 30)')
    parser.add_argument('--sync-mode', choices=['economical', 'fast'], default='economical',
                       help='同步模式 (預設: economical)')
    parser.add_argument('--production', action='store_true',
                       help='使用生產環境表格')
    parser.add_argument('--dry-run', action='store_true',
                       help='測試模式，不實際寫入')

    args = parser.parse_args()

    # 解析時間範圍
    try:
        time_ranges = json.loads(args.time_ranges)
    except json.JSONDecodeError as e:
        logger.error(f"❌ 時間範圍 JSON 格式錯誤: {e}")
        sys.exit(1)

    # 設定表格
    project_id = "tagtoo-tracking"
    if args.production:
        target_table = f"{project_id}.event_prod.integrated_event"
    else:
        target_table = f"{project_id}.event_test.integrated_event"

    # 建立並行執行器
    runner = ParallelSyncRunner(
        project_id=project_id,
        target_table=target_table,
        max_parallel_jobs=args.max_parallel_jobs,
        split_interval_minutes=args.split_interval_minutes,
        sync_mode=args.sync_mode
    )

    # 執行並行同步
    summary = runner.run_parallel_sync(time_ranges, dry_run=args.dry_run)

    # 顯示結果
    logger.info("=" * 60)
    logger.info("📊 並行同步完成摘要")
    logger.info("=" * 60)
    logger.info(f"✅ 成功任務: {summary['successful_tasks']}/{summary['total_tasks']}")
    logger.info(f"📊 總事件數: {summary['total_events']:,}")
    logger.info(f"✅ 同步事件: {summary['synced_events']:,}")
    logger.info(f"📈 同步率: {summary['sync_rate']:.2f}%")
    logger.info(f"💰 總成本: ${summary['total_cost_usd']:.4f} USD")
    logger.info(f"⏱️ 執行時間: {summary['execution_time_seconds']:.1f} 秒")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()
