#!/usr/bin/env python3
"""
Batch Size 優化器 - 動態計算最佳批次大小
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import pytz

# 添加 src 目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from google.cloud import bigquery
except ImportError as e:
    print(f"❌ 無法匯入 BigQuery: {e}")
    sys.exit(1)

logger = logging.getLogger(__name__)


class BatchSizeOptimizer:
    """Batch Size 動態優化器"""

    def __init__(self, project_id: str = "tagtoo-tracking"):
        self.project_id = project_id
        self.bq_client = bigquery.Client(project=project_id)
        self.timezone = pytz.timezone("Asia/Taipei")

        # BigQuery 限制
        self.MAX_ROWS_PER_REQUEST = 10000
        self.MAX_SIZE_PER_REQUEST_MB = 10
        self.MAX_REQUESTS_PER_SEC = 100

        # 記憶體和效能參數
        self.MEMORY_LIMIT_GB = 8
        self.TARGET_MEMORY_USAGE_PERCENT = 70  # 使用 70% 記憶體

    def estimate_event_size(self, source_table: str, sample_hours: int = 1) -> Dict[str, float]:
        """
        估算事件平均大小

        Args:
            source_table: 來源表格
            sample_hours: 取樣小時數

        Returns:
            事件大小統計
        """
        try:
            # 取樣最近的資料
            end_time = datetime.now(pytz.utc)
            start_time = end_time - timedelta(hours=sample_hours)

            query = f"""
            SELECT
                COUNT(*) as total_events,
                AVG(LENGTH(TO_JSON_STRING(STRUCT(
                    permanent, ec_id, partner_source, event_time, create_time,
                    link, event, value, currency, order_id, items, user,
                    partner_id, page, location
                )))) as avg_event_size_bytes,
                MAX(LENGTH(TO_JSON_STRING(STRUCT(
                    permanent, ec_id, partner_source, event_time, create_time,
                    link, event, value, currency, order_id, items, user,
                    partner_id, page, location
                )))) as max_event_size_bytes,
                STDDEV(LENGTH(TO_JSON_STRING(STRUCT(
                    permanent, ec_id, partner_source, event_time, create_time,
                    link, event, value, currency, order_id, items, user,
                    partner_id, page, location
                )))) as stddev_event_size_bytes
            FROM `{source_table}`
            WHERE event_time >= '{start_time.strftime('%Y-%m-%d %H:%M:%S')}'
              AND event_time < '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
            """

            job = self.bq_client.query(query)
            results = list(job.result())

            if results and results[0].total_events > 0:
                row = results[0]
                return {
                    'avg_size_bytes': float(row.avg_event_size_bytes or 0),
                    'max_size_bytes': float(row.max_event_size_bytes or 0),
                    'stddev_size_bytes': float(row.stddev_event_size_bytes or 0),
                    'total_events': int(row.total_events),
                    'sample_hours': sample_hours
                }
            else:
                # 使用預設估算值
                return {
                    'avg_size_bytes': 2048,  # 2KB 預設
                    'max_size_bytes': 8192,  # 8KB 預設
                    'stddev_size_bytes': 1024,  # 1KB 預設
                    'total_events': 0,
                    'sample_hours': sample_hours
                }

        except Exception as e:
            logger.warning(f"⚠️ 無法估算事件大小，使用預設值: {e}")
            return {
                'avg_size_bytes': 2048,
                'max_size_bytes': 8192,
                'stddev_size_bytes': 1024,
                'total_events': 0,
                'sample_hours': sample_hours
            }

    def calculate_optimal_batch_size(self,
                                   source_table: str,
                                   sync_mode: str = "economical",
                                   memory_limit_gb: int = 8) -> Dict[str, Any]:
        """
        計算最佳 batch size

        Args:
            source_table: 來源表格
            sync_mode: 同步模式
            memory_limit_gb: 記憶體限制

        Returns:
            優化建議
        """
        logger.info("🔍 分析事件大小...")
        event_stats = self.estimate_event_size(source_table)

        avg_size_bytes = event_stats['avg_size_bytes']
        max_size_bytes = event_stats['max_size_bytes']

        logger.info(f"📊 事件大小統計:")
        logger.info(f"   平均大小: {avg_size_bytes:.0f} bytes")
        logger.info(f"   最大大小: {max_size_bytes:.0f} bytes")
        logger.info(f"   取樣事件數: {event_stats['total_events']:,}")

        # 計算不同限制下的最大批次大小
        constraints = {}

        # 1. BigQuery API 行數限制
        constraints['api_rows_limit'] = self.MAX_ROWS_PER_REQUEST

        # 2. BigQuery API 大小限制 (10MB)
        max_size_mb = self.MAX_SIZE_PER_REQUEST_MB
        constraints['api_size_limit'] = int((max_size_mb * 1024 * 1024) / max_size_bytes)

        # 3. 記憶體限制 (考慮 polars DataFrame 和處理開銷)
        available_memory_bytes = memory_limit_gb * 1024 * 1024 * 1024 * (self.TARGET_MEMORY_USAGE_PERCENT / 100)
        # polars 處理時大約需要 3-4 倍記憶體 (原始資料 + DataFrame + 處理緩衝)
        memory_multiplier = 4 if sync_mode == "economical" else 2
        constraints['memory_limit'] = int(available_memory_bytes / (avg_size_bytes * memory_multiplier))

        # 4. 效能最佳化 (基於經驗值)
        if sync_mode == "economical":
            # 經濟模式：較大批次提高效率，但避免記憶體壓力
            constraints['performance_optimal'] = 8000
        else:
            # 快速模式：較小批次避免 MERGE 超時
            constraints['performance_optimal'] = 3000

        # 取最小值作為建議
        optimal_batch_size = min(constraints.values())

        # 確保不小於最小有效值
        optimal_batch_size = max(optimal_batch_size, 1000)

        # 計算預期記憶體使用
        expected_memory_mb = (optimal_batch_size * avg_size_bytes * memory_multiplier) / (1024 * 1024)

        # 計算預期請求大小
        expected_request_size_mb = (optimal_batch_size * avg_size_bytes) / (1024 * 1024)

        result = {
            'recommended_batch_size': optimal_batch_size,
            'current_batch_size': 5000,
            'improvement_factor': optimal_batch_size / 5000,
            'constraints': constraints,
            'limiting_factor': min(constraints, key=constraints.get),
            'event_stats': event_stats,
            'memory_analysis': {
                'expected_memory_usage_mb': expected_memory_mb,
                'memory_limit_mb': memory_limit_gb * 1024,
                'memory_usage_percent': (expected_memory_mb / (memory_limit_gb * 1024)) * 100
            },
            'request_analysis': {
                'expected_request_size_mb': expected_request_size_mb,
                'api_limit_mb': max_size_mb,
                'size_usage_percent': (expected_request_size_mb / max_size_mb) * 100
            }
        }

        return result

    def print_optimization_report(self, analysis: Dict[str, Any]):
        """列印優化報告"""
        print("=" * 60)
        print("📊 Batch Size 優化分析報告")
        print("=" * 60)

        current = analysis['current_batch_size']
        recommended = analysis['recommended_batch_size']
        improvement = analysis['improvement_factor']

        print(f"🔧 當前 Batch Size: {current:,}")
        print(f"✅ 建議 Batch Size: {recommended:,}")

        if improvement > 1.1:
            print(f"📈 效能提升: {improvement:.1f}x ({(improvement-1)*100:.0f}% 提升)")
        elif improvement < 0.9:
            print(f"⚠️ 建議降低: {1/improvement:.1f}x ({(1-improvement)*100:.0f}% 降低)")
        else:
            print(f"✅ 當前設定合適: {improvement:.1f}x")

        print(f"\n🚧 限制因素分析:")
        constraints = analysis['constraints']
        limiting_factor = analysis['limiting_factor']

        for factor, limit in constraints.items():
            marker = "🔴" if factor == limiting_factor else "🟢"
            factor_name = {
                'api_rows_limit': 'BigQuery API 行數限制',
                'api_size_limit': 'BigQuery API 大小限制',
                'memory_limit': '記憶體限制',
                'performance_optimal': '效能最佳化'
            }.get(factor, factor)

            print(f"   {marker} {factor_name}: {limit:,}")

        print(f"\n💾 記憶體分析:")
        mem = analysis['memory_analysis']
        print(f"   預期使用: {mem['expected_memory_usage_mb']:.1f} MB")
        print(f"   記憶體限制: {mem['memory_limit_mb']:.0f} MB")
        print(f"   使用率: {mem['memory_usage_percent']:.1f}%")

        print(f"\n📡 請求大小分析:")
        req = analysis['request_analysis']
        print(f"   預期請求大小: {req['expected_request_size_mb']:.2f} MB")
        print(f"   API 限制: {req['api_limit_mb']} MB")
        print(f"   使用率: {req['size_usage_percent']:.1f}%")

        print(f"\n📋 事件統計:")
        stats = analysis['event_stats']
        print(f"   平均大小: {stats['avg_size_bytes']:.0f} bytes")
        print(f"   最大大小: {stats['max_size_bytes']:.0f} bytes")
        print(f"   取樣數量: {stats['total_events']:,} 筆")

        print("=" * 60)


def main():
    """主程式"""
    import argparse

    parser = argparse.ArgumentParser(description="Batch Size 優化分析")
    parser.add_argument('--source-table',
                       default="tagtoo-tracking.event_prod.tagtoo_event",
                       help='來源表格')
    parser.add_argument('--sync-mode', choices=['economical', 'fast'],
                       default='economical',
                       help='同步模式')
    parser.add_argument('--memory-limit-gb', type=int, default=8,
                       help='記憶體限制 (GB)')

    args = parser.parse_args()

    optimizer = BatchSizeOptimizer()
    analysis = optimizer.calculate_optimal_batch_size(
        source_table=args.source_table,
        sync_mode=args.sync_mode,
        memory_limit_gb=args.memory_limit_gb
    )

    optimizer.print_optimization_report(analysis)

    # 輸出建議的環境變數設定
    recommended = analysis['recommended_batch_size']
    print(f"\n🔧 建議的環境變數設定:")
    print(f"export BATCH_SIZE={recommended}")


if __name__ == "__main__":
    main()
