"""
經濟模式功能完整測試套件

測試範圍：
1. 單元測試：所有新增方法的功能驗證
2. 整合測試：經濟模式與快速模式的功能等價性
3. 冪等性測試：重複執行無重複資料
4. 成本測試：確認成本節省效果
5. 錯誤處理和降級機制測試
6. 端到端測試：使用真實資料驗證

測試設計原則：
- 完全隔離測試環境，不依賴真實 GCP 服務
- 使用 polars 進行本地資料處理測試
- Mock 所有外部依賴但測試真實業務邏輯
- 驗證與手動腳本的功能等價性
"""

import json
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from typing import List, Dict, Any

import pytest
import polars as pl

# 確保測試環境隔離
os.environ.update({
    "PROJECT_ID": "test-project",
    "ENVIRONMENT": "test",
    "USE_POLARS": "true",  # 啟用經濟模式
    "ENABLE_COST_TRACKING": "true",
    "ENABLE_DEDUPLICATION": "true"
})


@pytest.fixture
def mock_gcp_clients():
    """完整 mock 所有 GCP 客戶端，避免真實連接"""
    with patch("src.main.create_bigquery_client") as mock_bq, \
         patch("src.main.create_firestore_client") as mock_fs, \
         patch("src.main.create_tasks_client") as mock_tasks:

        # BigQuery client mock
        mock_bq_client = MagicMock()
        mock_bq_client.query.return_value = MagicMock()
        mock_bq_client.get_table.return_value = MagicMock()
        mock_bq_client.insert_rows_json.return_value = []  # 無錯誤
        mock_bq.return_value = mock_bq_client

        # Firestore client mock
        mock_fs_client = MagicMock()
        mock_fs.return_value = mock_fs_client

        # Cloud Tasks client mock
        mock_tasks_client = MagicMock()
        mock_tasks.return_value = mock_tasks_client

        yield {
            "bigquery": mock_bq,
            "firestore": mock_fs,
            "tasks": mock_tasks,
            "bigquery_client": mock_bq_client,
            "firestore_client": mock_fs_client,
            "tasks_client": mock_tasks_client
        }


@pytest.fixture
def processor(mock_gcp_clients):
    """建立完全隔離的 processor 實例"""
    from src.main import LegacyEventSyncProcessor

    processor = LegacyEventSyncProcessor(
        source_table="test-project.test_dataset.source_table",
        target_table="test-project.test_dataset.target_table"
    )

    # 直接設定 mock 客戶端，避免 lazy loading 觸發真實連接
    processor._bigquery_client = mock_gcp_clients["bigquery_client"]
    processor._firestore_client = mock_gcp_clients["firestore_client"]
    processor._tasks_client = mock_gcp_clients["tasks_client"]

    return processor


@pytest.fixture
def sample_bigquery_events():
    """建立測試用的 BigQuery 事件資料"""
    events = []
    for i in range(5):
        mock_event = Mock()
        mock_event.get.side_effect = lambda key, default=None, i=i: {
            "permanent": f"test_permanent_{i}",
            "ec_id": 1000 + i,
            "event_time": datetime(2023, 1, 1, 12, i, 0),
            "link": f"http://example.com/page{i}",
            "event": {
                "name": "purchase",
                "value": 100.0 + i * 10,
                "currency": "USD",
                "custom_data": {"order_id": f"order_{i}"},
                "items": [{"id": f"p{i}", "name": f"product{i}", "price": 100.0 + i * 10, "quantity": 1}]
            },
            "user": {"em": f"test{i}@example.com", "ph": f"123456789{i}"},
            "location": {"country_code": "TW", "region_name": "Taipei", "city_name": "Taipei"}
        }.get(key, default)
        events.append(mock_event)
    return events


class TestEconomicalModeCore:
    """經濟模式核心功能測試"""

    @pytest.mark.unit
    def test_economical_mode_enabled_by_default(self, processor):
        """驗證經濟模式預設啟用"""
        assert processor.use_polars_mode is True

    @pytest.mark.unit
    def test_polars_dataframe_creation(self, processor, sample_bigquery_events):
        """測試 polars DataFrame 建立"""
        # 轉換事件資料
        transformed_data = []
        for event in sample_bigquery_events:
            transformed_data.append(processor.transform_event_data(event))

        # 建立 polars DataFrame
        df = pl.DataFrame(transformed_data, infer_schema_length=None)

        # 驗證 DataFrame 結構
        assert df.shape[0] == 5  # 5 筆記錄
        assert "permanent" in df.columns
        assert "ec_id" in df.columns
        assert "event" in df.columns
        assert "items" in df.columns

        # 驗證資料正確性
        first_row = df.row(0, named=True)
        assert first_row["permanent"] == "test_permanent_0"
        assert first_row["ec_id"] == 1000
        assert first_row["event"] == "purchase"

    @pytest.mark.unit
    def test_clean_polars_dataframe(self, processor, sample_bigquery_events):
        """測試 polars DataFrame 清理邏輯"""
        # 準備測試資料
        transformed_data = []
        for event in sample_bigquery_events:
            transformed_data.append(processor.transform_event_data(event))

        df = pl.DataFrame(transformed_data, infer_schema_length=None)

        # 執行清理
        clean_df = processor._clean_polars_dataframe(df, "test_batch")

        # 驗證清理結果
        assert clean_df.shape == df.shape  # 形狀不變
        assert "items" in clean_df.columns

        # 驗證 items 欄位處理
        items_column = clean_df.select("items").to_series()
        for items in items_column:
            assert isinstance(items, list)
            if items:  # 如果有資料
                assert isinstance(items[0], dict)
                assert "id" in items[0]

    @pytest.mark.unit
    def test_economical_idempotency_check_no_duplicates(self, processor):
        """測試冪等性檢查：無重複情況"""
        # 準備測試資料
        records = [
            {
                "permanent": "test1",
                "ec_id": 1001,
                "event_time": "2023-01-01 12:00:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "http://example.com",
                "value": 100.0,
                "currency": "USD",
                "order_id": "order1"
            },
            {
                "permanent": "test2",
                "ec_id": 1002,
                "event_time": "2023-01-01 12:01:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "http://example.com",
                "value": 200.0,
                "currency": "USD",
                "order_id": "order2"
            }
        ]

        # Mock BigQuery 查詢返回空結果（無重複）
        processor._bigquery_client.query.return_value.result.return_value = []

        # 執行冪等性檢查
        new_records = processor._economical_idempotency_check(records)

        # 驗證：應該返回所有記錄（無重複）
        assert len(new_records) == 2
        assert new_records == records

    @pytest.mark.unit
    def test_economical_idempotency_check_with_duplicates(self, processor):
        """測試冪等性檢查：有重複情況"""
        # 準備測試資料
        input_records = [
            {
                "permanent": "test1",
                "ec_id": 1001,
                "event_time": "2023-01-01 12:00:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "http://example.com",
                "value": 100.0,
                "currency": "USD",
                "order_id": "order1"
            },
            {
                "permanent": "test2",
                "ec_id": 1002,
                "event_time": "2023-01-01 12:01:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "http://example.com",
                "value": 200.0,
                "currency": "USD",
                "order_id": "order2"
            }
        ]

        # Mock BigQuery 查詢返回重複記錄
        existing_record = Mock()
        existing_record.get.side_effect = lambda key, default=None: {
            "permanent": "test1",
            "ec_id": 1001,
            "event_time": datetime(2023, 1, 1, 12, 0, 0),
            "event": "purchase",
            "partner_source": "legacy-tagtoo-event",
            "link": "http://example.com",
            "value": 100.0,
            "currency": "USD",
            "order_id": "order1"
        }.get(key, default)

        processor._bigquery_client.query.return_value.result.return_value = [existing_record]

        # 執行冪等性檢查
        new_records = processor._economical_idempotency_check(input_records)

        # 驗證：應該只返回非重複記錄
        assert len(new_records) == 1
        assert new_records[0]["permanent"] == "test2"

    @pytest.mark.unit
    def test_process_batch_economical_success(self, processor, sample_bigquery_events):
        """測試經濟模式批次處理成功情況"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock BigQuery 操作
        processor._bigquery_client.insert_rows_json.return_value = []  # 無錯誤

        # Mock 冪等性檢查返回所有記錄
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3  # 返回3筆記錄

            # 執行經濟模式處理
            result = processor._process_batch_economical(sample_bigquery_events, "test_batch")

            # 驗證結果
            assert result["synced_count"] == 3
            assert result["error_count"] == 0
            assert "cost_estimate" in result

            # 驗證成本估算
            cost_est = result["cost_estimate"]
            assert cost_est["processing_mode"] == "economical"
            assert cost_est["estimated_cost_usd"] > 0
            assert "cost_comparison" in cost_est
            assert cost_est["cost_comparison"]["cost_savings_usd"] >= 0

    @pytest.mark.unit
    def test_process_batch_economical_dry_run(self, processor, sample_bigquery_events):
        """測試經濟模式 dry-run 模式"""
        result = processor._process_batch_economical(sample_bigquery_events, "test_batch", dry_run=True)

        # Dry-run 模式不應該寫入資料
        assert result["synced_count"] == len(sample_bigquery_events)
        assert result["error_count"] == 0
        assert processor._bigquery_client.insert_rows_json.call_count == 0

    @pytest.mark.unit
    def test_process_batch_economical_with_errors(self, processor, sample_bigquery_events):
        """測試經濟模式處理錯誤情況"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock BigQuery 寫入錯誤
        processor._bigquery_client.insert_rows_json.return_value = [
            {"index": 0, "errors": [{"message": "Invalid data"}]}
        ]

        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3

            result = processor._process_batch_economical(sample_bigquery_events, "test_batch")

            # 驗證錯誤處理
            assert result["synced_count"] == 0
            assert result["error_count"] == 3  # 原始事件錯誤計數


class TestEconomicalVsFastModeComparison:
    """經濟模式與快速模式比較測試"""

    @pytest.mark.integration
    def test_mode_selection_logic(self, processor, sample_bigquery_events):
        """測試模式選擇邏輯"""
        # 驗證預設使用經濟模式
        assert processor.use_polars_mode is True

        # Mock 兩種模式的處理方法
        with patch.object(processor, '_process_batch_economical') as mock_eco, \
             patch.object(processor, '_process_batch_fast') as mock_fast:

            mock_eco.return_value = {"synced_count": 5, "error_count": 0}

            # 執行批次處理
            result = processor._process_batch(sample_bigquery_events, "test_batch")

            # 驗證經濟模式被調用
            mock_eco.assert_called_once()
            mock_fast.assert_not_called()

    @pytest.mark.integration
    def test_economical_mode_fallback_to_fast(self, processor, sample_bigquery_events):
        """測試經濟模式失敗時降級到快速模式"""
        # Mock 經濟模式拋出異常
        with patch.object(processor, '_process_batch_economical') as mock_eco, \
             patch.object(processor, '_process_batch_fast') as mock_fast:

            mock_eco.side_effect = Exception("polars 處理失敗")
            mock_fast.return_value = {"synced_count": 3, "error_count": 2}

            # 執行批次處理
            result = processor._process_batch(sample_bigquery_events, "test_batch")

            # 驗證降級機制
            mock_eco.assert_called_once()
            mock_fast.assert_called_once()
            assert result["synced_count"] == 3
            assert result["error_count"] == 2

    @pytest.mark.integration
    def test_functional_equivalence(self, processor, sample_bigquery_events):
        """測試經濟模式與快速模式的功能等價性"""
        # Mock BigQuery 操作確保一致的行為
        processor._bigquery_client.insert_rows_json.return_value = []

        # Mock 快速模式的 MERGE 操作
        with patch.object(processor, '_insert_to_bigquery') as mock_merge:
            mock_merge.return_value = {
                "affected_rows": 5,
                "cost_estimate": {"estimated_cost_usd": 0.05, "tb_processed": 0.001}
            }

            # 測試快速模式
            processor.use_polars_mode = False
            fast_result = processor._process_batch(sample_bigquery_events, "test_batch")

        # Mock 經濟模式的冪等性檢查
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 5

            # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
            processor.enable_idempotency_check = True

            # 測試經濟模式
            processor.use_polars_mode = True
            eco_result = processor._process_batch(sample_bigquery_events, "test_batch")

        # 驗證功能等價性（事件數量應該相同）
        assert fast_result["synced_count"] == eco_result["synced_count"]
        # 成本應該不同（經濟模式更低）
        assert eco_result["cost_estimate"]["estimated_cost_usd"] < fast_result["cost_estimate"]["estimated_cost_usd"]

    @pytest.mark.integration
    def test_data_consistency_between_modes(self, processor):
        """測試兩種模式產生資料的一致性"""
        # 準備相同的輸入資料
        mock_event = Mock()
        mock_event.get.side_effect = lambda key, default=None: {
            "permanent": "test_permanent",
            "ec_id": 1001,
            "event_time": datetime(2023, 1, 1, 12, 0, 0),
            "link": "http://example.com",
            "event": {"name": "purchase", "value": 100.0, "currency": "USD", "items": []},
            "user": {"em": "<EMAIL>", "ph": "1234567890"},
            "location": {"country_code": "TW", "region_name": "Taipei", "city_name": "Taipei"}
        }.get(key, default)

        # 兩種模式應該產生相同的轉換結果
        transformed_data = processor.transform_event_data(mock_event)

        # 驗證關鍵欄位
        assert transformed_data["permanent"] == "test_permanent"
        assert transformed_data["ec_id"] == 1001
        assert transformed_data["partner_source"] == "legacy-tagtoo-event"
        assert transformed_data["event"] == "purchase"
        assert transformed_data["value"] == 100.0
        assert transformed_data["currency"] == "USD"


class TestCostTrackingAndAnalysis:
    """成本追踪和分析功能測試"""

    @pytest.mark.unit
    def test_cost_tracker_initialization(self, processor):
        """測試成本追踪器初始化"""
        processor._ensure_cost_tracker_initialized()

        assert processor.cost_tracker is not None
        assert processor._enable_cost_tracking is True

    @pytest.mark.unit
    def test_cost_comparison_calculation(self, processor, sample_bigquery_events):
        """測試成本比較計算"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock 冪等性檢查
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3

            result = processor._process_batch_economical(sample_bigquery_events, "test_batch")

            # 驗證成本比較資訊
            cost_est = result["cost_estimate"]
            assert "cost_comparison" in cost_est

            comparison = cost_est["cost_comparison"]
            assert "estimated_fast_mode_cost_usd" in comparison
            assert "cost_savings_usd" in comparison
            assert "savings_percentage" in comparison
            assert "cost_efficiency_ratio" in comparison

            # 驗證節省效果
            assert comparison["cost_savings_usd"] >= 0
            assert comparison["savings_percentage"] >= 0

    @pytest.mark.unit
    def test_efficiency_metrics_calculation(self, processor, sample_bigquery_events):
        """測試效率指標計算"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3

            result = processor._process_batch_economical(sample_bigquery_events, "test_batch")

            # 驗證效率指標
            metrics = result["cost_estimate"]["efficiency_metrics"]
            assert "records_per_second" in metrics
            assert "cost_per_thousand_records" in metrics
            assert "processing_mode_advantage" in metrics

            # 驗證指標合理性
            assert metrics["records_per_second"] > 0
            assert metrics["cost_per_thousand_records"] >= 0

    @pytest.mark.unit
    def test_cost_tracking_integration(self, processor, sample_bigquery_events):
        """測試成本追踪整合"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        processor._ensure_cost_tracker_initialized()
        initial_cost = processor.cost_tracker.total_cost_usd

        # Mock 冪等性檢查
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3

            processor._process_batch_economical(sample_bigquery_events, "test_batch")

            # 驗證成本被正確追踪
            assert len(processor.cost_tracker.costs) > 0
            assert processor.cost_tracker.total_cost_usd > initial_cost


class TestIdempotencyAndDataIntegrity:
    """冪等性和資料完整性測試"""

    @pytest.mark.unit
    def test_normalize_event_time_function(self, processor):
        """測試時間格式標準化函數"""
        # 透過實際調用來測試 normalize_event_time 邏輯
        test_records = [
            {
                "permanent": "test1",
                "ec_id": 1001,
                "event_time": "2023-01-01T12:00:00.123456",  # 有微秒
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "",
                "value": 100.0,
                "currency": "USD",
                "order_id": "order1"
            }
        ]

        # Mock BigQuery 查詢（測試內部邏輯）
        processor._bigquery_client.query.return_value.result.return_value = []

        # 執行冪等性檢查（內部會調用時間標準化）
        result = processor._economical_idempotency_check(test_records)

        # 如果沒有異常拋出，說明時間標準化正常工作
        assert len(result) == 1

    @pytest.mark.unit
    def test_record_key_generation_consistency(self, processor):
        """測試記錄 key 生成的一致性"""
        # 準備相同資料的不同表示形式
        record1 = {
            "permanent": "test1",
            "ec_id": 1001,
            "event_time": "2023-01-01T12:00:00",
            "event": "purchase",
            "partner_source": "legacy-tagtoo-event",
            "link": "",
            "value": 100.0,
            "currency": "USD",
            "order_id": "order1"
        }

        record2 = {
            "permanent": "test1",
            "ec_id": 1001,
            "event_time": "2023-01-01 12:00:00",  # 不同的時間格式
            "event": "purchase",
            "partner_source": "legacy-tagtoo-event",
            "link": None,  # None vs 空字串
            "value": 100.0,
            "currency": "USD",
            "order_id": "order1"
        }

        # Mock BigQuery 查詢返回 record1
        existing_record = Mock()
        existing_record.get.side_effect = lambda key, default=None: record1.get(key, default)
        processor._bigquery_client.query.return_value.result.return_value = [existing_record]

        # 測試 record2 應該被識別為重複
        result = processor._economical_idempotency_check([record2])

        # 應該返回空列表（因為被識別為重複）
        assert len(result) == 0

    @pytest.mark.unit
    def test_duplicate_handling_within_batch(self, processor):
        """測試批次內重複記錄處理"""
        # 準備包含重複記錄的批次
        records = [
            {
                "permanent": "test1",
                "ec_id": 1001,
                "event_time": "2023-01-01 12:00:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "",
                "value": 100.0,
                "currency": "USD",
                "order_id": "order1"
            },
            {
                "permanent": "test1",  # 重複記錄
                "ec_id": 1001,
                "event_time": "2023-01-01 12:00:00",
                "event": "purchase",
                "partner_source": "legacy-tagtoo-event",
                "link": "",
                "value": 100.0,
                "currency": "USD",
                "order_id": "order1"
            }
        ]

        # Mock BigQuery 查詢返回空（沒有歷史重複）
        processor._bigquery_client.query.return_value.result.return_value = []

        result = processor._economical_idempotency_check(records)

        # 應該只返回一筆記錄（批次內去重）
        assert len(result) == 1

    @pytest.mark.integration
    def test_end_to_end_idempotency(self, processor):
        """端到端冪等性測試"""
        # 準備測試事件
        mock_events = []
        for i in range(3):
            mock_event = Mock()
            mock_event.get.side_effect = lambda key, default=None, i=i: {
                "permanent": f"test_{i}",
                "ec_id": 1000 + i,
                "event_time": datetime(2023, 1, 1, 12, 0, 0),
                "link": "http://example.com",
                "event": {"name": "purchase", "value": 100.0, "currency": "USD", "items": []},
                "user": {"em": f"test{i}@example.com"},
                "location": None
            }.get(key, default)
            mock_events.append(mock_event)

        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # 第一次執行
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3  # 全部是新記錄

            result1 = processor._process_batch_economical(mock_events, "test_batch_1")

        # 第二次執行相同資料（模擬重複處理）
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = []  # 全部是重複記錄

            result2 = processor._process_batch_economical(mock_events, "test_batch_2")

        # 驗證冪等性
        assert result1["synced_count"] == 3
        assert result2["synced_count"] == 0  # 第二次應該沒有新記錄


class TestErrorHandlingAndResilience:
    """錯誤處理和系統韌性測試"""

    @pytest.mark.unit
    def test_polars_dataframe_error_handling(self, processor):
        """測試 polars DataFrame 建立錯誤處理"""
        # 準備問題資料
        problematic_data = [
            {"invalid": "structure"},
            None,  # None 資料
            {"incomplete": "data"}
        ]

        # 測試錯誤處理不會導致程式崩潰
        try:
            df = pl.DataFrame(problematic_data, infer_schema_length=None)
            clean_df = processor._clean_polars_dataframe(df, "error_test")
            # 如果到這裡，說明錯誤處理正常
            assert True
        except Exception as e:
            # 如果拋出異常，驗證是預期的錯誤類型
            assert isinstance(e, (pl.PolarsError, ValueError))

    @pytest.mark.unit
    def test_bigquery_connection_error_handling(self, processor, sample_bigquery_events):
        """測試 BigQuery 連接錯誤處理"""
        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock BigQuery 連接錯誤
        processor._bigquery_client.insert_rows_json.side_effect = Exception("連接失敗")

        # Mock 冪等性檢查正常
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 3

            result = processor._process_batch_economical(sample_bigquery_events, "error_test")

            # 驗證錯誤被正確處理
            assert result["synced_count"] == 0
            assert result["error_count"] > 0
            assert "error" in result["cost_estimate"]

    @pytest.mark.unit
    def test_idempotency_check_fallback(self, processor):
        """測試冪等性檢查失敗時的降級處理"""
        records = [{"test": "data"}]

        # Mock BigQuery 查詢失敗
        processor._bigquery_client.query.side_effect = Exception("查詢失敗")

        # 執行冪等性檢查
        result = processor._economical_idempotency_check(records)

        # 驗證降級行為：返回所有記錄
        assert result == records

    @pytest.mark.integration
    def test_graceful_degradation_chain(self, processor, sample_bigquery_events):
        """測試完整的降級鏈：經濟模式 -> 快速模式 -> 錯誤處理"""
        # 設定經濟模式和快速模式都失敗
        with patch.object(processor, '_process_batch_economical') as mock_eco, \
             patch.object(processor, '_process_batch_fast') as mock_fast:

            mock_eco.side_effect = Exception("經濟模式失敗")
            mock_fast.side_effect = Exception("快速模式也失敗")

            # 執行應該拋出最終異常
            with pytest.raises(Exception) as exc_info:
                processor._process_batch(sample_bigquery_events, "degradation_test")

            # 驗證兩種模式都被嘗試
            mock_eco.assert_called_once()
            mock_fast.assert_called_once()
            assert "快速模式也失敗" in str(exc_info.value)


class TestPerformanceAndScalability:
    """效能和擴展性測試"""

    @pytest.mark.unit
    def test_large_batch_processing(self, processor):
        """測試大批次資料處理"""
        # 建立大批次測試資料（1000 筆）
        large_events = []
        for i in range(1000):
            mock_event = Mock()
            mock_event.get.side_effect = lambda key, default=None, i=i: {
                "permanent": f"test_{i}",
                "ec_id": 1000 + i,
                "event_time": datetime(2023, 1, 1, 12, i % 60, i % 60),
                "link": f"http://example.com/page{i}",
                "event": {"name": "purchase", "value": 100.0, "currency": "USD", "items": []},
                "user": {"em": f"test{i}@example.com"},
                "location": None
            }.get(key, default)
            large_events.append(mock_event)

        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock 冪等性檢查返回一半記錄（模擬現實情況）
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * 500

            # 測試大批次處理
            result = processor._process_batch_economical(large_events, "large_batch")

            # 驗證效能指標
            assert result["synced_count"] == 500
            assert "efficiency_metrics" in result["cost_estimate"]

            metrics = result["cost_estimate"]["efficiency_metrics"]
            assert metrics["records_per_second"] > 0

    @pytest.mark.unit
    def test_memory_efficiency(self, processor, sample_bigquery_events):
        """測試記憶體使用效率"""
        import psutil
        import os

        # 獲取處理前的記憶體使用
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss

        # 🔧 修復：明確啟用冪等性檢查以確保測試邏輯正確
        processor.enable_idempotency_check = True

        # Mock 冪等性檢查
        with patch.object(processor, '_economical_idempotency_check') as mock_idempotency:
            mock_idempotency.return_value = [{"test": "record"}] * len(sample_bigquery_events)

            # 執行處理
            processor._process_batch_economical(sample_bigquery_events, "memory_test")

        # 獲取處理後的記憶體使用
        memory_after = process.memory_info().rss
        memory_increase = memory_after - memory_before

        # 驗證記憶體增長在合理範圍內（< 100MB）
        assert memory_increase < 100 * 1024 * 1024  # 100MB


class TestIntegrationWithValidationScript:
    """與驗證腳本的整合測試"""

    @pytest.mark.integration
    def test_validation_script_compatibility(self, processor):
        """測試與 validate_smart_sync_rate.py 的相容性"""
        # 這個測試驗證經濟模式產生的資料格式
        # 與驗證腳本預期的格式相符

        # 準備符合驗證腳本預期的資料
        mock_event = Mock()
        mock_event.get.side_effect = lambda key, default=None: {
            "permanent": "test_permanent",
            "ec_id": 1001,
            "event_time": datetime(2023, 1, 1, 12, 0, 0),
            "link": "http://example.com",
            "event": {
                "name": "purchase",
                "value": 100.0,
                "currency": "USD",
                "custom_data": {"order_id": "order123"},
                "items": [{"id": "p1", "name": "product1", "price": 100.0, "quantity": 1}]
            },
            "user": {"em": "<EMAIL>", "ph": "1234567890"},
            "location": {"country_code": "TW", "region_name": "Taipei", "city_name": "Taipei"}
        }.get(key, default)

        # 轉換資料
        transformed = processor.transform_event_data(mock_event)

        # 驗證關鍵欄位符合驗證腳本預期
        required_fields = [
            "permanent", "ec_id", "partner_source", "event_time", "create_time",
            "link", "event", "value", "currency", "order_id", "items", "user",
            "partner_id", "page", "location", "raw_json"
        ]

        for field in required_fields:
            assert field in transformed, f"缺少必要欄位: {field}"

        # 驗證特定格式要求
        assert transformed["partner_source"] == "legacy-tagtoo-event"
        assert isinstance(transformed["items"], list)
        assert isinstance(transformed["user"], dict)


if __name__ == "__main__":
    pytest.main([
        "--color=yes",
        "-v",
        "-p", "no:warnings",
        __file__
    ])
