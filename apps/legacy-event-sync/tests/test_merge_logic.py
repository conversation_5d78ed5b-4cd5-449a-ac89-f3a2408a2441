#!/usr/bin/env python3
"""
Core Logic and Error Handling Tests for Optimized Merge

🎯 目的：
測試優化後 MERGE 相關的核心邏輯，確保：
1. 記憶體去重邏輯的業務規則正確性。
2. 錯誤處理和容錯機制（如 fallback 和清理）的穩健性。
3. 資料轉換邏輯的正確性。

這些是真正的單元測試，可以在 CI/CD 中執行，不依賴外部服務。
"""

import unittest
from unittest.mock import Mock, patch
from datetime import datetime
import json
import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import LegacyEventSyncProcessor
from google.cloud.exceptions import GoogleCloudError

def create_full_mock_row(permanent, ec_id, event_time, link, order_id, event_name="focus", value=None, currency=None):
    """輔助函數，創建一個結構完整的模擬 BigQuery Row 物件"""
    row = Mock()
    row.permanent = permanent
    row.ec_id = ec_id
    row.event_time = datetime.fromisoformat(event_time) if isinstance(event_time, str) else event_time
    row.link = link

    _data = {
        "permanent": permanent,
        "ec_id": ec_id,
        "event_time": row.event_time,
        "link": link,
        "event": {
            "name": event_name,
            "value": value,
            "currency": currency,
            "custom_data": {"order_id": order_id},
            "items": []
        },
        "user": {},
        "location": {}
    }

    def get_side_effect(key, default=None):
        if '.' in key:
            keys = key.split('.')
            val = _data
            for k in keys:
                if val is None: return default
                val = val.get(k, default)
            return val
        return _data.get(key, default)

    row.get.side_effect = get_side_effect
    return row


class TestOptimizedMergeCoreLogic(unittest.TestCase):
    """測試優化後 MERGE 的核心功能、業務邏輯和錯誤處理"""

    def setUp(self):
        """設定測試環境"""
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]

        # 🔧 修復：確保測試使用快速模式（而不是經濟模式）
        os.environ["USE_POLARS"] = "false"

        with patch('main.bigquery.Client'), \
             patch('main.bigquery.Table'), \
             patch('main.tasks_v2.CloudTasksClient'):

            self.processor = LegacyEventSyncProcessor(
                source_table="test_project.test_dataset.source_table",
                target_table="test_project.test_dataset.target_table"
            )

        mock_client = Mock()
        mock_table = Mock()
        mock_table.schema = []
        mock_client.get_table.return_value = mock_table
        mock_client.create_table.return_value = mock_table
        mock_client.insert_rows_json.return_value = []
        mock_client.delete_table.return_value = None

        mock_job = Mock()
        mock_job.result.return_value = []
        mock_job.num_dml_affected_rows = 1
        mock_client.query.return_value = mock_job

        self.processor._bigquery_client = mock_client

    def tearDown(self):
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]
        if "USE_POLARS" in os.environ:
            del os.environ["USE_POLARS"]

    def test_business_logic_in_memory_deduplication(self):
        """測試記憶體去重是否保留了有業務價值的'重複'記錄"""
        os.environ["USE_OPTIMIZED_MERGE"] = "true"
        base_time = "2023-01-01T00:00:00"

        test_data = [
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user1", 1, base_time, "link1", "order1"), # 完全重複
            create_full_mock_row("user1", 1, base_time, "link2", "order1"), # link 不同，應保留
            create_full_mock_row("user1", 1, base_time, "link1", "order2"), # order_id 不同，應保留
        ]

        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            with patch.object(self.processor, '_economical_idempotency_check') as mock_idempotency:
                # Mock 冪等性檢查返回3筆記錄
                mock_idempotency.return_value = [{"test": "record1"}, {"test": "record2"}, {"test": "record3"}]
                self.processor._process_batch(test_data, "batch-business-logic")
                self.assertEqual(mock_insert.call_count, 1)
                self.assertEqual(len(mock_insert.call_args[0][0]), 3)

    def test_null_value_handling_in_deduplication_key(self):
        """測試記憶體去重邏輯能正確處理包含 None 值的鍵"""
        os.environ["USE_OPTIMIZED_MERGE"] = "true"
        base_time = "2023-01-01T00:00:00"

        test_data = [
            create_full_mock_row("user1", 1, base_time, None, "order1"),
            create_full_mock_row("user1", 1, base_time, None, "order1"), # 重複
            create_full_mock_row("user1", 1, base_time, "link1", None), # order_id 為 None
            create_full_mock_row("user1", 1, base_time, "link1", None), # 重複
        ]

        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            with patch.object(self.processor, '_economical_idempotency_check') as mock_idempotency:
                # Mock 冪等性檢查返回2筆記錄
                mock_idempotency.return_value = [{"test": "record1"}, {"test": "record2"}]
                self.processor._process_batch(test_data, "batch-null-values")
                self.assertEqual(mock_insert.call_count, 1)
                self.assertEqual(len(mock_insert.call_args[0][0]), 2)

    def test_error_handling_and_cleanup_on_merge_failure(self):
        """測試當 MERGE 操作失敗時，錯誤會被拋出且清理邏輯會被執行"""
        test_data = [{"permanent": "user1"}]
        self.processor.bigquery_client.query.side_effect = Exception("MERGE query failed")
        with self.assertRaises(Exception) as context:
            self.processor._merge_to_bigquery(test_data)
        self.assertIn("MERGE query failed", str(context.exception))
        self.processor.bigquery_client.delete_table.assert_called_once()

    def test_error_handling_on_temp_table_creation_failure(self):
        """測試當臨時表創建失敗時的錯誤處理"""
        test_data = [{"permanent": "user1"}]
        self.processor.bigquery_client.create_table.side_effect = Exception("Cannot create temp table")
        with self.assertRaises(Exception) as context:
            self.processor._merge_to_bigquery(test_data)
        self.assertIn("Cannot create temp table", str(context.exception))
        self.processor.bigquery_client.delete_table.assert_called_once()

    def test_error_handling_on_data_loading_failure(self):
        """測試當資料載入臨時表失敗時的錯誤處理"""
        test_data = [{"permanent": "user1"}]
        self.processor.bigquery_client.insert_rows_json.return_value = ["Error loading data"]
        with self.assertRaises(GoogleCloudError):
            self.processor._merge_to_bigquery(test_data)
        self.processor.bigquery_client.delete_table.assert_called_once()


class TestDataTransformationLogic(unittest.TestCase):
    """測試資料轉換邏輯"""

    def setUp(self):
        """設定測試環境"""
        with patch('main.bigquery.Client'):
            self.processor = LegacyEventSyncProcessor(
                source_table="test_project.test_dataset.source_table",
                target_table="test_project.test_dataset.target_table"
            )

    def test_datetime_handling(self):
        """測試日期時間處理"""
        test_time = datetime(2025, 7, 15, 10, 30, 45)
        mock_row = create_full_mock_row("p1", 1, test_time, "l1", "o1")
        transformed = self.processor.transform_event_data(mock_row)
        self.assertEqual(transformed["event_time"], test_time.isoformat())

    def test_json_field_handling(self):
        """測試 JSON 欄位處理"""
        mock_row = Mock()
        mock_row.get.side_effect = lambda key, default=None: {
            "event_time": datetime.now(),
            "event": json.dumps({"name": "test", "value": 123, "items":[]}),
            "user": json.dumps({"em": "<EMAIL>"}),
            "location": "{}"
        }.get(key, default)

        transformed = self.processor.transform_event_data(mock_row)
        self.assertEqual(transformed["event"], "test")
        self.assertEqual(transformed["value"], 123)
        self.assertEqual(transformed["user"]["em"], "<EMAIL>")


if __name__ == '__main__':
    unittest.main(verbosity=2)
