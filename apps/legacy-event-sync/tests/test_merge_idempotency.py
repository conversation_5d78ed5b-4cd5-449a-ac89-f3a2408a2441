#!/usr/bin/env python3
"""
Optimized MERGE Idempotency Mock Test

🎯 目的：
使用 Mock 驗證優化後 MERGE 實作的冪等性邏輯，不依賴真實 BigQuery。

🧪 測試策略：
1. Mock BigQuery 操作。
2. 驗證 `_process_batch` 在不同開關狀態下的去重行為。
3. 驗證重複呼叫 `_process_batch` 的冪等性。
4. 確保清理邏輯和錯誤處理仍然正常。
"""

import re
import unittest
from unittest.mock import Mock, patch
from datetime import datetime
import sys
import os
import re

# 添加 src 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import LegacyEventSyncProcessor
import logging

logger = logging.getLogger(__name__)

def create_full_mock_row(permanent, ec_id, event_time, link, order_id, event_name="focus", value=None, currency=None):
    """輔助函數，創建一個結構完整的模擬 BigQuery Row 物件"""
    row = Mock()
    row.permanent = permanent
    row.ec_id = ec_id
    row.event_time = datetime.fromisoformat(event_time) if isinstance(event_time, str) else event_time
    row.link = link

    _data = {
        "permanent": permanent,
        "ec_id": ec_id,
        "event_time": row.event_time,
        "link": link,
        "event": {
            "name": event_name,
            "value": value,
            "currency": currency,
            "custom_data": {"order_id": order_id},
            "items": []
        },
        "user": {},
        "location": {}
    }

    def get_side_effect(key, default=None):
        if '.' in key:
            keys = key.split('.')
            val = _data
            for k in keys:
                if val is None: return default
                val = val.get(k, default)
            return val
        return _data.get(key, default)

    row.get.side_effect = get_side_effect
    return row


class TestOptimizedMergeIdempotency(unittest.TestCase):
    """優化後 MERGE 冪等性 Mock 測試類別"""

    def setUp(self):
        """設定測試環境"""
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]

        # 🔧 修復：確保測試使用快速模式（而不是經濟模式）
        os.environ["USE_POLARS"] = "false"

        with patch('main.bigquery.Client'), \
             patch('main.bigquery.Table'), \
             patch('main.tasks_v2.CloudTasksClient'):

            self.processor = LegacyEventSyncProcessor(
                source_table="test-project.test_dataset.source_table",
                target_table="test-project.test_dataset.target_table"
            )

        mock_client = Mock()
        mock_table = Mock()
        mock_table.schema = []
        mock_client.get_table.return_value = mock_table
        mock_client.create_table.return_value = mock_table
        mock_client.insert_rows_json.return_value = []
        mock_client.delete_table.return_value = None

        mock_job = Mock()
        mock_job.result.return_value = []
        mock_job.num_dml_affected_rows = 1
        mock_client.query.return_value = mock_job

        self.processor._bigquery_client = mock_client
        self.mock_client = mock_client

    def tearDown(self):
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]
        if "USE_POLARS" in os.environ:
            del os.environ["USE_POLARS"]

    def test_idempotency_with_optimization_on(self):
        """測試在優化開啟時，重複處理同批資料的冪等性"""
        os.environ["USE_OPTIMIZED_MERGE"] = "true"
        base_time = "2023-01-01T00:00:00"
        test_data = [
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user2", 2, "2023-01-01T01:00:00", "link2", "order2"),
        ]

        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            with patch.object(self.processor, '_economical_idempotency_check') as mock_idempotency:
                # Mock 冪等性檢查返回2筆記錄
                mock_idempotency.return_value = [{"test": "record1"}, {"test": "record2"}]
                self.processor._process_batch(test_data, "batch-1")
                self.assertEqual(mock_insert.call_count, 1)
                self.assertEqual(len(mock_insert.call_args[0][0]), 2)

                self.processor._process_batch(test_data, "batch-2")
                self.assertEqual(mock_insert.call_count, 2)
                self.assertEqual(len(mock_insert.call_args[0][0]), 2)

    def test_idempotency_with_optimization_off(self):
        """測試在優化關閉時，重複處理同批資料的行為"""
        os.environ["USE_OPTIMIZED_MERGE"] = "false"
        base_time = "2023-01-01T00:00:00"
        test_data = [
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user2", 2, "2023-01-01T01:00:00", "link2", "order2"),
        ]

        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            self.processor._process_batch(test_data, "batch-1")
            self.assertEqual(mock_insert.call_count, 1)
            self.assertEqual(len(mock_insert.call_args[0][0]), 3)

            self.processor._process_batch(test_data, "batch-2")
            self.assertEqual(mock_insert.call_count, 2)
            self.assertEqual(len(mock_insert.call_args[0][0]), 3)

    def test_merge_cleanup_always_executed(self):
        """測試即使 MERGE 失敗，清理邏輯也總是被執行"""
        test_data = [{"permanent": "user1"}]
        self.mock_client.query.side_effect = Exception("MERGE query failed")
        with self.assertRaises(Exception):
            self.processor._merge_to_bigquery(test_data)
        self.mock_client.delete_table.assert_called()

    def test_empty_data_handling(self):
        """測試當傳入空資料列表時的處理"""
        result = self.processor._process_batch([], "empty-batch")
        self.assertEqual(result["synced_count"], 0)
        self.assertEqual(result["error_count"], 0)
        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            self.processor._process_batch([], "empty-batch-2")
            mock_insert.assert_not_called()

    def test_simplified_merge_query_no_update_clause(self):
        """測試簡化後的 MERGE 查詢不包含 UPDATE 子句"""
        test_data = [{"permanent": "user1"}]
        executed_queries = []
        def capture_query(query, *args, **kwargs):
            executed_queries.append(query)
            mock_job = Mock()
            mock_job.result.return_value = []
            return mock_job
        self.mock_client.query.side_effect = capture_query
        with patch.object(self.processor, '_create_temp_table_and_load_data'):
            self.processor._insert_to_bigquery(test_data)
        self.assertTrue(len(executed_queries) > 0)
        merge_query = executed_queries[0]
        self.assertNotIn("WHEN MATCHED THEN UPDATE", merge_query.upper())
        self.assertIn("WHEN NOT MATCHED THEN", merge_query.upper())
        self.assertIn("INSERT", merge_query.upper())


if __name__ == "__main__":
    unittest.main(verbosity=2)
