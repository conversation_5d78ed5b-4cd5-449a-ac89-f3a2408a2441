# Legacy Event Sync - Terraform Variables Example
# 複製此檔案為 terraform.tfvars 並填入實際值

# 基本環境配置
environment = "prod"
deployment_version = "v1.0.0"

# 調度配置
sync_schedule = "0 2/4 * * *"  # 每 4 小時執行一次，從台灣時區凌晨 2 點開始
enable_scheduler = true  # 啟用 Cloud Scheduler 自動同步 (設為 false 可暫時停止)

# 敏感配置 - 請使用安全方式設定
# 註解：目前 legacy-event-sync 服務不需要 secret_key

# 方式 1: 透過環境變數設定
# export TF_VAR_secret_key="your-production-secret-key"  # 暫不需要
# export TF_VAR_api_key="your-api-key"

# 方式 2: 透過 terraform.tfvars (不要提交到版控)
# secret_key = "your-production-secret-key"  # 暫不需要
# api_key = "your-api-key"

# 方式 3: 透過 -var 參數
# terraform apply -var="secret_key=your-secret"  # 暫不需要

# 應用配置
log_level = "INFO"
batch_size = 200000
hours_per_segment = 4
max_concurrent_segments = 6

# BigQuery 成本追踪 (建議啟用以監控查詢成本)
enable_cost_tracking = true

# 冪等性檢查配置 (成本優化)
# 生產環境建議設為 false 以節省 64% 成本 (約 $306/年)
# 開發/測試環境可設為 true 確保資料品質
enable_idempotency_check = false  # Production: false, Dev/Test: true

# Cloud Function 資源配置 (2nd gen)
memory = "4096Mi"
cpu = "4"  # 4 vCPU - 適合資料處理工作負載
timeout = 3600  # 1 hour - 資料同步作業建議使用較長 timeout
max_instances = 10
min_instances = 0
