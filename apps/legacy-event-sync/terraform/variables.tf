# Legacy Event Sync Service Variables

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "deployment_version" {
  description = "Deployment version for source code"
  type        = string
  default     = "latest"
}

variable "service_name" {
  description = "Service name, used for Docker image and other resources"
  type        = string
  default     = "legacy-event-sync"
}

variable "sync_schedule" {
  description = "Cron schedule for sync job"
  type        = string
  default     = "0 * * * *" # Every hour, processing data from 2 hours ago to handle streaming buffer delay
}

variable "enable_scheduler" {
  description = "Enable Cloud Scheduler job for automatic sync"
  type        = bool
  default     = true
}

# ====== 成本監控變數 ======
variable "billing_account_id" {
  description = "Billing account ID for cost monitoring"
  type        = string
  default     = null # 可選，只在 prod 環境啟用成本監控時需要
}

# ====== 敏感配置變數 ======
variable "secret_key" {
  description = "Secret key for application security (目前服務未使用)"
  type        = string
  default     = null
  sensitive   = true
}

variable "api_key" {
  description = "API key for external services (optional)"
  type        = string
  default     = null
  sensitive   = true
}

variable "webhook_secret" {
  description = "Webhook secret for validation (optional)"
  type        = string
  default     = null
  sensitive   = true
}

# ====== 應用配置變數 ======
variable "use_optimized_merge" {
  description = "Enable the optimized in-memory deduplication and simplified MERGE logic"
  type        = bool
  default     = true
}

variable "enable_cost_tracking" {
  description = <<-EOT
    啟用 BigQuery 成本追踪功能

    說明：
    - 追踪所有 BigQuery 查詢的成本、執行時間和資源使用情況
    - 提供即時成本警告和效率分析
    - 在 API 回應中包含詳細的成本資訊
    - 支援成本閾值警告：$0.10 (warning), $0.50 (high), $2.00 (critical)

    建議值：
    - 開發環境：true（幫助開發者了解查詢成本）
    - 測試環境：true（驗證成本最佳化效果）
    - 生產環境：true（監控實際運營成本）

    效益：
    - 提供完全的 BigQuery 成本透明度
    - 識別高成本查詢和最佳化機會
    - 支援成本預算控制和監控告警
  EOT
  type        = bool
  default     = true
}

variable "enable_idempotency_check" {
  description = <<-EOT
    啟用冪等性檢查功能

    說明：
    - 控制是否在批次處理前執行冪等性檢查查詢
    - 冪等性檢查可防止重複記錄，但會增加 BigQuery 查詢成本
    - 在經濟模式下，冪等性檢查佔總成本的 64%

    建議值：
    - 開發環境：true（確保資料品質）
    - 測試環境：false（節省成本，可接受少量重複）
    - 生產環境：false（節省成本，定期清理重複記錄）

    成本影響：
    - 啟用：完整的資料品質保證
    - 關閉：節省約 64% 成本

    風險控制：
    - 關閉時建議設定定期清理作業
    - 監控重複記錄比例
    - 在關鍵業務時段可臨時啟用

    預設值說明：
    - 預設為 false 以節省成本
    - 如需確保資料品質，可設為 true
  EOT
  type        = bool
  default     = false
}

variable "log_level" {
  description = "Log level for the application"
  type        = string
  default     = "INFO"

  validation {
    condition     = contains(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], var.log_level)
    error_message = "Log level must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL."
  }
}

variable "batch_size" {
  description = "Batch size for data processing"
  type        = number
  default     = 5000 # 統一批次大小，與程式碼保持一致，提升穩定性
}

variable "minutes_per_segment" {
  description = "Minutes per processing segment (每個 segment 處理幾分鐘，支援小數)"
  type        = number
  default     = 5 # 預設 5 分鐘一個 segment，提升同步穩定性，降低 timeout 風險
}

variable "max_concurrent_segments" {
  description = "Maximum concurrent processing segments"
  type        = number
  default     = 20
}

# ====== Cloud Run 資源配置 ======
variable "cloud_run_cpu" {
  description = "CPU allocation for Cloud Run service"
  type        = string
  default     = "4"
}

variable "cloud_run_memory" {
  description = "Memory allocation for Cloud Run service"
  type        = string
  default     = "16Gi" # 測試: 從8GB增加到16GB，診斷記憶體不足問題
}

variable "cloud_run_timeout" {
  description = "Timeout in seconds for Cloud Run service"
  type        = number
  default     = 1800 # 30 minutes - 配合 Cloud Tasks dispatch_deadline 上限
}

variable "gunicorn_timeout" {
  description = "Timeout in seconds for gunicorn workers (should be less than cloud_run_timeout)"
  type        = number
  default     = 1740 # 29 minutes (60 seconds less than cloud_run_timeout for graceful error handling)
}

variable "cloud_run_max_instances" {
  description = "Maximum number of Cloud Run instances"
  type        = number
  default     = 10
}

variable "cloud_run_min_instances" {
  description = "Minimum number of Cloud Run instances"
  type        = number
  default     = 0
}

variable "gunicorn_workers" {
  description = "Number of gunicorn worker processes"
  type        = number
  default     = 4 # 從 2 增加到 4，利用多核心處理能力
}

# ====== Cloud Tasks Queue 配置 ======
variable "task_queue_max_concurrent_dispatches" {
  description = <<-EOT
    Cloud Tasks 佇列的最大並行派發數量。

    說明：
    - 控制同時執行的任務數量上限
    - 與 Cloud Run 實例的擴展能力相關
    - 設定過高可能導致下游服務過載
    - 設定過低會限制處理能力

    建議值：
    - 開發環境：20-50
    - 生產環境：100-200
    - 需根據實際的 Cloud Run 配置和下游服務容量調整
  EOT
  type        = number
  default     = 100
}

variable "task_queue_max_dispatches_per_second" {
  description = <<-EOT
    Cloud Tasks 佇列的每秒最大派發速率。

    說明：
    - 控制任務派發的頻率，避免瞬間大量請求
    - 與 max_burst_size 的計算相關（系統自動計算）
    - 影響 Token Bucket 的補充速率

    建議值：
    - 低流量：5-10 TPS
    - 中流量：20-50 TPS
    - 高流量：50-100 TPS
    - 需根據下游服務的處理能力調整

    注意：max_burst_size ≈ max_dispatches_per_second * 2秒
  EOT
  type        = number
  default     = 50 # 建議 5，依流量可再調整
}

# max_burst_size 是 Cloud Tasks 自動計算的屬性，不能手動設定
#
# 詳細說明：
# - Google Cloud 根據 max_dispatches_per_second 自動計算
# - 使用 Token Bucket 演算法控制瞬間處理能力
# - 計算公式：約等於 max_dispatches_per_second * burst_duration_seconds
# - 在 Terraform 中設定會導致錯誤：
#   "Can't configure a value for rate_limits.0.max_burst_size"
#
# 參考文檔：https://cloud.google.com/tasks/docs/configuring-queues
#
# variable "task_queue_max_burst_size" {
#   description = "The maximum burst size for Cloud Tasks queue (瞬間可同時發送的最大任務數)."
#   type        = number
#   default     = 100
# }

variable "excluded_event_types" {
  description = <<-EOT
    需要從同步中排除的事件類型清單。

    說明：
    - 指定哪些事件類型不需要同步到 integrated_event
    - 主要用於過濾高頻低價值事件（如 focus 滑鼠追蹤事件）
    - 可顯著減少處理量和 BigQuery 成本
    - 支援多個事件類型，以備未來擴充

    範例：
    - ["focus"] - 排除 focus 事件
    - ["focus", "scroll"] - 排除 focus 和 scroll 事件
    - [] - 不排除任何事件類型
  EOT
  type        = list(string)
  default     = ["focus"]
}

variable "streaming_buffer_offset_hours" {
  description = <<-EOT
    BigQuery Streaming Buffer 延遲緩衝時間（小時）。

    說明：
    - 控制同步時的時間偏移，避免 Streaming Buffer 延遲問題
    - 基於當前監控，真實 Streaming Buffer 延遲通常為 10-15 分鐘
    - 較大的偏移確保資料完整性，但會增加同步延遲

    建議值：
    - 1 (預設) - 基於真實 Streaming Buffer 監控的最佳化設定
    - 1.5 - 較保守的設定，適合延遲較不穩定的環境
    - 0.5 - 積極設定，僅適合延遲極穩定的情況
  EOT
  type        = number
  default     = 1
}

variable "sync_overlap_minutes" {
  description = <<-EOT
    同步時間窗口重疊緩衝（分鐘）。

    說明：
    - 為了防止 Streaming Buffer 延遲導致的資料遺漏，每次同步會包含額外的重疊時間
    - 例如：每 60 分鐘觸發一次，但處理 75 分鐘的資料（60 + 15 重疊）
    - 重疊部分會被 MERGE 操作自動去重，不會產生重複資料

    建議值：
    - 15 (預設) - 適合大部分情況，覆蓋典型的 Streaming Buffer 延遲
    - 30 - 保守設定，適合延遲較不穩定的環境
    - 5 - 激進設定，適合延遲非常穩定的環境
    - 0 - 停用重疊（不建議，可能遺漏資料）
  EOT
  type        = number
  default     = 15
}
