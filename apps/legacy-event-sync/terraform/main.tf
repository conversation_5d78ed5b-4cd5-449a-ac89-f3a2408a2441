# Legacy Event Sync Service Infrastructure
# 這個 app 特定的基礎設施配置

terraform {
  required_version = ">= 1.12.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/legacy-event-sync"
  }
}

provider "google" {
  project = local.shared_outputs.project_id
  region  = local.shared_outputs.region
}

# 引用共用基礎設施的資料
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

locals {
  shared_outputs = data.terraform_remote_state.shared.outputs

  labels = {
    app         = "legacy-event-sync"
    environment = var.environment
    managed-by  = "terraform"
  }
}

# ====== Secrets for Legacy Event Sync ======
# 註解：目前 legacy-event-sync 服務不需要 secret_key，但保留資源定義以備將來使用
resource "google_secret_manager_secret" "secret_key" {
  count     = var.secret_key != null ? 1 : 0
  secret_id = "legacy-event-sync-secret-key-${var.environment}"

  replication {
    auto {}
  }

  labels = local.labels
}

resource "google_secret_manager_secret_version" "secret_key" {
  count       = var.secret_key != null ? 1 : 0
  secret      = google_secret_manager_secret.secret_key[0].id
  secret_data = var.secret_key
}

resource "google_secret_manager_secret" "api_key" {
  count     = var.api_key != null ? 1 : 0
  secret_id = "legacy-event-sync-api-key-${var.environment}"

  replication {
    auto {}
  }

  labels = local.labels
}

resource "google_secret_manager_secret_version" "api_key" {
  count       = var.api_key != null ? 1 : 0
  secret      = google_secret_manager_secret.api_key[0].id
  secret_data = var.api_key
}

# ====== Secret Manager IAM for Service Account ======
resource "google_secret_manager_secret_iam_member" "secret_key_accessor" {
  count     = var.secret_key != null ? 1 : 0
  secret_id = google_secret_manager_secret.secret_key[0].secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.shared_outputs.service_account_email}"
}

resource "google_secret_manager_secret_iam_member" "api_key_accessor" {
  count     = var.api_key != null ? 1 : 0
  secret_id = google_secret_manager_secret.api_key[0].secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.shared_outputs.service_account_email}"
}

# ====== Cloud Tasks Queue ======
resource "google_cloud_tasks_queue" "segment_processing_queue" {
  name     = "legacy-event-sync-segments-${var.environment}"
  location = local.shared_outputs.region

  # Rate Limits 配置說明：
  # Cloud Tasks 使用 Token Bucket 演算法控制任務執行速率
  # 參考文檔：https://cloud.google.com/tasks/docs/configuring-queues
  rate_limits {
    # 最大並行派發數：同時執行的任務數量上限
    # 建議值：100（根據 Cloud Run 實例擴展能力調整）
    max_concurrent_dispatches = var.task_queue_max_concurrent_dispatches # 建議設為 100

    # 每秒最大派發速率：控制任務派發的頻率
    # 建議值：50（避免下游服務過載，可根據實際需求調整）
    max_dispatches_per_second = var.task_queue_max_dispatches_per_second # 建議設為 50

    # max_burst_size 說明：
    # - 此屬性由 Google Cloud 系統自動計算，無法手動設定
    # - 計算公式：約等於 max_dispatches_per_second * burst_duration_seconds
    # - 例如：50 TPS * 2秒 ≈ 100 burst size
    # - Token Bucket 中可容納的最大 token 數量，決定瞬間處理能力
    # - 在 Terraform 中嘗試設定此值會導致錯誤：
    #   "Can't configure a value for rate_limits.0.max_burst_size"
    # - 如需精確控制 burst 行為，可考慮：
    #   1. 調整 max_concurrent_dispatches（間接控制）
    #   2. 使用 queue.yaml 方式（失去 Terraform 管理優勢）
    # - 官方建議：讓系統自動計算以獲得最佳效果
  }

  retry_config {
    max_attempts  = 1 # 緊急修復：減少重試次數避免重複資料
    min_backoff   = "5s"
    max_backoff   = "3600s"
    max_doublings = 10
    # 注意：Cloud Tasks 的 task timeout 需要在發送任務時設定，而非佇列層級
    # 實際的 timeout 處理將在應用程式代碼中實現
  }

  stackdriver_logging_config {
    sampling_ratio = 1.0
  }
}

# ====== IAM Permissions for Cloud Tasks ======
resource "google_project_iam_member" "tasks_enqueuer" {
  project = local.shared_outputs.project_id
  role    = "roles/cloudtasks.enqueuer"
  member  = "serviceAccount:${local.shared_outputs.service_account_email}"
}

# ====== IAM Permission for Service Account Token Creation ======
# 讓 service account 能夠為自己創建 OIDC tokens (用於 Cloud Tasks)
resource "google_service_account_iam_member" "service_account_token_creator" {
  service_account_id = "projects/${local.shared_outputs.project_id}/serviceAccounts/${local.shared_outputs.service_account_email}"
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.shared_outputs.service_account_email}"
}

# ====== Cloud Run Service for Legacy Event Sync ======
resource "google_cloud_run_v2_service" "legacy_event_sync_service" {
  name     = "legacy-event-sync-${var.environment}"
  location = local.shared_outputs.region

  ingress = "INGRESS_TRAFFIC_ALL"

  template {
    service_account = local.shared_outputs.service_account_email

    containers {
      image = "${local.shared_outputs.artifact_registry_repository_url}/${var.service_name}:${var.deployment_version}"

      ports {
        container_port = 8080
      }

      resources {
        limits = {
          cpu    = var.cloud_run_cpu
          memory = var.cloud_run_memory
        }
      }

      env {
        name  = "PROJECT_ID"
        value = local.shared_outputs.project_id
      }
      env {
        name  = "BIGQUERY_DATASET"
        value = local.shared_outputs.bigquery_dataset_id
      }
      env {
        name  = "FIRESTORE_DATABASE"
        value = local.shared_outputs.firestore_database_name
      }
      env {
        name  = "ENVIRONMENT"
        value = var.environment
      }
      env {
        name  = "SOURCE_TABLE"
        value = "tagtoo-tracking.event_prod.tagtoo_event"
      }
      env {
        name  = "TARGET_TABLE"
        value = "${local.shared_outputs.project_id}.${local.shared_outputs.bigquery_dataset_id}.integrated_event"
      }
      env {
        name  = "SERVICE_NAME"
        value = var.service_name
      }
      env {
        name  = "LOG_LEVEL"
        value = var.log_level
      }
      env {
        name  = "BATCH_SIZE"
        value = tostring(var.batch_size)
      }
      env {
        name  = "MINUTES_PER_SEGMENT"
        value = tostring(var.minutes_per_segment)
      }
      env {
        name  = "TASK_QUEUE_NAME"
        value = google_cloud_tasks_queue.segment_processing_queue.name
      }
      env {
        name  = "WORKER_URL_PATH"
        value = "/process-segment"
      }
      env {
        name  = "GCP_LOCATION"
        value = local.shared_outputs.region
      }
      env {
        name  = "GOOGLE_SERVICE_ACCOUNT_EMAIL"
        value = local.shared_outputs.service_account_email
      }
      env {
        name  = "GUNICORN_TIMEOUT"
        value = tostring(var.gunicorn_timeout)
      }
      env {
        name  = "GUNICORN_WORKERS"
        value = tostring(var.gunicorn_workers)
      }
      env {
        name  = "USE_OPTIMIZED_MERGE"
        value = tostring(var.use_optimized_merge)
      }

      env {
        name  = "ENABLE_COST_TRACKING"
        value = tostring(var.enable_cost_tracking)
      }

      env {
        name  = "ENABLE_IDEMPOTENCY_CHECK"
        value = tostring(var.enable_idempotency_check)
      }

      # 🚀 普通模式配置：polars 本地處理 + 平行處理
      env {
        name  = "USE_POLARS"
        value = "true"
      }
      env {
        name  = "ENABLE_PARALLEL_PROCESSING"
        value = "true"
      }
      env {
        name  = "ENABLE_DASHBOARD"
        value = "false" # 停用儀表板以提升效能
      }

      env {
        name  = "EXCLUDED_EVENT_TYPES"
        value = jsonencode(var.excluded_event_types)
      }

      env {
        name  = "STREAMING_BUFFER_OFFSET_HOURS"
        value = tostring(var.streaming_buffer_offset_hours)
      }

      env {
        name  = "SYNC_OVERLAP_MINUTES"
        value = tostring(var.sync_overlap_minutes)
      }

      dynamic "env" {
        for_each = var.secret_key != null ? ["SECRET_KEY"] : []
        content {
          name = env.value
          value_source {
            secret_key_ref {
              secret  = google_secret_manager_secret.secret_key[0].secret_id
              version = "latest"
            }
          }
        }
      }

      dynamic "env" {
        for_each = var.api_key != null ? ["API_KEY"] : []
        content {
          name = env.value
          value_source {
            secret_key_ref {
              secret  = google_secret_manager_secret.api_key[0].secret_id
              version = "latest"
            }
          }
        }
      }
    }

    scaling {
      min_instance_count = var.cloud_run_min_instances
      max_instance_count = var.cloud_run_max_instances
    }

    timeout = "${var.cloud_run_timeout}s"
  }

  traffic {
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
    percent = 100
  }

  labels = local.labels

  depends_on = [
    google_secret_manager_secret_iam_member.secret_key_accessor,
    google_secret_manager_secret_iam_member.api_key_accessor,
    google_project_iam_member.tasks_enqueuer,
  ]
}

# ====== Secure Cloud Run IAM ======
# 只允許指定的 Service Account 調用服務
resource "google_cloud_run_v2_service_iam_member" "service_account_invoker" {
  project  = google_cloud_run_v2_service.legacy_event_sync_service.project
  location = google_cloud_run_v2_service.legacy_event_sync_service.location
  name     = google_cloud_run_v2_service.legacy_event_sync_service.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${local.shared_outputs.service_account_email}"
}

# ====== Cloud Scheduler Job ======
resource "google_cloud_scheduler_job" "legacy_sync_schedule" {
  name      = "legacy-event-sync-${var.environment}"
  region    = local.shared_outputs.region
  schedule  = var.sync_schedule
  time_zone = "Asia/Taipei"
  paused    = !var.enable_scheduler # 使用 paused 屬性來控制啟用狀態

  http_target {
    uri         = "${google_cloud_run_v2_service.legacy_event_sync_service.uri}/start-sync"
    http_method = "POST"
    headers = {
      "Content-Type" = "application/json"
    }
    body = base64encode(jsonencode({
      source = "scheduler"
    }))

    # 使用現有的 integrated-event service account 進行安全認證
    oidc_token {
      service_account_email = local.shared_outputs.service_account_email
      audience              = google_cloud_run_v2_service.legacy_event_sync_service.uri
    }
  }

  retry_config {
    retry_count = 3
  }

  depends_on = [
    google_cloud_run_v2_service_iam_member.service_account_invoker
  ]
}

# ====== Monitoring ======
resource "google_monitoring_alert_policy" "cloud_run_errors" {
  display_name = "Legacy Event Sync Cloud Run Errors"
  combiner     = "OR"

  conditions {
    display_name = "Cloud Run error rate"

    condition_threshold {
      filter          = "metric.type=\"run.googleapis.com/request_count\" resource.type=\"cloud_run_revision\" resource.labels.service_name=\"${google_cloud_run_v2_service.legacy_event_sync_service.name}\" metric.labels.response_code_class=\"5xx\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0.1

      duration = "300s"

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = [local.shared_outputs.notification_channel_id]

  alert_strategy {
    auto_close = "604800s" # 7 days
  }
}

# ====== Cost Monitoring ======
# 基本的成本告警 - 當月費用超過 $50 時通知
resource "google_billing_budget" "service_budget" {
  count = var.environment == "prod" && var.billing_account_id != null ? 1 : 0

  billing_account = var.billing_account_id
  display_name    = "Legacy Event Sync - Monthly Budget"

  budget_filter {
    projects = ["projects/${local.shared_outputs.project_id}"]
    labels = {
      app = "legacy-event-sync"
    }
  }

  amount {
    specified_amount {
      currency_code = "USD"
      units         = "50"
    }
  }

  threshold_rules {
    threshold_percent = 0.8 # 80% 時警告
    spend_basis       = "CURRENT_SPEND"
  }

  threshold_rules {
    threshold_percent = 1.0 # 100% 時警告
    spend_basis       = "FORECASTED_SPEND"
  }

  all_updates_rule {
    monitoring_notification_channels = [local.shared_outputs.notification_channel_id]
  }
}

# ====== Outputs ======
# 輸出重要的資源資訊，方便後續查詢與驗證

output "cloud_run_service_name" {
  description = "The name of the Cloud Run service."
  value       = google_cloud_run_v2_service.legacy_event_sync_service.name
}

output "cloud_run_service_uri" {
  description = "The URI of the Cloud Run service."
  value       = google_cloud_run_v2_service.legacy_event_sync_service.uri
}

output "cloud_scheduler_job_name" {
  description = "The name of the Cloud Scheduler job."
  value       = google_cloud_scheduler_job.legacy_sync_schedule.name
}

output "cloud_tasks_queue_name" {
  description = "The name of the Cloud Tasks queue."
  value       = google_cloud_tasks_queue.segment_processing_queue.name
}

output "scheduler_service_account_email" {
  description = "The email of the service account used by Cloud Scheduler."
  value       = local.shared_outputs.service_account_email
}

output "deployment_version" {
  description = "Currently deployed version of the service."
  value       = var.deployment_version
}

output "security_status" {
  description = "Security configuration status."
  value = {
    public_access_removed = "✅ Service no longer accepts public requests"
    scheduler_auth        = "✅ Cloud Scheduler uses OIDC authentication"
    cost_monitoring       = var.environment == "prod" && var.billing_account_id != null ? "✅ Cost monitoring enabled" : "⚠️ Cost monitoring disabled"
  }
}
