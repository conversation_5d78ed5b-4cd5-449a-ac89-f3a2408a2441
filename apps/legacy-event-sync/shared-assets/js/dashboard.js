/**
 * 🚀 Integrated Event Platform - 共用儀表板 JavaScript
 *
 * 提供即時更新、圖表管理、和使用者互動功能
 */

class DashboardManager {
    constructor() {
        this.refreshInterval = 30000; // 30秒自動重新整理
        this.charts = {};
        this.lastUpdate = new Date();
        this.isAutoRefreshEnabled = true;

        this.init();
    }

    init() {
        this.setupAutoRefresh();
        this.setupEventListeners();
        this.initializeCharts();
        this.startRealTimeUpdates();

        console.log('📊 儀表板管理器已初始化');
    }

    /**
     * 設定自動重新整理
     */
    setupAutoRefresh() {
        // 建立控制按鈕
        this.createRefreshControls();

        // 啟動自動重新整理
        if (this.isAutoRefreshEnabled) {
            this.startAutoRefresh();
        }
    }

    createRefreshControls() {
        const header = document.querySelector('.header');
        if (!header) return;

        const controls = document.createElement('div');
        controls.className = 'refresh-controls';
        controls.innerHTML = `
            <div style="margin-top: 15px;">
                <button id="refreshBtn" class="btn btn-primary">🔄 立即更新</button>
                <button id="toggleAutoRefresh" class="btn btn-secondary">
                    ${this.isAutoRefreshEnabled ? '⏸️ 暫停自動更新' : '▶️ 啟用自動更新'}
                </button>
                <span class="refresh-timer">下次更新：<span id="countdown">30</span>秒</span>
            </div>
        `;

        header.appendChild(controls);

        // 添加按鈕事件
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshDashboard();
        });

        document.getElementById('toggleAutoRefresh').addEventListener('click', () => {
            this.toggleAutoRefresh();
        });
    }

    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        let countdown = this.refreshInterval / 1000;
        const countdownElement = document.getElementById('countdown');

        // 倒數計時器
        this.countdownTimer = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }

            if (countdown <= 0) {
                if (this.isAutoRefreshEnabled) {
                    this.refreshDashboard();
                }
                countdown = this.refreshInterval / 1000;
            }
        }, 1000);
    }

    toggleAutoRefresh() {
        this.isAutoRefreshEnabled = !this.isAutoRefreshEnabled;
        const button = document.getElementById('toggleAutoRefresh');

        if (this.isAutoRefreshEnabled) {
            button.innerHTML = '⏸️ 暫停自動更新';
            this.startAutoRefresh();
        } else {
            button.innerHTML = '▶️ 啟用自動更新';
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
            }
        }
    }

    refreshDashboard() {
        console.log('🔄 正在重新整理儀表板...');

        // 顯示載入指示器
        this.showLoadingIndicator();

        // 重新載入頁面
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    showLoadingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'loading-overlay';
        indicator.innerHTML = `
            <div class="loading-spinner">
                <div class="loading"></div>
                <p>正在更新資料...</p>
            </div>
        `;

        document.body.appendChild(indicator);
    }

    /**
     * 設定事件監聽器
     */
    setupEventListeners() {
        // 頁面可見性變更
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 頁面隱藏時暫停更新
                this.pauseUpdates();
            } else {
                // 頁面顯示時恢復更新
                this.resumeUpdates();
            }
        });

        // 滑鼠懸停事件
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', this.handleCardHover);
            card.addEventListener('mouseleave', this.handleCardLeave);
        });

        // 響應式圖表調整
        window.addEventListener('resize', this.handleWindowResize.bind(this));
    }

    handleCardHover(event) {
        event.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
        event.currentTarget.style.boxShadow = '0 20px 50px rgba(0, 0, 0, 0.2)';
    }

    handleCardLeave(event) {
        event.currentTarget.style.transform = 'translateY(0) scale(1)';
        event.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
    }

    handleWindowResize() {
        // 調整圖表大小
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    /**
     * 初始化圖表
     */
    initializeCharts() {
        this.initializeCostChart();
        this.initializeThroughputChart();
    }

    initializeCostChart() {
        const canvas = document.getElementById('costTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.costTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: window.chartData?.cost_labels || [],
                datasets: [{
                    label: '累積成本 (USD)',
                    data: window.chartData?.cost_values || [],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#e74c3c',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: { size: 14, weight: 'bold' },
                            color: '#2c3e50'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#e74c3c',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: { size: 12 },
                            callback: function(value) {
                                return '$' + value.toFixed(4);
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: { size: 12 }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    initializeThroughputChart() {
        const canvas = document.getElementById('throughputChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.throughput = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: window.chartData?.throughput_labels || [],
                datasets: [{
                    label: '每分鐘處理事件數',
                    data: window.chartData?.throughput_values || [],
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: '#3498db',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: { size: 14, weight: 'bold' },
                            color: '#2c3e50'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return `處理量: ${context.parsed.y.toLocaleString()} 事件/分鐘`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: { size: 12 },
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: { size: 12 }
                        }
                    }
                }
            }
        });
    }

    /**
     * 啟動即時更新
     */
    startRealTimeUpdates() {
        // 更新時間戳記
        this.updateTimestamp();

        // 動畫效果
        this.animateNumbers();

        // 設定進度條動畫
        this.animateProgressBars();
    }

    updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 更新頁面上的時間戳記
        const timestampElements = document.querySelectorAll('.timestamp, .last-updated');
        timestampElements.forEach(element => {
            element.textContent = timeString;
        });
    }

    animateNumbers() {
        document.querySelectorAll('.big-number').forEach(element => {
            const finalValue = parseFloat(element.textContent.replace(/[^0-9.-]/g, ''));
            if (isNaN(finalValue)) return;

            let currentValue = 0;
            const increment = finalValue / 30; // 30 frames animation
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }

                // 更新顯示值
                if (element.textContent.includes('$')) {
                    element.textContent = '$' + currentValue.toFixed(4);
                } else if (element.textContent.includes('m')) {
                    element.textContent = Math.round(currentValue) + 'm';
                } else {
                    element.textContent = Math.round(currentValue).toLocaleString();
                }
            }, 16); // ~60fps
        });
    }

    animateProgressBars() {
        document.querySelectorAll('.progress-fill').forEach(progressBar => {
            const targetWidth = progressBar.style.width;
            progressBar.style.width = '0%';

            setTimeout(() => {
                progressBar.style.width = targetWidth;
            }, 500);
        });
    }

    pauseUpdates() {
        console.log('⏸️ 儀表板更新已暫停');
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }
    }

    resumeUpdates() {
        console.log('▶️ 儀表板更新已恢復');
        if (this.isAutoRefreshEnabled) {
            this.startAutoRefresh();
        }
    }

    /**
     * 清理資源
     */
    destroy() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        Object.values(this.charts).forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });

        console.log('🧹 儀表板管理器已清理');
    }
}

/**
 * 工具函數
 */
const DashboardUtils = {
    // 格式化數字
    formatNumber(num, decimals = 0) {
        return Number(num).toLocaleString('zh-TW', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },

    // 格式化成本
    formatCost(cost) {
        if (cost < 0.001) {
            return '$' + (cost * 1000).toFixed(2) + ' (千分之一)';
        }
        return '$' + cost.toFixed(4);
    },

    // 格式化時間
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    },

    // 取得成本等級顏色
    getCostLevelColor(cost) {
        if (cost < 0.01) return '#27ae60'; // 綠色 - 極低成本
        if (cost < 0.1) return '#f39c12';  // 橙色 - 低成本
        if (cost < 1.0) return '#e67e22';  // 橙紅色 - 中等成本
        return '#e74c3c';                  // 紅色 - 高成本
    }
};

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();

    // 暴露工具函數到全域
    window.DashboardUtils = DashboardUtils;

    console.log('🚀 儀表板已完全載入');
});

// 頁面卸載前清理
window.addEventListener('beforeunload', () => {
    if (window.dashboardManager) {
        window.dashboardManager.destroy();
    }
});
