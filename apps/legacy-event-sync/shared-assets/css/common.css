/* 🎨 Integrated Event Platform - 共用樣式表 */

/* === 基礎重置 === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* === 主要佈局 === */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* === 標題樣式 === */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header .subtitle {
    color: #7f8c8d;
    font-size: 16px;
    font-weight: 500;
}

/* === 卡片系統 === */
.card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
}

.card h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #34495e;
    font-weight: 600;
}

/* === 數字顯示 === */
.big-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 15px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cost-card .big-number {
    color: #e74c3c;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.progress-card .big-number {
    color: #27ae60;
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.events-card .big-number {
    color: #3498db;
    background: linear-gradient(45deg, #3498db, #2980b9);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.time-card .big-number {
    color: #9b59b6;
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* === 網格系統 === */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 40px;
}

/* === 進度條 === */
.progress-bar {
    width: 100%;
    height: 12px;
    background: #ecf0f1;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71, #1abc9c);
    border-radius: 20px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

/* === 狀態指示器 === */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-success {
    background: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.status-warning {
    background: #f39c12;
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
}

.status-error {
    background: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(39, 174, 96, 0); }
    100% { box-shadow: 0 0 0 0 rgba(39, 174, 96, 0); }
}

/* === 詳細資訊區域 === */
.details-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.detail-item {
    padding: 15px 0;
    border-bottom: 1px solid rgba(236, 240, 241, 0.5);
    transition: all 0.3s ease;
}

.detail-item:hover {
    background: rgba(52, 152, 219, 0.05);
    padding-left: 10px;
    border-radius: 8px;
}

.detail-label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 5px;
}

.detail-value {
    color: #7f8c8d;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 14px;
}

/* === 圖表容器 === */
.chart-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.chart-card:hover {
    transform: scale(1.02);
}

/* === 響應式設計 === */
@media (max-width: 1024px) {
    .charts-container {
        grid-template-columns: 1fr;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .big-number {
        font-size: 2rem;
    }

    .card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .big-number {
        font-size: 1.8rem;
    }

    .header .subtitle {
        font-size: 14px;
    }
}

/* === 動畫效果 === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* === 載入指示器 === */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === 工具提示 === */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #2c3e50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip:hover::after {
    opacity: 1;
}
