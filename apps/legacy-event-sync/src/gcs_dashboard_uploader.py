#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GCS 平台儀表板上傳器

負責將同步結果生成為靜態網頁儀表板，並上傳到 GCS bucket 供團隊存取。
支援即時更新、歷史版本管理、和多應用共享架構。
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import tempfile
from pathlib import Path

import pytz
from google.cloud import storage
from jinja2 import Template, Environment, FileSystemLoader

logger = logging.getLogger(__name__)


class PlatformDashboardUploader:
    """GCS 平台儀表板上傳器"""

    def __init__(self,
                 bucket_name: str,
                 app_name: str = "legacy-event-sync",
                 timezone: str = "Asia/Taipei"):
        """
        初始化儀表板上傳器

        Args:
            bucket_name: GCS bucket 名稱
            app_name: 應用程式名稱，用於建立目錄結構
            timezone: 時區設定
        """
        self.bucket_name = bucket_name
        self.app_name = app_name
        self.timezone = pytz.timezone(timezone)

        # 初始化 GCS 客戶端
        self.client = storage.Client()
        self.bucket = self.client.bucket(bucket_name)

        # 模板目錄路徑
        self.templates_dir = Path(__file__).parent.parent / "templates"
        self.shared_assets_dir = Path(__file__).parent.parent / "shared-assets"

        logger.info(f"📊 儀表板上傳器已初始化：bucket={bucket_name}, app={app_name}")

    def upload_dashboard(self,
                        sync_data: Dict[str, Any],
                        template_name: str = "dashboard_template.html") -> str:
        """
        生成並上傳儀表板

        Args:
            sync_data: 同步結果資料
            template_name: 模板檔案名稱

        Returns:
            儀表板的公開 URL
        """
        try:
            # 1. 準備儀表板資料
            dashboard_data = self._prepare_dashboard_data(sync_data)

            # 2. 生成 HTML 內容
            html_content = self._generate_dashboard_html(dashboard_data, template_name)

            # 3. 上傳共用資源（如果需要）
            self._ensure_shared_assets_uploaded()

            # 4. 上傳儀表板檔案
            dashboard_url = self._upload_dashboard_files(html_content, dashboard_data)

            logger.info(f"✅ 儀表板已成功上傳：{dashboard_url}")
            return dashboard_url

        except Exception as e:
            logger.error(f"❌ 儀表板上傳失敗：{e}")
            raise

    def _prepare_dashboard_data(self, sync_data: Dict[str, Any]) -> Dict[str, Any]:
        """準備儀表板所需的資料"""
        now = datetime.now(self.timezone)

        # 基礎資料處理
        total_events = sync_data.get("total_events", 0)
        synced_events = sync_data.get("synced_events", 0)
        error_events = sync_data.get("error_events", 0)

        # 成本資料處理 - 支援多種來源
        cost_data = sync_data.get("cost_estimate", {})
        total_cost = cost_data.get("estimated_cost_usd", 0.0)
        tb_processed = cost_data.get("tb_processed", 0.0)

        # 如果有 cost_tracker_summary，優先使用其資料
        cost_tracker_summary = sync_data.get("cost_tracker_summary", {})
        if cost_tracker_summary:
            cost_summary = cost_tracker_summary.get("cost_summary", {})
            if cost_summary:
                total_cost = cost_summary.get("total_cost_usd", total_cost)
                tb_processed = cost_summary.get("total_tb_processed", tb_processed)

        # 進度計算
        progress_percentage = (synced_events / total_events * 100) if total_events > 0 else 0

        # 狀態判斷
        if error_events == 0:
            status_class = "success"
            status_text = "正常運行"
        elif error_events < total_events * 0.1:  # 錯誤率 < 10%
            status_class = "warning"
            status_text = "部分異常"
        else:
            status_class = "error"
            status_text = "執行異常"

        # 成本等級
        cost_level = self._get_cost_level(total_cost)

        # 時間範圍處理
        time_range = self._extract_time_range(sync_data)

        # 執行時間計算
        execution_time = self._calculate_execution_time(sync_data)

        # 圖表資料準備
        chart_data = self._prepare_chart_data(sync_data)

        return {
            "last_updated": now.strftime("%Y-%m-%d %H:%M:%S"),
            "sync_mode": sync_data.get("sync_mode", "economical"),
            "sync_mode_desc": "🟢 經濟模式" if sync_data.get("sync_mode") == "economical" else "🔴 快速模式",
            "status_class": status_class,
            "status_text": status_text,
            "cost_data": {
                "total_cost_usd": f"{total_cost:.4f}",
                "tb_processed": f"{tb_processed:.6f}",
                "cost_level": cost_level
            },
            "events_data": {
                "total_events": f"{total_events:,}",
                "synced_events": f"{synced_events:,}",
                "error_events": f"{error_events:,}"
            },
            "progress_percentage": f"{progress_percentage:.1f}",
            "current_segment": sync_data.get("current_segment", "1"),
            "total_segments": sync_data.get("total_segments", "1"),
            "execution_time": execution_time,
            "time_range": time_range,
            "config": sync_data.get("config", {}),
            "chart_data": chart_data
        }

    def _get_cost_level(self, cost_usd: float) -> str:
        """取得成本等級描述"""
        if cost_usd < 0.01:
            return "🟢 極低成本"
        elif cost_usd < 0.1:
            return "🟡 低成本"
        elif cost_usd < 1.0:
            return "🟠 中等成本"
        else:
            return "🔴 高成本"

    def _extract_time_range(self, sync_data: Dict[str, Any]) -> Dict[str, str]:
        """提取時間範圍資訊"""
        # 從 sync_data 中提取時間資訊
        start_time = sync_data.get("start_time_str", "未知")
        end_time = sync_data.get("end_time_str", "未知")

        return {
            "start_time": start_time,
            "end_time": end_time
        }

    def _calculate_execution_time(self, sync_data: Dict[str, Any]) -> Dict[str, str]:
        """計算執行時間"""
        execution_seconds = sync_data.get("execution_time_seconds", 0)
        total_minutes = int(execution_seconds // 60)
        remaining_seconds = int(execution_seconds % 60)

        return {
            "total_minutes": str(total_minutes),
            "remaining_seconds": str(remaining_seconds),
            "formatted": f"{total_minutes}m {remaining_seconds}s"
        }

    def _prepare_chart_data(self, sync_data: Dict[str, Any]) -> Dict[str, List]:
        """準備圖表資料"""
        # 模擬圖表資料（實際專案中應該從真實資料產生）
        segments = sync_data.get("segments_results", [])

        cost_labels = []
        cost_values = []
        throughput_labels = []
        throughput_values = []

        cumulative_cost = 0.0

        for i, segment in enumerate(segments[:10]):  # 限制顯示最多10個分段
            segment_id = segment.get("segment_id", f"分段{i+1}")
            segment_cost = segment.get("cost_estimate", {}).get("estimated_cost_usd", 0)
            segment_events = segment.get("total_events", 0)

            cumulative_cost += segment_cost

            cost_labels.append(segment_id)
            cost_values.append(round(cumulative_cost, 6))

            throughput_labels.append(segment_id)
            # 假設每分段為1小時，計算每分鐘處理量
            throughput_values.append(round(segment_events / 60, 2) if segment_events > 0 else 0)

        # 如果沒有分段資料，提供預設值
        if not cost_labels:
            cost_labels = ["開始"]
            cost_values = [sync_data.get("cost_estimate", {}).get("estimated_cost_usd", 0)]
            throughput_labels = ["處理中"]
            throughput_values = [sync_data.get("total_events", 0) / 60]

        return {
            "cost_labels": json.dumps(cost_labels),
            "cost_values": json.dumps(cost_values),
            "throughput_labels": json.dumps(throughput_labels),
            "throughput_values": json.dumps(throughput_values)
        }

    def _generate_dashboard_html(self,
                                dashboard_data: Dict[str, Any],
                                template_name: str) -> str:
        """生成儀表板 HTML"""
        try:
            template_path = self.templates_dir / template_name

            if not template_path.exists():
                raise FileNotFoundError(f"模板檔案不存在：{template_path}")

            # 載入模板
            env = Environment(loader=FileSystemLoader(str(self.templates_dir)))
            template = env.get_template(template_name)

            # 渲染模板
            html_content = template.render(**dashboard_data)

            logger.debug(f"✅ HTML 模板渲染完成，長度：{len(html_content)} 字符")
            return html_content

        except Exception as e:
            logger.error(f"❌ HTML 生成失敗：{e}")
            # 提供降級 HTML
            return self._generate_fallback_html(dashboard_data)

    def _generate_fallback_html(self, dashboard_data: Dict[str, Any]) -> str:
        """生成降級版本的簡單 HTML"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-TW">
        <head>
            <meta charset="UTF-8">
            <title>Legacy Event Sync 儀表板</title>
            <style>
                body {{ font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }}
                .metric {{ display: inline-block; margin: 10px 20px; text-align: center; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
                .metric-label {{ font-size: 14px; color: #7f8c8d; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📊 Legacy Event Sync 儀表板</h1>
                <p>最後更新：{dashboard_data.get('last_updated', 'N/A')}</p>

                <div class="metric">
                    <div class="metric-value">{dashboard_data.get('events_data', {}).get('total_events', '0')}</div>
                    <div class="metric-label">總事件數</div>
                </div>

                <div class="metric">
                    <div class="metric-value">{dashboard_data.get('cost_data', {}).get('total_cost_usd', '0.0000')}</div>
                    <div class="metric-label">成本 (USD)</div>
                </div>

                <div class="metric">
                    <div class="metric-value">{dashboard_data.get('progress_percentage', '0')}%</div>
                    <div class="metric-label">完成進度</div>
                </div>

                <p><strong>狀態：</strong>{dashboard_data.get('status_text', '未知')}</p>
                <p><strong>模式：</strong>{dashboard_data.get('sync_mode_desc', '未知')}</p>
            </div>
        </body>
        </html>
        """
        return html

    def _ensure_shared_assets_uploaded(self):
        """確保共用資源已上傳到 GCS"""
        try:
            # 檢查 CSS 檔案
            css_path = "shared-assets/css/common.css"
            if not self._blob_exists(css_path):
                self._upload_shared_asset("css/common.css", "text/css")

            # 檢查 JS 檔案
            js_path = "shared-assets/js/dashboard.js"
            if not self._blob_exists(js_path):
                self._upload_shared_asset("js/dashboard.js", "application/javascript")

            logger.debug("✅ 共用資源檢查完成")

        except Exception as e:
            logger.warning(f"⚠️ 共用資源上傳失敗：{e}")

    def _blob_exists(self, blob_path: str) -> bool:
        """檢查 blob 是否存在"""
        try:
            blob = self.bucket.blob(blob_path)
            return blob.exists()
        except Exception:
            return False

    def _upload_shared_asset(self, asset_path: str, content_type: str):
        """上傳共用資源檔案"""
        local_path = self.shared_assets_dir / asset_path
        gcs_path = f"shared-assets/{asset_path}"

        if local_path.exists():
            blob = self.bucket.blob(gcs_path)
            blob.upload_from_filename(str(local_path), content_type=content_type)
            logger.debug(f"📤 已上傳共用資源：{gcs_path}")
        else:
            logger.warning(f"⚠️ 共用資源檔案不存在：{local_path}")

    def _upload_dashboard_files(self,
                               html_content: str,
                               dashboard_data: Dict[str, Any]) -> str:
        """上傳儀表板檔案並返回 URL"""
        timestamp = datetime.now(self.timezone).strftime("%Y%m%d_%H%M%S")

        # 1. 上傳最新版本
        latest_path = f"{self.app_name}/dashboard/index.html"
        self._atomic_upload(html_content, latest_path, "text/html")

        # 2. 備份歷史版本
        archive_path = f"{self.app_name}/dashboard/archive/{timestamp}.html"
        self._upload_blob(html_content, archive_path, "text/html")

        # 3. 上傳 JSON 資料 API
        json_data = json.dumps(dashboard_data, ensure_ascii=False, indent=2)
        api_path = f"{self.app_name}/api/latest.json"
        self._atomic_upload(json_data, api_path, "application/json")

        # 4. 備份 JSON 歷史
        json_archive_path = f"{self.app_name}/api/archive/{timestamp}.json"
        self._upload_blob(json_data, json_archive_path, "application/json")

        # 返回公開 URL
        dashboard_url = f"https://storage.googleapis.com/{self.bucket_name}/{latest_path}"
        return dashboard_url

    def _atomic_upload(self, content: str, blob_path: str, content_type: str):
        """原子性上傳（避免讀取到部分內容）"""
        # 先上傳到臨時位置
        temp_path = f"{blob_path}.tmp"
        temp_blob = self.bucket.blob(temp_path)
        temp_blob.upload_from_string(content, content_type=content_type)

        # 重命名為最終位置
        final_blob = self.bucket.blob(blob_path)
        temp_blob.reload()  # 確保 metadata 是最新的
        final_blob.upload_from_string(content, content_type=content_type)

        # 刪除臨時檔案
        temp_blob.delete()

        logger.debug(f"📤 原子性上傳完成：{blob_path}")

    def _upload_blob(self, content: str, blob_path: str, content_type: str):
        """一般上傳"""
        blob = self.bucket.blob(blob_path)
        blob.upload_from_string(content, content_type=content_type)
        logger.debug(f"📤 檔案上傳完成：{blob_path}")

    def cleanup_old_files(self, days_to_keep: int = 30):
        """清理舊檔案"""
        try:
            cutoff_date = datetime.now(pytz.utc) - timedelta(days=days_to_keep)

            # 清理歷史儀表板檔案
            archive_prefix = f"{self.app_name}/dashboard/archive/"
            self._cleanup_blobs_by_date(archive_prefix, cutoff_date)

            # 清理歷史 JSON 檔案
            json_archive_prefix = f"{self.app_name}/api/archive/"
            self._cleanup_blobs_by_date(json_archive_prefix, cutoff_date)

            logger.info(f"🧹 舊檔案清理完成，保留 {days_to_keep} 天內的檔案")

        except Exception as e:
            logger.warning(f"⚠️ 舊檔案清理失敗：{e}")

    def _cleanup_blobs_by_date(self, prefix: str, cutoff_date: datetime):
        """根據日期清理 blobs"""
        blobs = self.bucket.list_blobs(prefix=prefix)
        deleted_count = 0

        for blob in blobs:
            if blob.time_created and blob.time_created < cutoff_date:
                blob.delete()
                deleted_count += 1
                logger.debug(f"🗑️ 已刪除舊檔案：{blob.name}")

        if deleted_count > 0:
            logger.info(f"🧹 清理了 {deleted_count} 個舊檔案，前綴：{prefix}")

    def get_dashboard_url(self, latest: bool = True) -> str:
        """取得儀表板 URL"""
        if latest:
            path = f"{self.app_name}/dashboard/index.html"
        else:
            # 返回目錄列表 URL（如果需要瀏覽歷史版本）
            path = f"{self.app_name}/dashboard/"

        return f"https://storage.googleapis.com/{self.bucket_name}/{path}"

    def get_api_url(self) -> str:
        """取得 API 資料 URL"""
        path = f"{self.app_name}/api/latest.json"
        return f"https://storage.googleapis.com/{self.bucket_name}/{path}"


def create_dashboard_uploader(bucket_name: str = None,
                            app_name: str = "legacy-event-sync") -> PlatformDashboardUploader:
    """建立儀表板上傳器實例"""
    if not bucket_name:
        # 從環境變數取得 bucket 名稱
        bucket_name = os.getenv('PLATFORM_DASHBOARD_BUCKET')
        if not bucket_name:
            raise ValueError("未設定 PLATFORM_DASHBOARD_BUCKET 環境變數")

    return PlatformDashboardUploader(bucket_name, app_name)
