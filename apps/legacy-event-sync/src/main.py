"""
Legacy Event Sync Service - Cloud Run Worker/Coordinator

This service, running on Cloud Run, synchronizes data from the legacy
`tagtoo_event` table to the unified `integrated_event` table.

It operates in two modes:
- Coordinator (/start-sync): Triggered by Cloud Scheduler, it determines the
  time range to sync and creates tasks for each segment.
- Worker (/process-segment): Triggered by Cloud Tasks, it processes a single
  time segment of data.
"""

import json
import logging
import os
import re
from datetime import datetime, timedelta
import pytz
import polars as pl
import concurrent.futures

# 讀取服務版本資訊
try:
    from config import config as app_config  # noqa: E402  # 延遲導入避免循環
    SERVICE_VERSION = getattr(app_config.service, "version", "unknown")
except Exception:  # pragma: no cover
    SERVICE_VERSION = "unknown"

# 儲存於建置階段的 12 位 commit (由 CI/CD 傳入環境變數)
SERVICE_COMMIT = os.getenv("COMMIT_SHA") or os.getenv("SERVICE_COMMIT") or "unknown"

from typing import Any, Dict, List, Optional

from flask import Flask, jsonify, request
from google.cloud import bigquery, firestore, tasks_v2
from google.cloud.exceptions import GoogleCloudError, NotFound

# GCS 儀表板上傳器
# from gcs_dashboard_uploader import create_dashboard_uploader

# --- Logging Configuration ---
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# --- Environment Configuration ---
PROJECT_ID = os.environ.get("PROJECT_ID", "tagtoo-tracking")
ENVIRONMENT = os.environ.get("ENVIRONMENT", "dev")
GCP_LOCATION = os.environ.get("GCP_LOCATION", "asia-east1")

SOURCE_TABLE = os.environ.get("SOURCE_TABLE", "tagtoo-tracking.event_prod.tagtoo_event")
TARGET_TABLE_DATASET = os.environ.get("BIGQUERY_DATASET", "event_test")
TARGET_TABLE = os.environ.get(
    "TARGET_TABLE", f"{PROJECT_ID}.{TARGET_TABLE_DATASET}.integrated_event"
)

TASK_QUEUE_NAME = os.environ.get("TASK_QUEUE_NAME")
WORKER_URL = os.environ.get("WORKER_URL")
WORKER_URL_PATH = os.environ.get("WORKER_URL_PATH", "/process-segment")
BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "5000"))  # 統一批次大小，與 manual_sync.py 保持一致

# MINUTES_PER_SEGMENT 支援任意正 float（如 3 代表 3 分鐘）
def _parse_minutes_per_segment():
    val = os.environ.get("MINUTES_PER_SEGMENT", "5")
    try:
        fval = float(val)
        if fval <= 0:
            return 5.0
        return fval
    except Exception:
        return 5.0
MINUTES_PER_SEGMENT = _parse_minutes_per_segment()

def _parse_scheduler_interval_minutes():
    """
    自動推算 Cloud Scheduler cron 間隔（分鐘）。
    若無法自動推算，預設 60 分鐘。
    """
    cron = os.environ.get("SYNC_SCHEDULE")
    if not cron:
        return 60
    # 僅支援常見格式："0 * * * *"、"*/5 * * * *"、"15 2 * * *" 等
    parts = cron.strip().split()
    if len(parts) != 5:
        return 60
    minute, hour, *_ = parts
    # 1. "*/N * * * *" 代表每 N 分鐘
    m = re.match(r"\*/(\d+)", minute)
    if m:
        return int(m.group(1))
    # 2. "0 * * * *" 代表每小時
    if minute == "0" and hour == "*":
        return 60
    # 3. "M H * * *" 代表每天某時刻，間隔 1440 分鐘
    if minute.isdigit() and hour.isdigit():
        return 1440
    # 4. 其他複雜格式 fallback
    return 60

SCHEDULER_INTERVAL_MINUTES = _parse_scheduler_interval_minutes()

def _parse_excluded_event_types():
    """
    解析需要排除的事件類型清單。
    環境變數 EXCLUDED_EVENT_TYPES 應為 JSON 格式的字串陣列。
    範例: ["focus"] 或 ["focus", "scroll"]
    """
    excluded_str = os.environ.get("EXCLUDED_EVENT_TYPES", '["focus"]')
    try:
        excluded_list = json.loads(excluded_str)
        if isinstance(excluded_list, list):
            return excluded_list
        else:
            logger.warning(f"EXCLUDED_EVENT_TYPES 不是陣列格式，使用預設值: {excluded_str}")
            return ["focus"]
    except json.JSONDecodeError as e:
        logger.warning(f"無法解析 EXCLUDED_EVENT_TYPES: {e}，使用預設值")
        return ["focus"]

EXCLUDED_EVENT_TYPES = _parse_excluded_event_types()


class BigQueryCostTracker:
    """
    BigQuery 成本追踪器

    追踪 BigQuery 查詢的成本、執行時間和資源使用情況，
    提供詳細的成本分析和最佳化建議。
    """

    def __init__(self):
        """初始化成本追踪器"""
        self.costs = []
        self.total_cost_usd = 0.0
        self.total_bytes_processed = 0
        self.total_execution_time = 0.0
        self.start_time = datetime.utcnow()

        # 成本閾值設定
        self.cost_thresholds = {
            "warning": 0.1,   # $0.10 USD - 中等成本警告
            "high": 0.5,      # $0.50 USD - 高成本警告
            "critical": 2.0   # $2.00 USD - 關鍵成本警告
        }

    def track_operation(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float, segment_info: Optional[str] = None):
        """
        追踪單個 BigQuery 操作的成本

        Args:
            operation_name: 操作名稱（如 'get_events_to_sync', 'merge_operation'）
            cost_estimate: 成本估算結果（來自 estimate_query_cost）
            execution_time: 實際執行時間（秒）
            segment_info: 時間段資訊（可選，用於識別具體的時間範圍）
        """
        cost_usd = cost_estimate.get("estimated_cost_usd", 0)
        bytes_processed = cost_estimate.get("bytes_processed", 0)
        tb_processed = cost_estimate.get("tb_processed", 0)

        cost_info = {
            "operation": operation_name,
            "segment": segment_info,
            "timestamp": datetime.utcnow().isoformat(),
            "cost_usd": cost_usd,
            "bytes_processed": bytes_processed,
            "tb_processed": tb_processed,
            "execution_time_seconds": execution_time,
            "query_slots": cost_estimate.get("query_slots", 0),
            "cost_level": self._classify_cost_level(cost_usd)
        }

        # 記錄錯誤資訊（如果有）
        if "error" in cost_estimate:
            cost_info["error"] = cost_estimate["error"]

        self.costs.append(cost_info)
        self.total_cost_usd += cost_usd
        self.total_bytes_processed += bytes_processed
        self.total_execution_time += execution_time

        # 即時成本警告
        if cost_usd >= self.cost_thresholds["critical"]:
            logger.error(f"🚨 關鍵成本警告: {operation_name} 花費 ${cost_usd:.4f} USD (超過 ${self.cost_thresholds['critical']} 閾值)")
        elif cost_usd >= self.cost_thresholds["high"]:
            logger.warning(f"⚠️ 高成本警告: {operation_name} 花費 ${cost_usd:.4f} USD")
        elif cost_usd >= self.cost_thresholds["warning"]:
            logger.info(f"💡 中等成本提醒: {operation_name} 花費 ${cost_usd:.4f} USD")

    def _classify_cost_level(self, cost_usd: float) -> str:
        """根據成本金額分類成本等級"""
        if cost_usd >= self.cost_thresholds["critical"]:
            return "critical"
        elif cost_usd >= self.cost_thresholds["high"]:
            return "high"
        elif cost_usd >= self.cost_thresholds["warning"]:
            return "warning"
        else:
            return "low"

    def get_summary(self) -> Dict[str, Any]:
        """
        取得詳細的成本摘要

        Returns:
            包含總成本、操作統計、最佳化建議等資訊的字典
        """
        session_duration = (datetime.utcnow() - self.start_time).total_seconds()

        # 統計各種操作類型
        operation_stats = {}
        cost_by_level = {"low": 0, "warning": 0, "high": 0, "critical": 0}

        # 🆕 統計處理模式分布
        mode_stats = {"economical": {"count": 0, "total_cost": 0, "total_savings": 0},
                     "fast": {"count": 0, "total_cost": 0},
                     "unknown": {"count": 0, "total_cost": 0}}

        for cost_info in self.costs:
            op_type = cost_info["operation"].split("(")[0]  # 取得操作類型（移除參數部分）
            if op_type not in operation_stats:
                operation_stats[op_type] = {
                    "count": 0,
                    "total_cost": 0,
                    "total_time": 0,
                    "total_bytes": 0
                }

            operation_stats[op_type]["count"] += 1
            operation_stats[op_type]["total_cost"] += cost_info["cost_usd"]
            operation_stats[op_type]["total_time"] += cost_info["execution_time_seconds"]
            operation_stats[op_type]["total_bytes"] += cost_info["bytes_processed"]

            cost_by_level[cost_info["cost_level"]] += cost_info["cost_usd"]

            # 🆕 統計處理模式
            processing_mode = "unknown"
            if "processing_mode" in cost_info:
                processing_mode = cost_info.get("processing_mode", "unknown")
            elif "economical" in cost_info["operation"]:
                processing_mode = "economical"
            elif "merge" in cost_info["operation"].lower() or "fast" in cost_info["operation"]:
                processing_mode = "fast"

            if processing_mode in mode_stats:
                mode_stats[processing_mode]["count"] += 1
                mode_stats[processing_mode]["total_cost"] += cost_info["cost_usd"]

                # 記錄節省金額（僅經濟模式）
                if processing_mode == "economical" and "cost_comparison" in cost_info:
                    savings = cost_info["cost_comparison"].get("cost_savings_usd", 0)
                    mode_stats[processing_mode]["total_savings"] += savings

        # 生成最佳化建議
        optimization_suggestions = self._generate_optimization_suggestions()

        return {
            "session_info": {
                "start_time": self.start_time.isoformat(),
                "duration_seconds": round(session_duration, 2),
                "total_operations": len(self.costs)
            },
            "cost_summary": {
                "total_cost_usd": round(self.total_cost_usd, 4),
                "total_bytes_processed": self.total_bytes_processed,
                "total_tb_processed": round(self.total_bytes_processed / (1024**4), 6),
                "total_execution_time_seconds": round(self.total_execution_time, 2),
                "average_cost_per_operation": round(self.total_cost_usd / len(self.costs), 4) if self.costs else 0,
                "cost_efficiency_score": self._calculate_efficiency_score()
            },
            "cost_breakdown": {
                "by_level": {level: round(cost, 4) for level, cost in cost_by_level.items()},
                "by_operation": {op: {
                    "count": stats["count"],
                    "total_cost_usd": round(stats["total_cost"], 4),
                    "avg_cost_usd": round(stats["total_cost"] / stats["count"], 4),
                    "total_execution_time": round(stats["total_time"], 2),
                    "total_gb_processed": round(stats["total_bytes"] / (1024**3), 3)
                } for op, stats in operation_stats.items()},
                # 🆕 處理模式統計
                "by_processing_mode": {
                    mode: {
                        "count": stats["count"],
                        "total_cost_usd": round(stats["total_cost"], 4),
                        "avg_cost_usd": round(stats["total_cost"] / stats["count"], 4) if stats["count"] > 0 else 0,
                        "total_savings_usd": round(stats.get("total_savings", 0), 4),
                        "percentage_of_operations": round((stats["count"] / len(self.costs)) * 100, 1) if self.costs else 0
                    } for mode, stats in mode_stats.items() if stats["count"] > 0
                }
            },
            "operations": self.costs,
            "optimization": {
                "suggestions": optimization_suggestions,
                "estimated_monthly_cost": round(self.total_cost_usd * 30, 2),  # 假設每日執行
                "cost_projection": self._project_costs()
            }
        }

    def _calculate_efficiency_score(self) -> float:
        """
        計算成本效率分數 (0-100)

        基於執行時間、資料處理量和成本的平衡來評估效率
        """
        if not self.costs:
            return 0.0

        # 基準指標（這些數值可以根據實際經驗調整）
        baseline_cost_per_gb = 0.005  # $5 per TB = $0.005 per GB
        baseline_time_per_gb = 2.0    # 2 秒每 GB

        total_gb = self.total_bytes_processed / (1024**3) if self.total_bytes_processed > 0 else 1
        actual_cost_per_gb = self.total_cost_usd / total_gb
        actual_time_per_gb = self.total_execution_time / total_gb

        # 計算效率（越接近基準越好）
        cost_efficiency = min(baseline_cost_per_gb / actual_cost_per_gb, 2.0) if actual_cost_per_gb > 0 else 1.0
        time_efficiency = min(baseline_time_per_gb / actual_time_per_gb, 2.0) if actual_time_per_gb > 0 else 1.0

        # 綜合分數（50% 成本效率 + 50% 時間效率）
        efficiency_score = (cost_efficiency + time_efficiency) / 2 * 50

        return round(min(efficiency_score, 100.0), 1)

    def _generate_optimization_suggestions(self) -> List[str]:
        """根據實際使用情況生成最佳化建議"""
        suggestions = []

        if self.total_cost_usd > self.cost_thresholds["high"]:
            suggestions.append("總成本較高，建議檢查查詢是否有不必要的全表掃描")

        if self.total_bytes_processed > 100 * (1024**3):  # > 100 GB
            suggestions.append("資料掃描量大，建議使用分區表和叢集表來減少掃描範圍")

        high_cost_ops = [op for op in self.costs if op["cost_level"] in ["high", "critical"]]
        if high_cost_ops:
            suggestions.append(f"發現 {len(high_cost_ops)} 個高成本操作，建議優化這些查詢")

        slow_ops = [op for op in self.costs if op["execution_time_seconds"] > 30]
        if slow_ops:
            suggestions.append(f"發現 {len(slow_ops)} 個執行緩慢的操作，建議檢查索引和查詢計畫")

        if len(self.costs) > 20:
            suggestions.append("查詢次數較多，考慮合併相似的查詢以減少 API 呼叫")

        if not suggestions:
            suggestions.append("查詢效率良好，繼續保持現有的最佳化策略")

        return suggestions

    def _project_costs(self) -> Dict[str, Any]:
        """預測未來成本趨勢"""
        if not self.costs:
            return {"daily": 0, "monthly": 0, "yearly": 0}

        # 基於當前會話的平均成本推算
        session_hours = (datetime.utcnow() - self.start_time).total_seconds() / 3600
        hourly_rate = self.total_cost_usd / session_hours if session_hours > 0 else 0

        return {
            "daily_estimate": round(hourly_rate * 24, 4),
            "monthly_estimate": round(hourly_rate * 24 * 30, 2),
            "yearly_estimate": round(hourly_rate * 24 * 365, 2)
        }

    def reset(self):
        """重置追踪器（用於新的會話）"""
        self.costs = []
        self.total_cost_usd = 0.0
        self.total_bytes_processed = 0
        self.total_execution_time = 0.0
        self.start_time = datetime.utcnow()


def create_bigquery_client():
    """Creates and validates a BigQuery client."""
    try:
        logger.info(f"Initializing BigQuery client for project: {PROJECT_ID}")
        client = bigquery.Client(project=PROJECT_ID)
        list(client.list_datasets(max_results=1))  # Test connection
        logger.info("✅ BigQuery client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize BigQuery client: {e}", exc_info=True)
        raise

def create_firestore_client():
    """Creates a Firestore client."""
    try:
        logger.info(f"Initializing Firestore client for project: {PROJECT_ID}")
        client = firestore.Client(project=PROJECT_ID)
        logger.info("✅ Firestore client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Firestore client: {e}", exc_info=True)
        raise

def create_tasks_client():
    """Creates a Cloud Tasks client."""
    try:
        logger.info("Initializing Cloud Tasks client.")
        client = tasks_v2.CloudTasksClient()
        logger.info("✅ Cloud Tasks client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Cloud Tasks client: {e}", exc_info=True)
        raise


class LegacyEventSyncProcessor:
    """Handles the logic for the event synchronization."""

    def __init__(self, source_table: str, target_table: str, task_queue_name: Optional[str] = None, worker_url: Optional[str] = None, enable_deduplication: Optional[bool] = None):
        """Initializes the processor with configuration and lazy-loaded clients."""
        self.source_table = source_table
        self.target_table = target_table
        self.task_queue_name = task_queue_name or TASK_QUEUE_NAME
        self.worker_url = worker_url or WORKER_URL or self._build_worker_url()

        # 🎛️ Feature Toggle: 去重複邏輯控制
        # 預設從環境變數讀取，如果沒有設定則預設為 False (預設停用去重複)
        if enable_deduplication is None:
            self.enable_deduplication = os.environ.get("ENABLE_DEDUPLICATION", "false").lower() in ("true", "1", "yes")
        else:
            self.enable_deduplication = enable_deduplication

        # 🎛️ Feature Toggle: 冪等性檢查控制（新增）
        # 預設關閉冪等性檢查以節省成本（約64%），可透過環境變數啟用
        self.enable_idempotency_check = os.environ.get("ENABLE_IDEMPOTENCY_CHECK", "false").lower() in ("true", "1", "yes")

        logger.info(f"去重複邏輯狀態: {'啟用' if self.enable_deduplication else '停用'}")
        logger.info(f"冪等性檢查狀態: {'啟用' if self.enable_idempotency_check else '停用 (成本優化)'}")

        # 🎛️ Feature Toggle: 同步模式控制（經濟模式 vs 快速模式）
        # 預設使用普通模式：polars 本地處理 + 平行處理
        self.use_polars_mode = os.environ.get("USE_POLARS", "true").lower() in ("true", "1", "yes")

        # 🚀 普通模式：polars 本地處理 + 平行處理（最佳性價比）
        if self.use_polars_mode:
            # 檢查是否啟用平行處理
            self.enable_parallel_processing = os.environ.get("ENABLE_PARALLEL_PROCESSING", "true").lower() in ("true", "1", "yes")
            if self.enable_parallel_processing:
                mode_name = "🚀 普通模式"
                cost_info = "(成本低，約 $0.055/小時，效能提升 40-60%)"
            else:
                mode_name = "🟢 經濟模式"
                cost_info = "(成本低，約 $0.055/小時)"
        else:
            mode_name = "🔴 快速模式"
            cost_info = "(成本高，約 $11.26/小時)"

        logger.info(f"同步模式: {mode_name} {cost_info}")

        self._bigquery_client = None
        self._tasks_client = None
        self._firestore_client = None
        self.last_sync_doc_ref = (
            f"legacy_sync_metadata/{self.source_table}-{self.target_table}"
        )

        # 💰 初始化 BigQuery 成本追踪器
        self.cost_tracker = None  # 將在需要時初始化
        self._enable_cost_tracking = os.environ.get("ENABLE_COST_TRACKING", "true").lower() in ("true", "1", "yes")

        if self._enable_cost_tracking:
            logger.info("💰 BigQuery 成本追踪已啟用")
        else:
            logger.info("💰 BigQuery 成本追踪已停用")

        # 📊 初始化 GCS 儀表板上傳器
        self.dashboard_uploader = None  # 將在需要時初始化
        self._enable_dashboard = os.environ.get("ENABLE_DASHBOARD", "true").lower() in ("true", "1", "yes")
        self._dashboard_bucket = os.environ.get("PLATFORM_DASHBOARD_BUCKET", "integrated-event-platform-prod-tagtoo-tracking")

        if self._enable_dashboard:
            logger.info(f"📊 GCS 儀表板已啟用，bucket: {self._dashboard_bucket}")
        else:
            logger.info("📊 GCS 儀表板已停用")

    def _build_worker_url(self) -> str:
        """Build worker URL dynamically when running in Cloud Run."""
        # This will be set dynamically in the coordinate_sync method
        # when we have access to the Flask request context
        return None

    def _ensure_cost_tracker_initialized(self):
        """確保成本追踪器已初始化（如果啟用）"""
        if self._enable_cost_tracking and self.cost_tracker is None:
            self.cost_tracker = BigQueryCostTracker()
            logger.debug("💰 初始化 BigQuery 成本追踪器")

    def _ensure_dashboard_uploader_initialized(self):
        """確保儀表板上傳器已初始化（如果啟用）"""
        if self._enable_dashboard and self.dashboard_uploader is None:
            try:
                from gcs_dashboard_uploader import create_dashboard_uploader
                self.dashboard_uploader = create_dashboard_uploader(
                    bucket_name=self._dashboard_bucket,
                    app_name="legacy-event-sync"
                )
                logger.debug("📊 初始化 GCS 儀表板上傳器")
            except Exception as e:
                logger.warning(f"⚠️ 儀表板上傳器初始化失敗: {e}")
                self._enable_dashboard = False  # 停用儀表板功能

    def _track_bigquery_operation(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float, segment_info: Optional[str] = None):
        """
        追踪 BigQuery 操作的成本（如果啟用）

        Args:
            operation_name: 操作名稱
            cost_estimate: 成本估算結果
            execution_time: 執行時間
            segment_info: 時間段資訊（可選）
        """
        if self._enable_cost_tracking:
            self._ensure_cost_tracker_initialized()
            self.cost_tracker.track_operation(operation_name, cost_estimate, execution_time, segment_info)

    def get_streaming_buffer_status(self) -> Dict[str, Any]:
        """獲取來源表格的 Streaming Buffer 狀況"""
        try:
            table = self.bigquery_client.get_table(self.source_table)

            if table.streaming_buffer is None:
                return {
                    "has_streaming_buffer": False,
                    "message": "表格沒有 Streaming Buffer 資料",
                    "safe_offset_minutes": 0
                }

            buffer = table.streaming_buffer
            current_time = datetime.utcnow()

            # 計算延遲時間
            oldest_entry = buffer.oldest_entry_time
            if oldest_entry:
                # 確保時區一致性
                if oldest_entry.tzinfo is None:
                    oldest_entry = oldest_entry.replace(tzinfo=pytz.utc)
                if current_time.tzinfo is None:
                    current_time = current_time.replace(tzinfo=pytz.utc)

                delay_seconds = (current_time - oldest_entry).total_seconds()
                delay_minutes = delay_seconds / 60

                # 建議安全偏移時間：延遲時間的 1.5 倍，但至少 30 分鐘
                safe_offset_minutes = max(30, delay_minutes * 1.5)
            else:
                delay_seconds = delay_minutes = 0
                safe_offset_minutes = 30

            return {
                "has_streaming_buffer": True,
                "estimated_bytes": buffer.estimated_bytes,
                "estimated_rows": buffer.estimated_rows,
                "oldest_entry_time": oldest_entry,
                "current_time": current_time,
                "delay_seconds": delay_seconds,
                "delay_minutes": delay_minutes,
                "estimated_size_mb": buffer.estimated_bytes / (1024 * 1024) if buffer.estimated_bytes else 0,
                "safe_offset_minutes": int(safe_offset_minutes),
                "current_offset_minutes": 120,  # 目前設定的 2 小時
                "is_safe": safe_offset_minutes <= 120,
                "recommendation": "當前偏移充足" if safe_offset_minutes <= 120 else f"建議增加偏移至 {int(safe_offset_minutes)} 分鐘"
            }

        except Exception as e:
            logger.warning(f"無法獲取 Streaming Buffer 狀況: {e}")
            return {
                "has_streaming_buffer": False,
                "error": str(e),
                "safe_offset_minutes": 120  # 保守預設值
            }

    @property
    def bigquery_client(self):
        if self._bigquery_client is None:
            self._bigquery_client = create_bigquery_client()
        return self._bigquery_client

    @property
    def tasks_client(self):
        if self._tasks_client is None:
            self._tasks_client = create_tasks_client()
        return self._tasks_client

    @property
    def firestore_client(self):
        if self._firestore_client is None:
            self._firestore_client = create_firestore_client()
        return self._firestore_client

    def get_last_sync_time(self) -> Optional[datetime]:
        """Retrieves the last successful sync time from Firestore."""
        try:
            doc = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}").get()
            if doc.exists:
                return datetime.fromisoformat(doc.to_dict()["last_sync_utc"])
        except Exception as e:
            logger.error(f"Failed to get last sync time: {e}")
        return None

    def set_last_sync_time(self, sync_time: datetime):
        """Stores the last successful sync time in Firestore."""
        try:
            doc_ref = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}")
            doc_ref.set({"last_sync_utc": sync_time.isoformat()})
        except Exception as e:
            logger.error(f"Failed to set last sync time: {e}")

    def get_events_to_sync(self, start_time: datetime, end_time: datetime):
        """Returns a BigQuery result iterator for events within a time range - memory efficient."""
        segment_info = f"{start_time.strftime('%Y%m%d%H%M')}-{end_time.strftime('%Y%m%d%H%M')}"

        # 建構事件類型過濾條件
        event_filter_condition = ""
        if EXCLUDED_EVENT_TYPES:
            excluded_list = [f"'{event_type}'" for event_type in EXCLUDED_EVENT_TYPES]
            excluded_str = ", ".join(excluded_list)
            event_filter_condition = f" AND event.name NOT IN ({excluded_str})"
            logger.info(f"過濾事件類型: {EXCLUDED_EVENT_TYPES}")

        # 🚀 移除 LIMIT + 欄位選擇優化：source table 有分區，只選取必要欄位
        time_diff_hours = (end_time - start_time).total_seconds() / 3600
        logger.debug(f"🔍 查詢範圍: {time_diff_hours:.1f} 小時，source table 有分區，使用欄位選擇優化")

        # 🎯 欄位選擇優化：只選取轉換所需的核心欄位，預期節省 70% 掃描成本 (27GB → 8.6GB)
        query = f"""
            SELECT
                permanent,
                ec_id,
                event_time,
                link,
                event.name as event_name,
                event.value as event_value,
                event.currency as event_currency,
                event.custom_data.order_id as order_id,
                event.items as event_items,
                user.em as user_em,
                user.ph as user_ph,
                location.country_code as location_country_code,
                location.region_name as location_region_name,
                location.city_name as location_city_name
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time{event_filter_condition}
            ORDER BY event_time
        """
        # 🚨 根據時間範圍動態調整成本限制
        # source table 有分區但 SELECT * 仍需較高限制
        if time_diff_hours <= 1:
            max_bytes = 50 * 1024**3  # 50GB for ≤1 hour (SELECT * 需要)
        elif time_diff_hours <= 6:
            max_bytes = 100 * 1024**3  # 100GB for ≤6 hours
        else:
            max_bytes = 200 * 1024**3  # 200GB for >6 hours

        logger.debug(f"💰 動態成本限制: {max_bytes / 1024**3:.1f}GB (基於 {time_diff_hours:.1f} 小時範圍)")

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
            ],
            use_query_cache=True,  # 啟用查詢快取以節省成本
            maximum_bytes_billed=max_bytes  # 動態成本限制
        )

        # 💰 成本估算
        cost_estimate = self.estimate_query_cost(query, job_config)

        try:
            logger.info(f"Querying events from {start_time} to {end_time}")

            # 執行查詢並追踪時間
            execution_start = datetime.utcnow()
            logger.debug(f"🔍 查詢範圍: {time_diff_hours:.1f} 小時，無 LIMIT 限制確保完整同步")
            query_job = self.bigquery_client.query(query, job_config=job_config)
            result = query_job.result()  # 返回 RowIterator，不使用 list()
            execution_time = (datetime.utcnow() - execution_start).total_seconds()

            # 💰 追踪成本
            self._track_bigquery_operation(
                "get_events_to_sync",
                cost_estimate,
                execution_time,
                segment_info
            )

            # 📊 記錄查詢結果統計
            try:
                result_count = query_job.total_rows or 0
            except AttributeError:
                # 某些 QueryJob 可能沒有 total_rows 屬性
                result_count = 0
            logger.debug(f"📊 查詢結果: {result_count} 筆事件 (時間範圍: {time_diff_hours:.1f} 小時)")

            logger.info(f"Query job created: {query_job.job_id}, Cost: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, Time: {execution_time:.2f}s")
            return result

        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            # 即使失敗也追踪成本（dry run 的成本估算）
            self._track_bigquery_operation(
                "get_events_to_sync-TABLE_NOT_FOUND",
                cost_estimate,
                0,
                segment_info
            )
            return iter([])  # 返回空迭代器
        except Exception as e:
            logger.error(f"Failed to get events from BigQuery: {e}", exc_info=True)
            # 即使失敗也追踪成本
            self._track_bigquery_operation(
                "get_events_to_sync-ERROR",
                cost_estimate,
                0,
                segment_info
            )
            raise

    def transform_event_data(self, event: bigquery.Row) -> Dict[str, Any]:
        """Transforms a single event from the source format to the target format."""

        # 🎯 向後兼容的欄位處理：支援優化查詢和原始查詢
        # 優先使用優化查詢的展開欄位，如果不存在則回退到原始結構
        event_name = event.get("event_name")
        event_value = event.get("event_value")
        event_currency = event.get("event_currency")
        order_id = event.get("order_id")
        event_items = event.get("event_items")

        # 向後兼容：如果優化欄位不存在，使用原始 event 物件
        if event_name is None:
            event_details = event.get("event")
            if isinstance(event_details, str):
                try:
                    event_details = json.loads(event_details)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode event JSON: {event_details}")
                    event_details = {}

            event_name = event_details.get("name") if event_details else None
            event_value = event_details.get("value") if event_details else None
            event_currency = event_details.get("currency") if event_details else None
            event_items = event_details.get("items") if event_details else None

            # 處理 order_id
            if order_id is None and event_details:
                custom_data = event_details.get("custom_data")
                if isinstance(custom_data, dict):
                    order_id = custom_data.get("order_id")
                elif isinstance(custom_data, str):
                    try:
                        custom_data_dict = json.loads(custom_data)
                        order_id = custom_data_dict.get("order_id")
                    except json.JSONDecodeError:
                        order_id = None

        # 🎯 向後兼容的 user 欄位處理
        user_em = event.get("user_em")
        user_ph = event.get("user_ph")

        # 向後兼容：如果優化欄位不存在，使用原始 user 物件
        if user_em is None and user_ph is None:
            user_details = event.get("user")
            if isinstance(user_details, str):
                try:
                    user_details = json.loads(user_details)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode user JSON: {user_details}")
                    user_details = {}

            if isinstance(user_details, dict):
                user_em = user_details.get("em")
                user_ph = user_details.get("ph")

        # 🎯 向後兼容的 location 欄位處理
        location_country_code = event.get("location_country_code")
        location_region_name = event.get("location_region_name")
        location_city_name = event.get("location_city_name")

        # 向後兼容：如果優化欄位不存在，使用原始 location 物件
        if not any([location_country_code, location_region_name, location_city_name]):
            location_details = event.get("location")
            if isinstance(location_details, dict):
                location_country_code = location_details.get("country_code")
                location_region_name = location_details.get("region_name")
                location_city_name = location_details.get("city_name")

        # link 欄位統一為字串，None 轉為 ''
        link_value = event.get("link")
        if link_value is None:
            link_value = ''

        # 🎯 確保資料類型與 polars schema 一致，加強類型轉換健壯性
        def safe_str_convert(value):
            """安全的字串轉換，處理各種類型"""
            if value is None:
                return None
            return str(value)

        def safe_int_convert(value):
            """安全的整數轉換"""
            if value is None:
                return None
            try:
                # 處理字串類型的數字
                if isinstance(value, str):
                    # 移除可能的空白字符
                    value = value.strip()
                    if not value:
                        return None
                return int(value)
            except (ValueError, TypeError):
                logger.warning(f"無法轉換為整數: {value}, 類型: {type(value)}")
                return None

        return {
            "permanent": safe_str_convert(event.get("permanent")),
            "ec_id": safe_int_convert(event.get("ec_id")),
            "partner_source": "legacy-tagtoo-event",
            "event_time": event.get("event_time").isoformat() if event.get("event_time") else None,
            "create_time": datetime.utcnow().isoformat(),
            "link": safe_str_convert(link_value) or "",
            "event": safe_str_convert(event_name),
            "value": float(event_value) if event_value is not None else None,
            "currency": safe_str_convert(event_currency),
            "order_id": safe_str_convert(order_id),
            "items": [
                {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "description": None,  # availability 欄位實際資料皆為空，不進行對應
                    "price": item.get("price"),
                    "quantity": item.get("quantity"),
                }
                for item in (event_items or [])
                if isinstance(item, dict)
            ],
            "user": {
                "em": user_em,
                "ph": user_ph,
            },
            "partner_id": None,
            "page": None,
            "location": (
                {
                    "country_code": location_country_code,
                    "region_name": location_region_name,
                    "city_name": location_city_name,
                }
                if any([location_country_code, location_region_name, location_city_name])
                else None
            ),
            "raw_json": None,
        }

    def _insert_to_bigquery(self, data: List[Dict[str, Any]]):
        """
        使用 MERGE 操作插入資料到 BigQuery，實現完全冪等性。
        MERGE 會自動處理重複記錄，避免資料重複。
        """
        if not data:
            return {
                "affected_rows": 0,
                "cost_estimate": {
                    "estimated_cost_usd": 0.0,
                    "tb_processed": 0.0
                }
            }

        try:
            # 使用 MERGE 操作實現冪等插入
            merge_result = self._merge_to_bigquery(data)
            logger.info(f"Successfully merged {len(data)} records using MERGE operation")
            return merge_result
        except Exception as e:
            logger.error(f"MERGE operation failed, falling back to direct insert: {e}")
            # 後備方案：使用原始的 insert_rows_json
            table = self.bigquery_client.get_table(self.target_table)
            errors = self.bigquery_client.insert_rows_json(table, data)
            if errors:
                logger.error(f"BigQuery insert errors: {errors}")
                raise GoogleCloudError(f"BigQuery insert failed: {errors}")

            # 後備方案沒有費用估算
            return {
                "affected_rows": len(data),
                "cost_estimate": {
                    "estimated_cost_usd": 0.0,
                    "tb_processed": 0.0,
                    "error": "使用後備方案，無法估算費用"
                }
            }

    def _merge_to_bigquery(self, data: List[Dict[str, Any]]):
        """
        使用 MERGE 語句實現完全冪等性資料插入

        🔍 核心問題解決：
        原始 tagtoo_event 表存在大量重複記錄（每秒 80 筆相同事件），
        我們的同步過程必須確保不會創造額外重複，同時保持資料完整性。

        🚀 MERGE 操作流程：
        1. **臨時表建立**：建立與目標表相同結構的臨時表
        2. **資料載入**：將這批資料載入臨時表
        3. **內部去重**：在臨時表內先進行去重處理
        4. **智能合併**：使用複合主鍵比對，只插入真正新的記錄
        5. **自動清理**：確保臨時資源不會殘留

        🔑 主鍵策略說明：
        我們使用 8 個欄位的組合作為唯一性判斷：
        - permanent, ec_id, event_time, event: 基本事件識別
        - partner_source, link: 確保比對正確的資料來源
        - value, currency, order_id: 避免覆蓋有價值的業務資訊

        💡 為什麼不合併「重複」的 focus 事件？
        分析顯示同秒內的 focus 事件雖然看似重複，但在 link、referrer
        等欄位上有差異，代表可能是不同的真實用戶行為，強制合併會
        損失業務價值。

        ⚡ 冪等性保證：
        - 相同資料重複處理 → 不會產生重複記錄
        - 部分重複資料 → 只插入真正新的記錄
        - 網路重試 → 結果完全一致

        Args:
            data: 要同步的資料列表，已轉換為 BigQuery 格式

        Raises:
            Exception: 當 MERGE 操作失敗時，會記錄詳細錯誤並重新拋出
        """
        if not data:
            # 🚫 空資料直接返回，避免不必要的操作
            return

        # 🎲 生成唯一的臨時表名稱（避免並發衝突）
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 毫秒精度
        temp_table_id = f"temp_legacy_sync_{timestamp}"
        temp_table_ref = f"{PROJECT_ID}.{self.target_table.split('.')[1]}.{temp_table_id}"

        try:
            # 📋 步驟 1: 建立臨時表並載入資料
            # 使用與目標表相同的 Schema，確保資料類型一致
            self._create_temp_table_and_load_data(temp_table_ref, data)

            # 🔄 步驟 2: 執行 MERGE 操作
            # 智能比對並只插入真正新的記錄
            merge_result = self._execute_merge_query(temp_table_ref)

            # ✅ 操作成功記錄
            logger.info(f"MERGE operation completed successfully for {len(data)} records")

            return merge_result

        except Exception as e:
            # ❌ 失敗處理：記錄詳細錯誤資訊
            logger.error(f"MERGE operation failed: {e}", exc_info=True)
            raise
        finally:
            # 🧹 步驟 3: 清理臨時表（無論成功與否都要執行）
            try:
                self.bigquery_client.delete_table(temp_table_ref, not_found_ok=True)
                logger.debug(f"Cleaned up temporary table: {temp_table_ref}")
            except Exception as cleanup_error:
                # ⚠️ 清理失敗不影響主流程，但要記錄警告
                logger.warning(f"Failed to cleanup temp table {temp_table_ref}: {cleanup_error}")

    def _create_temp_table_and_load_data(self, temp_table_ref: str, data: List[Dict[str, Any]]):
        """
        建立臨時表並載入批次資料

        🎯 目的：
        1. 在 BigQuery 中建立臨時表來存放這批要處理的資料
        2. 透過臨時表進行 MERGE 操作，提升效能並減少鎖定時間
        3. 臨時表會在 1 小時後自動清理

        💡 臨時表優勢：
        - 減少主表鎖定時間：MERGE 操作只針對這批資料
        - 支援複雜的去重邏輯：可在臨時表內先進行內部去重
        - 更好的查詢最佳化：BigQuery 可針對小表進行最佳化
        - 自動清理：設定過期時間避免佔用儲存空間

        📊 資料載入策略：
        - 保持與目標表相同的 Schema 結構
        - 使用 JSON 格式批次載入提升效能
        - 錯誤處理確保資料完整性

        Args:
            temp_table_ref: 臨時表的完整參考路徑
            data: 要載入的資料列表（已轉換為字典格式）

        Raises:
            GoogleCloudError: 當臨時表建立或資料載入失敗時
        """
        # 📋 獲取目標表的 Schema 定義
        # 確保臨時表與目標表結構完全一致，避免型別不匹配問題
        target_table = self.bigquery_client.get_table(self.target_table)

        # 🏗️ 建立臨時表（使用相同的 Schema）
        temp_table = bigquery.Table(temp_table_ref, schema=target_table.schema)
        # ⏰ 設定過期時間（避免在測試環境中因 Mock 問題失敗）
        try:
            temp_table.expires = datetime.utcnow() + timedelta(hours=1)  # 1小時後自動過期
        except (ValueError, AttributeError):
            # 在測試環境中 Mock 可能不支援 expires 驗證，跳過設定
            pass
        temp_table = self.bigquery_client.create_table(temp_table)

        # 📤 批次載入資料到臨時表
        # 使用 insert_rows_json 進行高效 JSON 格式載入
        errors = self.bigquery_client.insert_rows_json(temp_table, data)
        if errors:
            # ❌ 載入失敗處理
            raise GoogleCloudError(f"Failed to load data into temp table: {errors}")

        # ✅ 載入成功記錄
        logger.debug(f"Created temp table {temp_table_ref} and loaded {len(data)} records")

    def _execute_merge_query(self, temp_table_ref: str):
        """
        執行 MERGE 查詢，實現完全冪等性的資料插入/更新

        🎛️ Feature Toggle: 支援啟用/停用去重複邏輯
        - 當 enable_deduplication=True：使用完整的 9 欄位複合主鍵去重
        - 當 enable_deduplication=False：直接插入所有資料，依賴 BigQuery 的自然去重

        🔍 問題背景：
        經過 BigQuery 分析發現，原始 tagtoo_event 表存在以下情況：
        1. 同一秒內相同用戶的相同事件可重複多達 80 次
        2. 但這些「重複」記錄在 link、referrer 等欄位上仍有差異
        3. 這代表它們可能是真實的不同用戶行為，不應隨意合併

        🎯 設計策略：
        1. **保持資料完整性**：不刻意合併可能有業務價值的不同記錄
        2. **防止同步重複**：確保我們的同步過程不會創造額外的重複
        3. **完整主鍵比對**：使用足夠的欄位組合來識別真正重複的記錄

        🔑 主鍵組合邏輯（當啟用去重時）：
        - permanent + ec_id + event_time + event：基本事件識別
        - partner_source + link：確保只比較相同來源和頁面的事件
        - value + currency + order_id：包含交易相關資訊避免覆蓋

        ⚡ 去重策略：
        1. 先對臨時表內部去重（處理批次內的重複）
        2. 再與目標表 MERGE（處理歷史重複）
        3. 使用 ROW_NUMBER() 保留時間最早的記錄
        """

        # 🚀 效能優化：移除複雜的 SQL 去重邏輯
        # 現在所有去重邏輯都在 Python 端處理，這裡只保留最高效的簡化版 MERGE
        logger.debug("使用簡化邏輯進行 MERGE 操作")
        merge_query = f"""
        MERGE `{self.target_table}` T
        USING (
          SELECT *
          FROM `{temp_table_ref}`
        ) S
        ON
          T.permanent = S.permanent
          AND T.ec_id = S.ec_id
          AND T.event_time = S.event_time
          AND T.event = S.event
          AND IFNULL(T.link, '') = IFNULL(S.link, '')
          AND T.value IS NOT DISTINCT FROM S.value
          AND T.currency IS NOT DISTINCT FROM S.currency
          AND T.order_id IS NOT DISTINCT FROM S.order_id
          AND T.partner_source = S.partner_source
        WHEN NOT MATCHED THEN
          INSERT (
            permanent, ec_id, partner_source, event_time, create_time,
            link, event, value, currency, order_id, items, user,
            partner_id, page, location, raw_json
          )
          VALUES (
            S.permanent, S.ec_id, S.partner_source, S.event_time, S.create_time,
            S.link, S.event, S.value, S.currency, S.order_id, S.items, S.user,
            S.partner_id, S.page, S.location, S.raw_json
          )
        """

        # 💰 成本估算
        job_config = bigquery.QueryJobConfig()  # MERGE 不需要參數
        cost_estimate = self.estimate_query_cost(merge_query, job_config)

        # 🚀 執行 MERGE 查詢
        execution_start = datetime.utcnow()
        job = self.bigquery_client.query(merge_query)
        job.result()  # 等待查詢完成
        execution_time = (datetime.utcnow() - execution_start).total_seconds()

        # 💰 追踪成本
        table_ref_parts = temp_table_ref.split('.')
        segment_info = table_ref_parts[-1] if len(table_ref_parts) > 0 else "unknown"
        self._track_bigquery_operation(
            "merge_operation",
            cost_estimate,
            execution_time,
            segment_info
        )

        # 📊 記錄執行結果
        affected_rows = job.num_dml_affected_rows or 0
        logger.info(f"MERGE operation completed: {affected_rows} rows inserted (0 updated), Cost: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, Time: {execution_time:.2f}s")

        return {
            "affected_rows": affected_rows,
            "cost_estimate": cost_estimate
        }

    def _process_batch_fast(self, events: List[bigquery.Row], batch_id: str, dry_run: bool = False) -> Dict[str, Any]:
        """快速模式批次處理：使用 BigQuery MERGE 操作（原有邏輯）"""
        transformed_data = []
        error_count = 0
        for event in events:
            try:
                transformed_data.append(self.transform_event_data(event))
            except Exception:
                error_count += 1
                logger.error(f"Failed to transform event in batch {batch_id}", exc_info=True)

        # 如果是 dry_run 模式，只轉換資料不寫入
        if dry_run:
            logger.info(f"[Dry-run] Batch {batch_id}: 轉換了 {len(transformed_data)} 筆事件，錯誤 {error_count} 筆")
            return {"synced_count": len(transformed_data), "error_count": error_count}

        # 🎛️ Feature Toggle: 根據環境變數決定是否啟用優化版 MERGE 的記憶體去重
        use_optimized_merge = os.environ.get("USE_OPTIMIZED_MERGE", "false").lower() in ("true", "1", "yes")

        if use_optimized_merge:
            logger.info(f"Batch {batch_id}: 啟用優化版 MERGE，執行記憶體去重...")
            unique_keys = set()
            deduplicated_data = []
            for item in transformed_data:
                # link 欄位：只將 None 轉為 ''，其餘保留原值（與 transform_event_data 一致）
                link_val = item.get("link")
                link_key = '' if link_val is None else link_val

                # value 欄位：將 None 轉為 'NULL'，其餘轉為字串（與 BigQuery MERGE 行為一致）
                value_val = item.get("value")
                if value_val is None:
                    value_key = 'NULL'
                else:
                    value_key = str(value_val)

                key = (
                    item.get("permanent"),
                    item.get("ec_id"),
                    item.get("event_time"),
                    item.get("event"),
                    link_key,
                    value_key,
                    item.get("currency"),
                    item.get("order_id"),
                    item.get("partner_source")
                )
                if key not in unique_keys:
                    unique_keys.add(key)
                    deduplicated_data.append(item)

            logger.info(f"Batch {batch_id}: 記憶體去重完成，從 {len(transformed_data)} 筆資料中篩選出 {len(deduplicated_data)} 筆唯一資料。")
            data_to_insert = deduplicated_data
        else:
            data_to_insert = transformed_data

        cost_estimate = {
            "estimated_cost_usd": 0.0,
            "tb_processed": 0.0
        }

        if data_to_insert:
            try:
                # 無論是否去重，都使用簡化版的 MERGE 邏輯
                insert_result = self._insert_to_bigquery(data_to_insert)
                logger.info(f"Batch {batch_id}: Successfully synced {len(data_to_insert)} events.")

                # 累積費用資訊
                if insert_result and "cost_estimate" in insert_result:
                    cost_estimate = insert_result["cost_estimate"]

            except GoogleCloudError:
                error_count += len(data_to_insert)

        return {
            "synced_count": len(data_to_insert),
            "error_count": error_count,
            "cost_estimate": cost_estimate
        }

    def _process_batch(self, events: List[bigquery.Row], batch_id: str, dry_run: bool = False) -> Dict[str, Any]:
        """
        智能批次處理：根據模式選擇經濟模式或快速模式

        通過 USE_POLARS 環境變數控制：
        - USE_POLARS=true：使用經濟模式（polars 本地處理，成本低）
        - USE_POLARS=false：使用快速模式（BigQuery MERGE，成本高）

        包含錯誤降級機制：經濟模式失敗時自動降級到快速模式
        """
        try:
            if self.use_polars_mode:
                logger.debug(f"📊 批次 {batch_id}: 使用經濟模式處理")
                return self._process_batch_economical(events, batch_id, dry_run)
            else:
                logger.debug(f"📊 批次 {batch_id}: 使用快速模式處理")
                return self._process_batch_fast(events, batch_id, dry_run)
        except Exception as e:
            # 🛡️ 錯誤降級機制：經濟模式失敗時降級到快速模式
            if self.use_polars_mode:
                logger.warning(f"⚠️ 批次 {batch_id} 經濟模式失敗，自動降級到快速模式: {e}")
                logger.warning(f"   錯誤類型: {type(e).__name__}")
                try:
                    return self._process_batch_fast(events, batch_id, dry_run)
                except Exception as fallback_error:
                    logger.error(f"❌ 批次 {batch_id} 快速模式降級也失敗: {fallback_error}")
                    raise fallback_error
            else:
                # 快速模式失敗時直接拋出異常
                logger.error(f"❌ 批次 {batch_id} 快速模式失敗: {e}")
                raise e

    def _process_batch_economical(self, events: List[bigquery.Row], batch_id: str, dry_run: bool = False) -> Dict[str, Any]:
        """
        經濟模式批次處理：使用 polars 本地處理 + JSON 直接寫入

        優點：
        - 避免昂貴的 BigQuery MERGE 操作
        - 成本節省約 200 倍（$11.26 → $0.055 USD/小時）
        - 本地處理速度快，記憶體效率高

        Args:
            events: BigQuery Row 事件列表
            batch_id: 批次識別碼
            dry_run: 是否為測試模式

        Returns:
            批次處理結果字典
        """
        execution_start = datetime.utcnow()
        logger.info(f"🟢 [Economical] 批次 {batch_id}: 開始經濟模式處理 {len(events)} 筆事件")

        # 1. 轉換事件資料（重用現有方法）
        transformed_data = []
        error_count = 0
        for event in events:
            try:
                transformed_data.append(self.transform_event_data(event))
            except Exception:
                error_count += 1
                logger.error(f"Failed to transform event in batch {batch_id}", exc_info=True)

        if not transformed_data:
            logger.warning(f"批次 {batch_id}: 沒有成功轉換的事件")
            return {"synced_count": 0, "error_count": error_count}

        # 如果是 dry_run 模式，只轉換資料不寫入
        if dry_run:
            logger.info(f"[Dry-run] 批次 {batch_id}: 轉換了 {len(transformed_data)} 筆事件，錯誤 {error_count} 筆")
            return {"synced_count": len(transformed_data), "error_count": error_count}

        try:
            # 2. 建立 polars DataFrame with explicit schema
            logger.debug(f"🔄 批次 {batch_id}: 建立 polars DataFrame")

            # 🎯 明確定義 schema 以避免類型推斷錯誤和混合類型問題
            schema = {
                "permanent": pl.Utf8,  # 強制為字串，避免數字/字串混合問題
                "ec_id": pl.Int64,
                "partner_source": pl.Utf8,
                "event_time": pl.Utf8,
                "create_time": pl.Utf8,
                "link": pl.Utf8,
                "event": pl.Utf8,
                "value": pl.Float64,
                "currency": pl.Utf8,
                "order_id": pl.Utf8,
                "items": pl.Object,  # 複雜物件類型
                "user": pl.Object,   # 複雜物件類型
                "partner_id": pl.Utf8,
                "page": pl.Utf8,
                "location": pl.Object,  # 複雜物件類型
                "raw_json": pl.Utf8
            }

            try:
                df = pl.DataFrame(transformed_data, schema=schema)
            except Exception as e:
                logger.warning(f"⚠️ 批次 {batch_id}: 明確 schema 失敗，回退到寬鬆推斷: {e}")
                # 回退方案：使用更大的推斷長度
                df = pl.DataFrame(transformed_data, infer_schema_length=len(transformed_data))

            # 3. 清理複雜類型欄位（移植自 manual_sync.py）
            clean_df = self._clean_polars_dataframe(df, batch_id)

            # 4. 轉換為 JSON 記錄
            records = clean_df.to_dicts()
            logger.debug(f"📊 批次 {batch_id}: 轉換為 {len(records)} 筆 JSON 記錄")

            # 5. 冪等性檢查（可選）
            if self.enable_idempotency_check:
                logger.debug(f"🔍 批次 {batch_id}: 執行冪等性檢查")
                new_records = self._economical_idempotency_check(records)
                logger.debug(f"📊 批次 {batch_id}: 冪等性檢查完成，{len(records)} → {len(new_records)} 筆")
            else:
                logger.debug(f"⚡ 批次 {batch_id}: 跳過冪等性檢查（成本優化模式）")
                new_records = records

            # 6. 批次寫入 BigQuery（JSON format）
            if new_records:
                table_ref = self.bigquery_client.get_table(self.target_table)
                errors = self.bigquery_client.insert_rows_json(table_ref, new_records)

                if errors:
                    logger.error(f"❌ 批次 {batch_id} BigQuery JSON 寫入錯誤:")
                    for error in errors[:3]:  # 只顯示前 3 個錯誤
                        logger.error(f"   - {error}")
                    error_count += len(new_records)
                    synced_count = 0
                else:
                    synced_count = len(new_records)
                    logger.info(f"✅ 批次 {batch_id}: 經濟模式成功寫入 {synced_count} 筆事件")
            else:
                synced_count = 0
                logger.info(f"批次 {batch_id}: 冪等性檢查後無新記錄需寫入")

            # 7. 成本追踪與對比分析
            execution_time = (datetime.utcnow() - execution_start).total_seconds()

            # 🎯 修正後的經濟模式成本計算
            total_cost_usd = 0.0
            cost_breakdown = {}

            # 1. 冪等性檢查成本（主要成本來源，如果啟用）
            if self.enable_idempotency_check and not dry_run:
                # 估算冪等性查詢成本：基於事件數量和查詢複雜度
                # 冪等性查詢通常掃描 ±5秒 時間窗口的資料
                idempotency_events_estimate = min(len(events) * 2, 50000)  # 估算查詢範圍
                idempotency_cost = idempotency_events_estimate * 0.000002  # 每筆事件約 $0.000002
                total_cost_usd += idempotency_cost
                cost_breakdown["idempotency_check"] = round(idempotency_cost, 6)
            else:
                cost_breakdown["idempotency_check"] = 0.0

            # 2. JSON 插入成本（實際為免費）
            json_insert_cost = 0.0  # insert_rows_json 是免費的
            cost_breakdown["json_insert"] = json_insert_cost

            # 3. 處理開銷（極小）
            processing_overhead = 0.0001  # 極小的處理開銷
            total_cost_usd += processing_overhead
            cost_breakdown["processing_overhead"] = processing_overhead

            # 計算與快速模式的對比（用於節省比例顯示）
            estimated_fast_mode_cost = len(events) * 0.0015  # MERGE 操作成本
            cost_savings = max(0, estimated_fast_mode_cost - total_cost_usd)
            savings_percentage = (cost_savings / estimated_fast_mode_cost * 100) if estimated_fast_mode_cost > 0 else 0

            # 計算處理量（用於顯示）
            estimated_tb_processed = len(events) * 0.001 / (1024**4)  # 估算處理量

            cost_estimate = {
                "estimated_cost_usd": round(total_cost_usd, 6),
                "tb_processed": round(estimated_tb_processed, 8),
                "bytes_processed": int(estimated_tb_processed * (1024**4)),
                "processing_mode": "economical",
                "records_processed": len(events),
                "execution_time_seconds": execution_time,
                # 成本比較與節省分析
                "cost_comparison": {
                    "estimated_fast_mode_cost_usd": round(estimated_fast_mode_cost, 6),
                    "cost_savings_usd": round(cost_savings, 6),
                    "savings_percentage": round(savings_percentage, 1),
                    "cost_efficiency_ratio": round(estimated_fast_mode_cost / total_cost_usd, 1) if total_cost_usd > 0 else 0
                },
                # 處理效率指標
                "efficiency_metrics": {
                    "records_per_second": round(len(events) / execution_time, 1) if execution_time > 0 else 0,
                    "cost_per_thousand_records": round((total_cost_usd * 1000) / len(events), 4) if len(events) > 0 else 0,
                    "processing_mode_advantage": f"{int(savings_percentage)}% 成本節省" if savings_percentage > 0 else "相同成本"
                },
                # 🎯 新增：詳細成本分解
                "cost_breakdown": cost_breakdown,
                "idempotency_check_enabled": self.enable_idempotency_check
            }

            # 追踪到成本系統
            self._track_bigquery_operation(
                "economical_batch_processing",
                cost_estimate,
                execution_time,
                batch_id
            )

            # 記錄成本節省資訊（如果顯著）
            if cost_savings > 0.01:  # 節省超過 $0.01 才記錄
                logger.info(f"💰 批次 {batch_id} 成本節省: ${cost_savings:.4f} USD ({savings_percentage:.1f}%)")
                logger.debug(f"   經濟模式: ${total_cost_usd:.4f} vs 快速模式估算: ${estimated_fast_mode_cost:.4f}")
                if self.enable_idempotency_check:
                    logger.debug(f"   成本組成: 冪等性檢查 ${cost_breakdown.get('idempotency_check', 0):.4f}, 處理開銷 ${cost_breakdown.get('processing_overhead', 0):.4f}")
                else:
                    logger.debug(f"   冪等性檢查已關閉，節省額外成本")

            return {
                "synced_count": synced_count,
                "error_count": error_count,
                "cost_estimate": cost_estimate
            }

        except Exception as e:
            logger.error(f"❌ 經濟模式批次 {batch_id} 處理失敗: {e}")
            error_count += len(transformed_data)
            return {
                "synced_count": 0,
                "error_count": error_count,
                "cost_estimate": {
                    "estimated_cost_usd": 0.0,
                    "tb_processed": 0.0,
                    "error": str(e)
                }
            }

    def _clean_polars_dataframe(self, df: pl.DataFrame, batch_id: str) -> pl.DataFrame:
        """
        清理 polars DataFrame 中的複雜類型欄位

        移植自 manual_sync.py，確保與快速模式完全一致的資料格式
        """
        logger.debug(f"📊 批次 {batch_id} polars 清理前: 形狀 {df.shape}")
        clean_df = df.clone()

        # 💡 修正 items 欄位處理 - 確保與快速模式一致
        if 'items' in clean_df.columns:
            def fix_items_field(x):
                # 如果已經是正確的 list，直接返回
                if isinstance(x, list):
                    return x
                # 如果是 numpy array，轉為 list
                elif hasattr(x, 'tolist'):
                    try:
                        return x.tolist() if len(x) > 0 else []
                    except:
                        return []
                # 如果是 None 或其他類型，返回空 list
                else:
                    return []

            clean_df = clean_df.with_columns([
                pl.col('items').map_elements(fix_items_field, return_dtype=pl.Object)
            ])
            logger.debug(f"   - 已修正 items 欄位處理邏輯（保持與快速模式一致）")

        # 確保結構化欄位有正確的預設值
        for col in ['user', 'location', 'page', 'raw_json']:
            if col in clean_df.columns:
                clean_df = clean_df.with_columns([
                    pl.col(col).map_elements(
                        lambda x: x if isinstance(x, dict) else None,
                        return_dtype=pl.Object
                    )
                ])

        logger.debug(f"📊 批次 {batch_id} polars 清理後: 形狀 {clean_df.shape}, 欄位 {len(clean_df.columns)}")
        return clean_df

    def sync_time_segment(self, start_time: datetime, end_time: datetime, dry_run: bool = False) -> Dict[str, Any]:
        """Syncs all data within a single time segment using streaming processing."""
        mode_desc = "dry-run" if dry_run else "sync"
        logger.info(f"Worker: Starting {mode_desc} for segment: {start_time} -> {end_time}")

        # 📊 添加進度監控：定期報告處理狀態
        last_progress_report = datetime.utcnow()
        progress_interval = 300  # 每5分鐘報告一次進度

        # 使用迭代器進行串流處理
        events_iterator = self.get_events_to_sync(start_time, end_time)

        total_synced = 0
        total_errors = 0
        batch_count = 0
        current_batch = []

        # 💰 累積費用資訊
        total_cost_usd = 0.0
        total_tb_processed = 0.0

        try:
            # 🚀 普通模式：實現 batch 層級平行化處理
            if hasattr(self, 'enable_parallel_processing') and self.enable_parallel_processing and self.use_polars_mode:
                logger.info("🚀 啟用普通模式：batch 層級平行化處理")
                batches = []

                # 收集所有批次
                for event in events_iterator:
                    current_batch.append(event)
                    if len(current_batch) >= BATCH_SIZE:
                        batches.append(current_batch.copy())
                        current_batch = []

                # 處理最後一個不滿的批次
                if current_batch:
                    batches.append(current_batch)

                # 🚀 使用 ThreadPoolExecutor 進行平行處理
                max_workers = min(len(batches), 4)  # 限制最大 worker 數量

                logger.info(f"🚀 將使用 {max_workers} 個 worker 平行處理 {len(batches)} 個批次")

                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有批次任務
                    future_to_batch = {}
                    for i, batch in enumerate(batches):
                        batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{i+1}"
                        future = executor.submit(self._process_batch, batch, batch_id, dry_run)
                        future_to_batch[future] = batch_id

                    # 收集結果
                    for future in concurrent.futures.as_completed(future_to_batch):
                        batch_id = future_to_batch[future]
                        try:
                            result = future.result()
                            total_synced += result["synced_count"]
                            total_errors += result["error_count"]
                            batch_count += 1

                            # 💰 累積費用資訊
                            if "cost_estimate" in result and not dry_run:
                                cost_est = result["cost_estimate"]
                                if not cost_est.get("error"):
                                    total_cost_usd += cost_est.get("estimated_cost_usd", 0.0)
                                    total_tb_processed += cost_est.get("tb_processed", 0.0)

                            logger.info(f"✅ 批次 {batch_id} 完成：{result['synced_count']} 筆成功，{result['error_count']} 筆錯誤")

                        except Exception as e:
                            logger.error(f"❌ 批次 {batch_id} 處理失敗: {e}")
                            total_errors += len(batches[future_to_batch[future]])
                            batch_count += 1

                logger.info(f"🚀 平行處理完成：{batch_count} 個批次，{total_synced} 筆成功，{total_errors} 筆錯誤")

            else:
                # 🟢 經濟模式：依序處理（原有邏輯）
                logger.info("🟢 使用經濟模式：依序處理")

                for event in events_iterator:
                    current_batch.append(event)

                    # 🕒 進度監控：定期報告處理狀態
                    current_time = datetime.utcnow()
                    if (current_time - last_progress_report).total_seconds() > progress_interval:
                        logger.info(f"Progress: Processed {total_synced} events in {batch_count} batches. Current batch size: {len(current_batch)}")
                        last_progress_report = current_time

                    # 當批次達到指定大小時進行處理
                    if len(current_batch) >= BATCH_SIZE:
                        batch_count += 1
                        batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{batch_count}"
                        mode_desc = "[Dry-run]" if dry_run else ""
                        logger.info(f"{mode_desc} Processing batch {batch_id} with {len(current_batch)} events")

                        result = self._process_batch(current_batch, batch_id, dry_run=dry_run)
                        total_synced += result["synced_count"]
                        total_errors += result["error_count"]

                        # 💰 累積費用資訊
                        if "cost_estimate" in result and not dry_run:
                            cost_est = result["cost_estimate"]
                            if not cost_est.get("error"):
                                total_cost_usd += cost_est.get("estimated_cost_usd", 0.0)
                                total_tb_processed += cost_est.get("tb_processed", 0.0)

                        # 清空批次，釋放記憶體
                        current_batch = []

                # 處理最後一個不滿的批次
                if current_batch:
                    batch_count += 1
                    batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{batch_count}"
                    mode_desc = "[Dry-run]" if dry_run else ""
                    logger.info(f"{mode_desc} Processing final batch {batch_id} with {len(current_batch)} events")

                    result = self._process_batch(current_batch, batch_id, dry_run=dry_run)
                    total_synced += result["synced_count"]
                    total_errors += result["error_count"]

                    # 💰 累積費用資訊
                    if "cost_estimate" in result and not dry_run:
                        cost_est = result["cost_estimate"]
                        if not cost_est.get("error"):
                            total_cost_usd += cost_est.get("estimated_cost_usd", 0.0)
                            total_tb_processed += cost_est.get("tb_processed", 0.0)

        except Exception as e:
            logger.error(f"Error during streaming processing: {e}", exc_info=True)
            status = "error"
            total_events = total_synced + total_errors
        else:
            total_events = total_synced + total_errors
            status = "success" if total_errors == 0 else "partial_success"

        if total_events == 0:
            logger.info(f"Segment {start_time} -> {end_time}: No new events to sync.")
            return {
                "status": "success",
                "total_events": 0,
                "synced_events": 0,
                "error_events": 0,
                "cost_estimate": {
                    "estimated_cost_usd": 0.0,
                    "tb_processed": 0.0
                }
            }

        logger.info(f"Segment {start_time} -> {end_time} sync complete. Status: {status}, Synced: {total_synced}, Errors: {total_errors}, Batches: {batch_count}")

        # 💰 準備費用資訊
        cost_estimate = {
            "estimated_cost_usd": total_cost_usd,
            "tb_processed": total_tb_processed
        } if not dry_run else None

        result = {
            "status": status,
            "total_events": total_events,
            "synced_events": total_synced,
            "error_events": total_errors,
            "batches_processed": batch_count
        }

        if cost_estimate:
            result["cost_estimate"] = cost_estimate

        # 📊 更新儀表板（如果啟用）
        self._update_dashboard_after_sync(result, start_time, end_time, dry_run)

        return result

    def estimate_query_cost(self, query: str, job_config: bigquery.QueryJobConfig) -> Dict[str, Any]:
        """Estimates the cost of a BigQuery query using dry run."""
        try:
            # Create a new dry run job config
            dry_run_config = bigquery.QueryJobConfig()

            # Copy relevant parameters from original config
            if job_config.query_parameters:
                dry_run_config.query_parameters = job_config.query_parameters
            if job_config.use_legacy_sql is not None:
                dry_run_config.use_legacy_sql = job_config.use_legacy_sql

            # Set dry run specific options
            dry_run_config.dry_run = True
            dry_run_config.use_query_cache = False

            # Execute dry run
            job = self.bigquery_client.query(query, job_config=dry_run_config)

            # Calculate cost (approximately $5 per TB in most regions)
            bytes_processed = job.total_bytes_processed or 0
            tb_processed = bytes_processed / (1024**4) if bytes_processed else 0
            estimated_cost_usd = tb_processed * 5.0  # $5 per TB

            return {
                "bytes_processed": bytes_processed,
                "tb_processed": round(tb_processed, 6),
                "estimated_cost_usd": round(estimated_cost_usd, 4),
                "query_slots": job.slot_millis // 1000 if job.slot_millis else 0
            }
        except Exception as e:
            logger.warning(f"Failed to estimate query cost: {e}")
            return {
                "bytes_processed": 0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "query_slots": 0,
                "error": str(e)
            }

    def get_source_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the source table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        # 建構事件類型過濾條件（與 get_events_to_sync 保持一致）
        event_filter_condition = ""
        if EXCLUDED_EVENT_TYPES:
            excluded_list = [f"'{event_type}'" for event_type in EXCLUDED_EVENT_TYPES]
            excluded_str = ", ".join(excluded_list)
            event_filter_condition = f" AND event.name NOT IN ({excluded_str})"

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time{event_filter_condition}
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get source table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_target_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the target table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.target_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
            AND partner_source = 'legacy-tagtoo-event'
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get target table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_latest_data_timestamp(self) -> Optional[datetime]:
        """Retrieves the latest event_time from the target table."""
        query = f"""
            SELECT MAX(event_time) as latest_time
            FROM `{self.target_table}`
            WHERE partner_source = 'legacy-tagtoo-event'
        """
        try:
            result = list(self.bigquery_client.query(query).result())
            if result and result[0].latest_time:
                return result[0].latest_time
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
        except Exception as e:
            logger.error(f"Failed to get latest data timestamp: {e}", exc_info=True)
        return None

    def get_sync_status_for_date(self, target_date: datetime = None) -> Dict[str, Any]:
        """Retrieves comprehensive sync status for a specific date with BigQuery cost analysis."""
        if target_date is None:
            target_date = datetime.utcnow()

        # Get basic sync metadata
        last_sync_time = self.get_last_sync_time()

        # Get data counts for the target date (with cost estimation)
        source_result = self.get_source_table_count_for_date(target_date)
        target_result = self.get_target_table_count_for_date(target_date)

        # Extract counts from the new format
        source_count = source_result.get("count", 0)
        target_count = target_result.get("count", 0)

        # Get latest data timestamp
        latest_timestamp = self.get_latest_data_timestamp()

        # Calculate sync coverage
        sync_coverage = (target_count / source_count * 100) if source_count > 0 else 0

        # Calculate total cost for this status check
        total_cost = (source_result.get("query_stats", {}).get("estimated_cost_usd", 0) +
                     target_result.get("query_stats", {}).get("estimated_cost_usd", 0))
        total_tb_processed = (source_result.get("query_stats", {}).get("tb_processed", 0) +
                             target_result.get("query_stats", {}).get("tb_processed", 0))
        total_execution_time = (source_result.get("query_stats", {}).get("execution_time_seconds", 0) +
                               target_result.get("query_stats", {}).get("execution_time_seconds", 0))

        # Determine status
        status = "healthy"
        warnings = []
        cost_warnings = []

        # Data coverage warnings
        if source_count == 0:
            warnings.append("No source data found for the specified date")
        elif target_count == 0:
            status = "warning"
            warnings.append("No target data found for the specified date")
        elif sync_coverage < 95:
            status = "warning"
            warnings.append(f"Sync coverage is only {sync_coverage:.1f}%")

        # Time-based warnings
        if last_sync_time:
            hours_since_sync = (datetime.utcnow() - last_sync_time).total_seconds() / 3600
            if hours_since_sync > 6:
                status = "warning"
                warnings.append(f"Last sync was {hours_since_sync:.1f} hours ago")
        else:
            status = "warning"
            warnings.append("No sync history found")

        # Cost-based warnings
        if total_cost > 0.5:  # > $0.50
            cost_warnings.append(f"High query cost: ${total_cost:.4f} USD")
            status = "warning"
        elif total_cost > 0.1:  # > $0.10
            cost_warnings.append(f"Moderate query cost: ${total_cost:.4f} USD")

        if total_execution_time > 30:  # > 30 seconds
            cost_warnings.append(f"Slow queries detected: {total_execution_time:.2f}s total execution time")

        # Optimization suggestions
        optimization_suggestions = []
        if total_tb_processed > 0.1:  # > 100 GB
            optimization_suggestions.append("Consider adding date partitioning filters to reduce data scan")
        if total_execution_time > 10:
            optimization_suggestions.append("Consider adding indexes or optimizing WHERE clauses")

        return {
            "date": target_date.strftime("%Y-%m-%d"),
            "status": status,
            "last_sync_time": last_sync_time.isoformat() if last_sync_time else None,
            "source_events_count": source_count,
            "target_events_count": target_count,
            "sync_coverage_percent": round(sync_coverage, 2),
            "latest_data_timestamp": latest_timestamp.isoformat() if latest_timestamp else None,
            "warnings": warnings + cost_warnings,
            "table_info": {
                "source_table": self.source_table,
                "target_table": self.target_table
            },
            "filtering_config": {
                "excluded_event_types": EXCLUDED_EVENT_TYPES,
                "filtering_enabled": len(EXCLUDED_EVENT_TYPES) > 0,
                "description": f"已過濾 {len(EXCLUDED_EVENT_TYPES)} 種事件類型" if EXCLUDED_EVENT_TYPES else "未啟用事件過濾"
            },
            "bigquery_analysis": {
                "total_cost_usd": round(total_cost, 4),
                "total_tb_processed": round(total_tb_processed, 6),
                "total_execution_time_seconds": round(total_execution_time, 2),
                "source_query_stats": source_result.get("query_stats", {}),
                "target_query_stats": target_result.get("query_stats", {}),
                "optimization_suggestions": optimization_suggestions
            },
            "streaming_buffer_info": self.get_streaming_buffer_status()
        }

    def normalize_to_hour_boundary(self, dt: datetime) -> datetime:
        """標準化時間到整點邊界，確保冪等性"""
        return dt.replace(minute=0, second=0, microsecond=0)

    def generate_time_segments(self, start_date: datetime, end_date: datetime) -> List[tuple[datetime, datetime]]:
        """Generates time segments for the given date range (以分鐘為單位)."""
        # 標準化時間到整點邊界，確保冪等性
        start_date = self.normalize_to_hour_boundary(start_date)
        end_date = self.normalize_to_hour_boundary(end_date)

        segments = []
        current_time = start_date
        while current_time < end_date:
            segment_end = current_time + timedelta(minutes=MINUTES_PER_SEGMENT)
            if segment_end > end_date:
                segment_end = end_date
            segments.append((current_time, segment_end))
            current_time = segment_end
        return segments

    def coordinate_sync(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None, worker_url: Optional[str] = None) -> Dict[str, Any]:
        """Coordinator logic: generates segments and creates Cloud Tasks."""
        logger.info("Coordinator: Starting sync coordination.")

        # Set worker URL if provided or build it dynamically
        if worker_url:
            self.worker_url = worker_url
        elif not self.worker_url:
            # Try to build it from Flask request context (preferred method)
            try:
                from flask import request
                # 修正：確保生產環境總是使用 HTTPS
                base_url = request.url_root.rstrip('/')
                if ENVIRONMENT == 'prod' and base_url.startswith('http://'):
                    # Cloud Run 內部可能使用 HTTP，但外部訪問需要 HTTPS
                    base_url = base_url.replace('http://', 'https://')
                self.worker_url = f"{base_url}{WORKER_URL_PATH}"
                logger.info(f"Built worker URL from request context: {self.worker_url}")
            except RuntimeError:
                # 不在 request context - 提供測試環境的 fallback
                # 這種情況通常發生在測試環境或直接調用時
                if ENVIRONMENT in ['dev', 'test', 'ci']:
                    # 測試環境使用 localhost fallback
                    self.worker_url = f"http://localhost:8080{WORKER_URL_PATH}"
                    logger.info(f"Using test fallback worker URL: {self.worker_url}")
                else:
                    # 生產環境建立基於環境變數的 URL
                    self.worker_url = f"https://legacy-event-sync-{ENVIRONMENT}-{GCP_LOCATION}.a.run.app{WORKER_URL_PATH}"
                    logger.info(f"Built production worker URL: {self.worker_url}")

        if not end_date:
            # 為了處理 BigQuery streaming buffer 延遲，使用可配置的時間偏移
            # 基於真實監控，Streaming Buffer 延遲通常 10-15 分鐘，預設 1 小時緩衝
            streaming_buffer_offset_hours = int(os.environ.get("STREAMING_BUFFER_OFFSET_HOURS", 1))
            # 標準化到整點邊界確保冪等性
            end_date = self.normalize_to_hour_boundary(datetime.utcnow() - timedelta(hours=streaming_buffer_offset_hours))
            logger.info(f"Using default end_date with {streaming_buffer_offset_hours}-hour buffer: {end_date}")
        else:
            # 標準化用戶提供的 end_date
            end_date = self.normalize_to_hour_boundary(end_date)
        if not start_date:
            # 📋 記錄 last_sync_time 用於決定處理起始點
            last_sync_time = self.get_last_sync_time()

            if last_sync_time:
                # 🎯 基於上次同步時間決定起始點，避免重複處理
                # last_sync_time 記錄的是上次處理時段的結束時間
                # 下次應該從這個時間開始，確保不重複處理
                start_date = last_sync_time

                logger.info(f"基於上次同步時間 {last_sync_time} 決定起始點：{start_date} -> {end_date}")
                logger.info(f"確保不重複處理已同步的時段")

                # ⚠️ 檢測是否有時間空隙，用於監控告警
                time_gap_minutes = (start_date - last_sync_time).total_seconds() / 60
                if abs(time_gap_minutes) > MINUTES_PER_SEGMENT + 1:  # 允許1分鐘容差
                    logger.warning(f"檢測到時間空隙：上次同步 {last_sync_time} 到當前處理 {start_date}，間隔 {time_gap_minutes:.1f} 分鐘")
                    logger.warning("建議檢查是否需要手動補齊遺漏的時段")
            else:
                # 🎯 首次運行或無同步記錄，使用固定時間窗口
                overlap_buffer_minutes = int(os.environ.get("SYNC_OVERLAP_MINUTES", 15))
                sync_window_minutes = SCHEDULER_INTERVAL_MINUTES + overlap_buffer_minutes
                default_start = end_date - timedelta(minutes=sync_window_minutes)

                logger.info(f"首次運行或無同步記錄，使用固定時間窗口：{default_start} -> {end_date}")
                logger.info(f"使用重疊同步機制：窗口大小 {sync_window_minutes} 分鐘（{SCHEDULER_INTERVAL_MINUTES} + {overlap_buffer_minutes} 重疊）")

                # 標準化到整點邊界確保冪等性
                start_date = self.normalize_to_hour_boundary(default_start)
        else:
            # 標準化用戶提供的 start_date
            start_date = self.normalize_to_hour_boundary(start_date)

        if start_date >= end_date:
            logger.info("No new time range to sync. Last sync was at %s.", start_date)
            return {"status": "no_new_data", "message": "No new time range to sync."}

        segments = self.generate_time_segments(start_date, end_date)
        if not segments:
            logger.info("No segments generated for the time range.")
            return {"status": "no_segments", "message": "No segments to process."}

        logger.info(f"Generated {len(segments)} segments to process from {start_date} to {end_date}.")

        # 加入安全檢查：避免創建過多任務
        max_segments_per_request = 50  # 限制單次最多50個segments
        if len(segments) > max_segments_per_request:
            logger.error(f"Too many segments ({len(segments)}). Maximum allowed: {max_segments_per_request}")
            return {
                "status": "error",
                "message": f"Time range too large. Generated {len(segments)} segments, but maximum allowed is {max_segments_per_request}. Please use smaller time ranges."
            }

        parent = self.tasks_client.queue_path(PROJECT_ID, GCP_LOCATION, self.task_queue_name)
        tasks_created = 0
        for start, end in segments:
            payload = {"start_time": start.isoformat(), "end_time": end.isoformat()}

            # 設定任務超時時間為 30 分鐘 (1800秒)，Cloud Tasks 上限
            from google.protobuf import duration_pb2
            timeout_duration = duration_pb2.Duration()
            timeout_duration.seconds = 1800  # 30 分鐘 (Cloud Tasks 上限)

            task = {
                "http_request": {
                    "http_method": tasks_v2.HttpMethod.POST,
                    "url": self.worker_url,
                    "headers": {"Content-type": "application/json"},
                    "body": json.dumps(payload).encode(),
                    # 新增 OIDC 認證設定，讓 Cloud Tasks 能夠呼叫受保護的 Cloud Run 服務
                    "oidc_token": {
                        "service_account_email": os.environ.get("GOOGLE_SERVICE_ACCOUNT_EMAIL", f"integrated-event-{ENVIRONMENT}@{PROJECT_ID}.iam.gserviceaccount.com"),
                        "audience": self.worker_url,
                    }
                },
                # 設定任務執行的超時時間，解決 600s 預設限制
                "dispatch_deadline": timeout_duration
            }
            try:
                logger.info(f"Creating task for segment {start} -> {end} with URL: {self.worker_url} (timeout: 1800s)")
                self.tasks_client.create_task(request={"parent": parent, "task": task})
                tasks_created += 1
            except GoogleCloudError as e:
                logger.error(f"Failed to create task for segment {start} -> {end}: {e}", exc_info=True)

        logger.info(f"Coordinator: Created {tasks_created} of {len(segments)} tasks.")

        if tasks_created == len(segments):
            # 🎯 更新 last_sync_time 僅用於監控，記錄最後成功處理的時段結束時間
            # 注意：這不影響下次處理的 start_date，只用於監控和告警
            self.set_last_sync_time(end_date)
            logger.info(f"Successfully updated monitoring timestamp to {end_date.isoformat()} (固定時段處理模式)")
        else:
            logger.warning(f"部分任務建立失敗 ({tasks_created}/{len(segments)})，不更新監控時間戳記")

        return {"status": "success", "total_segments": len(segments), "tasks_created": tasks_created}

    def _economical_idempotency_check(self, records: list) -> list:
        """
        經濟模式冪等性檢查：與已存在記錄比對，過濾重複

        移植自 manual_sync.py，使用與 MERGE 操作相同的 8 欄位複合主鍵：
        - permanent, ec_id, event_time, event
        - partner_source, link, value, currency, order_id

        Args:
            records: 要檢查的記錄列表

        Returns:
            過濾後的新記錄列表
        """
        if not records:
            return []

        try:
            logger.debug(f"🔍 開始冪等性檢查，輸入 {len(records)} 筆記錄")

            # 🔧 統一時間格式處理函數（移植自 manual_sync.py）
            def normalize_event_time(event_time_value):
                """統一時間格式處理，確保一致性"""
                if event_time_value is None:
                    return None

                try:
                    event_time_str = str(event_time_value)
                    # 移除時區信息
                    if '+' in event_time_str:
                        event_time_str = event_time_str.split('+')[0]
                    # 統一格式：移除 T 分隔符，確保使用空格
                    event_time_str = event_time_str.replace('T', ' ')
                    # 去掉微秒部分，只保留到秒
                    if '.' in event_time_str:
                        event_time_str = event_time_str.split('.')[0]
                    return event_time_str
                except Exception as e:
                    logger.warning(f"時間格式處理異常: {event_time_value}, 錯誤: {e}")
                    return str(event_time_value) if event_time_value is not None else None

            # 🔧 統一key生成函數（移植自 manual_sync.py）
            def generate_record_key(record_data):
                """生成統一的記錄key"""
                link_key = record_data.get("link") or ""
                value_key = record_data.get("value")
                event_time_normalized = normalize_event_time(record_data.get("event_time"))

                return (
                    record_data.get("permanent"),
                    record_data.get("ec_id"),
                    event_time_normalized,
                    record_data.get("event"),
                    record_data.get("partner_source"),
                    link_key,
                    value_key,
                    record_data.get("currency"),
                    record_data.get("order_id")
                )

            # 建立輸入記錄的 key 映射
            input_keys = {}
            for i, record in enumerate(records):
                try:
                    record_key = generate_record_key(record)
                    input_keys[record_key] = record

                    # Debug: 顯示前3筆記錄的 key
                    if i < 3:
                        logger.debug(f"   - 輸入記錄 {i+1} key: {record_key}")
                except Exception as e:
                    logger.warning(f"生成輸入記錄 key 失敗: {record}, 錯誤: {e}")
                    continue

            # 🔧 擴大查詢範圍，確保不遺漏記錄
            first_record = records[0]
            base_time = normalize_event_time(first_record.get("event_time"))
            if not base_time:
                logger.warning("無法獲取基準時間，跳過冪等性檢查")
                return records

            # 查詢範圍擴大到10秒，確保覆蓋所有可能的記錄
            check_query = f"""
            SELECT permanent, ec_id, event_time, event, partner_source,
                   IFNULL(link, '') as link, value, currency, order_id
            FROM `{self.target_table}`
            WHERE partner_source = 'legacy-tagtoo-event'
              AND event_time >= TIMESTAMP_SUB(TIMESTAMP('{base_time}'), INTERVAL 5 SECOND)
              AND event_time <= TIMESTAMP_ADD(TIMESTAMP('{base_time}'), INTERVAL 5 SECOND)
            """

            logger.debug(f"🔍 查詢已存在記錄，時間範圍: {base_time} ± 5秒")
            existing_results = list(self.bigquery_client.query(check_query).result())
            logger.debug(f"🔍 查詢到 {len(existing_results)} 筆已存在記錄")

            # 建立已存在記錄的 key set
            existing_keys = set()
            for i, row in enumerate(existing_results):
                try:
                    existing_key = generate_record_key(row)
                    existing_keys.add(existing_key)

                    # Debug: 顯示前3筆已存在記錄的 key
                    if i < 3:
                        logger.debug(f"   - 已存在記錄 {i+1} key: {existing_key}")
                except Exception as e:
                    logger.warning(f"生成已存在記錄 key 失敗: {dict(row)}, 錯誤: {e}")
                    continue

            # 過濾出新記錄
            new_records = []
            duplicate_count = 0

            for record_key, record in input_keys.items():
                if record_key not in existing_keys:
                    new_records.append(record)
                else:
                    duplicate_count += 1
                    logger.debug(f"   - 發現重複記錄: {record_key}")

            logger.debug(f"✅ 冪等性檢查完成：{len(records)} 筆輸入，{duplicate_count} 筆重複，{len(new_records)} 筆新記錄")
            logger.debug(f"   - 重複率: {(duplicate_count / len(records) * 100):.1f}%")

            return new_records

        except Exception as e:
            logger.error(f"❌ 冪等性檢查失敗: {e}")
            logger.error(f"   - 錯誤類型: {type(e).__name__}")
            import traceback
            logger.error(f"   - 詳細堆疊: {traceback.format_exc()}")
            logger.warning("使用降級策略：跳過重複檢查，直接寫入所有記錄")
            # 降級策略：如果檢查失敗，返回所有記錄（保持原有行為）
            return records

    def _update_dashboard_after_sync(self,
                                   sync_result: Dict[str, Any],
                                   start_time: datetime,
                                   end_time: datetime,
                                   dry_run: bool = False):
        """
        同步完成後更新儀表板

        Args:
            sync_result: 同步結果
            start_time: 開始時間
            end_time: 結束時間
            dry_run: 是否為測試模式
        """
        if not self._enable_dashboard:
            return

        try:
            self._ensure_dashboard_uploader_initialized()
            if not self.dashboard_uploader:
                return

            # 準備儀表板資料
            import pytz
            taiwan_tz = pytz.timezone("Asia/Taipei")
            dashboard_data = {
                **sync_result,
                "sync_mode": "economical" if self.use_polars_mode else "fast",
                "start_time_str": start_time.astimezone(taiwan_tz).strftime("%Y-%m-%d %H:%M:%S"),
                "end_time_str": end_time.astimezone(taiwan_tz).strftime("%Y-%m-%d %H:%M:%S"),
                "dry_run": dry_run,
                "execution_time_seconds": 0,  # 可以從實際執行時間計算
                "config": {
                    "source_table": self.source_table,
                    "target_table": self.target_table,
                    "batch_size": BATCH_SIZE,
                    "split_interval_minutes": _parse_minutes_per_segment(),
                    "enable_deduplication": self.enable_deduplication,
                    "max_workers": 4,  # 預設值
                    "memory_limit_gb": 8   # 預設值
                }
            }

            # 添加成本追踪資訊（如果有）
            if self.cost_tracker and self._enable_cost_tracking:
                cost_summary = self.cost_tracker.get_summary()
                dashboard_data["cost_tracker_summary"] = cost_summary

            # 上傳儀表板
            dashboard_url = self.dashboard_uploader.upload_dashboard(dashboard_data)
            logger.info(f"📊 儀表板已更新：{dashboard_url}")

        except Exception as e:
            # 儀表板更新失敗不應影響主要功能
            logger.warning(f"⚠️ 儀表板更新失敗: {e}")

# --- Flask App Routes ---
processor = LegacyEventSyncProcessor(
    source_table=SOURCE_TABLE,
    target_table=TARGET_TABLE,
    task_queue_name=TASK_QUEUE_NAME,
    worker_url=WORKER_URL,
)

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify(
        {
            "status": "healthy",
            "service": "legacy-event-sync",
            "version": SERVICE_VERSION,
            "commit": SERVICE_COMMIT,
            "timestamp": datetime.utcnow().isoformat(),
        }
    )

@app.route("/start-sync", methods=["POST"])
def start_sync_endpoint():
    """Coordinator endpoint, triggered by Cloud Scheduler."""
    try:
        data = request.get_json() or {}
        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        start_date = datetime.fromisoformat(start_date_str) if start_date_str else None
        end_date = datetime.fromisoformat(end_date_str) if end_date_str else None

        # Build worker URL from current request
        # 確保生產環境總是使用 HTTPS
        base_url = request.url_root.rstrip('/')
        if ENVIRONMENT == 'prod' or base_url.startswith('https://'):
            # 生產環境或已經是 HTTPS，確保使用 HTTPS
            if base_url.startswith('http://'):
                base_url = base_url.replace('http://', 'https://')
            worker_url = f"{base_url}{WORKER_URL_PATH}"
        else:
            # 開發/測試環境，保持原來的協議
            worker_url = f"{base_url}{WORKER_URL_PATH}"

        result = processor.coordinate_sync(start_date, end_date, worker_url)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Coordinator error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/process-segment", methods=["POST"])
def process_segment_endpoint():
    """Worker endpoint, triggered by Cloud Tasks."""
    try:
        # 💰 初始化成本追踪器（為這個 segment 建立新的追踪會話）
        processor._ensure_cost_tracker_initialized()
        if processor.cost_tracker:
            processor.cost_tracker.reset()  # 重置以追踪這個 segment 的成本

        data = request.get_json(silent=True)
        if not data or "start_time" not in data or "end_time" not in data:
            logger.error("Invalid task payload received. Payload: %s", data)
            return "Invalid payload", 400

        start_time = datetime.fromisoformat(data["start_time"])
        end_time = datetime.fromisoformat(data["end_time"])

        result = processor.sync_time_segment(start_time, end_time)

        # 💰 加入成本資訊到回應中
        if processor.cost_tracker and processor._enable_cost_tracking:
            cost_summary = processor.cost_tracker.get_summary()
            result["bigquery_costs"] = cost_summary

            # 記錄成本資訊到日誌
            logger.info(f"Segment {start_time}-{end_time} BigQuery costs: "
                       f"${cost_summary['cost_summary']['total_cost_usd']:.4f} USD, "
                       f"{cost_summary['session_info']['total_operations']} operations, "
                       f"Efficiency score: {cost_summary['cost_summary']['cost_efficiency_score']}")

        # Cloud Tasks expects a 2xx response to consider the task successful
        if result.get("status") in ["success", "partial_success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500
    except (ValueError, TypeError) as e:
        logger.error(f"Worker error processing segment due to value/type error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": f"Invalid date format or payload structure: {e}"}), 400
    except Exception as e:
        logger.error(f"Worker error processing segment: {e}", exc_info=True)
        # Return a non-2xx status to signal task failure for retry
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/sync-status", methods=["GET"])
def sync_status_endpoint():
    """Sync status endpoint, provides comprehensive sync status information."""
    try:
        # Get optional date parameter
        date_param = request.args.get("date")
        target_date = None

        if date_param:
            try:
                target_date = datetime.strptime(date_param, "%Y-%m-%d")
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "Invalid date format. Use YYYY-MM-DD format."
                }), 400

        result = processor.get_sync_status_for_date(target_date)
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Sync status endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route("/backfill-sync", methods=["POST"])
def backfill_sync_endpoint():
    """回補遺漏時段的同步任務，支援指定時間範圍"""
    try:
        data = request.get_json() or {}

        # 必須指定開始和結束時間
        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        if not start_date_str or not end_date_str:
            return jsonify({
                "status": "error",
                "message": "請指定 start_date 和 end_date (ISO 格式)"
            }), 400

        try:
            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)
        except ValueError:
            return jsonify({
                "status": "error",
                "message": "日期格式錯誤，請使用 ISO 格式 (例如: 2025-07-17T13:00:00)"
            }), 400

        # 安全檢查：限制回補範圍，避免建立過多任務
        time_diff_hours = (end_date - start_date).total_seconds() / 3600
        max_backfill_hours = 72  # 最多回補3天

        if time_diff_hours > max_backfill_hours:
            return jsonify({
                "status": "error",
                "message": f"回補範圍過大 ({time_diff_hours:.1f} 小時)，最多允許 {max_backfill_hours} 小時"
            }), 400

        if start_date >= end_date:
            return jsonify({
                "status": "error",
                "message": "開始時間必須早於結束時間"
            }), 400

        # Build worker URL from current request
        base_url = request.url_root.rstrip('/')
        if ENVIRONMENT == 'prod' or base_url.startswith('https://'):
            if base_url.startswith('http://'):
                base_url = base_url.replace('http://', 'https://')
            worker_url = f"{base_url}{WORKER_URL_PATH}"
        else:
            worker_url = f"{base_url}{WORKER_URL_PATH}"

        # 執行回補同步（強制使用指定的時間範圍）
        logger.info(f"開始回補同步：{start_date} -> {end_date}")
        result = processor.coordinate_sync(start_date, end_date, worker_url)

        # 為回補任務添加特殊標記
        result["operation_type"] = "backfill"
        result["backfill_range"] = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "hours_covered": round(time_diff_hours, 1)
        }

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Backfill sync endpoint error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/bigquery-costs", methods=["GET"])
def bigquery_costs_endpoint():
    """
    BigQuery 成本分析端點

    提供詳細的 BigQuery 使用成本分析，包括即時成本估算、
    歷史成本趨勢和最佳化建議。

    Query Parameters:
    - date: 指定日期 (YYYY-MM-DD 格式，可選)
    - analysis_type: 分析類型 ('quick' 或 'detailed'，預設 'quick')
    """
    try:
        # 📋 解析查詢參數
        date_param = request.args.get("date")
        analysis_type = request.args.get("analysis_type", "quick")

        target_date = datetime.utcnow() if not date_param else datetime.strptime(date_param, "%Y-%m-%d")

        # 💰 初始化成本追踪器進行分析
        processor._ensure_cost_tracker_initialized()
        if processor.cost_tracker:
            processor.cost_tracker.reset()  # 重置以進行新的成本分析

        response_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "analysis_date": target_date.strftime("%Y-%m-%d"),
            "analysis_type": analysis_type,
            "cost_tracking_enabled": processor._enable_cost_tracking
        }

        if not processor._enable_cost_tracking:
            response_data["message"] = "成本追踪已停用，請設定環境變數 ENABLE_COST_TRACKING=true 啟用"
            return jsonify(response_data), 200

        if analysis_type == "detailed":
            # 🔍 詳細分析：執行實際的成本分析查詢
            logger.info(f"執行詳細成本分析：{target_date.strftime('%Y-%m-%d')}")

            # 執行同步狀態檢查（會觸發 BigQuery 查詢並追踪成本）
            sync_status = processor.get_sync_status_for_date(target_date)

            # 取得成本追踪摘要
            if processor.cost_tracker:
                cost_summary = processor.cost_tracker.get_summary()
                response_data["detailed_analysis"] = {
                    "sync_status": sync_status,
                    "cost_breakdown": cost_summary,
                    "real_time_costs": cost_summary["cost_summary"]
                }

        else:
            # ⚡ 快速分析：僅提供基本的成本評估
            logger.info(f"執行快速成本分析：{target_date.strftime('%Y-%m-%d')}")

            # 執行同步狀態檢查（已包含成本分析）
            sync_status = processor.get_sync_status_for_date(target_date)

            response_data["quick_analysis"] = {
                "bigquery_summary": sync_status.get("bigquery_analysis", {}),
                "sync_overview": {
                    "source_events": sync_status.get("source_events_count", 0),
                    "target_events": sync_status.get("target_events_count", 0),
                    "sync_coverage": sync_status.get("sync_coverage_percent", 0),
                    "status": sync_status.get("status", "unknown")
                }
            }

        # 📊 加入通用的最佳化建議
        response_data["optimization_recommendations"] = [
            "🎯 使用分區表 (PARTITION BY DATE) 減少資料掃描範圍",
            "🔍 避免 SELECT * 查詢，只選取必要欄位",
            "⏰ 在測試環境使用 LIMIT 限制查詢範圍",
            "📈 定期監控查詢成本，設定預算警告",
            "🏗️ 考慮使用 CLUSTERED 表加速常用查詢",
            "💾 利用 BigQuery 快取機制，避免重複查詢"
        ]

        # 🎛️ 加入成本控制設定資訊
        response_data["cost_control_settings"] = {
            "cost_thresholds": {
                "warning": "$0.10 USD",
                "high": "$0.50 USD",
                "critical": "$2.00 USD"
            },
            "current_pricing": "$5.00 per TB (on-demand pricing)",
            "suggestions": [
                "考慮使用 BigQuery 預留容量降低成本",
                "設定每日查詢預算避免意外高額費用",
                "使用 Cloud Monitoring 建立成本警報"
            ]
        }

        return jsonify(response_data), 200

    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": f"無效的日期格式，請使用 YYYY-MM-DD: {e}"
        }), 400
    except Exception as e:
        logger.error(f"BigQuery costs endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


@app.route("/streaming-buffer-status", methods=["GET"])
def streaming_buffer_status_endpoint():
    """
    BigQuery Streaming Buffer 狀況檢查端點

    提供即時的 Streaming Buffer 狀況分析，協助優化同步時機。

    Query Parameters:
    - include_recommendations: 是否包含同步偏移建議 ('true' 或 'false'，預設 'true')

    Response:
    - streaming_buffer: 當前 Streaming Buffer 狀況
    - current_settings: 目前同步配置
    - recommendations: 優化建議 (如果啟用)
    """
    logger.info("Streaming Buffer 狀況檢查端點被調用")

    try:
        include_recommendations = request.args.get('include_recommendations', 'true').lower() == 'true'

        # 獲取 Streaming Buffer 狀況
        buffer_status = processor.get_streaming_buffer_status()

        response_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "streaming_buffer": buffer_status,
            "current_settings": {
                "sync_offset_minutes": 120,  # 目前設定的 2 小時
                "sync_schedule": "每 60 分鐘觸發一次",
                "batch_size": BATCH_SIZE,
                "minutes_per_segment": MINUTES_PER_SEGMENT
            }
        }

        if include_recommendations and buffer_status.get("has_streaming_buffer"):
            delay_minutes = buffer_status.get("delay_minutes", 0)
            safe_offset = buffer_status.get("safe_offset_minutes", 120)

            # 智能建議
            recommendations = []

            if delay_minutes <= 30:
                recommendations.append({
                    "type": "optimization",
                    "priority": "medium",
                    "message": f"當前延遲只有 {delay_minutes:.1f} 分鐘，可考慮縮短同步偏移時間",
                    "suggested_action": f"可嘗試將偏移時間從 120 分鐘縮短至 {max(60, safe_offset)} 分鐘"
                })
            elif delay_minutes > 120:
                recommendations.append({
                    "type": "warning",
                    "priority": "high",
                    "message": f"當前延遲達 {delay_minutes:.1f} 分鐘，建議增加同步偏移時間",
                    "suggested_action": f"建議將偏移時間增加至 {safe_offset} 分鐘"
                })
            else:
                recommendations.append({
                    "type": "status",
                    "priority": "low",
                    "message": "當前偏移時間配置合理",
                    "suggested_action": "建議定期監控以確保配置最佳化"
                })

            # 歷史趨勢建議
            if delay_minutes < 60:
                recommendations.append({
                    "type": "trend_analysis",
                    "priority": "low",
                    "message": "建議分析歷史趨勢以確認是否可長期縮短偏移時間",
                    "suggested_action": "執行: python scripts/check_streaming_buffer.py --analyze-trends --recommend-offset"
                })

            response_data["recommendations"] = recommendations

        # 加入快速診斷
        if buffer_status.get("has_streaming_buffer"):
            delay_minutes = buffer_status.get("delay_minutes", 0)
            if delay_minutes <= 15:
                status_level = "excellent"
                status_message = "延遲極低，適合即時處理"
            elif delay_minutes <= 60:
                status_level = "good"
                status_message = "延遲在合理範圍內"
            elif delay_minutes <= 120:
                status_level = "acceptable"
                status_message = "延遲稍高，建議監控"
            else:
                status_level = "concerning"
                status_message = "延遲較高，建議增加偏移時間"

            response_data["quick_diagnosis"] = {
                "status_level": status_level,
                "status_message": status_message,
                "delay_assessment": f"當前延遲 {delay_minutes:.1f} 分鐘",
                "sync_safety": buffer_status.get("is_safe", True)
            }

        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"Streaming Buffer status endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
