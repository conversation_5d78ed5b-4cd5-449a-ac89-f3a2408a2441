# 家樂福離線事件資料專案 - Git 忽略檔案

# 自動生成的報告檔案（根目錄）
environment_setup_report.md
environment_setup_report.json
set_env.sh

# 工具執行產生的臨時報告檔案
*_bigquery_basic_check_*.txt
*_data_update_status_*.txt
*_time_distribution_analysis_*.txt
*_replication_report_*.txt
*_permission_report_*.txt
cost_analysis.py

# 自動生成的配置檔案
config.json

# 執行時產生的日誌檔案
*.log
carrefour_validation.log

# 臨時檔案
*.tmp
*.temp

# PR 準備檔案（暫時性，不應版本控制）
PR_DESCRIPTION.md
SLACK_REPORT.md

# Python 相關
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虛擬環境（如果不想追蹤）
# venv/
# .venv/

# IDE 相關
.vscode/
.idea/
*.swp
*.swo
*~

# macOS 相關
.DS_Store
.AppleDouble
.LSOverride

# Windows 相關
Thumbs.db
ehthumbs.db
Desktop.ini

# 認證檔案（安全考量）
*.json.key
*credentials*.json
.env.local
.env.production

# 執行時產生的快取檔案
.cache/
.pytest_cache/

# 備份檔案
*.bak
*.backup
# 大型中介 JSON 檔案（超過 20KB 或 1000 行）
reports/comprehensive_data_analysis*.json
reports/negative_values_investigation.json

# JSON 檔案管理策略 - 基於檔案大小和使用頻率分析
# 保留核心小型 JSON 檔案（< 20KB），排除大型中介檔案

# 大型中介 JSON 檔案（> 50KB）
reports/comprehensive_data_analysis*.json
reports/negative_values_investigation.json
reports/null_ph_id_analysis.json
reports/sha256_security_assessment.json

# 重複或舊版 JSON 檔案
reports/real_overlap_analysis_report.json
reports/ph_id_interactive_report_v2.json
reports/comprehensive_overlap_analysis.json
reports/ph_id_analysis_interactive_data.json
reports/ph_id_overlap_validation_report.json
reports/corrected_data_quality_analysis.json
reports/data_range_diagnostic_report.json
reports/simple_hash_reverse_analysis.json
reports/null_ph_id_communication_summary.json
reports/schema_comparison_report.json
reports/environment_setup_report.json

# 重複檔案目錄
reports/final_analysis/

# 可重新生成的執行報告
reports/complete_report_generation.json

# 保留的核心 JSON 檔案（< 20KB）：
# - reports/project_metadata.json (922B)
# - reports/corrected_overlap_analysis.json (5.1K)
# - reports/detailed_data_distribution_analysis.json (22K)
# - reports/full_overlap_analysis_100_percent.json (15K)
# - reports/items_format_validation.json (2.5K)
# - reports/offline_deep_analysis.json (16K)
# - reports/data_copy_report.json (2.3K)
# - reports/hash_validation_results.json (659B)

# Terraform 相關檔案
terraform/.terraform/
terraform/.terraform.lock.hcl
terraform/terraform.tfstate
terraform/terraform.tfstate.backup
terraform/tfplan
terraform/tfplan.out
terraform/function-source.zip
terraform/*.tfvars.backup
terraform/crash.log

# Terraform 產生的壓縮檔案
*.zip
