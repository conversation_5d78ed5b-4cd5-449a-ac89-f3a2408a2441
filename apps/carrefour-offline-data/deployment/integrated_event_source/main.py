#!/usr/bin/env python3
"""
家樂福 integrated_event 轉換 Cloud Function 入口點

這個 Cloud Function 負責將家樂福線下交易資料轉換為 integrated_event 格式。

設計原則：
1. HTTP 觸發器，支援 Cloud Scheduler 排程執行
2. 完整的錯誤處理和日誌記錄
3. 參數驗證和成本控制
4. 詳細的執行報告和監控指標

URL: carrefour-integrated-event-prod
排程: 每日 11:30 執行
依賴: carrefour-daily-merge-prod (11:00)
"""

import json
import logging
import os
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import functions_framework
from flask import Request
from carrefour_integrated_event_converter import CarrefourIntegratedEventConverter

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_request_parameters(request_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    驗證請求參數

    Args:
        request_json: 請求 JSON 資料

    Returns:
        驗證後的參數字典
    """
    # 預設參數
    default_params = {
        "lookback_days": 1,
        "dry_run": False,
        "max_cost_usd": 5.0,
        "limit": None,  # 生產環境不限制
        "operation_type": "daily_integrated_event_conversion"
    }

    # 合併請求參數與預設值
    params = {}
    for key, default_value in default_params.items():
        if key in request_json:
            params[key] = request_json[key]
        else:
            params[key] = default_value

    # 參數驗證
    if not isinstance(params["lookback_days"], int) or params["lookback_days"] < 1 or params["lookback_days"] > 30:
        raise ValueError("lookback_days 必須是 1-30 之間的整數")

    if not isinstance(params["max_cost_usd"], (int, float)) or params["max_cost_usd"] < 0 or params["max_cost_usd"] > 100:
        raise ValueError("max_cost_usd 必須在 0-100 之間")

    if params["limit"] is not None:
        if not isinstance(params["limit"], int) or params["limit"] < 1:
            raise ValueError("limit 必須是正整數")

    # 如果是測試環境，強制啟用 dry_run
    environment = os.getenv("ENVIRONMENT", "dev")
    if environment in ["dev", "test"]:
        params["dry_run"] = True
        logger.info(f"測試環境 ({environment}) 強制啟用 dry_run 模式")

    # 處理布林值參數
    if isinstance(params["dry_run"], str):
        params["dry_run"] = params["dry_run"].lower() in ('true', '1', 'yes')

    return params

def create_error_response(error_message: str, status_code: int = 500) -> Dict[str, Any]:
    """
    建立錯誤回應

    Args:
        error_message: 錯誤訊息
        status_code: HTTP 狀態碼

    Returns:
        錯誤回應字典
    """
    return {
        "success": False,
        "error": error_message,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "status_code": status_code,
        "service": "carrefour-integrated-event"
    }

def create_success_response(execution_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    建立成功回應

    Args:
        execution_result: 執行結果

    Returns:
        成功回應字典
    """
    return {
        "success": True,
        "execution_result": execution_result,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "status_code": 200,
        "service": "carrefour-integrated-event"
    }

@functions_framework.http
def main(request: Request) -> Dict[str, Any]:
    """
    Cloud Function 主要入口點

    Args:
        request: HTTP 請求物件

    Returns:
        執行結果的 JSON 回應
    """
    execution_start_time = datetime.now(timezone.utc)

    try:
        # 記錄執行開始
        logger.info("=== 家樂福 integrated_event 轉換 Cloud Function 開始執行 ===")
        logger.info(f"執行時間: {execution_start_time.isoformat()}")
        logger.info(f"環境: {os.getenv('ENVIRONMENT', 'unknown')}")

        # 健康檢查
        if request.method == 'GET':
            return {
                "success": True,
                "message": "家樂福 integrated_event 轉換 Cloud Function 運行正常",
                "timestamp": execution_start_time.isoformat(),
                "environment": os.getenv("ENVIRONMENT", "unknown"),
                "operation_type": "health_check",
                "service": "carrefour-integrated-event"
            }

        if request.method != 'POST':
            return create_error_response(f"不支援的請求方法: {request.method}", 405)

        # 解析 JSON 請求
        try:
            request_json = request.get_json(silent=True) or {}
        except Exception as e:
            return create_error_response(f"JSON 解析失敗: {str(e)}", 400)

        logger.info(f"請求參數: {json.dumps(request_json, ensure_ascii=False)}")

        # 驗證參數
        try:
            validated_params = validate_request_parameters(request_json)
            logger.info(f"驗證後參數: {json.dumps(validated_params, ensure_ascii=False)}")
        except ValueError as e:
            return create_error_response(f"參數驗證失敗: {str(e)}", 400)

        # 建立轉換器並執行
        converter = CarrefourIntegratedEventConverter(
            project_id=os.getenv("PROJECT_ID", "tagtoo-tracking"),
            source_dataset="event_prod"
        )

        # 執行轉換
        execution_result = converter.execute_conversion(
            lookback_days=validated_params["lookback_days"],
            limit=validated_params["limit"],
            dry_run=validated_params["dry_run"],
            max_cost_usd=validated_params["max_cost_usd"]
        )

        # 計算執行時間
        execution_end_time = datetime.now(timezone.utc)
        execution_duration = (execution_end_time - execution_start_time).total_seconds()

        # 新增執行時間資訊到結果
        execution_result["execution_metadata"] = {
            "start_time": execution_start_time.isoformat(),
            "end_time": execution_end_time.isoformat(),
            "duration_seconds": execution_duration,
            "cloud_function_name": os.getenv("K_SERVICE", "carrefour-integrated-event"),
            "environment": os.getenv("ENVIRONMENT", "unknown"),
            "revision": os.getenv("K_REVISION", "unknown"),
            "parameters": validated_params
        }

        # 記錄執行結果
        if execution_result.get("success"):
            logger.info("✅ 執行成功完成")
            if execution_result.get("inserted_rows", 0) > 0:
                logger.info(f"📝 成功寫入 {execution_result['inserted_rows']:,} 筆交易到 integrated_event")
            else:
                logger.info("📝 沒有新的交易需要轉換")
        else:
            logger.warning(f"❌ 執行失敗: {execution_result.get('message', 'Unknown error')}")

        logger.info(f"⏱️ 執行時間: {execution_duration:.2f} 秒")

        # 記錄重要指標
        stats = execution_result.get("stats", {})
        if stats:
            logger.info("📊 執行統計:")
            logger.info(f"  🎯 媒合成功率: {stats.get('match_rate_percentage', 0):.2f}%")
            logger.info(f"  👥 媒合用戶數: {stats.get('unique_matched_users', 0):,}")
            logger.info(f"  📝 新增交易數: {stats.get('new_transactions_to_insert', 0):,}")
            logger.info(f"  💰 查詢成本: ${execution_result.get('estimated_cost', 0):.4f}")

        logger.info("=== 家樂福 integrated_event 轉換 Cloud Function 執行完成 ===")

        return create_success_response(execution_result)

    except Exception as e:
        # 記錄詳細錯誤
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "execution_time": datetime.now(timezone.utc).isoformat()
        }

        logger.error(f"Cloud Function 執行失敗: {json.dumps(error_details, ensure_ascii=False)}")

        return create_error_response(
            f"內部錯誤: {str(e)}",
            500
        )

# 本地測試支援
if __name__ == "__main__":
    """本地測試執行"""
    import argparse
    from flask import Flask, request as flask_request

    app = Flask(__name__)

    @app.route("/", methods=["GET", "POST"])
    def test_endpoint():
        return main(flask_request)

    # 命令列參數
    parser = argparse.ArgumentParser(description='本地測試家樂福 integrated_event 轉換 Cloud Function')
    parser.add_argument('--port', type=int, default=8080, help='本地測試埠號')
    parser.add_argument('--debug', action='store_true', help='除錯模式')

    args = parser.parse_args()

    print(f"啟動本地測試伺服器: http://localhost:{args.port}")
    print("測試 GET 請求: curl http://localhost:8080")
    print("""測試 POST 請求: curl -X POST http://localhost:8080 \\
  -H "Content-Type: application/json" \\
  -d '{"dry_run": true, "lookback_days": 1, "limit": 100}'""")

    app.run(host="0.0.0.0", port=args.port, debug=args.debug)