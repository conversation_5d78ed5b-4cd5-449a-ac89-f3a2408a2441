#!/usr/bin/env python3
"""
家樂福線下交易資料轉換為 integrated_event 格式

此模組負責：
1. 從最新 24 小時的 carrefour_offline_transaction 抓取資料
2. 與 tagtoo_entity (ec_id=715) 進行媒合取得 permanent
3. 轉換為 integrated_event 標準格式
4. 寫入 integrated_event 表，避免重複

重要特性：
- 考慮 Carrefour 資料延遲（至少2天前的資料）
- 基於資料的實際最新日期而非 CURRENT_DATE
- 完整的錯誤處理和監控指標
- 支援 dry-run 模式進行測試
"""

import os
import sys
import logging
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import traceback

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CarrefourIntegratedEventConverter:
    """家樂福線下交易資料轉換器"""

    def __init__(self,
                 project_id: str = "tagtoo-tracking",
                 source_dataset: str = "event_prod"):
        """
        初始化轉換器

        Args:
            project_id: BigQuery 專案 ID
            source_dataset: 資料集名稱
        """
        self.project_id = project_id
        self.source_dataset = source_dataset
        self.client = bigquery.Client(project=project_id)

        # 表格配置
        self.tables = {
            "carrefour_offline": f"{project_id}.{source_dataset}.carrefour_offline_transaction",
            "tagtoo_entity": f"{project_id}.{source_dataset}.tagtoo_entity",
            "integrated_event": f"{project_id}.{source_dataset}.integrated_event"
        }

        # 執行統計
        self.stats = {
            "execution_start": datetime.now(timezone.utc),
            "data_date_range": {},
            "processing_metrics": {},
            "error_logs": []
        }

    def get_latest_data_date(self) -> str:
        """
        獲取 Carrefour 資料的實際最新日期

        Returns:
            最新資料日期 (YYYY-MM-DD 格式)
        """
        query = f"""
        SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_data_date
        FROM `{self.tables["carrefour_offline"]}`
        WHERE ph_id IS NOT NULL
        """

        try:
            result = self.client.query(query).result()
            max_date = list(result)[0].max_data_date

            if max_date is None:
                raise ValueError("無法取得 Carrefour 資料的最新日期")

            logger.info(f"Carrefour 資料最新日期: {max_date}")
            return max_date.strftime('%Y-%m-%d')

        except Exception as e:
            logger.error(f"取得最新資料日期失敗: {e}")
            raise

    def analyze_data_volume(self, lookback_days: int = 1) -> Dict[str, Any]:
        """
        分析指定時間範圍內的資料量

        Args:
            lookback_days: 回溯天數

        Returns:
            資料量分析結果
        """
        latest_date = self.get_latest_data_date()

        query = f"""
        WITH analysis_data AS (
          SELECT
            COUNT(*) as total_transactions,
            COUNT(DISTINCT TO_HEX(ph_id)) as unique_ph_ids,
            COUNT(DISTINCT transaction_id) as unique_transaction_ids,
            MIN(DATE(TIMESTAMP_SECONDS(event_times))) as min_date,
            MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date,
            SUM(total_amount) as total_value
          FROM `{self.tables["carrefour_offline"]}`
          WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(DATE('{latest_date}'), INTERVAL {lookback_days} DAY)
            AND ph_id IS NOT NULL
            AND transaction_id IS NOT NULL
        )
        SELECT * FROM analysis_data
        """

        try:
            result = self.client.query(query).result()
            analysis = dict(list(result)[0])

            # 格式化結果
            for key, value in analysis.items():
                if isinstance(value, datetime):
                    analysis[key] = value.strftime('%Y-%m-%d')

            analysis['lookback_days'] = lookback_days
            analysis['latest_data_date'] = latest_date

            logger.info(f"資料量分析完成: {analysis['total_transactions']:,} 筆交易")
            return analysis

        except Exception as e:
            logger.error(f"資料量分析失敗: {e}")
            raise

    def execute_matching_analysis(self, lookback_days: int = 1) -> Dict[str, Any]:
        """
        執行媒合分析，計算媒合成功率和相關指標

        Args:
            lookback_days: 回溯天數

        Returns:
            媒合分析結果
        """
        latest_date = self.get_latest_data_date()

        query = f"""
        WITH latest_data_date AS (
          SELECT DATE('{latest_date}') as max_data_date
        ),
        recent_transactions AS (
          SELECT
            transaction_id,
            event_name,
            event_times,
            store_id,
            store_name,
            gender,
            member_id,
            ph_id,
            total_amount,
            payment_method,
            items,
            TO_HEX(ph_id) as ph_id_hex,
            DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date
          FROM `{self.tables["carrefour_offline"]}` t, latest_data_date l
          WHERE DATE(TIMESTAMP_SECONDS(t.event_times)) >= DATE_SUB(l.max_data_date, INTERVAL {lookback_days} DAY)
            AND t.ph_id IS NOT NULL
            AND t.transaction_id IS NOT NULL
        ),
        entity_mapping AS (
          SELECT DISTINCT
            permanent,
            mobile
          FROM `{self.tables["tagtoo_entity"]}`
          WHERE ec_id = 715
            AND mobile IS NOT NULL
            AND permanent IS NOT NULL
        ),
        matched_transactions AS (
          SELECT
            r.*,
            e.permanent
          FROM recent_transactions r
          INNER JOIN entity_mapping e ON r.ph_id_hex = e.mobile
        ),
        duplicate_check AS (
          SELECT
            m.*,
            CASE WHEN EXISTS (
              SELECT 1 FROM `{self.tables["integrated_event"]}` ie
              WHERE ie.order_id = m.transaction_id
                AND ie.partner_source = 'carrefour_offline'
            ) THEN 1 ELSE 0 END as already_exists
          FROM matched_transactions m
        )
        SELECT
          COUNT(*) as total_matched_transactions,
          COUNT(DISTINCT permanent) as unique_matched_users,
          COUNT(DISTINCT transaction_id) as unique_matched_transaction_ids,
          COUNT(DISTINCT ph_id_hex) as unique_ph_ids,
          SUM(already_exists) as already_in_integrated_event,
          COUNT(*) - SUM(already_exists) as new_transactions_to_insert,
          MIN(transaction_date) as date_range_start,
          MAX(transaction_date) as date_range_end,
          SUM(total_amount) as total_matched_value
        FROM duplicate_check
        """

        try:
            result = self.client.query(query).result()
            matching_stats = dict(list(result)[0])

            # 計算媒合率
            data_analysis = self.analyze_data_volume(lookback_days)
            if data_analysis['total_transactions'] > 0:
                match_rate = (matching_stats['total_matched_transactions'] /
                            data_analysis['total_transactions']) * 100
                matching_stats['match_rate_percentage'] = round(match_rate, 2)
            else:
                matching_stats['match_rate_percentage'] = 0.0

            # 格式化日期
            for key in ['date_range_start', 'date_range_end']:
                if matching_stats[key]:
                    matching_stats[key] = matching_stats[key].strftime('%Y-%m-%d')

            # 記錄統計資訊
            self.stats["data_date_range"] = {
                "start": matching_stats['date_range_start'],
                "end": matching_stats['date_range_end'],
                "lookback_days": lookback_days
            }
            self.stats["processing_metrics"] = matching_stats

            logger.info("📊 媒合分析結果:")
            logger.info(f"  📅 資料範圍: {matching_stats['date_range_start']} ~ {matching_stats['date_range_end']}")
            logger.info(f"  🎯 媒合成功率: {matching_stats['match_rate_percentage']:.2f}%")
            logger.info(f"  ✅ 媒合交易數: {matching_stats['total_matched_transactions']:,} 筆")
            logger.info(f"  👥 媒合用戶數: {matching_stats['unique_matched_users']:,} 人")
            logger.info(f"  🔄 重複資料: {matching_stats['already_in_integrated_event']:,} 筆")
            logger.info(f"  ➕ 新增資料: {matching_stats['new_transactions_to_insert']:,} 筆")

            return matching_stats

        except Exception as e:
            logger.error(f"媒合分析失敗: {e}")
            raise

    def generate_conversion_query(self, lookback_days: int = 1, limit: int = None) -> str:
        """
        生成完整的轉換查詢語句

        Args:
            lookback_days: 回溯天數
            limit: 限制處理筆數 (測試用)

        Returns:
            完整的 SQL 查詢語句
        """
        latest_date = self.get_latest_data_date()

        query = f"""
        WITH latest_data_date AS (
          SELECT DATE('{latest_date}') as max_data_date
        ),
        recent_transactions AS (
          SELECT
            transaction_id,
            event_name,
            event_times,
            store_id,
            store_name,
            gender,
            member_id,
            ph_id,
            total_amount,
            payment_method,
            items,
            TO_HEX(ph_id) as ph_id_hex,
            DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date
          FROM `{self.tables["carrefour_offline"]}` t, latest_data_date l
          WHERE DATE(TIMESTAMP_SECONDS(t.event_times)) >= DATE_SUB(l.max_data_date, INTERVAL {lookback_days} DAY)
            AND t.ph_id IS NOT NULL
            AND t.transaction_id IS NOT NULL
        ),
        entity_mapping AS (
          SELECT DISTINCT
            permanent,
            mobile
          FROM `{self.tables["tagtoo_entity"]}`
          WHERE ec_id = 715
            AND mobile IS NOT NULL
            AND permanent IS NOT NULL
        ),
        matched_transactions AS (
          SELECT
            r.*,
            e.permanent
          FROM recent_transactions r
          INNER JOIN entity_mapping e ON r.ph_id_hex = e.mobile
        ),
        valid_transactions AS (
          SELECT m.*
          FROM matched_transactions m
          WHERE NOT EXISTS (
            SELECT 1 FROM `{self.tables["integrated_event"]}` ie
            WHERE ie.order_id = m.transaction_id
              AND ie.partner_source = 'carrefour_offline'
          )
        )
        -- 轉換為 integrated_event 格式
        SELECT
          v.permanent,
          CAST(NULL AS INT64) as partner_id,
          715 as ec_id,
          'carrefour_offline' as partner_source,
          COALESCE(v.event_name, 'Purchase') as event,
          TIMESTAMP_SECONDS(v.event_times - 8*3600) as event_time,  -- 轉回 UTC
          CURRENT_TIMESTAMP() as create_time,
          CAST(v.total_amount AS FLOAT64) as value,
          'TWD' as currency,
          v.transaction_id as order_id,
          CAST(NULL AS STRING) as link,
          -- user 資訊
          STRUCT(
            CASE
              WHEN v.member_id IS NOT NULL THEN TO_HEX(v.member_id)
              ELSE CAST(NULL AS STRING)
            END as partner_user_id,  -- 使用 member_id (轉為十六進位)
            CAST(NULL AS STRING) as em,
            v.ph_id_hex as ph
          ) as user,
          -- 商品陣列轉換
          ARRAY(
            SELECT STRUCT(
              item.item_id as id,
              item.item_name as name,
              CONCAT(
                COALESCE(item.GRP_CLASS_DESC, ''),
                IF(item.CLASS_DESC IS NOT NULL, CONCAT(' > ', item.CLASS_DESC), ''),
                IF(item.SUB_CLASS_DESC IS NOT NULL, CONCAT(' > ', item.SUB_CLASS_DESC), '')
              ) as description,
              CAST(item.unit_price AS FLOAT64) as price,
              CAST(item.quantity AS FLOAT64) as quantity
            )
            FROM UNNEST(v.items) as item
            WHERE item.item_id IS NOT NULL
          ) as items,
          -- page 資訊 (線下交易無網頁資訊)
          STRUCT(
            CAST(NULL AS STRING) as title,
            CAST(NULL AS STRING) as description
          ) as page,
          -- location 資訊 (暫時為空，未來可從 store_id 推導)
          STRUCT(
            'TW' as country_code,  -- 台灣
            CAST(NULL AS STRING) as region_name,
            CAST(NULL AS STRING) as city_name
          ) as location,
          -- raw_json 存放額外資訊
          STRUCT(
            'v1.0' as schema_version,
            TO_JSON(STRUCT(
              STRUCT(
                v.store_id as store_id,
                v.store_name as store_name
              ) as store_info,
              STRUCT(
                v.gender as gender
              ) as customer_info,
              STRUCT(
                v.payment_method as payment_method,
                v.event_times as original_event_times_taiwan,
                v.transaction_date as transaction_date
              ) as transaction_info
            )) as data
          ) as raw_json
        FROM valid_transactions v
        WHERE v.permanent IS NOT NULL  -- 確保有 permanent
          AND v.transaction_id IS NOT NULL  -- 確保有 transaction_id
          AND ARRAY_LENGTH(v.items) > 0  -- 確保有商品項目
        """

        # 如果有 limit 參數，添加 LIMIT 子句
        if limit and limit > 0:
            query += f" LIMIT {limit}"

        return query

    def estimate_conversion_cost(self, lookbook_days: int = 1, limit: int = None) -> float:
        """
        估算轉換查詢的成本

        Args:
            lookbook_days: 回溯天數
            limit: 限制處理筆數

        Returns:
            預估成本 (USD)
        """
        query = self.generate_conversion_query(lookbook_days, limit)

        try:
            # 建立 dry run 查詢配置
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            query_job = self.client.query(query, job_config=job_config)

            # 計算成本 (每 TB $5)
            total_bytes = query_job.total_bytes_processed
            cost_usd = (total_bytes / (1024**4)) * 5.0  # TB 轉換和定價

            logger.info(f"💰 轉換查詢成本估算:")
            logger.info(f"  📊 處理資料量: {total_bytes / (1024**3):.2f} GB")
            logger.info(f"  💵 預估成本: ${cost_usd:.4f} USD")

            return cost_usd

        except Exception as e:
            logger.error(f"成本估算失敗: {e}")
            raise

    def execute_conversion(self,
                          lookback_days: int = 1,
                          limit: int = None,
                          dry_run: bool = False,
                          max_cost_usd: float = 5.0) -> Dict[str, Any]:
        """
        執行完整的轉換流程

        Args:
            lookback_days: 回溯天數
            limit: 限制處理筆數 (測試用)
            dry_run: 是否僅模擬執行
            max_cost_usd: 最大允許成本

        Returns:
            執行結果
        """
        logger.info("🚀 開始執行家樂福線下交易資料轉換...")
        logger.info(f"📝 參數: lookback_days={lookback_days}, limit={limit}, dry_run={dry_run}")

        try:
            # 1. 分析資料量和媒合效果
            logger.info("📊 步驟 1: 分析資料量和媒合效果...")
            matching_stats = self.execute_matching_analysis(lookback_days)

            if matching_stats['new_transactions_to_insert'] == 0:
                logger.warning("⚠️ 沒有新的交易需要轉換")
                return {
                    "success": True,
                    "message": "沒有新的交易需要轉換",
                    "stats": matching_stats,
                    "inserted_rows": 0
                }

            # 2. 估算成本
            logger.info("💰 步驟 2: 估算查詢成本...")
            estimated_cost = self.estimate_conversion_cost(lookback_days, limit)

            if estimated_cost > max_cost_usd:
                raise ValueError(f"查詢成本 ${estimated_cost:.4f} 超過限制 ${max_cost_usd}")

            # 3. 執行轉換 (或 dry run)
            if dry_run:
                logger.info("🧪 DRY RUN 模式: 僅模擬執行，不實際寫入資料")
                return {
                    "success": True,
                    "message": "DRY RUN 完成",
                    "stats": matching_stats,
                    "estimated_cost": estimated_cost,
                    "would_insert_rows": matching_stats['new_transactions_to_insert']
                }

            # 4. 實際執行轉換
            logger.info("⚙️ 步驟 3: 執行資料轉換和寫入...")
            conversion_query = self.generate_conversion_query(lookback_days, limit)

            # 插入資料到 integrated_event
            insert_query = f"""
            INSERT INTO `{self.tables["integrated_event"]}`
            ({conversion_query})
            """

            # 執行插入
            job_config = bigquery.QueryJobConfig(
                labels={
                    "application": "carrefour-integrated-event",
                    "operation": "daily-conversion",
                    "environment": os.getenv("ENVIRONMENT", "prod")
                }
            )

            query_job = self.client.query(insert_query, job_config=job_config)
            result = query_job.result()

            # 統計執行結果
            inserted_rows = query_job.num_dml_affected_rows or 0

            # 記錄成功資訊
            execution_time = (datetime.now(timezone.utc) - self.stats["execution_start"]).total_seconds()

            logger.info("✅ 轉換執行完成!")
            logger.info(f"  📝 寫入筆數: {inserted_rows:,} 筆")
            logger.info(f"  ⏱️ 執行時間: {execution_time:.2f} 秒")
            logger.info(f"  💰 實際成本: ${estimated_cost:.4f} USD")

            return {
                "success": True,
                "message": "轉換執行完成",
                "stats": matching_stats,
                "estimated_cost": estimated_cost,
                "inserted_rows": inserted_rows,
                "execution_time_seconds": execution_time
            }

        except Exception as e:
            # 記錄錯誤
            error_detail = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "traceback": traceback.format_exc(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.stats["error_logs"].append(error_detail)

            logger.error(f"❌ 轉換執行失敗: {e}")
            logger.error(f"詳細錯誤: {traceback.format_exc()}")

            return {
                "success": False,
                "message": f"轉換執行失敗: {str(e)}",
                "error": error_detail,
                "stats": self.stats.get("processing_metrics", {})
            }

# 命令列介面
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='家樂福線下交易資料轉換為 integrated_event')
    parser.add_argument('--lookback-days', type=int, default=1, help='回溯天數 (預設: 1)')
    parser.add_argument('--limit', type=int, help='限制處理筆數 (測試用)')
    parser.add_argument('--dry-run', action='store_true', help='僅模擬執行，不實際寫入')
    parser.add_argument('--max-cost', type=float, default=5.0, help='最大允許成本 (USD)')
    parser.add_argument('--project-id', default='tagtoo-tracking', help='BigQuery 專案 ID')

    args = parser.parse_args()

    # 執行轉換
    converter = CarrefourIntegratedEventConverter(project_id=args.project_id)
    result = converter.execute_conversion(
        lookback_days=args.lookback_days,
        limit=args.limit,
        dry_run=args.dry_run,
        max_cost_usd=args.max_cost
    )

    # 輸出結果
    print("\n" + "="*60)
    print("家樂福線下交易資料轉換結果")
    print("="*60)
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))