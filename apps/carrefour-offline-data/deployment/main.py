#!/usr/bin/env python3
"""
Google Cloud Function 主程式
家樂福受眾媒合系統 Cloud Function 入口點

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any
import functions_framework
from google.cloud import pubsub_v1

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 導入自定義模組
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analysis.carrefour_audience_matching import CarrefourAudienceMatching
from analysis.data_quality_validator import DataQualityValidator

class CloudFunctionConfig:
    """Cloud Function 配置管理"""

    def __init__(self):
        self.project_id = os.getenv('PROJECT_ID', 'tagtoo-tracking')
        self.target_project_id = os.getenv('TARGET_PROJECT_ID', 'tagtoo-ml-workflow')
        self.ec_id = int(os.getenv('EC_ID', '715'))
        self.max_cost_usd = float(os.getenv('MAX_COST_USD', '50.0'))
        self.batch_size = int(os.getenv('BATCH_SIZE', '10000'))
        self.test_mode = os.getenv('TEST_MODE', 'false').lower() == 'true'

    def get_config(self) -> Dict:
        """取得配置字典"""
        return {
            "source_tables": {
                "tagtoo_event": f"{self.project_id}.event_prod.tagtoo_event",
                "carrefour_offline": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
            },
            "target_table": f"{self.target_project_id}.tagtoo_export_results.special_lta_temp_for_update_{{date}}",
            "product_csv": "家樂福商品分類表（塔圖內部用） - 所有商品分類.csv",
            "batch_size": self.batch_size,
            "max_cost_usd": self.max_cost_usd,
            "ec_id": self.ec_id
        }

@functions_framework.http
def main(request):
    """
    HTTP 觸發器主程式

    Args:
        request: HTTP 請求物件

    Returns:
        HTTP 回應
    """
    try:
        logger.info("開始執行家樂福受眾媒合 Cloud Function...")

        # 解析請求參數
        request_json = request.get_json(silent=True)
        if not request_json:
            request_json = {}

        # 執行媒合流程
        result = execute_audience_matching(request_json)

        # 回傳結果
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "result": result
        }, 200

    except Exception as e:
        logger.error(f"Cloud Function 執行失敗: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }, 500

@functions_framework.cloud_event
def main_pubsub(cloud_event):
    """
    Pub/Sub 觸發器主程式

    Args:
        cloud_event: Cloud Event 物件
    """
    try:
        logger.info("開始執行家樂福受眾媒合 Cloud Function (Pub/Sub)...")

        # 解析 Pub/Sub 訊息
        message_data = {}
        if cloud_event.data and 'message' in cloud_event.data:
            message = cloud_event.data['message']
            if 'data' in message:
                import base64
                message_data = json.loads(base64.b64decode(message['data']).decode('utf-8'))

        # 執行媒合流程
        result = execute_audience_matching(message_data)

        logger.info(f"Cloud Function 執行完成: {result}")

    except Exception as e:
        logger.error(f"Cloud Function 執行失敗: {e}")
        raise

def execute_audience_matching(params: Dict[str, Any]) -> Dict:
    """
    執行受眾媒合流程

    Args:
        params: 執行參數

    Returns:
        執行結果
    """
    # 載入配置
    config = CloudFunctionConfig()

    # 解析參數
    test_mode = params.get('test_mode', config.test_mode)
    days_back = params.get('days_back', 30 if not test_mode else 7)
    limit = params.get('limit', 1000 if test_mode else None)

    logger.info(f"執行模式: {'測試' if test_mode else '生產'}")
    logger.info(f"回溯天數: {days_back}")
    logger.info(f"記錄限制: {limit}")

    # 初始化媒合系統
    matcher = CarrefourAudienceMatching(config.get_config())

    # 執行流程
    execution_stats = {
        "start_time": datetime.now().isoformat(),
        "test_mode": test_mode,
        "days_back": days_back,
        "limit": limit
    }

    try:
        # 1. 建立用戶對應表
        logger.info("建立用戶對應表...")
        user_mapping_success = matcher.build_user_mapping_table(days_back=days_back)
        if not user_mapping_success:
            raise Exception("建立用戶對應表失敗")

        execution_stats["user_mappings"] = len(matcher.user_mapping)

        # 2. 查詢線下購買資料
        logger.info("查詢線下購買資料...")
        purchases = matcher.query_offline_purchases(limit=limit)
        if not purchases:
            raise Exception("查詢線下購買資料失敗")

        execution_stats["purchase_records"] = len(purchases)

        # 3. 生成受眾標籤
        logger.info("生成受眾標籤...")
        audience_data = matcher.generate_audience_segments(purchases)

        execution_stats["audience_users"] = len(audience_data)
        execution_stats["avg_segments_per_user"] = (
            sum(len(user['segment_id'].split(',')) for user in audience_data) / len(audience_data)
            if audience_data else 0
        )

        # 4. 資料品質驗證
        logger.info("驗證資料品質...")
        validator = DataQualityValidator()
        validation_results = validator.validate_audience_data(audience_data)

        execution_stats["data_quality"] = {
            "overall_score": validator.quality_metrics.get("overall_score", 0),
            "grade": validator.quality_metrics.get("grade", "N/A")
        }

        # 5. 寫入目標表格 (僅生產模式)
        if not test_mode and audience_data:
            logger.info("寫入目標表格...")
            write_success = matcher.write_to_target_table(audience_data)
            execution_stats["write_success"] = write_success

            if write_success:
                target_table = matcher.config["target_table"].format(date=datetime.now().strftime('%Y%m%d'))
                execution_stats["target_table"] = target_table
                execution_stats["records_written"] = len(audience_data)
        else:
            execution_stats["write_success"] = "skipped" if test_mode else "no_data"

        execution_stats["end_time"] = datetime.now().isoformat()
        execution_stats["status"] = "success"

        logger.info("受眾媒合流程執行完成")
        return execution_stats

    except Exception as e:
        execution_stats["end_time"] = datetime.now().isoformat()
        execution_stats["status"] = "failed"
        execution_stats["error"] = str(e)

        logger.error(f"受眾媒合流程執行失敗: {e}")
        raise

def health_check():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "carrefour-audience-matching",
        "version": "1.0.0"
    }

# 本地測試用
if __name__ == "__main__":
    # 模擬 HTTP 請求
    class MockRequest:
        def get_json(self, silent=True):
            return {"test_mode": True, "days_back": 3, "limit": 50}

    result, status_code = main(MockRequest())
    print(f"結果 ({status_code}): {json.dumps(result, indent=2, ensure_ascii=False)}")
