#!/bin/bash
# 家樂福受眾媒合系統 Cloud Function 部署腳本
#
# 使用方法:
#   ./deploy.sh [test|production]
#
# 作者: Tagtoo Data Team
# 日期: 2025-08-25

set -e  # 遇到錯誤立即退出

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查參數
ENVIRONMENT=${1:-test}
if [[ "$ENVIRONMENT" != "test" && "$ENVIRONMENT" != "production" ]]; then
    log_error "無效的環境參數。使用方法: $0 [test|production]"
    exit 1
fi

log_info "開始部署家樂福受眾媒合系統到 $ENVIRONMENT 環境..."

# 設定變數
PROJECT_ID="tagtoo-tracking"
FUNCTION_NAME="carrefour-audience-matching-$ENVIRONMENT"
REGION="asia-east1"
SOURCE_DIR="../src"
DEPLOYMENT_DIR="."

# 環境特定配置
if [[ "$ENVIRONMENT" == "production" ]]; then
    MEMORY="4096MB"
    TIMEOUT="540s"
    MAX_INSTANCES="3"
    MAX_COST_USD="100.0"
    TEST_MODE="false"
    SCHEDULE="0 2 * * *"  # 每天凌晨 2 點
else
    MEMORY="2048MB"
    TIMEOUT="300s"
    MAX_INSTANCES="1"
    MAX_COST_USD="10.0"
    TEST_MODE="true"
    SCHEDULE="0 */6 * * *"  # 每 6 小時
fi

# 檢查必要工具
log_info "檢查必要工具..."
if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI 未安裝"
    exit 1
fi

if ! command -v python3 &> /dev/null; then
    log_error "Python 3 未安裝"
    exit 1
fi

# 檢查 gcloud 認證
log_info "檢查 gcloud 認證..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "請先執行 gcloud auth login"
    exit 1
fi

# 設定專案
log_info "設定 GCP 專案..."
gcloud config set project $PROJECT_ID

# 啟用必要的 API
log_info "啟用必要的 API..."
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
gcloud services enable pubsub.googleapis.com

# 準備部署檔案
log_info "準備部署檔案..."
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# 複製原始碼
cp -r $SOURCE_DIR/* $TEMP_DIR/
cp main.py $TEMP_DIR/
cp requirements.txt $TEMP_DIR/

# 複製商品分類檔案
cp "../docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv" $TEMP_DIR/

# 建立 requirements.txt (如果不存在)
if [[ ! -f "$TEMP_DIR/requirements.txt" ]]; then
    log_info "建立 requirements.txt..."
    cat > $TEMP_DIR/requirements.txt << EOF
google-cloud-bigquery>=3.11.0
google-cloud-functions-framework>=3.4.0
google-cloud-pubsub>=2.18.0
pandas>=2.0.0
functions-framework>=3.4.0
EOF
fi

# 部署 Cloud Function
log_info "部署 Cloud Function..."
cd $TEMP_DIR

gcloud functions deploy $FUNCTION_NAME \
    --gen2 \
    --runtime=python39 \
    --region=$REGION \
    --source=. \
    --entry-point=main \
    --memory=$MEMORY \
    --timeout=$TIMEOUT \
    --max-instances=$MAX_INSTANCES \
    --set-env-vars="PROJECT_ID=$PROJECT_ID,TARGET_PROJECT_ID=tagtoo-ml-workflow,EC_ID=715,MAX_COST_USD=$MAX_COST_USD,TEST_MODE=$TEST_MODE" \
    --service-account="carrefour-audience-matching@$PROJECT_ID.iam.gserviceaccount.com" \
    --trigger-http \
    --allow-unauthenticated

# 取得 Function URL
FUNCTION_URL=$(gcloud functions describe $FUNCTION_NAME --region=$REGION --format="value(serviceConfig.uri)")
log_success "Cloud Function 部署完成: $FUNCTION_URL"

# 建立 Pub/Sub 主題 (如果不存在)
TOPIC_NAME="carrefour-audience-matching-$ENVIRONMENT"
if ! gcloud pubsub topics describe $TOPIC_NAME &> /dev/null; then
    log_info "建立 Pub/Sub 主題..."
    gcloud pubsub topics create $TOPIC_NAME
fi

# 建立 Cloud Scheduler 工作 (僅生產環境)
if [[ "$ENVIRONMENT" == "production" ]]; then
    JOB_NAME="carrefour-audience-matching-schedule"

    # 刪除現有工作 (如果存在)
    if gcloud scheduler jobs describe $JOB_NAME --location=$REGION &> /dev/null; then
        log_info "刪除現有的 Scheduler 工作..."
        gcloud scheduler jobs delete $JOB_NAME --location=$REGION --quiet
    fi

    log_info "建立 Cloud Scheduler 工作..."
    gcloud scheduler jobs create http $JOB_NAME \
        --location=$REGION \
        --schedule="$SCHEDULE" \
        --time-zone="Asia/Taipei" \
        --uri="$FUNCTION_URL" \
        --http-method=POST \
        --headers="Content-Type=application/json" \
        --message-body='{"test_mode": false, "days_back": 30}' \
        --description="家樂福受眾媒合系統自動執行"

    log_success "Cloud Scheduler 工作建立完成"
fi

# 執行測試
log_info "執行部署測試..."
TEST_PAYLOAD='{"test_mode": true, "days_back": 3, "limit": 10}'

curl -X POST \
    -H "Content-Type: application/json" \
    -d "$TEST_PAYLOAD" \
    "$FUNCTION_URL" \
    --max-time 60 \
    --silent \
    --show-error \
    --fail

if [[ $? -eq 0 ]]; then
    log_success "部署測試通過"
else
    log_warning "部署測試失敗，請檢查 Cloud Function 日誌"
fi

# 顯示部署資訊
echo ""
echo "=================================="
echo "部署完成資訊"
echo "=================================="
echo "環境: $ENVIRONMENT"
echo "Function 名稱: $FUNCTION_NAME"
echo "Function URL: $FUNCTION_URL"
echo "記憶體: $MEMORY"
echo "逾時: $TIMEOUT"
echo "最大實例數: $MAX_INSTANCES"
echo "測試模式: $TEST_MODE"

if [[ "$ENVIRONMENT" == "production" ]]; then
    echo "排程: $SCHEDULE (Asia/Taipei)"
    echo "Scheduler 工作: $JOB_NAME"
fi

echo ""
echo "=================================="
echo "後續操作"
echo "=================================="
echo "1. 檢查 Function 日誌:"
echo "   gcloud functions logs read $FUNCTION_NAME --region=$REGION"
echo ""
echo "2. 手動觸發測試:"
echo "   curl -X POST -H 'Content-Type: application/json' -d '$TEST_PAYLOAD' '$FUNCTION_URL'"
echo ""
echo "3. 監控 Function 執行:"
echo "   https://console.cloud.google.com/functions/details/$REGION/$FUNCTION_NAME"

if [[ "$ENVIRONMENT" == "production" ]]; then
    echo ""
    echo "4. 檢查 Scheduler 狀態:"
    echo "   gcloud scheduler jobs describe $JOB_NAME --location=$REGION"
fi

log_success "家樂福受眾媒合系統部署完成！"
