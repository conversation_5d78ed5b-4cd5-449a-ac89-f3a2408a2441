# Google Cloud Function 部署配置
# 家樂福受眾媒合系統自動化部署

name: carrefour-audience-matching
description: "家樂福線下購買資料與 Tagtoo 線上事件資料媒合及受眾分類系統"

# 運行時配置
runtime: python39
entry_point: main
timeout: 540s # 9 分鐘
memory: 2048MB
max_instances: 1

# 環境變數
env_vars:
  PROJECT_ID: "tagtoo-tracking"
  TARGET_PROJECT_ID: "tagtoo-ml-workflow"
  EC_ID: "715"
  MAX_COST_USD: "50.0"
  BATCH_SIZE: "10000"

# 觸發器配置
trigger:
  type: "pubsub"
  topic: "carrefour-audience-matching-trigger"

# 或者使用 HTTP 觸發器
# trigger:
#   type: "http"
#   security_level: "secure_always"

# 或者使用排程觸發器 (Cloud Scheduler)
# schedule:
#   cron: "0 2 * * *"  # 每天凌晨 2 點執行
#   timezone: "Asia/Taipei"

# IAM 權限
iam:
  service_account: "<EMAIL>"
  roles:
    - "roles/bigquery.dataViewer"
    - "roles/bigquery.jobUser"
    - "roles/bigquery.dataEditor" # 用於寫入目標表格

# 網路配置
network:
  vpc_connector: "projects/tagtoo-tracking/locations/asia-east1/connectors/default-connector"

# 監控和日誌
monitoring:
  enable_cloud_logging: true
  enable_cloud_monitoring: true

# 錯誤報告
error_reporting:
  enabled: true

# 部署腳本
deployment:
  source_dir: "../src"
  requirements_file: "../requirements.txt"
  include_files:
    - "analysis/*.py"
    - "../docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv"

# 測試配置
testing:
  test_mode: true
  limit_records: 100
  days_back: 7
