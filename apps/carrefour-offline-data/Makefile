# Carrefour Offline Data Service - Makefile
# 常用開發指令

.PHONY: help install test test-unit test-integration tf-check tf-validate tf-fmt tf-plan tf-init auth-check clean info

# 顯示幫助資訊
help:
	@echo "Carrefour Offline Data Service - Available Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install       - 安裝 Python 依賴套件"
	@echo ""
	@echo "Testing:"
	@echo "  test          - 運行所有測試（自動安裝依賴）"
	@echo "  test-unit     - 運行單元測試"
	@echo "  test-integration - 運行整合測試（真實 BigQuery）"
	@echo ""
	@echo "Terraform:"
	@echo "  tf-check      - 完整 Terraform 檢查（推薦）"
	@echo "  tf-validate   - Terraform 語法驗證"
	@echo "  tf-fmt        - Terraform 格式化"
	@echo "  tf-plan       - Terraform 計畫檢查"
	@echo "  tf-init       - Terraform 初始化"
	@echo ""
	@echo "Authentication:"
	@echo "  auth-check    - 檢查認證狀態"
	@echo ""
	@echo "Cleanup:"
	@echo "  clean         - 清理暫存檔案"
	@echo ""
	@echo "Info:"
	@echo "  info          - 顯示專案資訊"

# 安裝 Python 依賴套件
install:
	@echo "📦 安裝 Python 依賴套件..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 pip 直接安裝"; \
		pip install -r requirements.txt; \
	else \
		echo "🖥️  本地環境：檢查虛擬環境"; \
		if [ -d "venv" ] && [ -f "venv/bin/activate" ]; then \
			echo "📦 使用虛擬環境安裝"; \
			source venv/bin/activate && pip install -r requirements.txt; \
		else \
			echo "⚠️  建議建立虛擬環境，目前使用系統 Python"; \
			pip install -r requirements.txt; \
		fi; \
	fi
	@echo "✅ 依賴套件安裝完成"

# 運行所有測試
test: install
	@echo "🧪 執行所有測試..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：執行 pytest 測試"; \
		python -m pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=xml --cov-report=html --junitxml=pytest-report.xml; \
	else \
		echo "🖥️  本地環境：執行測試"; \
		if [ -d "venv" ] && [ -f "venv/bin/activate" ]; then \
			echo "📦 使用虛擬環境"; \
			source venv/bin/activate && python -m pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=xml --cov-report=html; \
		else \
			echo "⚠️  使用系統 Python 執行測試"; \
			python -m pytest tests/ -v --cov=src --cov-report=term-missing; \
		fi; \
	fi

# 運行單元測試
test-unit: install
	@echo "🧪 執行單元測試..."
	@if [ -d "venv" ]; then \
		source venv/bin/activate && python -m pytest tests/ -v -m unit --tb=short; \
	else \
		python -m pytest tests/ -v -m unit --tb=short; \
	fi

# 運行整合測試（使用真實 BigQuery）
test-integration: install
	@echo "🔍 執行整合測試（使用真實 BigQuery）..."
	@echo "⚠️  注意：這些測試會連接真實的 BigQuery"
	@$(MAKE) auth-check
	@if [ -d "venv" ]; then \
		source venv/bin/activate && python -m pytest tests/ -v -m integration --tb=short; \
	else \
		python -m pytest tests/ -v -m integration --tb=short; \
	fi

# ====== Terraform 相關指令 ======

# 完整 Terraform 檢查（推薦使用）
tf-check:
	@echo "🔍 執行完整 Terraform 檢查..."
	@$(MAKE) tf-init
	@$(MAKE) tf-fmt
	@$(MAKE) tf-validate
	@echo "✅ Terraform 檢查全部通過！"

# Terraform 初始化
tf-init:
	@echo "🚀 初始化 Terraform..."
	@cd terraform && terraform init -backend=false
	@echo "✅ Terraform 初始化完成"

# Terraform 格式化
tf-fmt:
	@echo "🎨 格式化 Terraform 檔案..."
	@cd terraform && terraform fmt -recursive -check || (echo "❌ 格式化檢查失敗，執行自動格式化..." && terraform fmt -recursive)
	@echo "✅ Terraform 格式化完成"

# Terraform 語法驗證
tf-validate:
	@echo "🔍 驗證 Terraform 語法..."
	@cd terraform && terraform validate
	@echo "✅ Terraform 語法驗證通過"

# Terraform 計畫檢查
tf-plan:
	@echo "📋 執行 Terraform 計畫檢查..."
	@cd terraform && terraform init
	@echo "🔍 生產環境計畫檢查:"
	@cd terraform && terraform plan -var-file="environments/prod.tfvars"
	@echo "✅ Terraform 計畫檢查完成"

# 檢查認證狀態
auth-check:
	@echo "🔍 檢查認證狀態..."
	@if [ ! -f ~/.config/gcloud/application_default_credentials.json ]; then \
		echo "❌ 未找到 ADC 檔案"; \
		echo "請執行: gcloud auth application-default login"; \
		exit 1; \
	else \
		echo "✅ ADC 檔案存在: ~/.config/gcloud/application_default_credentials.json"; \
	fi
	@if command -v gcloud >/dev/null 2>&1; then \
		echo "📋 當前 gcloud 狀態:"; \
		gcloud auth list --filter=status:ACTIVE --format="table(account,status)" || true; \
	else \
		echo "⚠️  gcloud CLI 未安裝，無法檢查詳細狀態"; \
	fi

# 清理暫存檔案
clean:
	@echo "🧹 清理暫存檔案..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	@rm -rf .coverage htmlcov/ coverage.xml pytest-report.xml
	@rm -rf terraform/.terraform terraform/terraform.tfstate* terraform/tfplan*
	@echo "✅ 清理完成"

# 顯示專案資訊
info:
	@echo "📚 Carrefour Offline Data Service"
	@echo ""
	@echo "🎯 用途: 家樂福離線資料受眾媒合系統"
	@echo "🐍 語言: Python 3.11"
	@echo "☁️  平台: Google Cloud (BigQuery, Cloud Functions)"
	@echo ""
	@echo "🚀 快速開始:"
	@echo "  1. gcloud auth application-default login  # 設定認證"
	@echo "  2. make tf-check                          # 檢查 Terraform"
	@echo "  3. make test                              # 執行測試"
	@echo "  4. make tf-plan                           # 檢查部署計畫"
	@echo ""
	@echo "📁 重要目錄:"
	@echo "  terraform/     - Terraform 基礎設施配置"
	@echo "  src/           - 主要程式碼"
	@echo "  tests/         - 測試程式碼"
	@echo "  docs/          - 文檔"
