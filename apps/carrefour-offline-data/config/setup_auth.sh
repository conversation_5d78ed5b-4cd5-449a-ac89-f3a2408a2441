#!/bin/bash
#
# 家樂福離線事件資料驗證 - Service Account 認證設定腳本
#
# 根據 Google Cloud 最佳實務，使用 Service Account Impersonation 方式
# 設定 <EMAIL> 認證
#
# Author: AI Assistant
# Date: 2025-08-15
#

set -e  # 遇到錯誤立即退出

# 設定變數
SERVICE_ACCOUNT="<EMAIL>"
PROJECT_ID="tw-eagle-prod"

echo "🚀 開始設定家樂福離線事件資料驗證認證..."
echo "============================================================"

# 檢查 gcloud CLI 是否已安裝
if ! command -v gcloud &> /dev/null; then
    echo "❌ 錯誤: gcloud CLI 未安裝"
    echo "請先安裝 Google Cloud CLI: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

echo "✅ gcloud CLI 已安裝"

# 檢查是否已登入 gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "⚠️  尚未登入 gcloud，請先執行登入..."
    gcloud auth login
fi

CURRENT_USER=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
echo "✅ 目前登入用戶: $CURRENT_USER"

# 注意：不修改全域專案設定，讓使用者保持原有配置
echo "🔧 使用專案 ID: $PROJECT_ID (不修改全域設定)"

# 檢查用戶是否有 Service Account Token Creator 權限
echo "🔍 檢查 Service Account Token Creator 權限..."
if gcloud iam service-accounts get-iam-policy $SERVICE_ACCOUNT --format="value(bindings.members)" 2>/dev/null | grep -q "user:$CURRENT_USER"; then
    echo "✅ 用戶具有 Service Account 權限"
else
    echo "⚠️  警告: 可能缺少 Service Account Token Creator 權限"
    echo "如果後續認證失敗，請聯繫管理員授予以下權限:"
    echo "  gcloud iam service-accounts add-iam-policy-binding $SERVICE_ACCOUNT \\"
    echo "    --member=\"user:$CURRENT_USER\" \\"
    echo "    --role=\"roles/iam.serviceAccountTokenCreator\""
fi

# 設定 Application Default Credentials 使用 Service Account Impersonation
echo "🔐 設定 Application Default Credentials 使用 Service Account Impersonation..."
gcloud auth application-default login --impersonate-service-account=$SERVICE_ACCOUNT

# 驗證認證設定
echo "🧪 驗證認證設定..."
if gcloud auth application-default print-access-token --impersonate-service-account=$SERVICE_ACCOUNT &> /dev/null; then
    echo "✅ Service Account Impersonation 認證設定成功！"
    echo "📋 認證資訊:"
    echo "  - Service Account: $SERVICE_ACCOUNT"
    echo "  - Project ID: $PROJECT_ID"
    echo "  - 認證方式: Application Default Credentials with Service Account Impersonation"
else
    echo "❌ 認證設定失敗"
    echo "請檢查:"
    echo "  1. Service Account 是否存在"
    echo "  2. 是否具有 Service Account Token Creator 權限"
    echo "  3. 網路連線是否正常"
    exit 1
fi

# 測試 BigQuery 存取權限
echo "🔍 測試 BigQuery 存取權限..."
if gcloud auth application-default print-access-token --impersonate-service-account=$SERVICE_ACCOUNT | \
   xargs -I {} curl -s -H "Authorization: Bearer {}" \
   "https://bigquery.googleapis.com/bigquery/v2/projects/$PROJECT_ID/datasets/rmn_tagtoo" > /dev/null; then
    echo "✅ BigQuery 存取權限正常"
else
    echo "⚠️  BigQuery 存取權限測試失敗，但認證設定已完成"
    echo "請確認 Service Account 是否具有 BigQuery 讀取權限"
fi

echo ""
echo "🎉 認證設定完成！"
echo "現在可以執行驗證腳本:"
echo "  python validate_carrefour_data_v3.py"
echo ""
echo "如需重新設定認證，請重新執行此腳本"
