CREATE TABLE `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` (
  event_name STRING,
  event_times INTEGER,
  store_id STRING,
  store_name STRING,
  gender STRING,
  member_id BYTES,
  ph_id BYTES,
  transaction_id STRING,
  items ARRAY<STRUCT<
    item_id STRING,
    item_name STRING,
    GRP_CLASS_KEY STRING,
    GRP_CLASS_DESC STRING,
    CLASS_KEY STRING,
    CLASS_DESC STRING,
    SUB_CLASS_KEY STRING,
    SUB_CLASS_DESC STRING,
    quantity NUMERIC,
    unit_price NUMERIC,
    subtotal NUMERIC
  >>,
  total_amount NUMERIC,
  payment_method STRING
)
OPTIONS (
  description='家樂福離線事件資料 - 從 tw-eagle-prod.rmn_tagtoo.offline_transaction_day 複製',
  labels=[('source', 'carrefour'), ('type', 'offline_transaction'), ('env', 'prod')]
);
