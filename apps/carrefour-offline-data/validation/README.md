# 驗證腳本目錄

此目錄包含用於驗證系統邏輯一致性、效能測試和遷移安全性的腳本。

## 📁 目錄結構

```
validation/
├── README.md                    # 本文件
├── migration_validation.py     # 遷移驗證腳本
└── [future_validation_scripts] # 未來的驗證腳本
```

## 🔍 腳本說明

### migration_validation.py

**用途**：驗證高效能 SQL 方法與原有 Python 方法的邏輯一致性

**功能**：

- 階層式標籤生成邏輯驗證
- SQL 與 Python 輸出格式比較
- 實際資料測試和驗證
- 邊界情況處理測試

**使用場景**：

- 系統遷移前的安全性驗證
- 邏輯變更後的回歸測試
- 故障排除和問題診斷
- 新團隊成員的學習參考

**執行方式**：

```bash
cd /path/to/carrefour-offline-data
source venv/bin/activate
python validation/migration_validation.py
```

**預期輸出**：

- ✅ 所有測試通過：邏輯一致性確認
- ❌ 部分測試失敗：需要檢查邏輯差異

## 🎯 使用指南

### 何時使用驗證腳本

1. **系統遷移前**：

   - 驗證新舊方法的邏輯一致性
   - 確保遷移的安全性

2. **邏輯變更後**：

   - 驗證變更沒有破壞現有邏輯
   - 回歸測試

3. **故障排除**：

   - 當發現資料異常時
   - 驗證系統邏輯是否正確

4. **新功能開發**：
   - 作為新功能的測試模板
   - 確保新功能與現有邏輯一致

### 如何擴展驗證腳本

1. **添加新的測試案例**：

   ```python
   def test_new_feature_consistency(self) -> bool:
       """測試新功能的一致性"""
       # 實作測試邏輯
       pass
   ```

2. **修改測試資料**：

   ```python
   # 在 __init__ 方法中修改測試日期
   self.test_date = "YYYY-MM-DD"
   ```

3. **添加新的驗證維度**：
   - 效能基準測試
   - 記憶體使用測試
   - 成本分析測試

## 📊 測試覆蓋範圍

### 當前測試項目

1. **階層式標籤邏輯**：

   - 小分類標籤生成
   - 中分類標籤生成（前4位）
   - 大分類標籤生成（前3位）
   - 標籤去重和排序

2. **SQL 邏輯一致性**：

   - 高效能 SQL 查詢執行
   - 結果格式驗證
   - 資料完整性檢查

3. **Python 邏輯一致性**：
   - 原有方法執行
   - 標籤生成邏輯
   - 輸出格式比較

### 測試案例

```python
test_cases = [
    "10012",  # 5位數字 - 完整階層
    "1001",   # 4位數字 - 中大分類
    "100",    # 3位數字 - 僅大分類
    "20034",  # 不同階層測試
    "30567",  # 另一個階層測試
]
```

## 🔧 維護指南

### 定期維護任務

1. **每季度檢查**：

   - 驗證腳本是否仍能正常執行
   - 更新測試資料日期
   - 檢查依賴套件版本

2. **系統變更後**：

   - 運行完整驗證測試
   - 更新測試案例（如有需要）
   - 記錄測試結果

3. **年度審查**：
   - 評估驗證腳本的有效性
   - 考慮是否需要新的驗證維度
   - 更新文檔和使用指南

### 故障排除

**常見問題**：

1. **權限錯誤**：

   ```bash
   # 確認 BigQuery 權限
   gcloud auth list
   gcloud config set project tagtoo-tracking
   ```

2. **Import 錯誤**：

   ```bash
   # 確認路徑正確
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src/analysis"
   ```

3. **資料不存在**：
   ```python
   # 修改測試日期為有資料的日期
   self.test_date = "2025-08-26"  # 已知有資料的日期
   ```

## 📈 未來擴展計畫

### 計畫中的驗證腳本

1. **效能基準測試**：

   - 執行時間測量
   - 記憶體使用監控
   - 成本分析比較

2. **資料品質驗證**：

   - 資料完整性檢查
   - 異常值檢測
   - 資料一致性驗證

3. **端到端測試**：
   - 完整流程測試
   - 整合測試
   - 用戶接受度測試

### 貢獻指南

如需添加新的驗證腳本：

1. 遵循現有的命名規範
2. 包含完整的文檔說明
3. 添加適當的測試案例
4. 更新此 README 文件

---

**最後更新**：2025-08-27
**維護者**：資料工程團隊
**版本**：v1.0
