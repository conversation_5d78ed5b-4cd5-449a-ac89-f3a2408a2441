#!/usr/bin/env python3
"""
遷移驗證測試腳本

此腳本用於驗證將 carrefour_audience_matching.py 的日常邏輯
遷移到 historical_backfill.py 的高效能方法是否安全。

測試內容：
1. 邏輯一致性驗證
2. 輸出格式比較
3. 效能基準測試
4. 邊界情況測試
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set
from google.cloud import bigquery

# 添加 src 目錄到 Python 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'analysis'))

from carrefour_audience_matching import CarrefourAudienceMatching

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MigrationValidator:
    """遷移驗證器"""

    def __init__(self):
        self.client = bigquery.Client(project="tagtoo-tracking")
        self.test_date = "2025-08-26"  # 使用已知有資料的日期

    def get_optimized_sql_query(self, days_back: int = 1) -> str:
        """
        獲取優化版 SQL 查詢 (基於 historical_backfill.py 邏輯)
        """
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')

        return f"""
        WITH historical_mobile_users AS (
          SELECT DISTINCT
            permanent,
            mobile,
            DATE(latest_entity_time) as activity_date
          FROM `tagtoo-tracking.event_prod.tagtoo_entity`
          WHERE ec_id = 715
            AND mobile IS NOT NULL
            AND latest_entity_time >= '{start_date}'
            AND latest_entity_time < '{end_date}'
        ),
        carrefour_offline_data AS (
          SELECT DISTINCT
            TO_HEX(off.ph_id) as mobile,
            item.SUB_CLASS_KEY
          FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` off
          JOIN UNNEST(off.items) as item
          WHERE item.SUB_CLASS_KEY IS NOT NULL
            AND item.SUB_CLASS_KEY != ''
        ),
        hierarchical_segments AS (
          SELECT
            h.permanent,
            h.activity_date,
            c.SUB_CLASS_KEY
          FROM historical_mobile_users h
          INNER JOIN carrefour_offline_data c ON h.mobile = c.mobile
        ),
        audience_mapping AS (
          SELECT
            permanent,
            activity_date,
            ARRAY_AGG(DISTINCT segment_id) as segment_ids
          FROM (
            -- 小分類標籤 (完整 SUB_CLASS_KEY)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUB_CLASS_KEY) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL AND SUB_CLASS_KEY != ''

            UNION ALL

            -- 中分類標籤 (前4位數字)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 4)) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL
              AND SUB_CLASS_KEY != ''
              AND LENGTH(SUB_CLASS_KEY) >= 4

            UNION ALL

            -- 大分類標籤 (前3位數字)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 3)) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL
              AND SUB_CLASS_KEY != ''
              AND LENGTH(SUB_CLASS_KEY) >= 3
          )
          GROUP BY permanent, activity_date
        )
        SELECT
          permanent,
          activity_date,
          ARRAY_TO_STRING(ARRAY(SELECT * FROM UNNEST(segment_ids) ORDER BY 1), ',') as segment_id
        FROM audience_mapping
        WHERE ARRAY_LENGTH(segment_ids) > 0
        ORDER BY activity_date, permanent
        """

    def test_sql_logic_consistency(self) -> bool:
        """測試 SQL 邏輯一致性"""
        logger.info("=== 測試 SQL 邏輯一致性 ===")

        # 執行優化版 SQL 查詢
        query = self.get_optimized_sql_query(days_back=1)

        try:
            # 先執行 dry run 檢查成本
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            query_job = self.client.query(query, job_config=job_config)

            processed_bytes = query_job.total_bytes_processed
            estimated_cost = processed_bytes / (1024**4) * 5

            logger.info(f"SQL 查詢預估成本: ${estimated_cost:.4f} USD")
            logger.info(f"處理資料量: {processed_bytes:,} bytes")

            # 實際執行查詢 (限制結果數量)
            limited_query = query + " LIMIT 1000"
            query_job = self.client.query(limited_query)
            results = list(query_job.result())

            logger.info(f"SQL 查詢成功，取得 {len(results)} 筆樣本資料")

            # 檢查結果格式
            if results:
                sample = results[0]
                logger.info(f"樣本資料格式:")
                logger.info(f"  permanent: {sample.permanent}")
                logger.info(f"  activity_date: {sample.activity_date}")
                logger.info(f"  segment_id: {sample.segment_id}")

                # 驗證標籤格式
                segments = sample.segment_id.split(',')
                logger.info(f"  標籤數量: {len(segments)}")
                logger.info(f"  標籤樣本: {segments[:3]}")

                return True
            else:
                logger.warning("SQL 查詢未返回任何結果")
                return False

        except Exception as e:
            logger.error(f"SQL 邏輯測試失敗: {e}")
            return False

    def test_python_logic_consistency(self) -> bool:
        """測試 Python 邏輯一致性"""
        logger.info("=== 測試 Python 邏輯一致性 ===")

        try:
            # 初始化現有系統
            matcher = CarrefourAudienceMatching(execution_mode="manual")

            # 使用 entity_optimized 方法 (最接近新邏輯)
            purchases = matcher.query_offline_purchases_entity_optimized(days_back=1, limit=1000)

            if not purchases:
                logger.warning("Python 查詢未返回任何結果")
                return False

            logger.info(f"Python 查詢成功，取得 {len(purchases)} 筆購買記錄")

            # 模擬標籤生成邏輯
            user_segments = {}
            for purchase in purchases:
                permanent = purchase["permanent"]
                sub_class_key = purchase["SUB_CLASS_KEY"]

                if permanent not in user_segments:
                    user_segments[permanent] = set()

                # 生成階層式標籤 (與 SQL 邏輯一致)
                if sub_class_key and len(sub_class_key) >= 3:
                    segments = []
                    segments.append(f"tm:c_715_pc_{sub_class_key}")  # 小分類
                    if len(sub_class_key) >= 4:
                        segments.append(f"tm:c_715_pc_{sub_class_key[:4]}")  # 中分類
                    if len(sub_class_key) >= 3:
                        segments.append(f"tm:c_715_pc_{sub_class_key[:3]}")  # 大分類

                    user_segments[permanent].update(segments)

            # 轉換為最終格式
            python_results = []
            for permanent, segments in user_segments.items():
                if segments:
                    segment_string = ",".join(sorted(segments))
                    python_results.append({
                        "permanent": permanent,
                        "segment_id": segment_string
                    })

            logger.info(f"Python 處理完成，生成 {len(python_results)} 個用戶標籤")

            # 顯示樣本
            if python_results:
                sample = python_results[0]
                logger.info(f"樣本資料格式:")
                logger.info(f"  permanent: {sample['permanent']}")
                logger.info(f"  segment_id: {sample['segment_id']}")

                segments = sample['segment_id'].split(',')
                logger.info(f"  標籤數量: {len(segments)}")
                logger.info(f"  標籤樣本: {segments[:3]}")

            return True

        except Exception as e:
            logger.error(f"Python 邏輯測試失敗: {e}")
            return False

    def test_hierarchical_segments_logic(self) -> bool:
        """測試階層式標籤生成邏輯"""
        logger.info("=== 測試階層式標籤生成邏輯 ===")

        # 測試案例
        test_cases = [
            "10012",  # 5位數字
            "1001",   # 4位數字
            "100",    # 3位數字
            "20034",  # 不同階層
            "30567",  # 另一個階層
        ]

        try:
            # 初始化現有系統
            matcher = CarrefourAudienceMatching(execution_mode="manual")

            all_consistent = True

            for sub_class_key in test_cases:
                # Python 方法
                python_segments = matcher._generate_hierarchical_segments(sub_class_key)
                python_result = ",".join(sorted(python_segments))

                # SQL 方法 (模擬)
                sql_segments = []
                sql_segments.append(f"tm:c_715_pc_{sub_class_key}")  # 小分類
                if len(sub_class_key) >= 4:
                    sql_segments.append(f"tm:c_715_pc_{sub_class_key[:4]}")  # 中分類
                if len(sub_class_key) >= 3:
                    sql_segments.append(f"tm:c_715_pc_{sub_class_key[:3]}")  # 大分類
                sql_result = ",".join(sorted(sql_segments))

                # 比較結果
                match = python_result == sql_result
                status = "✅" if match else "❌"

                logger.info(f"  {sub_class_key}:")
                logger.info(f"    Python: {python_result}")
                logger.info(f"    SQL:    {sql_result}")
                logger.info(f"    匹配:   {status}")

                if not match:
                    all_consistent = False

            logger.info(f"階層式標籤邏輯一致性: {'✅ 通過' if all_consistent else '❌ 失敗'}")
            return all_consistent

        except Exception as e:
            logger.error(f"階層式標籤測試失敗: {e}")
            return False

    def run_all_tests(self) -> bool:
        """執行所有測試"""
        logger.info("開始遷移驗證測試")
        logger.info("=" * 50)

        test_results = []

        # 測試 1: 階層式標籤邏輯
        test_results.append(self.test_hierarchical_segments_logic())

        # 測試 2: SQL 邏輯一致性
        test_results.append(self.test_sql_logic_consistency())

        # 測試 3: Python 邏輯一致性
        test_results.append(self.test_python_logic_consistency())

        # 總結結果
        logger.info("=" * 50)
        logger.info("測試結果總結:")

        test_names = [
            "階層式標籤邏輯",
            "SQL 邏輯一致性",
            "Python 邏輯一致性"
        ]

        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✅ 通過" if result else "❌ 失敗"
            logger.info(f"  {i+1}. {name}: {status}")

        all_passed = all(test_results)
        final_status = "✅ 所有測試通過" if all_passed else "❌ 部分測試失敗"
        logger.info(f"\n總體結果: {final_status}")

        if all_passed:
            logger.info("\n🎉 遷移驗證成功！可以安全地將日常系統遷移到高效能方法。")
        else:
            logger.info("\n⚠️  遷移驗證失敗，需要進一步檢查邏輯差異。")

        return all_passed

def main():
    """主函數"""
    validator = MigrationValidator()
    success = validator.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
