[tool:pytest]
# pytest 配置檔案

# 測試目錄
testpaths = tests

# 預設選項
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings

# 測試標記定義
markers =
    unit: 單元測試
    integration: 整合測試
    slow: 執行時間較長的測試
    bigquery: 需要 BigQuery 連線的測試

# Python 檔案模式
python_files = test_*.py *_test.py

# Python 類別模式
python_classes = Test*

# Python 函數模式
python_functions = test_*

# 最小 Python 版本
minversion = 6.0
