# 家樂福離線資料系統

> Last Updated: 2025-09-18
> Version: 8.2.0 (每日增量合併系統 + 智慧去重機制)

## 📋 專案概述

本專案實現家樂福離線資料的完整處理流程，包含資料複製、每日增量合併和受眾媒合三大核心功能。採用多重排程器混合架構，確保資料同步與受眾分析的自動化執行。

### 🔄 每日增量合併系統 (v8.2.0 新功能)

**資料來源**：`carrefour_offline_transaction_day` (每日更新)
**資料目標**：`carrefour_offline_transaction` (主要資料表)
**執行頻率**：每日台灣時間 11:00
**合併策略**：基於 `transaction_id` 的智慧去重增量合併

**核心特性**：

- ✅ **智慧去重機制**：自動檢測重疊資料，僅新增缺失記錄
- ✅ **成本最佳化**：動態成本估算，預估成本上限 $5.00 USD
- ✅ **架構自動修復**：自動處理 `ph_id_hex` 欄位生成
- ✅ **詳細分析報告**：完整的執行日誌和資料分析
- ✅ **安全回填支援**：支援歷史資料安全回填驗證
- ✅ **並行執行**：與 ph_id_direct 同時運作 (max_instances=2)

**重大成就**：成功回填 416 萬筆歷史資料，將月度更新升級為每日增量更新，大幅提升資料時效性。

### 🔄 資料複製功能

**資料來源**：`tw-eagle-prod.rmn_tagtoo` (offline_transaction_day, offline_transaction_month)
**資料目標**：`tagtoo-tracking.event_prod` (carrefour_offline_transaction_day, carrefour_offline_transaction_month)
**執行頻率**：

- 日交易資料：每日台灣時間 10:30
- 月快照資料：每月 5 號台灣時間 10:30
  **預估成本**：$0.02 USD/天
  **預估執行時間**：30-60 秒

#### 🆕 ph_id_hex 輔助欄位功能 (v8.1.0)

為了改善團隊同事的查詢體驗，系統現在會自動為所有複製的表格新增 `ph_id_hex` 輔助欄位：

- **欄位類型**：STRING
- **欄位內容**：`ph_id` 的十六進制字串表示（等同於 `TO_HEX(ph_id)` 的結果）
- **使用優勢**：不需要在查詢時使用 `TO_HEX(ph_id)` 函數，可直接使用 `ph_id_hex` 欄位
- **向後相容**：保持原有 `ph_id` (BYTES) 欄位不變，僅新增輔助欄位
- **成本控制**：內建 dry-run 成本估算機制，預估成本上限 $2.00 USD

### 🎯 受眾媒合功能

**資料來源**：`tagtoo-tracking.event_prod` (tagtoo_event, carrefour_offline_transaction_day, tagtoo_entity)
**資料目標**：`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update*`
**執行頻率**：每日凌晨 2:00 (台灣時間)
**預估成本**：$0.37 USD/天
**預估執行時間**：2-3 分鐘

**整體狀態**：✅ 生產環境運行中，✅ 三重排程器架構，✅ 完整監控和警報

## 🏗️ 系統架構

**部署方式**：Google Cloud Platform 全託管服務
**觸發機制**：Cloud Scheduler + Cloud Function Gen2 + BigQuery
**架構設計**：Service Account 架構分離設計
**監控系統**：完整的警報和日誌監控

> ⚠️ **重要提醒**：系統使用 **Cloud Function Gen2**，其 URL 格式為 `*.a.run.app`。
> 如果您在 GCP Console 看到 `*.cloudfunctions.net` 格式的 URL，那可能是舊版本的 Gen1 Function。
> 請確認使用正確的 Gen2 URL 進行手動觸發。

### Service Account 架構分離

```yaml
架構分離設計:
  基礎設施層:
    Service Account: <EMAIL>
    職責: Cloud Scheduler 觸發和基礎設施管理
    權限: Cloud Function/Run 觸發權限

  專案執行層:
    Service Account: <EMAIL>
    職責: Cloud Function 執行和資料處理
    權限: BigQuery 資料存取、跨專案寫入權限
```

## 🔄 自動化執行流程

### 資料複製流程 (10:30 AM)

```mermaid
sequenceDiagram
    participant CS1 as Cloud Scheduler<br/>(Daily/Monthly)
    participant CF1 as Data Replication<br/>Function
    participant TW as tw-eagle-prod<br/>BigQuery
    participant TT as tagtoo-tracking<br/>BigQuery

    Note over CS1: 基礎設施層 SA
    CS1->>+CF1: HTTP POST (table_name)
    Note over CS1,CF1: integrated-event-prod SA

    Note over CF1: 專案執行層 SA
    CF1->>CF1: 切換到 carrefour-bigquery-view

    CF1->>+TW: 查詢來源資料
    Note over TW: offline_transaction_day/month
    TW-->>-CF1: 返回交易記錄

    CF1->>+TT: 寫入目標表格
    Note over TT: carrefour_offline_transaction_*
    TT-->>-CF1: 確認寫入成功

    CF1-->>-CS1: HTTP 200 (複製完成)
```

### 受眾媒合流程 (2:00 AM)

```mermaid
sequenceDiagram
    participant CS2 as Cloud Scheduler<br/>(Audience)
    participant CF2 as Audience Matching<br/>Function
    participant BQ as BigQuery<br/>tagtoo-tracking
    participant ML as ML Workflow<br/>BigQuery

    Note over CS2: 基礎設施層 SA
    CS2->>+CF2: HTTP POST (媒合參數)
    Note over CS2,CF2: integrated-event-prod SA

    Note over CF2: 專項執行層 SA
    CF2->>CF2: 切換到 carrefour-tagtoo-bigquery-view

    CF2->>+BQ: 查詢交易與事件資料
    Note over BQ: carrefour_offline_transaction_day<br/>tagtoo_event, tagtoo_entity
    BQ-->>-CF2: 返回媒合資料

    CF2->>CF2: 執行受眾媒合邏輯
    CF2->>CF2: 生成受眾標籤

    CF2->>+ML: 寫入受眾媒合結果
    Note over ML: special_lta_temp_for_update_*
    ML-->>-CF2: 確認寫入成功

    CF2-->>-CS2: HTTP 200 (媒合完成)
```

## 🚀 系統管理

### 監控和狀態檢查

1. **檢查系統狀態**：

   ```bash
   # 檢查三個 Cloud Scheduler 狀態
   gcloud scheduler jobs describe carrefour-daily-replication-prod --location=asia-east1
   gcloud scheduler jobs describe carrefour-monthly-replication-prod --location=asia-east1
   gcloud scheduler jobs describe carrefour-audience-matching-prod --location=asia-east1

   # 檢查兩個 Cloud Function 狀態
   gcloud functions describe carrefour-data-replication-prod --region=asia-east1 --gen2
   gcloud functions describe carrefour-offline-data-prod --region=asia-east1 --gen2

   # 檢查最近執行日誌
   gcloud logging read 'resource.type="cloud_scheduler_job"' \
     --filter='resource.labels.job_id:carrefour' --limit=10
   ```

2. **手動觸發執行**：

   ```bash
   # 手動觸發資料複製 (日表格)
   gcloud scheduler jobs run carrefour-daily-replication-prod --location=asia-east1

   # 手動觸發資料複製 (月表格)
   gcloud scheduler jobs run carrefour-monthly-replication-prod --location=asia-east1

   # 手動觸發受眾媒合
   gcloud scheduler jobs run carrefour-audience-matching-prod --location=asia-east1

   # 直接呼叫資料複製 Cloud Function (Gen2)
   curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
     -H "Content-Type: application/json" \
     -d '{"operation_type":"data_replication","table_name":"offline_transaction_day"}' \
     https://carrefour-data-replication-prod-shiddvur4q-de.a.run.app/

   # 直接呼叫受眾媒合 Cloud Function (Gen2)
   curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
     -H "Content-Type: application/json" \
     -d '{"execution_mode":"manual","days_back":1,"dry_run":false}' \
     https://carrefour-offline-data-prod-shiddvur4q-de.a.run.app/
   ```

3. **檢查處理結果**：

   ```bash
   # 檢查資料複製結果
   bq query --use_legacy_sql=false \
     "SELECT COUNT(*) as records, MAX(_PARTITIONTIME) as latest_partition
      FROM \`tagtoo-tracking.event_prod.carrefour_offline_transaction_day\`"

   bq query --use_legacy_sql=false \
     "SELECT COUNT(*) as records, MAX(_PARTITIONTIME) as latest_partition
      FROM \`tagtoo-tracking.event_prod.carrefour_offline_transaction_month\`"

   # 檢查受眾媒合結果
   bq query --use_legacy_sql=false \
     "SELECT COUNT(*) as total_records, MAX(created_at) as latest_update
      FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`"

   # 檢查 Cloud Function 執行日誌
   gcloud logging read 'resource.type="cloud_run_revision"' \
     --filter='resource.labels.service_name:carrefour' --limit=20
   ```

## 🔐 權限配置

### Service Account 權限詳情

#### 基礎設施層 Service Account

- **Email**: `<EMAIL>`
- **用途**: Cloud Scheduler 觸發和基礎設施管理
- **權限**:
  - `roles/cloudfunctions.invoker` - Cloud Function 觸發
  - `roles/run.invoker` - Cloud Run Service 觸發

#### 專案執行層 Service Account

- **Email**: `<EMAIL>`
- **用途**: Cloud Function 執行和資料處理
- **權限**:
  - `roles/bigquery.dataEditor` - BigQuery 資料編輯 (本專案)
  - `roles/bigquery.jobUser` - BigQuery Job 執行 (本專案)
  - `roles/storage.objectUser` - Storage 物件存取
  - `roles/bigquery.dataEditor` - BigQuery 資料編輯 (ML Workflow 專案)
  - `roles/bigquery.jobUser` - BigQuery Job 執行 (ML Workflow 專案)

### 權限驗證

```bash
# 檢查基礎設施層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 檢查專案執行層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 檢查跨專案權限
gcloud projects get-iam-policy tagtoo-ml-workflow \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

## 🛠️ 開發環境設定

### 本地開發和測試

```bash
# 啟動虛擬環境
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt

# 設定環境變數
export GOOGLE_CLOUD_PROJECT=tagtoo-tracking
export ENVIRONMENT=dev

# 本地測試執行
python main.py --days-back 1 --dry-run --execution-mode manual
```

- 📖 **跨平台設定**：參考 [`docs/CROSS_PLATFORM_SETUP.md`](docs/CROSS_PLATFORM_SETUP.md)
- 📋 **操作手冊**：參考 [`docs/OPERATIONS_MANUAL.md`](docs/OPERATIONS_MANUAL.md)
- ✅ **執行檢查清單**：參考 [`docs/EXECUTION_CHECKLIST.md`](docs/EXECUTION_CHECKLIST.md)

## 🎯 主要功能

### 核心系統（src/）

1. **智慧資料複製**：支援 1500+ 萬筆資料的高效複製
2. **四層次驗證**：從基本統計到深度結構驗證
3. **Schema 一致性檢查**：確保來源與目標表格完全一致
4. **監控日誌系統**：結構化日誌和效能監控
5. **環境自動化設定**：一鍵環境配置和依賴安裝
6. **整合執行腳本**：完整流程自動化

### 輔助工具（tools/）

- 規格驗證工具（v1-v3）
- 表格檢查和診斷工具

### 完整文檔（docs/）

- 操作手冊和執行檢查清單
- 技術策略和權限申請文件
- 開發記錄和驗證規格

## 🛠️ 技術架構

- **Python 3.9+**：主要開發語言
- **Google Cloud BigQuery**：資料存取和查詢
- **Service Account**：`<EMAIL>`
- **認證方式**：Service Account Impersonation
- **效能設計**：2,000-5,000 行/秒，成本 < $1 USD

## 🚀 快速開始

### 環境設定

1. **自動化環境設定**：

   ```bash
   python3 src/setup_environment.py
   ```

2. **手動環境設定**：

   ```bash
   # 建立虛擬環境
   python3 -m venv venv
   source venv/bin/activate

   # 安裝依賴
   pip install -r requirements.txt

   # 設定認證
   source config/set_env.sh
   ```

### 執行完整流程（權限開通後）

```bash
# 一鍵執行完整流程
python3 src/run_full_pipeline.py

# 或分步執行
python3 src/schema_validator.py      # Schema 驗證
python3 src/copy_carrefour_data.py   # 資料複製
python3 src/data_validator.py        # 資料驗證
```

## 📁 專案結構

```text
carrefour-offline-data/
├── README.md                          # 專案說明文檔
├── main.py                            # Cloud Function 入口點 ⭐
├── requirements.txt                   # Python 依賴套件
├── 📁 src/                            # 核心系統程式碼
│   └── analysis/
│       └── carrefour_audience_matching.py  # 受眾媒合核心邏輯
│
├── 📁 terraform/                      # 基礎設施即程式碼
│   ├── main.tf                        # 主要 Terraform 配置
│   ├── variables.tf                   # 變數定義
│   ├── outputs.tf                     # 輸出定義
│   ├── iam.tf                         # IAM 權限配置 (架構分離)
│   └── environments/                  # 環境特定配置
│       ├── dev.tfvars                 # 開發環境
│       ├── staging.tfvars             # 測試環境
│       └── prod.tfvars                # 生產環境
│
├── 📁 docs/                           # 專案文檔
│   ├── architecture/                  # 架構設計文檔
│   │   ├── service-account-permissions.md
│   │   └── automation_architecture.md
│   ├── deployment/                    # 部署相關文檔
│   │   ├── terraform_deployment_guide.md
│   │   └── terraform_deployment_requirements.md
│   └── operations/                    # 運維操作文檔
│       └── trigger-flow-and-permissions.md
```

## 🔧 關鍵檔案說明

### 🌟 核心檔案

- **`main.py`**：Cloud Function 入口點，處理 HTTP 請求和參數解析
- **`src/analysis/carrefour_audience_matching.py`**：受眾媒合核心邏輯
- **`terraform/iam.tf`**：Service Account 架構分離配置
- **`terraform/main.tf`**：Cloud Function 和 Cloud Scheduler 配置

### 重要文檔

- **`docs/architecture/service-account-permissions.md`**：權限配置詳解
- **`docs/operations/trigger-flow-and-permissions.md`**：觸發流程和權限驗證
- **`docs/deployment/terraform_deployment_guide.md`**：部署指南

## 📊 系統效能指標

### 當前運行狀況

- **執行時間**：平均 2-3 分鐘
- **處理用戶數**：約 32,000+ 個用戶
- **生成標籤數**：約 1,680,000+ 個標籤實例
- **查詢成本**：約 $0.18 USD/次
- **成功率**：99.9%+
- **記憶體使用**：< 800Mi
- **CPU 使用**：< 1 vCPU

### 監控指標

```yaml
關鍵指標:
  執行成功率: > 95%
  執行時間: < 180 秒
  查詢成本: < $0.2 USD
  記憶體使用: < 800Mi
  錯誤率: < 1%
```

## 🚨 故障排除

### 常見問題

#### 1. HTTP 403 PERMISSION_DENIED

**症狀**：Cloud Scheduler 觸發失敗
**原因**：缺少 Cloud Run Service 觸發權限
**解決方案**：

```bash
# 檢查 IAM 權限配置
terraform plan -var-file="environments/prod.tfvars"
terraform apply -var-file="environments/prod.tfvars"
```

#### 2. BigQuery 存取被拒

**症狀**：無法讀取或寫入 BigQuery 資料
**原因**：Service Account 權限不足
**解決方案**：

```bash
# 檢查專案執行層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

#### 3. Cloud Function 執行超時

**症狀**：執行時間超過 10 分鐘
**原因**：資料量過大或查詢效率問題
**解決方案**：檢查 BigQuery 查詢效能和成本

### 🔔 監控和警報系統 (2025-09-01 最新配置)

#### **警報策略覆蓋範圍**

- ✅ **執行時間監控**：超過 300 秒（5 分鐘）觸發警報
- ✅ **錯誤率監控**：任何執行失敗立即觸發警報（5 分鐘檢測）
- ✅ **記憶體使用率監控**：超過 90% 記憶體使用率觸發警報
- ✅ **執行頻率異常監控**：每日執行超過 1 次觸發警報
- ✅ **Cloud Scheduler 失敗監控**：調度失敗立即觸發警報

#### **通知配置**

- **通知頻道架構**：專案隔離設計，符合企業級最佳實務
- **當前頻道**：`家樂福離線資料系統 - prod - <EMAIL>`
- **Email 通知**：<EMAIL>（專項專用頻道，完全隔離）
- **警報策略數量**：5 個已部署並驗證的監控項目
- **用戶標籤**：project, environment, purpose, owner（便於管理分類）
- **多收件人支援**：可透過建立多個通知頻道實現（詳見技術文檔）

#### **監控技術細節**

- **Cloud Monitoring**：基於原生 Cloud Function 指標
- **Cloud Logging**：詳細的執行日誌和錯誤追蹤
- **監控間隔**：300 秒（平衡檢測速度與成本效益）
- **警報閾值**：基於實際運行數據優化設定

#### **通知頻道架構設計**

```yaml
架構原則:
  專案隔離: 每個專案建立專用通知頻道
  標準化命名: "{專案名稱} - {環境} - {收件人}"
  用戶標籤: project, environment, purpose, owner

當前配置:
  頻道名稱: "家樂福離線資料系統 - prod - <EMAIL>"
  支援擴展: 可添加多個收件人（<EMAIL> 等）
  成本影響: 零成本（通知頻道完全免費）

未來擴展:
  多收件人: 在 notification_emails 變數中添加新 email
  跨專案: 每個新專案建立專用通知頻道
  通知方式: 支援 Email、SMS、Slack、PagerDuty 等
```

## 🔄 部署和更新

### Terraform 部署

```bash
# 進入 Terraform 目錄
cd terraform

# 檢查配置
terraform plan -var-file="environments/prod.tfvars"

# 部署更新
terraform apply -var-file="environments/prod.tfvars"
```

### 程式碼更新

```bash
# 更新程式碼後，Terraform 會自動重新部署 Cloud Function
terraform apply -var-file="environments/prod.tfvars"
```

## 📚 相關文檔

### 架構設計

- [Service Account 權限配置詳解](docs/architecture/service-account-permissions.md)
- [自動化架構設計](docs/architecture/automation_architecture.md)

### 部署和運維

- [Terraform 部署指南](docs/deployment/terraform_deployment_guide.md)
- [觸發流程和權限驗證](docs/operations/trigger-flow-and-permissions.md)

### 企業級架構標準

- [Service Account 架構分離設計原則](../../docs/architecture/service-account-separation-design.md)
- [安全性最佳實務指南](../../docs/architecture/security-best-practices.md)

## 📞 支援聯絡

**系統負責人**：Frank Zheng (<EMAIL>)
**技術支援**：Data Engineering Team
**緊急聯絡**：透過 Google Cloud Monitoring 警報系統

## ⚠️ 監控警報狀態 (2025-09-04 更新)

### 🔧 當前配置狀態

- ✅ **核心功能**：Cloud Functions 和 Cloud Scheduler 正常運作
- ✅ **基本監控**：Email 通知頻道已配置
- ⚠️ **監控警報**：暫時移除複雜監控警報，確保部署穩定性

### 📋 已移除的監控警報

為確保 CI/CD 流程穩定性，暫時移除以下監控警報：

- BigQuery 作業失敗警報 (指標配置問題)
- 查詢成本超標警報 (指標不存在)
- Cloud Scheduler 作業失敗警報 (標籤配置錯誤)
- 函數執行超時警報 (過濾器語法錯誤)
- 系統健康檢查警報 (資源類型錯誤)
- 用戶數異常變化警報 (聚合函數配置錯誤)
- 監控儀表板 (指標配置錯誤)

### 🎯 TODO: 監控警報重建計畫

1. **研究正確的 Google Cloud Monitoring 指標**

   - 驗證 BigQuery 指標名稱和標籤
   - 確認 Cloud Scheduler 指標配置
   - 測試聚合函數和過濾器語法

2. **重新設計監控策略**

   - 建立測試環境驗證監控配置
   - 逐步重新引入監控警報
   - 建立監控警報最佳實務文檔

3. **優先級排序**
   - 高優先級：函數執行失敗警報
   - 中優先級：成本監控和執行時間警報
   - 低優先級：系統健康檢查和儀表板

### 📞 監控支援

- **當前監控**：透過 Cloud Console 手動檢查
- **日誌監控**：Cloud Logging 完整保留
- **緊急聯絡**：<EMAIL>

## ⚠️ 記憶體限制問題 (2025-09-05 更新)

### 🔧 當前狀況

- **回溯天數**: 暫時設為 1 天 (原計畫 30 天)
- **記憶體限制**: Cloud Function 1024 MiB
- **問題**: 30 天回溯導致記憶體使用 1051 MiB，超過限制

### 📊 記憶體使用分析

```text
1 天回溯:  記憶體使用 < 1024 MiB ✅ 正常
30 天回溯: 記憶體使用 1051 MiB ❌ 超限
```

### 🎯 優化策略選項

1. **增加記憶體配置** (短期解決方案)

   - 將 Cloud Function 記憶體從 1024 MiB 增加到 2048 MiB
   - 成本影響：約增加 100% 記憶體成本

2. **分批處理** (中期解決方案)

   - 將 30 天資料分成多個批次處理
   - 每批處理 5-10 天資料，避免記憶體峰值

3. **架構升級** (長期解決方案)
   - 遷移到 Cloud Run (更靈活的記憶體配置)
   - 使用 Dataflow 處理大量資料
   - 實作資料流處理架構

### 📋 實作優先級

1. **立即**: 測試增加記憶體配置到 2048 MiB
2. **短期**: 實作分批處理邏輯
3. **中期**: 評估 Cloud Run 遷移
4. **長期**: 考慮 Dataflow 架構

---

**最後更新**：2025-09-05
**版本**：v7.2 (記憶體限制修正版)
**狀態**：✅ 核心功能正常 (1天回溯)，⚠️ 30天回溯待記憶體優化
