#!/usr/bin/env python3
"""
測試三碼大分類統計功能

Author: AI Assistant
Date: 2025-09-15
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from analysis.ph_id_direct_processor import PhIdDirectProcessor


class TestCategoryStatistics(unittest.TestCase):
    """測試三碼大分類統計功能"""

    def setUp(self):
        """設定測試環境"""
        # 模擬配置
        self.mock_config = {
            "source_table": "test.table",
            "target_dataset": "test_dataset",
            "gcs_bucket": "test_bucket",
            "max_cost_usd": 50.0,
            "batch_size": 1000
        }

        # 建立處理器實例（模擬初始化）
        with patch('analysis.ph_id_direct_processor.bigquery.Client'):
            with patch('analysis.ph_id_direct_processor.storage.Client'):
                with patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):
                    self.processor = PhIdDirectProcessor.__new__(PhIdDirectProcessor)
                    self.processor.config = self.mock_config
                    self.processor.data_range_days = 1

    def test_generate_category_statistics_normal_case(self):
        """測試正常情況下的統計功能"""
        # 準備測試資料
        test_data = [
            {
                "ph_id_hex": "ABC123",
                "segment_ids": ["tm:c_715_pc_100", "tm:c_715_pc_314"],
                "segment_count": 2
            },
            {
                "ph_id_hex": "DEF456",
                "segment_ids": ["tm:c_715_pc_100", "tm:c_715_pc_311", "tm:c_715_pc_314"],
                "segment_count": 3
            },
            {
                "ph_id_hex": "GHI789",
                "segment_ids": ["tm:c_715_pc_324"],
                "segment_count": 1
            }
        ]

        # 執行統計
        result = self.processor._generate_category_statistics(test_data)

        # 驗證結果
        self.assertEqual(result["total_ph_id_records"], 3)
        self.assertEqual(result["total_segment_instances"], 6)
        self.assertEqual(result["total_category_instances"], 6)
        self.assertEqual(result["unique_categories_count"], 4)

        # 驗證分類統計
        category_breakdown = {item["category"]: item["count"] for item in result["category_breakdown"]}
        self.assertEqual(category_breakdown["100"], 2)  # 出現2次
        self.assertEqual(category_breakdown["314"], 2)  # 出現2次
        self.assertEqual(category_breakdown["311"], 1)  # 出現1次
        self.assertEqual(category_breakdown["324"], 1)  # 出現1次

        # 驗證百分比計算
        for item in result["category_breakdown"]:
            if item["category"] == "100":
                self.assertAlmostEqual(item["percentage"], 33.33, places=1)

    def test_generate_category_statistics_empty_data(self):
        """測試空資料的情況"""
        test_data = []

        result = self.processor._generate_category_statistics(test_data)

        self.assertEqual(result["total_ph_id_records"], 0)
        self.assertEqual(result["total_segment_instances"], 0)
        self.assertEqual(result["total_category_instances"], 0)
        self.assertEqual(result["unique_categories_count"], 0)
        self.assertEqual(len(result["category_breakdown"]), 0)

    def test_generate_category_statistics_invalid_segments(self):
        """測試包含無效標籤的情況"""
        test_data = [
            {
                "ph_id_hex": "ABC123",
                "segment_ids": [
                    "tm:c_715_pc_100",  # 有效
                    "invalid_segment",   # 無效
                    "tm:c_715_pc_314",  # 有效
                    "tm:c_715_pv_100",  # 無效格式（pv而非pc）
                    ""                   # 空字串
                ],
                "segment_count": 5
            }
        ]

        result = self.processor._generate_category_statistics(test_data)

        # 只有2個有效的三碼大分類
        self.assertEqual(result["total_category_instances"], 2)
        self.assertEqual(result["unique_categories_count"], 2)

        category_breakdown = {item["category"]: item["count"] for item in result["category_breakdown"]}
        self.assertEqual(category_breakdown["100"], 1)
        self.assertEqual(category_breakdown["314"], 1)

    def test_generate_category_statistics_missing_segment_ids(self):
        """測試缺少 segment_ids 欄位的情況"""
        test_data = [
            {
                "ph_id_hex": "ABC123",
                # 缺少 segment_ids
                "segment_count": 0
            },
            {
                "ph_id_hex": "DEF456",
                "segment_ids": None,  # None 值
                "segment_count": 0
            }
        ]

        result = self.processor._generate_category_statistics(test_data)

        self.assertEqual(result["total_ph_id_records"], 2)
        self.assertEqual(result["total_segment_instances"], 0)
        self.assertEqual(result["total_category_instances"], 0)
        self.assertEqual(result["unique_categories_count"], 0)

    @patch('analysis.ph_id_direct_processor.logger')
    def test_log_category_statistics(self, mock_logger):
        """測試統計日誌輸出功能"""
        # 準備測試統計資料
        test_statistics = {
            "total_ph_id_records": 1000,
            "total_segment_instances": 2500,
            "total_category_instances": 2500,
            "unique_categories_count": 10,
            "category_breakdown": [
                {"category": "314", "count": 500, "percentage": 20.0},
                {"category": "311", "count": 400, "percentage": 16.0},
                {"category": "100", "count": 300, "percentage": 12.0}
            ]
        }

        # 執行日誌記錄
        self.processor._log_category_statistics(test_statistics)

        # 驗證日誌調用
        self.assertTrue(mock_logger.info.called)

        # 檢查關鍵日誌內容
        log_calls = [call[0][0] for call in mock_logger.info.call_args_list]

        # 驗證包含統計標題
        self.assertTrue(any("三碼大分類統計報告" in call for call in log_calls))

        # 驗證包含總筆數
        self.assertTrue(any("總處理筆數: 1,000" in call for call in log_calls))

        # 驗證包含分類統計
        self.assertTrue(any("314: 500 筆 (20.0%)" in call for call in log_calls))

    def test_category_extraction_regex(self):
        """測試三碼大分類提取的正則表達式"""
        import re

        test_cases = [
            ("tm:c_715_pc_100", "100"),
            ("tm:c_715_pc_314", "314"),
            ("tm:c_715_pc_1000", "100"),  # 只取前三碼
            ("tm:c_715_pv_100", None),    # pv 格式不匹配
            ("invalid_format", None),      # 完全不匹配
            ("", None),                    # 空字串
            (None, None)                   # None 值
        ]

        for segment_id, expected in test_cases:
            if segment_id is None:
                result = None
            else:
                match = re.search(r'tm:c_715_pc_(\d{3})', str(segment_id))
                result = match.group(1) if match else None

            self.assertEqual(result, expected, f"Failed for input: {segment_id}")


if __name__ == '__main__':
    unittest.main()
