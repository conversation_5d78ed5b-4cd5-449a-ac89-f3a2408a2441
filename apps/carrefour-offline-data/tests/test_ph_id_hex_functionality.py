#!/usr/bin/env python3
"""
測試 ph_id_hex 欄位功能的完整測試套件

此測試套件驗證：
1. daily replication 的 ph_id_hex 欄位新增和填充功能
2. monthly merge 的 ph_id_hex 欄位新增和填充功能
3. ph_id_hex 欄位內容的正確性（與 TO_HEX(ph_id) 一致）
4. 成本控制機制的正常運作
5. 錯誤處理機制的健全性
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from typing import Dict, Any

# 添加專案路徑到 sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'tools'))

from automated_daily_replication import AutomatedDailyReplicator
from automated_monthly_merge import AutomatedMonthlyMerger


class TestPhIdHexFunctionality(unittest.TestCase):
    """ph_id_hex 欄位功能測試類別"""

    def setUp(self):
        """測試前置設定"""
        self.daily_replicator = AutomatedDailyReplicator("offline_transaction_day")
        self.monthly_merger = AutomatedMonthlyMerger("test-project")

        # Mock BigQuery 客戶端
        self.mock_client = Mock()
        self.daily_replicator.client = self.mock_client
        self.monthly_merger.client = self.mock_client

    def test_daily_replication_ph_id_hex_method_exists(self):
        """測試 daily replication 是否有 ph_id_hex 處理方法"""
        self.assertTrue(hasattr(self.daily_replicator, '_add_ph_id_hex_and_populate'))
        self.assertTrue(callable(getattr(self.daily_replicator, '_add_ph_id_hex_and_populate')))

    def test_monthly_merge_ph_id_hex_method_exists(self):
        """測試 monthly merge 是否有 ph_id_hex 處理方法"""
        self.assertTrue(hasattr(self.monthly_merger, '_add_ph_id_hex_and_populate'))
        self.assertTrue(callable(getattr(self.monthly_merger, '_add_ph_id_hex_and_populate')))

    def test_daily_replication_ph_id_hex_alter_table(self):
        """測試 daily replication 的 ALTER TABLE 操作"""
        target_table_id = "test-project.test_dataset.test_table"

        # Mock query 結果
        mock_alter_job = Mock()
        mock_alter_job.result.return_value = None

        mock_dry_job = Mock()
        mock_dry_job.total_bytes_processed = 1024 * 1024  # 1MB

        mock_update_job = Mock()
        mock_update_job.result.return_value = None

        self.mock_client.query.side_effect = [mock_alter_job, mock_dry_job, mock_update_job]

        # 執行測試
        result = self.daily_replicator._add_ph_id_hex_and_populate(target_table_id)

        # 驗證結果
        self.assertTrue(result["alter_done"])
        self.assertEqual(result["target_table"], target_table_id)
        self.assertGreater(result["update_bytes"], 0)
        self.assertGreaterEqual(result["estimated_cost_usd"], 0)

        # 驗證 SQL 調用
        self.assertEqual(self.mock_client.query.call_count, 3)

        # 驗證 ALTER TABLE SQL
        alter_call = self.mock_client.query.call_args_list[0]
        alter_sql = alter_call[0][0]
        self.assertIn("ALTER TABLE", alter_sql)
        self.assertIn("ADD COLUMN IF NOT EXISTS ph_id_hex STRING", alter_sql)
        self.assertIn(target_table_id, alter_sql)

    def test_monthly_merge_ph_id_hex_alter_table(self):
        """測試 monthly merge 的 ALTER TABLE 操作"""
        target_table_id = "test-project.test_dataset.test_table"

        # Mock query 結果
        mock_alter_job = Mock()
        mock_alter_job.result.return_value = None

        mock_dry_job = Mock()
        mock_dry_job.total_bytes_processed = 2048 * 1024  # 2MB

        mock_update_job = Mock()
        mock_update_job.result.return_value = None

        self.mock_client.query.side_effect = [mock_alter_job, mock_dry_job, mock_update_job]

        # 執行測試
        result = self.monthly_merger._add_ph_id_hex_and_populate(target_table_id)

        # 驗證結果
        self.assertTrue(result["alter_done"])
        self.assertEqual(result["target_table"], target_table_id)
        self.assertGreater(result["update_bytes"], 0)
        self.assertGreaterEqual(result["estimated_cost_usd"], 0)

        # 驗證 SQL 調用
        self.assertEqual(self.mock_client.query.call_count, 3)

        # 驗證 UPDATE SQL 包含 WHERE ph_id_hex IS NULL（monthly merge 特有）
        update_call = self.mock_client.query.call_args_list[2]
        update_sql = update_call[0][0]
        self.assertIn("UPDATE", update_sql)
        self.assertIn("SET ph_id_hex = TO_HEX(ph_id)", update_sql)
        self.assertIn("WHERE ph_id_hex IS NULL", update_sql)

    def test_cost_control_mechanism(self):
        """測試成本控制機制"""
        target_table_id = "test-project.test_dataset.test_table"

        # Mock 高成本情況
        mock_alter_job = Mock()
        mock_alter_job.result.return_value = None

        mock_dry_job = Mock()
        # 設定高成本（超過上限）
        mock_dry_job.total_bytes_processed = 1024 ** 4 * 10  # 10TB，應該超過成本上限

        mock_update_job = Mock()
        mock_update_job.result.return_value = None

        self.mock_client.query.side_effect = [mock_alter_job, mock_dry_job, mock_update_job]

        # 執行測試
        with patch('automated_daily_replication.logger') as mock_logger:
            result = self.daily_replicator._add_ph_id_hex_and_populate(target_table_id)

            # 驗證成本計算
            expected_cost = (1024 ** 4 * 10) / (1024 ** 4) * self.daily_replicator.cost_per_tb
            self.assertAlmostEqual(result["estimated_cost_usd"], expected_cost, places=4)

            # 驗證警告日誌被調用
            mock_logger.warning.assert_called()
            warning_call = mock_logger.warning.call_args[0][0]
            self.assertIn("超過上限", warning_call)

    def test_error_handling(self):
        """測試錯誤處理機制"""
        target_table_id = "test-project.test_dataset.test_table"

        # Mock ALTER TABLE 失敗
        self.mock_client.query.side_effect = Exception("BigQuery 連線失敗")

        # 執行測試並驗證例外處理
        with self.assertRaises(Exception) as context:
            self.daily_replicator._add_ph_id_hex_and_populate(target_table_id)

        self.assertIn("BigQuery 連線失敗", str(context.exception))

    def test_ph_id_hex_sql_correctness(self):
        """測試 ph_id_hex SQL 語句的正確性"""
        target_table_id = "test-project.test_dataset.test_table"

        # Mock query 結果
        mock_jobs = [Mock() for _ in range(3)]
        for job in mock_jobs:
            job.result.return_value = None
        mock_jobs[1].total_bytes_processed = 1024  # dry-run job

        self.mock_client.query.side_effect = mock_jobs

        # 執行測試
        self.daily_replicator._add_ph_id_hex_and_populate(target_table_id)

        # 驗證 SQL 語句
        calls = self.mock_client.query.call_args_list

        # ALTER TABLE SQL
        alter_sql = calls[0][0][0]
        self.assertIn("ALTER TABLE", alter_sql.upper())
        self.assertIn("ADD COLUMN IF NOT EXISTS ph_id_hex STRING", alter_sql)

        # UPDATE SQL
        update_sql = calls[2][0][0]
        self.assertIn("UPDATE", update_sql.upper())
        self.assertIn("SET ph_id_hex = TO_HEX(ph_id)", update_sql)

    def test_configuration_consistency(self):
        """測試配置參數的一致性"""
        # 驗證成本控制參數存在且合理
        self.assertTrue(hasattr(self.daily_replicator, 'max_enrichment_cost_usd'))
        self.assertTrue(hasattr(self.daily_replicator, 'cost_per_tb'))
        self.assertTrue(hasattr(self.monthly_merger, 'max_enrichment_cost_usd'))
        self.assertTrue(hasattr(self.monthly_merger, 'cost_per_tb'))

        # 驗證成本參數為正數
        self.assertGreater(self.daily_replicator.max_enrichment_cost_usd, 0)
        self.assertGreater(self.daily_replicator.cost_per_tb, 0)
        self.assertGreater(self.monthly_merger.max_enrichment_cost_usd, 0)
        self.assertGreater(self.monthly_merger.cost_per_tb, 0)


class TestIntegrationScenarios(unittest.TestCase):
    """整合測試場景"""

    def setUp(self):
        """測試前置設定"""
        self.daily_replicator = AutomatedDailyReplicator("offline_transaction_day")
        self.monthly_merger = AutomatedMonthlyMerger("test-project")

    @patch('automated_daily_replication.bigquery.Client')
    def test_daily_replication_integration(self, mock_client_class):
        """測試 daily replication 整合場景"""
        # 這個測試需要實際的 BigQuery 環境，暫時跳過
        self.skipTest("需要實際 BigQuery 環境進行整合測試")

    @patch('automated_monthly_merge.bigquery.Client')
    def test_monthly_merge_integration(self, mock_client_class):
        """測試 monthly merge 整合場景"""
        # 這個測試需要實際的 BigQuery 環境，暫時跳過
        self.skipTest("需要實際 BigQuery 環境進行整合測試")


if __name__ == '__main__':
    # 設定測試執行器
    unittest.main(verbosity=2)
