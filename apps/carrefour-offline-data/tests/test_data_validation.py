#!/usr/bin/env python3
"""
家樂福 ph_id 直接處理功能資料正確性驗證

驗證範圍：
1. AVRO 檔案格式符合 Meta 要求
2. ph_id 轉換正確性
3. segment_id 生成邏輯
4. 資料去重化驗證
5. 與參考實作的一致性

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import json
import os
import sys
import unittest
import re
from unittest.mock import Mock, patch

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from analysis.ph_id_direct_processor import PhIdDirectProcessor


class TestDataValidation(unittest.TestCase):
    """資料正確性驗證測試"""

    def setUp(self):
        """測試前置設定"""
        self.test_config = {
            "source_table": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
            "target_dataset": "tagtoo-tracking.event_prod",
            "gcs_bucket": "tagtoo-ml-workflow",
            "gcs_path_prefix": "carrefour_ph_id_direct",
            "pubsub_topic": "projects/tagtoo-ml-workflow/topics/carrefour-ph-id-processing",
            "ec_id": 715,
            "max_cost_usd": 50.0,
            "batch_size": 1000
        }

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_avro_query_structure(self, mock_publisher, mock_storage, mock_bigquery):
        """測試 AVRO 查詢結構符合 Meta 要求"""
        processor = PhIdDirectProcessor()
        processor.config = self.test_config

        target_date = "2025-09-11"
        output_uri = "gs://test-bucket/test_path/*.avro"

        query = processor.generate_avro_export_query(target_date, output_uri)

        # 檢查基本結構
        self.assertIn("EXPORT DATA", query)
        self.assertIn("format = 'AVRO'", query)
        self.assertIn("use_avro_logical_types = TRUE", query)
        self.assertIn(output_uri, query)

        # 檢查必要欄位
        required_fields = [
            "group_id",
            "emails",
            "phones",
            "fb_info",
            "segment_id"
        ]

        for field in required_fields:
            self.assertIn(field, query)

        # 檢查 ph_id 轉換
        self.assertIn("TO_HEX(ph_id)", query)

        # 檢查 segment_id 格式
        self.assertIn("tm:c_715_pc_", query)

        # 檢查使用相對日期過濾邏輯而不是硬編碼日期
        self.assertIn("DATE_SUB", query)
        self.assertIn("MAX(DATE(TIMESTAMP_SECONDS(event_times)))", query)

        # 檢查去重化邏輯
        self.assertIn("ARRAY_AGG(DISTINCT", query)
        self.assertIn("GROUP BY ph_id", query)

    def test_segment_id_format(self):
        """測試 segment_id 格式正確性"""
        # 測試 segment_id 格式：tm:c_{ec_id}_pc_{sub_class_key}
        ec_id = 715
        sub_class_key = "10012"
        expected_format = f"tm:c_{ec_id}_pc_{sub_class_key}"

        # 檢查格式符合預期
        self.assertEqual(expected_format, "tm:c_715_pc_10012")

        # 檢查格式規則
        pattern = r"^tm:c_\d+_pc_\d+$"
        self.assertRegex(expected_format, pattern)

    def test_ph_id_hex_conversion(self):
        """測試 ph_id 轉 hex 格式正確性"""
        # 模擬 BigQuery TO_HEX 函數行為
        # ph_id 是 32 bytes 的 SHA256 雜湊，轉為 hex 應該是 64 字元

        # 測試 hex 格式驗證
        valid_hex_examples = [
            "68eaa7034f7cc4ba4101668d640eceb7feb7a7b395b6b21e967327e8322854ae",
            "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
        ]

        for hex_value in valid_hex_examples:
            # 檢查長度 (64 字元)
            self.assertEqual(len(hex_value), 64)

            # 檢查只包含 hex 字元
            self.assertRegex(hex_value, r"^[0-9a-f]{64}$")

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_avro_schema_compatibility(self, mock_publisher, mock_storage, mock_bigquery):
        """測試 AVRO schema 與 Meta 要求的相容性"""
        processor = PhIdDirectProcessor()
        processor.config = self.test_config

        query = processor.generate_avro_export_query("2025-09-11", "gs://test/*.avro")

        # 檢查 Meta 要求的欄位結構
        meta_requirements = {
            "group_id": "CAST(NULL AS STRING)",
            "emails": "CAST([] AS ARRAY<STRING>)",
            "phones": "[ph_id_hex]",
            "fb_info": "[STRUCT(['', '', ''] AS fbp_fbc_ip)]",
            "segment_id": "segment_ids_array"
        }

        for field, expected_pattern in meta_requirements.items():
            # 檢查欄位存在且格式正確
            self.assertIn(field, query)
            if "CAST" in expected_pattern or "STRUCT" in expected_pattern:
                # 檢查型別轉換
                self.assertIn("CAST", query)

    def test_data_deduplication_logic(self):
        """測試資料去重化邏輯"""
        # 模擬重複資料情況
        mock_data = [
            {"ph_id": "abc123", "sub_class_key": "100"},
            {"ph_id": "abc123", "sub_class_key": "100"},  # 重複
            {"ph_id": "abc123", "sub_class_key": "200"},  # 同 ph_id 不同 segment
            {"ph_id": "def456", "sub_class_key": "100"},  # 不同 ph_id
        ]

        # 預期去重化結果
        expected_result = {
            "abc123": ["tm:c_715_pc_100", "tm:c_715_pc_200"],
            "def456": ["tm:c_715_pc_100"]
        }

        # 模擬去重化邏輯
        result = {}
        for item in mock_data:
            ph_id = item["ph_id"]
            segment = f"tm:c_715_pc_{item['sub_class_key']}"

            if ph_id not in result:
                result[ph_id] = set()
            result[ph_id].add(segment)

        # 轉換為列表並排序以便比較
        for ph_id in result:
            result[ph_id] = sorted(list(result[ph_id]))

        self.assertEqual(result, expected_result)

    def test_reference_implementation_consistency(self):
        """測試與參考實作的一致性"""
        # 檢查與 carrefour_repack_audience 的一致性

        # 參考實作的關鍵特徵
        reference_features = {
            "ph_id_conversion": "TO_HEX(ph_id)",
            "segment_prefix": "tm:c_715_pc_",
            "avro_format": "format = 'AVRO'",
            "logical_types": "use_avro_logical_types = TRUE",
            "phones_array": "[ph_id_hex] AS phones",
            "empty_emails": "CAST([] AS ARRAY<STRING>) AS emails"
        }

        with patch('analysis.ph_id_direct_processor.bigquery.Client'), \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            processor = PhIdDirectProcessor()
            processor.config = self.test_config

            query = processor.generate_avro_export_query("2025-09-11", "gs://test/*.avro")

            # 檢查所有參考特徵都存在
            for feature_name, feature_pattern in reference_features.items():
                with self.subTest(feature=feature_name):
                    self.assertIn(feature_pattern, query)

    def test_cost_estimation_accuracy(self):
        """測試成本估算準確性"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client') as mock_bigquery, \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            # 設定 mock
            mock_job = Mock()
            mock_job.total_bytes_processed = 1024 * 1024 * 1024  # 1GB
            mock_bigquery.return_value.query.return_value = mock_job

            processor = PhIdDirectProcessor()

            cost = processor._estimate_query_cost("SELECT * FROM test")

            # 1GB = 1/1024 TB, BigQuery 成本 $5/TB
            expected_cost = (1024 * 1024 * 1024) / (1024**4) * 5
            self.assertAlmostEqual(cost, expected_cost, places=6)

            # 檢查成本在合理範圍內
            self.assertGreater(cost, 0)
            self.assertLess(cost, 1.0)  # 1GB 不應該超過 $1

    def test_date_handling(self):
        """測試日期處理正確性"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client'), \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            processor = PhIdDirectProcessor()

            # 測試指定日期
            result = processor._calculate_target_date("2025-09-11")
            self.assertEqual(result, "2025-09-11")

            # 測試日期格式
            self.assertRegex(result, r"^\d{4}-\d{2}-\d{2}$")

            # 測試自動日期
            auto_result = processor._calculate_target_date("auto")
            self.assertRegex(auto_result, r"^\d{4}-\d{2}-\d{2}$")

    def test_gcs_path_generation(self):
        """測試 GCS 路徑生成正確性"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client'), \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            processor = PhIdDirectProcessor()
            processor.config = self.test_config

            target_date = "2025-09-11"
            output_uri = "gs://tagtoo-ml-workflow/carrefour_ph_id_direct/20250911/carrefour_ph_id_export_123456_*.avro"

            # 測試 AVRO 查詢中的路徑生成
            query = processor.generate_avro_export_query(target_date, output_uri)

            # 檢查路徑包含在查詢中
            self.assertIn(output_uri, query)

            # 檢查包含必要元素
            self.assertIn("tagtoo-ml-workflow", query)
            self.assertIn("carrefour_ph_id_direct", query)
            self.assertIn("20250911", query)
            self.assertIn(".avro", query)


if __name__ == '__main__':
    # 設定測試環境
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'test-project'

    # 執行測試
    unittest.main(verbosity=2)
