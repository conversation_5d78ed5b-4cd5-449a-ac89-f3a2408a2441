#!/usr/bin/env python3
"""
家樂福 ph_id 直接處理功能效能測試

測試範圍：
1. 執行時間效能
2. 記憶體使用效能
3. BigQuery 查詢效能
4. 成本控制驗證
5. 並發處理能力

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import json
import os
import sys
import time
import unittest
from unittest.mock import Mock, patch
from datetime import datetime

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from analysis.ph_id_direct_processor import PhIdDirectProcessor


class TestPerformance(unittest.TestCase):
    """效能測試"""

    def setUp(self):
        """測試前置設定"""
        self.test_config = {
            "source_table": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
            "target_dataset": "tagtoo-tracking.event_prod",
            "gcs_bucket": "tagtoo-ml-workflow",
            "gcs_path_prefix": "carrefour_ph_id_direct",
            "pubsub_topic": "projects/tagtoo-ml-workflow/topics/carrefour-ph-id-processing",
            "ec_id": 715,
            "max_cost_usd": 50.0,
            "batch_size": 1000
        }

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_initialization_performance(self, mock_publisher, mock_storage, mock_bigquery):
        """測試初始化效能"""
        start_time = time.time()

        processor = PhIdDirectProcessor()

        end_time = time.time()
        initialization_time = end_time - start_time

        # 初始化應該在 2 秒內完成
        self.assertLess(initialization_time, 2.0)

        # 檢查必要屬性已設定
        self.assertIsNotNone(processor.client)
        self.assertIsNotNone(processor.storage_client)
        self.assertIsNotNone(processor.publisher)
        self.assertIsNotNone(processor.execution_id)

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_query_generation_performance(self, mock_publisher, mock_storage, mock_bigquery):
        """測試查詢生成效能"""
        processor = PhIdDirectProcessor()
        processor.config = self.test_config

        start_time = time.time()

        # 生成多個查詢測試效能
        for i in range(10):
            target_date = f"2025-09-{11+i:02d}"
            output_uri = f"gs://test-bucket/test_{i}/*.avro"
            query = processor.generate_avro_export_query(target_date, output_uri)

            # 檢查查詢不為空
            self.assertGreater(len(query), 100)

        end_time = time.time()
        generation_time = end_time - start_time

        # 10 個查詢生成應該在 1 秒內完成
        self.assertLess(generation_time, 1.0)

        # 平均每個查詢生成時間
        avg_time = generation_time / 10
        self.assertLess(avg_time, 0.1)  # 每個查詢 < 100ms

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_cost_estimation_performance(self, mock_publisher, mock_storage, mock_bigquery):
        """測試成本估算效能"""
        # 設定 mock
        mock_job = Mock()
        mock_job.total_bytes_processed = 1024 * 1024 * 1024  # 1GB
        mock_bigquery.return_value.query.return_value = mock_job

        processor = PhIdDirectProcessor()

        start_time = time.time()

        # 執行多次成本估算
        for i in range(5):
            cost = processor._estimate_query_cost(f"SELECT * FROM test_{i}")
            self.assertGreater(cost, 0)

        end_time = time.time()
        estimation_time = end_time - start_time

        # 5 次成本估算應該在 5 秒內完成
        self.assertLess(estimation_time, 5.0)

        # 平均每次估算時間
        avg_time = estimation_time / 5
        self.assertLess(avg_time, 1.0)  # 每次估算 < 1 秒

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_dry_run_performance(self, mock_publisher, mock_storage, mock_bigquery):
        """測試 dry run 效能"""
        # 設定 mock
        mock_bigquery_client = Mock()
        mock_job = Mock()
        mock_job.total_bytes_processed = 1024 * 1024 * 1024  # 1GB
        mock_bigquery_client.query.return_value = mock_job
        mock_bigquery.return_value = mock_bigquery_client

        mock_storage.return_value = Mock()
        mock_publisher.return_value = Mock()

        # Mock the processor methods
        with patch.object(PhIdDirectProcessor, 'extract_ph_id_segments') as mock_extract, \
             patch.object(PhIdDirectProcessor, 'execute_avro_export') as mock_export, \
             patch.object(PhIdDirectProcessor, 'create_temp_mapping_table') as mock_temp, \
             patch.object(PhIdDirectProcessor, 'publish_completion_notification') as mock_notify:

            # 設定 mock 返回值 - 修正為正確的列表格式
            mock_extract.return_value = [
                {
                    "ph_id_hex": f"TEST{i:06d}",
                    "segment_ids": [f"tm:c_715_pc_{100 + (i % 10)}"],
                    "segment_count": 1
                }
                for i in range(100)  # 模擬 100 筆測試資料
            ]
            mock_export.return_value = {"status": "success", "files_created": 5}
            mock_temp.return_value = {"table_name": "test_table", "record_count": 8000}
            mock_notify.return_value = {"message_id": "test-message-id"}

            processor = PhIdDirectProcessor()

            start_time = time.time()

            result = processor.run_complete_pipeline(
                target_date="2025-09-11",
                dry_run=True
            )

            end_time = time.time()
            execution_time = end_time - start_time

            # Dry run 應該在 10 秒內完成
            self.assertLess(execution_time, 10.0)

            # 檢查結果
            self.assertEqual(result["status"], "success")
            self.assertTrue(result["dry_run"])
            self.assertIn("execution_time_seconds", result)

    def test_memory_usage_estimation(self):
        """測試記憶體使用估算"""
        # 模擬不同資料量的記憶體使用
        test_cases = [
            {"records": 1000, "expected_mb": 1},
            {"records": 10000, "expected_mb": 10},
            {"records": 100000, "expected_mb": 100},
            {"records": 1000000, "expected_mb": 1000}
        ]

        for case in test_cases:
            # 估算記憶體使用 (假設每筆記錄約 1KB)
            estimated_memory_mb = case["records"] * 1024 / (1024 * 1024)

            # 檢查估算在合理範圍內
            self.assertLessEqual(estimated_memory_mb, case["expected_mb"] * 1.5)
            self.assertGreaterEqual(estimated_memory_mb, case["expected_mb"] * 0.5)

    def test_cost_control_limits(self):
        """測試成本控制限制"""
        test_scenarios = [
            {"bytes": 1024**3, "should_pass": True},      # 1GB
            {"bytes": 10 * 1024**3, "should_pass": True},  # 10GB
            {"bytes": 100 * 1024**3, "should_pass": True},  # 100GB
            {"bytes": 1000 * 1024**3, "should_pass": True}, # 1TB
            {"bytes": 10000 * 1024**3, "should_pass": True}, # 10TB (limit)
            {"bytes": 20000 * 1024**3, "should_pass": False} # 20TB (over limit)
        ]

        max_cost_limit = 50.0  # $50 USD

        for scenario in test_scenarios:
            # 計算成本 (BigQuery: $5/TB)
            cost_usd = (scenario["bytes"] / (1024**4)) * 5

            # 檢查成本控制邏輯
            within_limit = cost_usd <= max_cost_limit
            self.assertEqual(within_limit, scenario["should_pass"])

            # 檢查成本計算合理性
            self.assertGreater(cost_usd, 0)

    def test_concurrent_processing_simulation(self):
        """測試並發處理模擬"""
        # 模擬多個並發請求的處理時間
        concurrent_requests = 5

        start_time = time.time()

        # 模擬並發處理
        results = []
        for i in range(concurrent_requests):
            # 模擬處理時間 (實際會是 I/O 等待)
            processing_time = 0.1  # 100ms per request
            time.sleep(processing_time)

            result = {
                "request_id": i,
                "status": "success",
                "processing_time": processing_time
            }
            results.append(result)

        end_time = time.time()
        total_time = end_time - start_time

        # 檢查所有請求都完成
        self.assertEqual(len(results), concurrent_requests)

        # 檢查總時間合理 (序列處理)
        expected_min_time = concurrent_requests * 0.1
        self.assertGreaterEqual(total_time, expected_min_time * 0.9)
        self.assertLessEqual(total_time, expected_min_time * 1.5)

    def test_large_dataset_simulation(self):
        """測試大型資料集模擬"""
        # 模擬不同大小的資料集處理
        dataset_sizes = [
            {"name": "small", "records": 1000, "max_time": 1.0},
            {"name": "medium", "records": 10000, "max_time": 5.0},
            {"name": "large", "records": 100000, "max_time": 30.0},
            {"name": "xlarge", "records": 1000000, "max_time": 300.0}
        ]

        for dataset in dataset_sizes:
            start_time = time.time()

            # 模擬資料處理 (簡化版)
            processing_time = dataset["records"] / 10000  # 假設每 10k 記錄需要 1 秒

            # 模擬處理延遲
            time.sleep(min(processing_time, 0.1))  # 最多等待 100ms

            end_time = time.time()
            actual_time = end_time - start_time

            # 檢查處理時間在預期範圍內 (模擬環境)
            self.assertLess(actual_time, 1.0)  # 模擬環境下應該很快

            # 檢查理論處理時間
            self.assertLess(processing_time, dataset["max_time"])

    def test_error_handling_performance(self):
        """測試錯誤處理效能"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client') as mock_bigquery, \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            # 設定 mock 拋出異常
            mock_bigquery.side_effect = Exception("Mock error")

            start_time = time.time()

            # 測試錯誤處理
            with self.assertRaises(Exception):
                processor = PhIdDirectProcessor()

            end_time = time.time()
            error_handling_time = end_time - start_time

            # 錯誤處理應該很快完成
            self.assertLess(error_handling_time, 1.0)


if __name__ == '__main__':
    # 設定測試環境
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'test-project'

    # 執行測試
    unittest.main(verbosity=2)
