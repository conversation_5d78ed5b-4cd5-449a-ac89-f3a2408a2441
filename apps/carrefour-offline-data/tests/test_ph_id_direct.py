#!/usr/bin/env python3
"""
家樂福 ph_id 直接處理功能單元測試

測試範圍：
1. PhIdDirectProcessor 核心功能
2. GCSUploader 上傳功能
3. PubSubNotifier 通知功能
4. 配置載入和驗證
5. 錯誤處理機制

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import json
import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from pathlib import Path

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from analysis.ph_id_direct_processor import PhIdDirectProcessor
from tools.gcs_uploader import GCSUploader
from tools.pubsub_notifier import PubSubNotifier


class TestPhIdDirectProcessor(unittest.TestCase):
    """PhIdDirectProcessor 單元測試"""

    def setUp(self):
        """測試前置設定"""
        self.test_config = {
            "source_table": "test-project.test_dataset.test_table",
            "target_dataset": "test-project.test_dataset",
            "gcs_bucket": "test-bucket",
            "gcs_path_prefix": "test_prefix",
            "pubsub_topic": "projects/test-project/topics/test-topic",
            "ec_id": 715,
            "max_cost_usd": 10.0,
            "batch_size": 1000
        }

        # Mock BigQuery client
        self.mock_bigquery_client = Mock()
        self.mock_storage_client = Mock()
        self.mock_publisher = Mock()

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_processor_initialization(self, mock_publisher, mock_storage, mock_bigquery):
        """測試處理器初始化"""
        mock_bigquery.return_value = self.mock_bigquery_client
        mock_storage.return_value = self.mock_storage_client
        mock_publisher.return_value = self.mock_publisher

        processor = PhIdDirectProcessor()

        self.assertIsNotNone(processor.client)
        self.assertIsNotNone(processor.storage_client)
        self.assertIsNotNone(processor.publisher)
        self.assertIsNotNone(processor.execution_id)
        self.assertIn("carrefour_ph_id_direct", processor.execution_id)

    def test_calculate_target_date(self):
        """測試目標日期計算"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client'), \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            processor = PhIdDirectProcessor()

            # 測試指定日期
            result = processor._calculate_target_date("2025-09-11")
            self.assertEqual(result, "2025-09-11")

            # 測試自動日期 (昨天) - 簡化測試，只檢查返回值格式
            result = processor._calculate_target_date("auto")
            # 檢查返回值是 YYYY-MM-DD 格式
            self.assertRegex(result, r'^\d{4}-\d{2}-\d{2}$')

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_estimate_query_cost(self, mock_publisher, mock_storage, mock_bigquery):
        """測試查詢成本估算"""
        # 設定 mock
        mock_job = Mock()
        mock_job.total_bytes_processed = 1024 * 1024 * 1024  # 1GB
        mock_bigquery.return_value.query.return_value = mock_job

        processor = PhIdDirectProcessor()

        test_query = "SELECT * FROM test_table"
        cost = processor._estimate_query_cost(test_query)

        # 1GB = 1/1024 TB, 成本應該約為 $5/1024
        expected_cost = (1024 * 1024 * 1024) / (1024**4) * 5
        self.assertAlmostEqual(cost, expected_cost, places=6)

    def test_generate_avro_export_query(self):
        """測試 AVRO 匯出查詢生成"""
        with patch('analysis.ph_id_direct_processor.bigquery.Client'), \
             patch('analysis.ph_id_direct_processor.storage.Client'), \
             patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient'):

            processor = PhIdDirectProcessor()
            processor.config = self.test_config

            target_date = "2025-09-11"
            output_uri = "gs://test-bucket/test_path/*.avro"

            query = processor.generate_avro_export_query(target_date, output_uri)

            # 檢查查詢包含必要元素
            self.assertIn("EXPORT DATA", query)
            self.assertIn(output_uri, query)
            self.assertIn("format = 'AVRO'", query)
            self.assertIn("TO_HEX(ph_id)", query)
            self.assertIn("tm:c_715_pc_", query)
            # 檢查使用相對日期過濾邏輯而不是硬編碼日期
            self.assertIn("DATE_SUB", query)
            self.assertIn("MAX(DATE(TIMESTAMP_SECONDS(event_times)))", query)


class TestGCSUploader(unittest.TestCase):
    """GCSUploader 單元測試"""

    def setUp(self):
        """測試前置設定"""
        self.bucket_name = "test-bucket"
        self.mock_client = Mock()
        self.mock_bucket = Mock()

    @patch('tools.gcs_uploader.storage.Client')
    def test_uploader_initialization(self, mock_storage):
        """測試上傳器初始化"""
        mock_storage.return_value = self.mock_client
        self.mock_client.bucket.return_value = self.mock_bucket

        uploader = GCSUploader(self.bucket_name)

        self.assertEqual(uploader.bucket_name, self.bucket_name)
        self.assertEqual(uploader.client, self.mock_client)
        self.assertEqual(uploader.bucket, self.mock_bucket)

    @patch('tools.gcs_uploader.storage.Client')
    @patch('tools.gcs_uploader.Path')
    @patch('builtins.open', create=True)
    def test_upload_file_success(self, mock_open, mock_path, mock_storage):
        """測試檔案上傳成功"""
        # 設定 mock
        mock_storage.return_value = self.mock_client
        self.mock_client.bucket.return_value = self.mock_bucket

        mock_file = Mock()
        mock_file.exists.return_value = True
        mock_file.stat.return_value.st_size = 1024
        mock_path.return_value = mock_file

        mock_blob = Mock()
        self.mock_bucket.blob.return_value = mock_blob

        mock_file_obj = Mock()
        mock_open.return_value.__enter__.return_value = mock_file_obj

        uploader = GCSUploader(self.bucket_name)

        result = uploader.upload_file("test.txt", "test/test.txt")

        # 檢查結果
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["file_size"], 1024)
        self.assertIn("gcs_uri", result)

        # 檢查 mock 調用
        mock_blob.upload_from_file.assert_called_once_with(mock_file_obj)

    @patch('tools.gcs_uploader.storage.Client')
    @patch('tools.gcs_uploader.Path')
    def test_upload_file_not_found(self, mock_path, mock_storage):
        """測試檔案不存在錯誤"""
        mock_storage.return_value = self.mock_client

        mock_file = Mock()
        mock_file.exists.return_value = False
        mock_path.return_value = mock_file

        uploader = GCSUploader(self.bucket_name)

        with self.assertRaises(FileNotFoundError):
            uploader.upload_file("nonexistent.txt", "test/test.txt")


class TestPubSubNotifier(unittest.TestCase):
    """PubSubNotifier 單元測試"""

    def setUp(self):
        """測試前置設定"""
        self.project_id = "test-project"
        self.mock_publisher = Mock()
        self.mock_subscriber = Mock()

    @patch('tools.pubsub_notifier.pubsub_v1.PublisherClient')
    @patch('tools.pubsub_notifier.pubsub_v1.SubscriberClient')
    def test_notifier_initialization(self, mock_subscriber, mock_publisher):
        """測試通知器初始化"""
        mock_publisher.return_value = self.mock_publisher
        mock_subscriber.return_value = self.mock_subscriber

        notifier = PubSubNotifier(self.project_id)

        self.assertEqual(notifier.project_id, self.project_id)
        self.assertEqual(notifier.publisher, self.mock_publisher)
        self.assertEqual(notifier.subscriber, self.mock_subscriber)

    @patch('tools.pubsub_notifier.pubsub_v1.PublisherClient')
    @patch('tools.pubsub_notifier.pubsub_v1.SubscriberClient')
    def test_publish_message_success(self, mock_subscriber, mock_publisher):
        """測試訊息發送成功"""
        mock_publisher.return_value = self.mock_publisher
        mock_subscriber.return_value = self.mock_subscriber

        # 設定 mock
        mock_future = Mock()
        mock_future.result.return_value = "test-message-id"
        self.mock_publisher.publish.return_value = mock_future
        self.mock_publisher.topic_path.return_value = "projects/test-project/topics/test-topic"

        notifier = PubSubNotifier(self.project_id)

        message_data = {"test": "data"}
        message_id = notifier.publish_message("test-topic", message_data)

        self.assertEqual(message_id, "test-message-id")
        self.mock_publisher.publish.assert_called_once()

    @patch('tools.pubsub_notifier.pubsub_v1.PublisherClient')
    @patch('tools.pubsub_notifier.pubsub_v1.SubscriberClient')
    def test_publish_processing_completion(self, mock_subscriber, mock_publisher):
        """測試處理完成通知"""
        mock_publisher.return_value = self.mock_publisher
        mock_subscriber.return_value = self.mock_subscriber

        mock_future = Mock()
        mock_future.result.return_value = "completion-message-id"
        self.mock_publisher.publish.return_value = mock_future
        self.mock_publisher.topic_path.return_value = "projects/test-project/topics/test-topic"

        notifier = PubSubNotifier(self.project_id)

        result = {"status": "success", "record_count": 1000}
        message_id = notifier.publish_processing_completion(
            "test-topic", "test-execution-id", result, "2025-09-11"
        )

        self.assertEqual(message_id, "completion-message-id")

        # 檢查發送的訊息內容
        call_args = self.mock_publisher.publish.call_args
        message_bytes = call_args[0][1]
        message_data = json.loads(message_bytes.decode('utf-8'))

        self.assertEqual(message_data["event_type"], "ph_id_processing_completed")
        self.assertEqual(message_data["execution_id"], "test-execution-id")
        self.assertEqual(message_data["target_date"], "2025-09-11")


class TestIntegration(unittest.TestCase):
    """整合測試"""

    @patch('analysis.ph_id_direct_processor.bigquery.Client')
    @patch('analysis.ph_id_direct_processor.storage.Client')
    @patch('analysis.ph_id_direct_processor.pubsub_v1.PublisherClient')
    def test_dry_run_pipeline(self, mock_publisher, mock_storage, mock_bigquery):
        """測試 dry run 完整流程"""
        # 設定 mock BigQuery
        mock_bigquery_client = Mock()
        mock_job = Mock()
        mock_job.total_bytes_processed = 1024 * 1024  # 1MB

        # Mock query results for extract_ph_id_segments
        mock_results = [
            Mock(ph_id_hex="abc123", segment_ids=["tm:c_715_pc_100"]),
            Mock(ph_id_hex="def456", segment_ids=["tm:c_715_pc_200"])
        ]
        mock_bigquery_client.query.return_value = mock_results
        mock_bigquery.return_value = mock_bigquery_client

        mock_storage.return_value = Mock()
        mock_publisher.return_value = Mock()

        # Mock the processor methods that would fail in dry run
        with patch.object(PhIdDirectProcessor, 'extract_ph_id_segments') as mock_extract, \
             patch.object(PhIdDirectProcessor, 'execute_avro_export') as mock_export, \
             patch.object(PhIdDirectProcessor, 'create_temp_mapping_table') as mock_temp, \
             patch.object(PhIdDirectProcessor, 'publish_completion_notification') as mock_notify:

            # 設定 mock 返回值 - 修正為正確的列表格式
            mock_extract.return_value = [
                {
                    "ph_id_hex": "abc123",
                    "segment_ids": ["tm:c_715_pc_100", "tm:c_715_pc_314"],
                    "segment_count": 2
                },
                {
                    "ph_id_hex": "def456",
                    "segment_ids": ["tm:c_715_pc_200", "tm:c_715_pc_311"],
                    "segment_count": 2
                }
            ]
            mock_export.return_value = {"status": "success", "files_created": 5}
            mock_temp.return_value = {"table_name": "test_table", "record_count": 800}
            mock_notify.return_value = {"message_id": "test-message-id"}

            processor = PhIdDirectProcessor()

            # 執行 dry run
            result = processor.run_complete_pipeline(
                target_date="2025-09-11",
                dry_run=True
            )

            # 檢查結果
            self.assertEqual(result["status"], "success")
            self.assertEqual(result["target_date"], "2025-09-11")
            self.assertTrue(result["dry_run"])
            self.assertIn("steps", result)
            self.assertIn("execution_time_seconds", result)


if __name__ == '__main__':
    # 設定測試環境
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'test-project'

    # 執行測試
    unittest.main(verbosity=2)
