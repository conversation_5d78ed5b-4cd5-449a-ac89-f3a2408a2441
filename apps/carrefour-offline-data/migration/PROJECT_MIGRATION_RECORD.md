# 家樂福離線事件資料專案移動記錄

## 📋 移動概述

**移動日期**：2025-08-15
**原始位置**：`/Users/<USER>/tagtoo/tagtoo-partners/apps/carrefour-offline-data`
**目標位置**：`/Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data`
**移動原因**：GCP project 配置更符合組織架構

## 🎯 專案完成狀態

### ✅ 技術實作完成度：100%

#### 已完成的 6 個主要任務：

1. **Schema 一致性檢查與修正**（最高優先級）✅

   - 目標表格已建立：`tagtoo-tracking.event_prod.carrefour_offline_data`
   - Schema 驗證通過：與來源表格 100% 一致
   - 工具：`schema_validator.py`

2. **優化資料複製腳本**✅

   - 智慧批次策略：基於效能自動調整
   - 指數退避重試機制：3 次重試
   - 成本監控：每批次 < $10 USD
   - 進度追蹤：即時 ETA 計算
   - 工具：`copy_carrefour_data.py`

3. **建立資料驗證流程**✅

   - Level 1: 基本統計比較
   - Level 2: 資料類型和 NULL 值分布
   - Level 3: 隨機抽樣 1000 筆比對
   - Level 4: 複雜 STRUCT 欄位驗證
   - 工具：`data_validator.py`

4. **執行環境準備與測試**✅

   - 自動化環境設定：`setup_environment.py`
   - 依賴管理：所有套件已安裝
   - 權限驗證：完整檢查機制
   - 配置檔案：`config.json`, `set_env.sh`

5. **監控、日誌與通知系統**✅

   - 結構化日誌：JSON 格式
   - 效能監控：吞吐量、成本追蹤
   - 進度追蹤：即時進度顯示
   - 工具：`monitoring_logger.py`

6. **文件與流程標準化**✅
   - 操作手冊：`OPERATIONS_MANUAL.md`
   - 執行檢查清單：`EXECUTION_CHECKLIST.md`
   - 開發記錄：`DEVELOPMENT.md`
   - 整合腳本：`run_full_pipeline.py`

### 🔄 當前狀態

- **技術準備度**：100% 完成
- **環境狀態**：完全就緒
- **阻塞因素**：等待 BigQuery Job User 權限開通
- **預期執行時間**：權限開通後 2 小時內完成

## 📊 技術規格摘要

### 資料規模

- **來源**：`tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
- **目標**：`tagtoo-tracking.event_prod.carrefour_offline_data`
- **資料量**：15,242,556 行，18.63 GB
- **欄位數**：11 個（包含複雜 STRUCT）

### 效能預期

- **複製時間**：1-2 小時
- **吞吐量**：2,000-5,000 行/秒
- **成本**：< $1 USD
- **成功率**：> 99%

### 權限需求

- ✅ 來源表格讀取：已有
- ❌ 來源查詢權限：需要 `bigquery.jobs.create` ⚠️ **關鍵阻塞**
- ✅ 目標表格讀寫：已有

## 🛠️ 核心技術決策

### 1. 智慧批次策略

- **決策**：動態調整批次大小（10,000-50,000 行）
- **原因**：平衡效能和成本控制
- **實作**：基於歷史效能數據自動調整

### 2. 成本控制機制

- **決策**：每批次執行前 dry-run 估算
- **原因**：確保符合 $10 USD 單次查詢限制
- **實作**：自動成本監控和警報

### 3. 多層次驗證

- **決策**：4 個層次的漸進式驗證
- **原因**：確保資料完整性和品質
- **實作**：從基本統計到深度結構驗證

### 4. 斷點續傳支援

- **決策**：支援中斷恢復
- **原因**：提高大資料量處理的可靠性
- **實作**：狀態追蹤和恢復機制

## 📁 完整檔案清單

### 核心系統檔案

- `run_full_pipeline.py` - 整合執行腳本
- `copy_carrefour_data.py` - 智慧資料複製系統
- `data_validator.py` - 四層次驗證系統
- `schema_validator.py` - Schema 一致性檢查
- `monitoring_logger.py` - 監控日誌系統
- `setup_environment.py` - 環境設定工具

### 操作文件

- `OPERATIONS_MANUAL.md` - 完整操作手冊
- `EXECUTION_CHECKLIST.md` - 執行檢查清單
- `DEVELOPMENT.md` - 開發狀態記錄
- `data_copy_strategy.md` - 技術策略文件
- `permission_request.md` - 權限申請文件

### 配置和腳本

- `config.json` - 系統配置
- `set_env.sh` - 環境變數腳本
- `setup_auth.sh` - 認證設定腳本
- `create_carrefour_table.sql` - 建表 DDL
- `requirements.txt` - Python 依賴

### 驗證工具

- `validate_carrefour_data_v3.py` - 規格驗證工具
- `check_bigquery_table.py` - 表格檢查工具

### 報告檔案

- `schema_comparison_report.md` - Schema 比較報告
- `environment_setup_report.md` - 環境設定報告
- `carrefour-validation-spec.md` - 驗證規格文件

## 🔧 關鍵技術實作細節

### Service Account 配置

```
<EMAIL>
```

### 權限申請指令

```bash
gcloud projects add-iam-policy-binding tw-eagle-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

### 執行指令（權限開通後）

```bash
source venv/bin/activate
source set_env.sh
python3 run_full_pipeline.py
```

## 📝 開發歷程重要里程碑

### 2025-08-15 上午

- 建立專案結構和虛擬環境
- 配置 Service Account 和基本權限
- 開發初版驗證腳本

### 2025-08-15 下午

- 完成目標表格建立和 Schema 驗證
- 開發智慧資料複製系統
- 實作多層次驗證流程
- 建立監控日誌系統
- 完成操作文件和檢查清單

### 當前狀態

- 所有技術實作 100% 完成
- 等待 BigQuery Job User 權限開通
- 準備移動到 integrated-event 專案

## 🎯 移動後的後續計畫

### 立即執行（移動後）

1. 在新位置重新測試所有模組
2. 更新配置檔案中的路徑（如需要）
3. 驗證虛擬環境和依賴

### 權限開通後

1. 執行完整的資料複製和驗證流程
2. 生成最終報告
3. 建立 git commit 歷史

### Git Commit 策略

1. Initial commit: 專案結構和基礎配置
2. Feature commit: 各個主要功能模組
3. Documentation commit: 操作手冊和文件
4. Integration commit: 整合腳本和最終優化

## 🚨 重要注意事項

1. **虛擬環境**：移動後需要重新啟動虛擬環境
2. **認證狀態**：Service Account Impersonation 設定需要確認
3. **權限狀態**：移動不影響 BigQuery 權限，仍需等待開通
4. **配置檔案**：所有配置都是相對路徑，移動後仍然有效

## 📞 聯繫資訊

如移動後有任何問題，請參考：

- `OPERATIONS_MANUAL.md` - 完整操作指南
- `EXECUTION_CHECKLIST.md` - 執行檢查清單
- 本記錄檔案 - 完整的開發上下文

---

**記錄建立時間**：2025-08-15
**記錄建立者**：AI Assistant
**專案狀態**：技術實作完成，等待權限開通
**移動準備度**：100% 就緒
