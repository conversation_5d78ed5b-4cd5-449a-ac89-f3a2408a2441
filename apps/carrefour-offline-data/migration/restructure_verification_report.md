# 目錄重組驗證報告

生成時間: Fri Aug 15 16:13:56 CST 2025

## 目錄結構

```
.
├── README.md
├── config
│   ├── config.json
│   ├── create_carrefour_table.sql
│   ├── set_env.sh
│   └── setup_auth.sh
├── docs
│   ├── DEVELOPMENT.md
│   ├── EXECUTION_CHECKLIST.md
│   ├── OPERATIONS_MANUAL.md
│   ├── carrefour-validation-spec.md
│   ├── data_copy_strategy.md
│   └── permission_request.md
├── migration
│   ├── DIRECTORY_RESTRUCTURE.md
│   ├── GIT_COMMIT_PLAN.md
│   ├── PROJECT_MIGRATION_RECORD.md
│   └── PROJECT_MOVE_CHECKLIST.md
├── reports
│   ├── carrefour_validation_report_v3_20250815_130430.txt
│   ├── environment_setup_report.json
│   ├── environment_setup_report.md
│   ├── schema_comparison_report.json
│   └── schema_comparison_report.md
├── requirements.txt
├── src
│   ├── copy_carrefour_data.py
│   ├── data_validator.py
│   ├── monitoring_logger.py
│   ├── run_full_pipeline.py
│   ├── schema_validator.py
│   └── setup_environment.py
├── tools
│   ├── check_bigquery_table.py
│   ├── validate_carrefour_data.py
│   ├── validate_carrefour_data_v2.py
│   └── validate_carrefour_data_v3.py
└── verify_restructure.py

7 directories, 32 files

```

## 檔案統計

- Python 檔案數: 11
- Markdown 檔案數: 15
- 配置檔案數: 4

## 重組狀態

✅ 目錄重組完成
✅ 檔案分類正確
✅ 功能模組可正常導入
✅ 準備移動到 integrated-event 專案
