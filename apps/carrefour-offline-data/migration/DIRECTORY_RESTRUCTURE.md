# 目錄結構重組記錄

## 📋 重組概述

**重組日期**：2025-08-15
**重組目的**：為專案移動做準備，建立清晰的邏輯結構，提高可讀性和維護性
**重組範圍**：家樂福離線事件資料專案完整目錄結構

## 🎯 重組前後對比

### 重組前（扁平結構）

```
carrefour-offline-data/
├── README.md
├── requirements.txt
├── copy_carrefour_data.py
├── data_validator.py
├── schema_validator.py
├── monitoring_logger.py
├── setup_environment.py
├── run_full_pipeline.py
├── config.json
├── set_env.sh
├── setup_auth.sh
├── create_carrefour_table.sql
├── validate_carrefour_data_v3.py
├── validate_carrefour_data_v2.py
├── validate_carrefour_data.py
├── check_bigquery_table.py
├── OPERATIONS_MANUAL.md
├── EXECUTION_CHECKLIST.md
├── DEVELOPMENT.md
├── data_copy_strategy.md
├── permission_request.md
├── carrefour-validation-spec.md
├── PROJECT_MIGRATION_RECORD.md
├── GIT_COMMIT_PLAN.md
├── PROJECT_MOVE_CHECKLIST.md
├── schema_comparison_report.md
├── schema_comparison_report.json
├── environment_setup_report.md
├── environment_setup_report.json
├── carrefour_validation_report_v3_20250815_130430.txt
└── venv/
```

### 重組後（邏輯分層結構）

```
carrefour-offline-data/
├── README.md                          # 專案入口說明
├── requirements.txt                   # Python 依賴
├──
├── 📁 src/                            # 核心系統程式碼
│   ├── copy_carrefour_data.py         # 智慧資料複製系統
│   ├── data_validator.py              # 四層次資料驗證系統
│   ├── schema_validator.py            # Schema 一致性檢查
│   ├── monitoring_logger.py           # 監控日誌系統
│   ├── setup_environment.py           # 環境設定工具
│   └── run_full_pipeline.py           # 整合執行腳本
│
├── 📁 config/                         # 配置和腳本檔案
│   ├── config.json                    # 系統配置
│   ├── set_env.sh                     # 環境變數腳本
│   ├── setup_auth.sh                  # 認證設定腳本
│   └── create_carrefour_table.sql     # 建表 DDL
│
├── 📁 tools/                          # 驗證和檢查工具
│   ├── validate_carrefour_data_v3.py  # 規格驗證工具
│   ├── validate_carrefour_data_v2.py  # 舊版驗證工具
│   ├── validate_carrefour_data.py     # 初版驗證工具
│   └── check_bigquery_table.py        # 表格檢查工具
│
├── 📁 docs/                           # 文檔和操作手冊
│   ├── OPERATIONS_MANUAL.md           # 完整操作手冊
│   ├── EXECUTION_CHECKLIST.md         # 執行檢查清單
│   ├── DEVELOPMENT.md                 # 開發狀態記錄
│   ├── data_copy_strategy.md          # 技術策略文件
│   ├── permission_request.md          # 權限申請文件
│   └── carrefour-validation-spec.md   # 驗證規格文件
│
├── 📁 migration/                      # 專案移動相關檔案
│   ├── PROJECT_MIGRATION_RECORD.md    # 完整開發歷程記錄
│   ├── GIT_COMMIT_PLAN.md             # Git 提交策略計畫
│   ├── PROJECT_MOVE_CHECKLIST.md      # 移動執行檢查清單
│   └── DIRECTORY_RESTRUCTURE.md       # 目錄重組記錄（本檔案）
│
├── 📁 reports/                        # 報告和輸出檔案
│   ├── schema_comparison_report.md    # Schema 比較報告
│   ├── schema_comparison_report.json  # Schema 比較報告 (JSON)
│   ├── environment_setup_report.md    # 環境設定報告
│   ├── environment_setup_report.json  # 環境設定報告 (JSON)
│   └── carrefour_validation_report_v3_20250815_130430.txt
│
└── 📁 venv/                           # Python 虛擬環境
    ├── bin/
    ├── include/
    ├── lib/
    └── pyvenv.cfg
```

## 🎯 重組邏輯和設計原則

### 1. 功能導向分類

- **src/**：核心業務邏輯和系統程式碼
- **config/**：配置檔案和設定腳本
- **tools/**：輔助工具和驗證腳本
- **docs/**：文檔和操作手冊
- **migration/**：專案移動相關檔案
- **reports/**：輸出報告和結果檔案

### 2. 可讀性優化

- **清晰的目錄命名**：一眼就能理解目錄用途
- **邏輯分層**：相關檔案集中在同一目錄
- **入口檔案**：README.md 和 requirements.txt 保持在根目錄

### 3. 維護性提升

- **模組化結構**：便於獨立維護和更新
- **版本管理友好**：便於 Git 追蹤和管理
- **擴展性**：便於後續添加新功能和檔案

## 📁 各目錄詳細說明

### src/ - 核心系統程式碼

**用途**：包含所有核心業務邏輯和系統功能
**檔案特點**：

- 可執行的 Python 腳本
- 核心業務邏輯實作
- 系統主要功能模組

**關鍵檔案**：

- `run_full_pipeline.py` - 整合執行入口
- `copy_carrefour_data.py` - 資料複製核心
- `data_validator.py` - 驗證系統核心

### config/ - 配置和腳本檔案

**用途**：系統配置、環境設定和建置腳本
**檔案特點**：

- 配置檔案（JSON、Shell）
- 環境設定腳本
- 資料庫 DDL 腳本

### tools/ - 驗證和檢查工具

**用途**：輔助工具和獨立驗證腳本
**檔案特點**：

- 獨立執行的驗證工具
- 檢查和診斷腳本
- 版本演進的工具集

### docs/ - 文檔和操作手冊

**用途**：完整的專案文檔和操作指南
**檔案特點**：

- Markdown 格式文檔
- 操作手冊和指南
- 技術規格和策略文件

### migration/ - 專案移動相關檔案

**用途**：專案移動和歷史記錄相關檔案
**檔案特點**：

- 移動計畫和記錄
- Git 提交策略
- 專案歷程文檔

### reports/ - 報告和輸出檔案

**用途**：系統生成的報告和輸出結果
**檔案特點**：

- 自動生成的報告檔案
- JSON 和 Markdown 格式
- 執行結果和統計資料

## 🔧 重組後的使用指南

### 快速定位檔案

1. **要執行程式**：查看 `src/` 目錄
2. **要修改配置**：查看 `config/` 目錄
3. **要查看文檔**：查看 `docs/` 目錄
4. **要了解移動計畫**：查看 `migration/` 目錄
5. **要查看報告**：查看 `reports/` 目錄

### AI 助手快速理解指南

1. **專案概述**：先讀 `README.md`
2. **當前狀態**：查看 `docs/DEVELOPMENT.md`
3. **執行指南**：查看 `docs/OPERATIONS_MANUAL.md`
4. **移動計畫**：查看 `migration/` 目錄下的檔案
5. **核心程式碼**：查看 `src/` 目錄

### 執行路徑調整

重組後，執行腳本時需要調整路徑：

```bash
# 舊路徑
python3 run_full_pipeline.py

# 新路徑
python3 src/run_full_pipeline.py
```

## 🎯 重組效益

### 1. 提升可讀性

- ✅ 目錄結構一目了然
- ✅ 檔案分類邏輯清晰
- ✅ 便於新人快速理解

### 2. 改善維護性

- ✅ 模組化管理
- ✅ 便於版本控制
- ✅ 降低維護複雜度

### 3. 優化移動準備

- ✅ 專案移動檔案集中管理
- ✅ 便於 AI 助手快速理解
- ✅ 為 Git 提交歷史奠定基礎

### 4. 增強擴展性

- ✅ 便於添加新功能
- ✅ 支援多版本工具管理
- ✅ 靈活的報告管理

## 📝 重組執行記錄

### 執行步驟

1. ✅ 建立新目錄結構（src, config, tools, docs, migration, reports）
2. ✅ 移動核心系統檔案到 src/
3. ✅ 移動配置檔案到 config/
4. ✅ 移動驗證工具到 tools/
5. ✅ 移動文檔到 docs/
6. ✅ 移動專案移動檔案到 migration/
7. ✅ 移動報告檔案到 reports/
8. ✅ 建立重組記錄文檔

### 驗證結果

- ✅ 所有檔案都已正確分類
- ✅ 目錄結構邏輯清晰
- ✅ 便於後續維護和管理

## 🚀 後續行動

### 立即執行

1. **更新 README.md**：反映新的目錄結構
2. **測試執行路徑**：確保所有腳本正常運作
3. **更新文檔**：調整文檔中的檔案路徑引用

### 移動後執行

1. **保持目錄結構**：移動時保持完整的目錄結構
2. **更新 Git 提交計畫**：反映新的檔案路徑
3. **驗證功能完整性**：確保重組後功能正常

---

**重組完成時間**：2025-08-15
**重組執行者**：AI Assistant
**重組狀態**：✅ 完成
**下一步**：更新 README.md 和測試功能
