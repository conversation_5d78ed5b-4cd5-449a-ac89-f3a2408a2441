# 專案移動檢查清單

## 📋 移動前準備

### ✅ 文檔和記錄準備

- [x] **PROJECT_MIGRATION_RECORD.md** - 完整開發歷程記錄
- [x] **GIT_COMMIT_PLAN.md** - Git 提交策略計畫
- [x] **PROJECT_MOVE_CHECKLIST.md** - 本檢查清單
- [x] **DEVELOPMENT.md** - 當前開發狀態記錄
- [x] **所有技術文檔** - 操作手冊、執行清單等

### ✅ 檔案完整性檢查

```bash
# 在移動前執行，確認所有檔案都存在
ls -la /Users/<USER>/tagtoo/tagtoo-partners/apps/carrefour-offline-data/
```

**必要檔案清單**：

- [x] 核心系統檔案（6 個）
- [x] 操作文件（5 個）
- [x] 配置檔案（4 個）
- [x] 驗證工具（4 個）
- [x] 報告檔案（3 個）
- [x] 移動記錄檔案（3 個）

### ✅ 虛擬環境狀態記錄

```bash
# 記錄當前虛擬環境狀態
source venv/bin/activate
pip list > current_packages.txt
python --version > python_version.txt
```

## 🚚 執行移動

### 步驟 1: 建立目標目錄

```bash
# 確保目標目錄存在
mkdir -p /Users/<USER>/tagtoo/integrated-event/apps/
```

### 步驟 2: 複製整個專案

```bash
# 使用 cp -r 保持所有檔案屬性
cp -r /Users/<USER>/tagtoo/tagtoo-partners/apps/carrefour-offline-data /Users/<USER>/tagtoo/integrated-event/apps/
```

### 步驟 3: 驗證複製完整性

```bash
# 比較檔案數量
find /Users/<USER>/tagtoo/tagtoo-partners/apps/carrefour-offline-data -type f | wc -l
find /Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data -type f | wc -l

# 比較目錄結構
diff -r /Users/<USER>/tagtoo/tagtoo-partners/apps/carrefour-offline-data /Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data
```

## 🔧 移動後設定

### ✅ 環境重新設定

#### 1. 切換到新目錄

```bash
cd /Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data
```

#### 2. 重新啟動虛擬環境

```bash
# 啟動虛擬環境
source venv/bin/activate

# 驗證 Python 版本
python --version

# 驗證套件安裝
pip list
```

#### 3. 測試環境設定

```bash
# 執行環境檢查
python3 setup_environment.py
```

### ✅ 認證狀態確認

#### 1. 檢查 gcloud 配置

```bash
gcloud config configurations list
gcloud config configurations activate tagtoo-tracking
```

#### 2. 確認 Service Account Impersonation

```bash
gcloud auth application-default print-access-token --impersonate-service-account=<EMAIL>
```

#### 3. 測試 BigQuery 連線

```bash
python3 -c "
from schema_validator import SchemaValidator
validator = SchemaValidator()
print('認證測試:', validator.authenticate())
"
```

### ✅ 功能測試

#### 1. 模組導入測試

```bash
python3 -c "
print('🧪 測試所有模組導入...')
modules = [
    'setup_environment',
    'schema_validator',
    'copy_carrefour_data',
    'data_validator',
    'monitoring_logger',
    'run_full_pipeline'
]

for module in modules:
    try:
        __import__(module)
        print(f'✅ {module} 導入成功')
    except Exception as e:
        print(f'❌ {module} 導入失敗: {e}')
"
```

#### 2. Schema 驗證測試

```bash
python3 schema_validator.py
```

#### 3. 環境報告生成

```bash
python3 setup_environment.py
```

## 📝 Git 歷史建立

### ✅ Git 初始化（如果需要）

```bash
# 檢查是否已有 Git 倉庫
if [ ! -d ".git" ]; then
    git init
    echo "Git 倉庫已初始化"
fi
```

### ✅ 按計畫執行提交

按照 `GIT_COMMIT_PLAN.md` 中的順序執行 9 個提交：

```bash
# Commit 1: 專案初始化
git add README.md requirements.txt carrefour-validation-spec.md
git commit -m "[feat](carrefour): 初始化家樂福離線事件資料驗證專案..."

# Commit 2: 環境設定
git add setup_environment.py setup_auth.sh config.json set_env.sh
git commit -m "[feat](carrefour): 實作自動化環境設定和認證配置..."

# ... 繼續執行其他 7 個提交
```

## ✅ 移動後驗證

### 1. 檔案完整性確認

- [ ] 所有檔案都已正確複製
- [ ] 虛擬環境正常運作
- [ ] 所有模組可以正常導入

### 2. 功能完整性確認

- [ ] Schema 驗證功能正常
- [ ] 環境設定工具正常
- [ ] 認證狀態正確

### 3. 文檔完整性確認

- [ ] 所有操作文檔都存在
- [ ] 移動記錄檔案完整
- [ ] Git 提交計畫可執行

### 4. 權限狀態確認

- [ ] Service Account 認證正常
- [ ] BigQuery 連線正常
- [ ] 權限狀態與移動前一致

## 🚨 移動後的注意事項

### 1. 路徑相關

- ✅ **配置檔案**：使用相對路徑，移動後仍然有效
- ✅ **虛擬環境**：已包含在專案中，移動後可直接使用
- ✅ **認證檔案**：使用絕對路徑，不受移動影響

### 2. 權限狀態

- ⚠️ **BigQuery 權限**：仍然缺少 `bigquery.jobs.create`
- ✅ **Service Account**：配置不變
- ✅ **目標表格**：已建立，不受影響

### 3. 執行狀態

- ✅ **技術實作**：100% 完成
- ✅ **環境準備**：完全就緒
- ⏳ **等待權限**：仍需等待家樂福開通權限

## 📞 移動後的下一步

### 立即執行

1. **完成移動驗證**：確保所有功能正常
2. **建立 Git 歷史**：按計畫執行 9 個提交
3. **更新文檔**：記錄移動完成狀態

### 權限開通後

1. **執行完整流程**：

   ```bash
   source venv/bin/activate
   source set_env.sh
   python3 run_full_pipeline.py
   ```

2. **生成最終報告**：完成第 10 個 Git 提交

### 長期維護

1. **定期同步**：如有更新需求
2. **文檔維護**：保持文檔最新狀態
3. **權限管理**：定期檢查權限狀態

## ✅ 移動完成確認

移動完成後，請確認以下項目：

- [ ] **新位置**：`/Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data`
- [ ] **檔案完整**：所有檔案都已正確複製
- [ ] **環境正常**：虛擬環境和依賴都正常
- [ ] **認證有效**：Service Account 認證正常
- [ ] **功能測試**：所有模組都可以正常導入和執行
- [ ] **Git 歷史**：按計畫建立了完整的提交歷史
- [ ] **文檔完整**：所有開發記錄和操作文檔都保存完整

---

**檢查清單版本**：1.0
**建立時間**：2025-08-15
**適用範圍**：家樂福離線事件資料專案移動
**執行順序**：請按照清單順序逐項檢查和執行
