# Git Commit 計畫 - 家樂福離線事件資料專案

## 📋 提交策略概述

為了在專案移動後建立完整且有意義的 Git 歷史，我們將按照邏輯開發順序建立多個提交，每個提交代表一個完整的功能或里程碑。

## 🎯 提交順序和內容

### Commit 1: 專案初始化和基礎設定

```bash
git add README.md requirements.txt carrefour-validation-spec.md
git commit -m "[feat](carrefour): 初始化家樂福離線事件資料驗證專案

- 建立專案結構和說明文件
- 定義驗證規格和需求
- 設定 Python 依賴清單

專案目標：將 tw-eagle-prod.rmn_tagtoo.offline_transaction_day (15M+ 行)
複製到 tagtoo-tracking.event_prod.carrefour_offline_data 並進行完整驗證

資料規模：15,242,556 行，18.63 GB
驗證標準：四大類別驗證（權限、結構、內容、資料量）"
```

### Commit 2: 環境設定和認證配置

```bash
git add setup_environment.py setup_auth.sh config.json set_env.sh
git commit -m "[feat](carrefour): 實作自動化環境設定和認證配置

- 開發環境自動化設定工具 (setup_environment.py)
- 配置 Service Account Impersonation 認證
- 建立系統配置檔案和環境變數腳本
- 實作依賴檢查和權限驗證機制

技術特點：
- 自動安裝必要套件 (google-cloud-bigquery, pandas, numpy, tqdm)
- Service Account: <EMAIL>
- 完整的權限狀態檢查和報告生成"
```

### Commit 3: 目標表格建立和 Schema 驗證系統

```bash
git add create_carrefour_table.sql schema_validator.py
git commit -m "[feat](carrefour): 建立目標表格和 Schema 一致性驗證系統

- 建立目標表格 tagtoo-tracking.event_prod.carrefour_offline_data
- 完整複製來源表格 Schema（11 個欄位，包含複雜 STRUCT）
- 實作深度 Schema 比較工具 (schema_validator.py)
- 支援巢狀 STRUCT 結構驗證和自動修正 DDL 生成

Schema 驗證結果：✅ 100% 一致
複雜結構：items ARRAY<STRUCT<11 個商品相關欄位>>
驗證通過：所有欄位名稱、類型、mode 完全匹配"
```

### Commit 4: 智慧資料複製系統

```bash
git add copy_carrefour_data.py
git commit -m "[feat](carrefour): 實作智慧批次資料複製系統

- 開發高效能資料複製工具，支援 1500+ 萬筆資料處理
- 智慧批次策略：基於記憶體和效能自動調整（10K-50K 行/批次）
- 指數退避重試機制：3 次重試 + 智慧延遲
- 成本監控：每批次 dry-run 估算，確保 < $10 USD
- 即時進度追蹤：ETA 計算、吞吐量統計、斷點續傳支援

效能設計：
- 預期處理時間：1-2 小時
- 預期吞吐量：2,000-5,000 行/秒
- 預期成本：< $1 USD
- 目標成功率：> 99%"
```

### Commit 5: 多層次資料驗證系統

```bash
git add data_validator.py
git commit -m "[feat](carrefour): 實作四層次資料驗證系統

- Level 1: 基本統計比較（總行數、欄位數、資料大小）
- Level 2: 資料類型和 NULL 值分布驗證
- Level 3: 隨機抽樣 1000 筆進行逐欄位比對
- Level 4: 複雜 STRUCT 欄位（items 陣列）深度結構驗證

符合規格：完全符合 carrefour-validation-spec.md 四大驗證類別
- 權限驗證 (Permission Validation)
- 資料結構驗證 (Structure Validation)
- 資料內容驗證 (Content Validation)
- 資料量驗證 (Volume Validation)

驗證標準：隨機抽樣匹配率 > 95%，NULL 值分布誤差 < 1%"
```

### Commit 6: 監控日誌和通知系統

```bash
git add monitoring_logger.py
git commit -m "[feat](carrefour): 建立結構化監控日誌和通知系統

- 實作結構化日誌記錄（JSON 格式）
- 效能指標監控：吞吐量、成本、錯誤率追蹤
- 進度追蹤系統：即時進度、ETA 計算
- 通知系統：控制台 + 檔案雙重通知
- 操作監控：支援批次級別的詳細追蹤

監控特點：
- 包含 timestamp、operation_type、batch_id、status、metrics
- 支援指數退避重試監控
- 自動生成效能統計報告
- 全域監控實例，支援跨模組使用"
```

### Commit 7: 舊版驗證工具和檢查腳本

```bash
git add validate_carrefour_data.py validate_carrefour_data_v2.py validate_carrefour_data_v3.py check_bigquery_table.py
git commit -m "[feat](carrefour): 加入規格驗證工具和表格檢查腳本

- validate_carrefour_data_v3.py: 符合規格要求的完整驗證工具
- 支援四大驗證類別的詳細檢查和報告生成
- check_bigquery_table.py: 基礎表格狀態檢查工具
- 保留舊版驗證工具作為參考和備用

規格符合性：
- 完全符合 carrefour-validation-spec.md 要求
- 生成結構化驗證報告
- 包含權限、結構、內容、資料量四大類別驗證"
```

### Commit 8: 完整操作文件和流程標準化

```bash
git add OPERATIONS_MANUAL.md EXECUTION_CHECKLIST.md DEVELOPMENT.md data_copy_strategy.md permission_request.md
git commit -m "[docs](carrefour): 建立完整操作文件和流程標準化

- OPERATIONS_MANUAL.md: 詳細操作手冊（執行步驟、問題排除、緊急處理）
- EXECUTION_CHECKLIST.md: 權限開通後的執行檢查清單
- DEVELOPMENT.md: 開發狀態記錄和技術決策文檔
- data_copy_strategy.md: 技術策略和實作方案
- permission_request.md: BigQuery Job User 權限申請文件

文檔特點：
- 涵蓋從環境設定到最終報告的完整流程
- 包含常見問題排除和緊急處理程序
- 提供詳細的執行檢查清單和成功標準
- 記錄所有技術決策和實作細節"
```

### Commit 9: 整合執行腳本和最終優化

```bash
git add run_full_pipeline.py PROJECT_MIGRATION_RECORD.md GIT_COMMIT_PLAN.md
git commit -m "[feat](carrefour): 實作整合執行腳本和專案移動記錄

- run_full_pipeline.py: 一鍵執行完整流程的整合腳本
- 串聯所有步驟：環境檢查 → Schema 驗證 → 資料複製 → 多層次驗證 → 報告生成
- PROJECT_MIGRATION_RECORD.md: 完整的專案移動和開發歷程記錄
- GIT_COMMIT_PLAN.md: Git 提交策略和歷史重建計畫

整合特點：
- 5 個階段的完整流程自動化
- 詳細的進度追蹤和錯誤處理
- 自動生成最終摘要和建議
- 支援中斷恢復和狀態追蹤

專案狀態：技術實作 100% 完成，等待權限開通"
```

### Commit 10: 權限開通後的執行結果（未來）

```bash
# 權限開通並執行完成後
git add carrefour_validation_report_*.txt data_validation_report.* monitoring_report_*.json pipeline_summary_*.json
git commit -m "[feat](carrefour): 完成資料複製和驗證，生成最終報告

- 成功複製 15,242,556 行資料（18.63 GB）
- 四層次驗證全部通過
- 執行時間：X 小時 Y 分鐘
- 平均吞吐量：X,XXX 行/秒
- 總成本：$X.XX USD
- 成功率：XX.X%

驗證結果：
- Level 1: 基本統計 ✅
- Level 2: NULL 值分布 ✅
- Level 3: 隨機抽樣比對 ✅
- Level 4: STRUCT 結構驗證 ✅

交付成果：完整的資料複製 + 四層次驗證 + 詳細報告"
```

## 🔧 提交執行指令

移動專案後，按順序執行以下指令：

```bash
# 1. 初始化 Git（如果需要）
git init

# 2. 按順序執行上述 9 個提交
# （複製上面的 git add 和 git commit 指令）

# 3. 權限開通並執行完成後，執行第 10 個提交
```

## 📝 提交訊息格式說明

使用 Conventional Commits 格式：

- `[feat]`: 新功能
- `[docs]`: 文檔更新
- `(carrefour)`: 專案範圍標識

每個提交訊息包含：

1. **簡短標題**：描述主要變更
2. **詳細說明**：技術實作細節
3. **關鍵指標**：效能、成本、規模等數據
4. **驗證結果**：測試和驗證狀態

## 🎯 提交策略優勢

1. **邏輯順序**：按照實際開發順序建立歷史
2. **功能完整**：每個提交代表一個完整功能
3. **詳細記錄**：包含所有技術決策和實作細節
4. **易於追蹤**：清楚的里程碑和進度記錄
5. **便於回滾**：每個提交都是穩定狀態

---

**計畫建立時間**：2025-08-15
**適用範圍**：家樂福離線事件資料專案移動後的 Git 歷史重建
**執行時機**：專案移動到 integrated-event 後立即執行
