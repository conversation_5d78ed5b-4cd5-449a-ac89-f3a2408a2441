# 🏪 家樂福離線資料自動化流程修正分析報告

**分析日期：** 2025-09-03
**修正版本：** v2.0
**狀態：** 🚨 需要重新設計資料流程

---

## 🎯 執行摘要

根據最新的需求分析，發現目前的 Cloud Scheduler 自動化任務需要重大調整。實際的資料流程應該包含**資料複製**和**受眾媒合**兩個階段。

### 🔍 關鍵發現

- ❌ **目前流程不完整**：缺少資料複製階段
- ✅ **來源資料正常更新**：Daily 表格每日凌晨 2:00 更新
- ⚠️ **Monthly 表格需要建立**：目標表格不存在
- 📋 **需要兩套排程**：Daily 和 Monthly 不同更新頻率

---

## 📊 實際資料狀況分析

### 來源表格狀況 (tw-eagle-prod)

| 表格                        | 狀態    | 最新更新         | 資料量        | 更新頻率      |
| --------------------------- | ------- | ---------------- | ------------- | ------------- |
| `offline_transaction_day`   | ✅ 正常 | 2025-09-04 02:00 | 16,196,526 筆 | 每日凌晨 2:00 |
| `offline_transaction_month` | ⚠️ 較舊 | 2025-08-26 02:51 | 45,646,997 筆 | 每月 5 號     |

### 目標表格狀況 (tagtoo-tracking)

| 表格                                  | 狀態      | 最新更新         | 資料量        | 同步狀況 |
| ------------------------------------- | --------- | ---------------- | ------------- | -------- |
| `carrefour_offline_transaction_day`   | ⚠️ 落後   | 2025-09-04 05:55 | 15,855,981 筆 | 需要同步 |
| `carrefour_offline_transaction_month` | ❌ 不存在 | -                | -             | 需要建立 |

---

## 🔄 修正後的完整資料流程

### 階段 1：資料複製 (Data Replication)

**目標：** 將家樂福資料從 tw-eagle-prod 複製到 tagtoo-tracking

#### Daily 資料複製

- **觸發時間**：每日凌晨 2:30 (在來源更新後)
- **來源**：`tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
- **目標**：`tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
- **策略**：完全覆蓋 (WRITE_TRUNCATE)

#### Monthly 資料複製

- **觸發時間**：每月 5 號凌晨 3:00
- **來源**：`tw-eagle-prod.rmn_tagtoo.offline_transaction_month`
- **目標**：`tagtoo-tracking.event_prod.carrefour_offline_transaction_month`
- **策略**：完全覆蓋 (WRITE_TRUNCATE)

### 階段 2：受眾媒合 (Audience Matching)

**目標：** 基於複製後的資料生成受眾標籤

- **觸發時間**：每日凌晨 3:00 (在資料複製完成後)
- **資料來源**：`tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
- **輸出**：`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{date}`

---

## 🛠️ 實作計畫

### 立即行動項目

#### 1. 建立 Monthly 目標表格

```sql
CREATE TABLE `tagtoo-tracking.event_prod.carrefour_offline_transaction_month`
LIKE `tw-eagle-prod.rmn_tagtoo.offline_transaction_month`
OPTIONS (
  description='家樂福離線交易月度資料 - 從 tw-eagle-prod 複製',
  labels=[('source', 'carrefour'), ('type', 'offline_transaction'), ('frequency', 'monthly')]
);
```

#### 2. 建立資料複製 Cloud Function

**功能需求：**

- 支援 Daily 和 Monthly 兩種模式
- 自動檢測來源資料更新時間
- 完整的錯誤處理和重試機制
- 成本估算和控制

#### 3. 修正現有的受眾媒合邏輯

**調整項目：**

- 資料來源改為 `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
- 移除跨專案查詢，降低複雜度和成本
- 保持現有的 `tagtoo_entity` 優化邏輯

### 新的 Cloud Scheduler 配置

#### 資料複製排程

```yaml
Daily 複製:
  名稱: carrefour-data-replication-daily
  時間: 0 2 * * * (每日凌晨 2:30)
  參數: { "mode": "daily", "source_check": true }

Monthly 複製:
  名稱: carrefour-data-replication-monthly
  時間: 0 3 5 * * (每月 5 號凌晨 3:00)
  參數: { "mode": "monthly", "source_check": true }
```

#### 受眾媒合排程

```yaml
受眾媒合:
  名稱: carrefour-audience-matching
  時間: 0 3 * * * (每日凌晨 3:00)
  參數: { "execution_mode": "scheduled", "days_back": 1 }
```

---

## 📋 技術實作細節

### 資料複製邏輯

```python
def replicate_carrefour_data(mode: str = "daily"):
    """
    複製家樂福資料

    Args:
        mode: "daily" 或 "monthly"
    """
    if mode == "daily":
        source_table = "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
        target_table = "tagtoo-tracking.event_prod.carrefour_offline_transaction_day"
    else:
        source_table = "tw-eagle-prod.rmn_tagtoo.offline_transaction_month"
        target_table = "tagtoo-tracking.event_prod.carrefour_offline_transaction_month"

    # 1. 檢查來源資料更新時間
    source_modified = check_table_modified_time(source_table)
    target_modified = check_table_modified_time(target_table)

    if source_modified <= target_modified:
        logger.info("來源資料無更新，跳過複製")
        return

    # 2. 估算複製成本
    cost = estimate_copy_cost(source_table)
    if cost > MAX_COPY_COST:
        logger.error(f"複製成本過高: ${cost:.2f}")
        return

    # 3. 執行複製
    copy_job = client.copy_table(
        source_table,
        target_table,
        job_config=bigquery.CopyJobConfig(
            write_disposition="WRITE_TRUNCATE"
        )
    )

    copy_job.result()  # 等待完成
    logger.info(f"資料複製完成: {copy_job.destination.num_rows:,} 筆")
```

### 修正後的受眾媒合邏輯

```python
def entity_optimized_run_v2():
    """修正版受眾媒合 - 使用本地複製的資料"""

    # 使用本地資料，避免跨專案查詢
    config = {
        "source_tables": {
            "tagtoo_entity": "tagtoo-tracking.event_prod.tagtoo_entity",
            "carrefour_offline": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day"
        },
        "target_table": "tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{date}"
    }

    # 執行優化查詢
    query = f"""
    WITH user_mapping AS (
        SELECT DISTINCT permanent, mobile
        FROM `{config["source_tables"]["tagtoo_entity"]}`
        WHERE ec_id = 715 AND mobile IS NOT NULL
        AND latest_entity_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
    ),
    purchase_data AS (
        SELECT
            um.permanent,
            item.SUB_CLASS_KEY
        FROM user_mapping um
        JOIN `{config["source_tables"]["carrefour_offline"]}` off
            ON um.mobile = TO_HEX(off.ph_id)
        CROSS JOIN UNNEST(off.items) as item
        WHERE item.SUB_CLASS_KEY IS NOT NULL
    )
    SELECT
        permanent,
        ARRAY_TO_STRING(ARRAY_AGG(DISTINCT
            CONCAT('tm:c_715_pc_', SUB_CLASS_KEY)
        ), ',') as segment_id
    FROM purchase_data
    GROUP BY permanent
    """

    # 執行並寫入結果
    # ...
```

---

## 💰 成本與效能分析

### 預期改善

| 項目           | 修正前 | 修正後 | 改善     |
| -------------- | ------ | ------ | -------- |
| 跨專案查詢成本 | ~$0.20 | $0.01  | -95%     |
| 查詢複雜度     | 高     | 低     | 大幅簡化 |
| 執行時間       | 40 秒  | 20 秒  | -50%     |
| 資料一致性     | 中等   | 高     | 提升     |

### 額外成本

- **資料複製成本**：每日約 $0.05 USD
- **儲存成本**：約 80GB 額外儲存，每月約 $2 USD
- **總成本增加**：每月約 $3.5 USD

---

## 🚨 風險評估與緩解

### 主要風險

1. **資料延遲風險**：複製失敗導致受眾媒合使用舊資料
2. **儲存成本風險**：重複儲存增加成本
3. **同步複雜性**：需要維護兩套排程

### 緩解措施

1. **監控機制**：實時監控複製狀態和資料新鮮度
2. **自動重試**：複製失敗時自動重試機制
3. **成本控制**：設定複製成本上限和警報

---

## 📅 實作時程

### 第一週：基礎建設

- [ ] 建立 Monthly 目標表格
- [ ] 開發資料複製 Cloud Function
- [ ] 建立複製排程

### 第二週：整合測試

- [ ] 修正受眾媒合邏輯
- [ ] 端到端測試
- [ ] 效能調優

### 第三週：生產部署

- [ ] 生產環境部署
- [ ] 監控設定
- [ ] 文檔更新

---

**報告生成時間：** 2025-09-03 19:00:00
**修正原因：** 根據實際需求重新設計資料流程
**下一步：** 開始實作資料複製機制
