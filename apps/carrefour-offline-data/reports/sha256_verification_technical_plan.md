# SHA256 雜湊值驗證技術方案

> 生成日期: 2025-08-19
> 目的: 調查重疊率異常現象的真實原因
> 狀態: 技術方案規劃

## 🎯 問題陳述

**觀察到的異常現象**：

- 7天後重疊率未增加（停留在 2.379%）
- 線上 unique user.ph 數量在 7天後幾乎不再增加
- 不符合理論預期：隨查詢天數增加，應收集更多用戶

**需要驗證的假設**：

1. SHA256 雜湊匹配是否準確？
2. BigQuery 查詢邏輯是否正確？
3. 是否存在資料處理或過濾機制？

## 🔐 SHA256 逆向驗證策略

### 方法 1: 格式驗證與樣本比對 ⭐ **推薦**

**實施方案**：

```python
# 1. 格式一致性驗證
def verify_format_consistency():
    # 比較 TO_HEX(ph_id) 和 user.ph 的長度分佈
    # 檢查字符集是否一致（0-9, A-F）
    # 驗證格式轉換邏輯

# 2. 小樣本直接比對
def sample_verification(sample_size=100):
    # 取得離線樣本：TO_HEX(ph_id)
    # 取得線上樣本：user.ph
    # 計算樣本重疊率
    # 與整體重疊率比較
```

**優點**：

- 實施成本低
- 可快速驗證匹配邏輯
- 不需要逆向工程

**限制**：

- 無法驗證原始值
- 依賴樣本代表性

### 方法 2: 統計模式分析

**實施方案**：

```python
# 分析重疊率的統計模式
def statistical_pattern_analysis():
    # 檢查重疊率隨時間的變化模式
    # 分析是否符合預期的累積增長
    # 識別異常的平台期現象

# 用戶分佈分析
def user_distribution_analysis():
    # 分析 user.ph 在不同時間範圍的分佈
    # 檢查是否存在重複或過濾
    # 驗證累積邏輯
```

### 方法 3: 彩虹表方法 ⚠️ **理論方案**

**技術挑戰**：

- SHA256 是密碼學安全的雜湊函數
- 計算成本極高（2^256 可能性）
- 需要知道原始值的格式和範圍

**可行性評估**：

- **不推薦**：成本過高，實用性低
- **替代方案**：使用已知樣本進行驗證

## 🔍 線上資料完整性驗證

### 查詢邏輯驗證

**檢查項目**：

1. **累積查詢邏輯**：

   ```sql
   -- 驗證 7天 vs 14天 是否真的累積
   WITH users_7d AS (...), users_14d AS (...)
   SELECT additional_users_8_to_14d
   ```

2. **每日新增用戶分析**：

   ```sql
   -- 檢查每日新增用戶數量
   SELECT DATE(event_time), COUNT(DISTINCT user.ph)
   GROUP BY DATE(event_time)
   ```

3. **資料處理機制檢查**：
   - 是否存在去重邏輯？
   - 是否有資料過濾條件？
   - 是否有時間窗口限制？

### 資料品質檢查

**驗證項目**：

1. **時間範圍一致性**
2. **用戶 ID 格式一致性**
3. **資料完整性**
4. **查詢結果穩定性**

## 🔄 替代匹配策略

### 策略 1: 多重驗證機制

**實施方案**：

```python
def multi_verification_approach():
    # 1. 格式驗證
    format_match = verify_format_consistency()

    # 2. 樣本比對
    sample_match = sample_verification()

    # 3. 統計驗證
    statistical_match = statistical_analysis()

    # 綜合評估
    return combine_results(format_match, sample_match, statistical_match)
```

### 策略 2: 增量驗證

**實施方案**：

```python
def incremental_verification():
    # 逐步增加時間範圍
    for days in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]:
        overlap_rate = calculate_overlap(days)
        user_count = get_unique_users(days)
        # 分析增長模式
```

### 策略 3: 交叉驗證

**實施方案**：

```python
def cross_validation():
    # 使用不同的查詢方法
    # 比較結果一致性
    # 識別潛在問題
```

## 📋 實施計畫

### 階段 1: 立即執行（1-2天）

1. ✅ **格式驗證**：比對 TO_HEX(ph_id) 和 user.ph 格式
2. ✅ **樣本比對**：小樣本直接比對驗證
3. ✅ **查詢邏輯檢查**：驗證累積查詢是否正確

### 階段 2: 深度調查（3-5天）

1. **統計模式分析**：分析重疊率變化模式
2. **資料流追蹤**：追蹤資料處理流程
3. **替代查詢方法**：使用不同方法驗證結果

### 階段 3: 結論與建議（1天）

1. **綜合分析結果**
2. **確定異常原因**
3. **提供解決方案**

## 🎯 預期成果

### 成功指標

1. **確定異常原因**：找出重疊率平台期的真實原因
2. **驗證匹配準確性**：確認 SHA256 匹配是否正確
3. **提供解決方案**：給出具體的改進建議

### 風險評估

1. **技術風險**：低（使用已驗證的方法）
2. **成本風險**：低（控制在 $1 USD 以內）
3. **時間風險**：低（5-7天完成）

## 🔧 技術實施細節

### 工具和技術棧

- **BigQuery**：資料查詢和分析
- **Python**：資料處理和統計分析
- **統計方法**：相關性分析、趨勢分析
- **視覺化**：Chart.js 圖表展示

### 成本控制

- **查詢成本限制**：每次查詢 < $0.1 USD
- **總成本預算**：< $1.0 USD
- **成本監控**：每次查詢前進行成本估算

## 📊 報告和文檔

### 輸出文檔

1. **技術調查報告**：詳細的技術分析結果
2. **業務影響評估**：對業務決策的影響
3. **改進建議**：具體的解決方案和建議
4. **實施指南**：後續實施的詳細步驟

---

_此技術方案提供了系統性的方法來調查重疊率異常現象，重點關注實用性和成本效益_
