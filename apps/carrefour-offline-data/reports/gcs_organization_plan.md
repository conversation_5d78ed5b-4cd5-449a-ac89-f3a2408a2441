# GCS 報告資料結構整理計畫

> 生成日期: 2025-08-19
> 目標: 整理 `gs://integrated_event/carrefour_offline_data/reports/` 目錄

## 📂 當前檔案分析

### 檔案分類

#### ✅ 保留檔案 (高優先級)

1. **comprehensive_overlap_analysis.json** - 最新的全面重疊分析 (待上傳)
2. **ph_id_enhanced_interactive_report.json** (15.57 KB) - 增強版互動式報告
3. **project_completion_summary.md** (4.30 KB) - 專案完成總結
4. **index.html** (5.66 KB) - 主要索引頁面
5. **project_metadata.json** (922 bytes) - 專案元資料

#### 📦 歸檔檔案 (中優先級)

1. **ph_id_interactive_report_v2.json** (6.57 KB) - 舊版互動式報告
2. **ph_id_overlap_validation_report.json** (3.62 KB) - 初版重疊驗證
3. **real_overlap_analysis_report.json** (10.42 KB) - 真實重疊分析 (小樣本)
4. **ph_id_analysis_interactive_data.json** (5.29 KB) - 基礎分析資料
5. **schema_comparison_report.json** (874 bytes) - Schema 比較報告
6. **schema_comparison_report.md** (777 bytes) - Schema 比較文檔

#### 🗑️ 刪除檔案 (過時檔案)

1. **carrefour_data_analysis_20250818_181514.json** (1.11 MB) - 舊版分析 (有日期戳)
2. **carrefour_data_analysis_20250818_181514.md** (592 bytes) - 舊版分析文檔
3. **carrefour_data_analysis_dashboard.html** (18.78 KB) - 舊版儀表板
4. **carrefour_data_analysis_dashboard_v2.html** (24.87 KB) - 舊版儀表板 v2
5. **schema_analysis_20250818_183856.json** (10.27 KB) - 舊版 Schema 分析
6. **schema_validation_report_20250818_183856.json** (503 bytes) - 舊版驗證報告
7. **environment_setup_report.json** (627 bytes) - 環境設定報告
8. **environment_setup_report.md** (692 bytes) - 環境設定文檔

#### 📚 保留但移至 legacy/ 目錄

1. **README.md** (3.41 KB) - 舊版說明文檔
2. **ph_id_format_analysis_report.md** (6.55 KB) - 格式分析報告
3. **schema_contract_validation_report.md** (5.81 KB) - 合約驗證報告

## 🎯 建議的新目錄結構

```
gs://integrated_event/carrefour_offline_data/reports/
├── current/                                    # 當前使用的檔案
│   ├── comprehensive_overlap_analysis.json    # 最新全面分析
│   ├── ph_id_enhanced_interactive_report.json # 增強版報告
│   ├── project_completion_summary.md          # 專案總結
│   ├── index.html                             # 主要索引頁
│   └── project_metadata.json                  # 專案元資料
├── archive/                                   # 歸檔的舊版本
│   ├── ph_id_interactive_report_v2.json
│   ├── ph_id_overlap_validation_report.json
│   ├── real_overlap_analysis_report.json
│   ├── ph_id_analysis_interactive_data.json
│   ├── schema_comparison_report.json
│   └── schema_comparison_report.md
└── legacy/                                    # 舊版文檔
    ├── README.md
    ├── ph_id_format_analysis_report.md
    └── schema_contract_validation_report.md
```

## 📋 執行步驟

### 步驟 1: 上傳最新檔案

```bash
# 上傳最新的全面分析報告
gsutil cp reports/comprehensive_overlap_analysis.json gs://integrated_event/carrefour_offline_data/reports/current/

# 上傳新的自包含 HTML 檔案 (待建立)
gsutil cp reports/self_contained_report.html gs://integrated_event/carrefour_offline_data/reports/current/
```

### 步驟 2: 建立目錄結構

```bash
# 建立新目錄
gsutil -m mkdir gs://integrated_event/carrefour_offline_data/reports/current/
gsutil -m mkdir gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil -m mkdir gs://integrated_event/carrefour_offline_data/reports/legacy/
```

### 步驟 3: 移動重要檔案到 current/

```bash
gsutil mv gs://integrated_event/carrefour_offline_data/reports/ph_id_enhanced_interactive_report.json gs://integrated_event/carrefour_offline_data/reports/current/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/project_completion_summary.md gs://integrated_event/carrefour_offline_data/reports/current/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/index.html gs://integrated_event/carrefour_offline_data/reports/current/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/project_metadata.json gs://integrated_event/carrefour_offline_data/reports/current/
```

### 步驟 4: 歸檔舊版本到 archive/

```bash
gsutil mv gs://integrated_event/carrefour_offline_data/reports/ph_id_interactive_report_v2.json gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/ph_id_overlap_validation_report.json gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/real_overlap_analysis_report.json gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/ph_id_analysis_interactive_data.json gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/schema_comparison_report.json gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/schema_comparison_report.md gs://integrated_event/carrefour_offline_data/reports/archive/
```

### 步驟 5: 移動文檔到 legacy/

```bash
gsutil mv gs://integrated_event/carrefour_offline_data/reports/README.md gs://integrated_event/carrefour_offline_data/reports/legacy/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/ph_id_format_analysis_report.md gs://integrated_event/carrefour_offline_data/reports/legacy/
gsutil mv gs://integrated_event/carrefour_offline_data/reports/schema_contract_validation_report.md gs://integrated_event/carrefour_offline_data/reports/legacy/
```

### 步驟 6: 刪除過時檔案

```bash
# 刪除有日期戳的舊檔案
gsutil rm gs://integrated_event/carrefour_offline_data/reports/carrefour_data_analysis_20250818_181514.json
gsutil rm gs://integrated_event/carrefour_offline_data/reports/carrefour_data_analysis_20250818_181514.md
gsutil rm gs://integrated_event/carrefour_offline_data/reports/carrefour_data_analysis_dashboard.html
gsutil rm gs://integrated_event/carrefour_offline_data/reports/carrefour_data_analysis_dashboard_v2.html
gsutil rm gs://integrated_event/carrefour_offline_data/reports/schema_analysis_20250818_183856.json
gsutil rm gs://integrated_event/carrefour_offline_data/reports/schema_validation_report_20250818_183856.json
gsutil rm gs://integrated_event/carrefour_offline_data/reports/environment_setup_report.json
gsutil rm gs://integrated_event/carrefour_offline_data/reports/environment_setup_report.md
```

## 📊 預期結果

### 空間節省

- **刪除檔案總大小**: ~1.2 MB
- **整理後結構**: 更清晰的檔案組織
- **維護性**: 更容易找到和管理檔案

### 檔案優先級

1. **current/**: 4-5 個最重要的檔案
2. **archive/**: 6 個歸檔的舊版本
3. **legacy/**: 3 個舊版文檔

### 存取路徑

- **主要報告**: `gs://integrated_event/carrefour_offline_data/reports/current/`
- **歷史版本**: `gs://integrated_event/carrefour_offline_data/reports/archive/`
- **參考文檔**: `gs://integrated_event/carrefour_offline_data/reports/legacy/`

## ✅ 執行確認

執行完成後，使用以下命令驗證結構：

```bash
gsutil ls -la gs://integrated_event/carrefour_offline_data/reports/current/
gsutil ls -la gs://integrated_event/carrefour_offline_data/reports/archive/
gsutil ls -la gs://integrated_event/carrefour_offline_data/reports/legacy/
```

---

_整理計畫生成時間: 2025-08-19_
_預計執行時間: 10-15 分鐘_
