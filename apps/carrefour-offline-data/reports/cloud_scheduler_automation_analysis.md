# 🏪 家樂福離線資料 Cloud Scheduler 自動化任務分析報告

**分析日期：** 2025-09-04
**分析人員：** AI Assistant
**目標受眾：** 技術團隊成員

---

## 📋 執行摘要

本報告詳細分析家樂福離線資料專案的 Cloud Scheduler 自動化任務，包含完整的工作流程、資料處理邏輯、以及技術實作細節。

### 🎯 核心發現

- ✅ **每日凌晨 2:00 自動執行**：使用台灣時區 (Asia/Taipei)
- ✅ **高效能資料處理**：採用 `tagtoo_entity` 表格優化查詢
- ✅ **成本控制優秀**：單次執行成本約 $0.01-0.20 USD
- ✅ **執行時間短**：約 40 秒完成整個流程
- ⚠️ **單一實例限制**：最大實例數設為 1，避免並行衝突

---

## 🔧 Cloud Scheduler 配置詳情

### 基本配置

| 項目         | 設定值                                 | 說明                    |
| ------------ | -------------------------------------- | ----------------------- |
| **任務名稱** | `carrefour-offline-data-prod-schedule` | 生產環境排程任務        |
| **執行時間** | `0 2 * * *`                            | 每日台灣時間凌晨 2:00   |
| **時區**     | `Asia/Taipei`                          | 台灣時區                |
| **觸發方式** | HTTP POST                              | 直接呼叫 Cloud Function |
| **重試機制** | 最多 3 次                              | 失敗時自動重試          |

### 觸發參數

```json
{
  "execution_mode": "scheduled",
  "days_back": 1,
  "dry_run": false,
  "environment": "prod"
}
```

### 認證機制

- **OIDC Token 認證**：使用基礎設施 Service Account
- **目標 Service Account**：`<EMAIL>`
- **權限範圍**：BigQuery 讀取、Cloud Function 執行

---

## 🚀 主要執行腳本工作流程

### 1. 入口點：`deployment/main.py`

**函數：** `main(request)` → `execute_audience_matching(params)`

**主要職責：**

- 解析 HTTP 請求參數
- 初始化 `CarrefourAudienceMatching` 系統
- 執行完整的受眾媒合流程
- 回傳執行結果和統計資訊

### 2. 核心執行：`main.py`

**函數：** `entity_optimized_run()`

**執行模式：** 使用 `tagtoo_entity` 表格的超級優化版

**關鍵特點：**

- 跳過傳統的用戶對應表建立步驟
- 使用高效能 SQL 查詢一次完成所有處理
- BigQuery 內部處理所有 JOIN 和聚合邏輯

---

## 📊 資料表格分析

### 主要資料來源

#### 1. `tagtoo_entity` 表格

- **專案：** `tagtoo-tracking.event_prod.tagtoo_entity`
- **用途：** 用戶身份對應關係
- **關鍵欄位：**
  - `permanent`：永久用戶識別碼
  - `mobile`：手機號碼 (對應 `TO_HEX(ph_id)`)
  - `ec_id`：電商識別碼 (715 = 家樂福)
  - `latest_entity_time`：最新活動時間

#### 2. `offline_transaction_day` 表格

- **專案：** `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
- **用途：** 家樂福離線交易資料
- **關鍵欄位：**
  - `ph_id`：用戶手機號碼 (BYTES 格式)
  - `items`：購買商品陣列
  - `event_times`：交易時間戳記

#### 3. 目標表格

- **專案：** `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{YYYYMMDD}`
- **用途：** ML 工作流程的受眾標籤資料
- **Schema：**
  - `permanent`：用戶識別碼
  - `segment_id`：階層式受眾標籤 (逗號分隔)
  - `created_at`：建立時間
  - `source_type`：資料來源類型
  - `execution_id`：執行識別碼

---

## 🔗 資料比對機制詳解

### JOIN 條件

```sql
-- 核心比對邏輯
FROM `tagtoo-tracking.event_prod.tagtoo_entity` entity
JOIN `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` offline
  ON entity.mobile = TO_HEX(offline.ph_id)
```

### 比對欄位說明

| 表格                      | 欄位            | 格式   | 說明                     |
| ------------------------- | --------------- | ------ | ------------------------ |
| `tagtoo_entity`           | `mobile`        | STRING | 16進位字串               |
| `offline_transaction_day` | `ph_id`         | BYTES  | 二進位格式               |
| **轉換函數**              | `TO_HEX(ph_id)` | STRING | 將 BYTES 轉為 16進位字串 |

### 篩選條件

1. **時間範圍**：`latest_entity_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)`
2. **電商識別**：`ec_id = 715` (家樂福)
3. **資料完整性**：
   - `mobile IS NOT NULL`
   - `ph_id IS NOT NULL`
   - `SUB_CLASS_KEY IS NOT NULL AND SUB_CLASS_KEY != ''`

---

## 🏷️ 受眾標籤生成邏輯

### 階層式標籤系統

基於商品的 `SUB_CLASS_KEY` 生成三層標籤：

```sql
-- 小分類標籤 (完整代碼)
CONCAT('tm:c_715_pc_', SUB_CLASS_KEY) as segment_id

-- 中分類標籤 (前4位)
CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 4)) as segment_id

-- 大分類標籤 (前3位)
CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 3)) as segment_id
```

### 標籤範例

假設 `SUB_CLASS_KEY = "10012"`：

- 小分類：`tm:c_715_pc_10012`
- 中分類：`tm:c_715_pc_1001`
- 大分類：`tm:c_715_pc_100`

### 聚合處理

```sql
-- 用戶層級聚合
ARRAY_AGG(DISTINCT segment_id) as segment_ids

-- 最終輸出格式
ARRAY_TO_STRING(ARRAY(SELECT * FROM UNNEST(segment_ids) ORDER BY 1), ',') as segment_id
```

---

## ⚡ 效能優化策略

### 1. 查詢優化

**傳統方法 vs 優化方法：**

| 方法     | 查詢次數   | 預估成本   | 執行時間 |
| -------- | ---------- | ---------- | -------- |
| 傳統方法 | 1 + N 批次 | ~$2.15 USD | ~20 分鐘 |
| 優化方法 | 1 次       | ~$0.01 USD | ~40 秒   |

### 2. 技術優勢

- **單一 SQL 查詢**：避免多次網路往返
- **BigQuery 內部處理**：充分利用分散式計算
- **欄位選擇優化**：只選取必要欄位
- **時間範圍限制**：預設回溯 1 天，減少掃描量

### 3. 成本控制

```python
# 成本估算機制
cost = self._estimate_query_cost(query)
if cost > self.config["max_cost_usd"]:
    logger.error(f"查詢成本 ${cost:.4f} 超過限制")
    return []
```

---

## 🔄 完整資料處理流程

### 階段 1：身份對應建立

```sql
SELECT DISTINCT permanent, mobile
FROM `tagtoo-tracking.event_prod.tagtoo_entity`
WHERE ec_id = 715
  AND mobile IS NOT NULL
  AND latest_entity_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
```

### 階段 2：離線資料查詢

```sql
SELECT TO_HEX(ph_id) as mobile, item.SUB_CLASS_KEY
FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
CROSS JOIN UNNEST(items) as item
WHERE TO_HEX(ph_id) IN (SELECT mobile FROM stage1_results)
  AND item.SUB_CLASS_KEY IS NOT NULL
```

### 階段 3：標籤生成與聚合

```python
# Python 層級處理
for purchase in purchases:
    permanent = purchase["permanent"]
    sub_class_key = purchase["SUB_CLASS_KEY"]

    # 生成階層式標籤
    segments = [
        f"tm:c_715_pc_{sub_class_key}",           # 小分類
        f"tm:c_715_pc_{sub_class_key[:4]}",      # 中分類
        f"tm:c_715_pc_{sub_class_key[:3]}"       # 大分類
    ]

    user_segments[permanent].update(segments)
```

### 階段 4：目標表格寫入

```python
# 格式化並寫入
audience_data.append({
    "permanent": permanent,
    "segment_id": ",".join(sorted(segments)),
    "created_at": datetime.now(timezone.utc),
    "source_type": "carrefour_offline_purchase",
    "source_entity": "carrefour_audience_matching_system",
    "execution_id": execution_id
})
```

---

## 📈 監控與警報

### 效能監控指標

| 指標         | 閾值       | 警報級別 |
| ------------ | ---------- | -------- |
| 執行時間     | > 300 秒   | WARNING  |
| 記憶體使用   | > 90%      | WARNING  |
| 錯誤率       | > 5%       | CRITICAL |
| 日成本       | > $1.0 USD | WARNING  |
| 單次查詢成本 | > $0.5 USD | WARNING  |

### 通知設定

- **接收者**：<EMAIL>
- **通知頻道**：Email + Cloud Logging
- **重試機制**：最多 3 次，間隔 5 分鐘

---

## 🛠️ 技術架構總結

### Cloud Function 配置

```yaml
運行時：Python 3.11
記憶體：1024Mi
CPU：1 vCPU
超時：600 秒 (10 分鐘)
最大實例：1 (避免並行衝突)
最小實例：0 (成本優化)
```

### 環境變數

```bash
ENVIRONMENT=prod
PROJECT_ID=tagtoo-tracking
TARGET_PROJECT=tagtoo-ml-workflow
EC_ID=715
MAX_COST_USD=1.0
```

### 部署架構

```
Cloud Scheduler → Cloud Function → BigQuery → ML Workflow
     ↓              ↓               ↓           ↓
  每日 2:00      受眾媒合邏輯      資料查詢     標籤應用
```

---

## 🔄 完整資料處理流程圖

![家樂福離線資料 Cloud Scheduler 自動化流程](mermaid_diagram_rendered)

---

## 💡 業務邏輯與技術實作關聯

### 業務目標

1. **用戶行為追蹤**：將家樂福線下購買行為與線上用戶身份連結
2. **精準行銷**：基於購買商品類別生成受眾標籤
3. **自動化處理**：每日自動更新受眾標籤，支援即時行銷活動

### 技術實作對應

| 業務需求 | 技術實作                 | 關鍵技術點                 |
| -------- | ------------------------ | -------------------------- |
| 身份連結 | `mobile = TO_HEX(ph_id)` | 16進位轉換確保格式一致     |
| 商品分類 | 階層式標籤系統           | SUB_CLASS_KEY 的多層次切分 |
| 即時更新 | Cloud Scheduler 每日觸發 | 台灣時區凌晨 2:00 執行     |
| 成本控制 | 查詢成本估算             | 預估超過 $1 USD 則停止執行 |
| 效能優化 | 單一 SQL 查詢            | BigQuery 內部分散式處理    |

---

## 🚨 關鍵技術風險與緩解措施

### 1. 資料一致性風險

**風險：** `tagtoo_entity` 與 `offline_transaction_day` 時間不同步

**緩解措施：**

- 使用 1 天回溯期確保資料覆蓋
- 在 SQL 中加入時間範圍檢查
- 監控每日處理的用戶數量變化

### 2. 成本控制風險

**風險：** BigQuery 查詢成本意外飆升

**緩解措施：**

```python
# 多層次成本控制
cost = self._estimate_query_cost(query)
if cost > self.config["max_cost_usd"]:
    logger.error(f"查詢成本 ${cost:.4f} 超過限制")
    return []
```

### 3. 並行執行風險

**風險：** 多個實例同時執行造成資料重複

**緩解措施：**

- Cloud Function 最大實例數設為 1
- 使用 `execution_id` 追蹤每次執行
- 目標表格按日期分區避免衝突

---

## 📋 運維操作指南

### 手動觸發執行

```bash
# 手動觸發 Cloud Scheduler
gcloud scheduler jobs run carrefour-offline-data-prod-schedule \
  --location=asia-east1

# 直接呼叫 Cloud Function
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  -H "Content-Type: application/json" \
  -d '{"execution_mode":"manual","days_back":1,"dry_run":false}' \
  https://carrefour-offline-data-prod-shiddvur4q-de.a.run.app/
```

### 監控檢查

```bash
# 檢查 Cloud Scheduler 狀態
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1

# 檢查 Cloud Function 日誌
gcloud logging read 'resource.type="cloud_function" AND
  resource.labels.function_name="carrefour-offline-data-prod"' \
  --limit=10 --format="table(timestamp,severity,textPayload)"

# 檢查 BigQuery 作業
bq ls -j --max_results=10 --format=prettyjson
```

### 故障排除

1. **執行失敗**：檢查 Cloud Function 日誌和 BigQuery 權限
2. **成本超標**：檢查資料量增長和查詢優化
3. **時間超時**：檢查 BigQuery 查詢效能和網路狀況

---

## 🔮 未來發展規劃

### 短期優化 (1-3 個月)

1. **監控儀表板**：建立 Grafana 監控面板
2. **警報優化**：細化警報閾值和通知機制
3. **成本分析**：建立詳細的成本追蹤報告

### 中期發展 (3-6 個月)

1. **多品牌支援**：擴展到其他零售品牌
2. **即時處理**：從每日批次改為準即時處理
3. **機器學習整合**：加入預測性受眾標籤

### 長期願景 (6-12 個月)

1. **全渠道整合**：整合線上線下所有觸點資料
2. **自動化優化**：AI 驅動的查詢和成本優化
3. **企業級部署**：支援多租戶和企業級 SLA

---

**報告生成時間：** 2025-09-03 18:30:00
**資料來源：** Terraform 配置、Python 原始碼、部署文檔
**技術棧：** Cloud Scheduler + Cloud Functions + BigQuery + Python
**版本：** v1.0 - 完整技術分析版
