#!/usr/bin/env python3
"""
家樂福離線事件資料 BigQuery 表格基本資訊檢查腳本

使用 <EMAIL> 權限
只檢查表格基本資訊，避免大量查詢產生費用

Author: AI Assistant
Date: 2025-08-15
"""

import os
import sys
from datetime import datetime
from typing import Dict, Any

# Google Cloud 相關
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

class CarrefourTableChecker:
    """家樂福離線事件資料表格檢查器"""

    def __init__(self, project_id: str = "tw-eagle-prod"):
        self.project_id = project_id
        self.dataset_id = "rmn_tagtoo"
        self.table_id = "offline_transaction_day"
        self.client = None

    def authenticate(self) -> bool:
        """使用預設認證進行身份驗證"""
        try:
            # 嘗試使用預設認證
            credentials, project = default()
            print(f"✅ 認證成功！使用專案: {project}")

            # 初始化 BigQuery 客戶端
            self.client = bigquery.Client(
                project=self.project_id,
                credentials=credentials
            )

            return True

        except DefaultCredentialsError as e:
            print(f"❌ 認證失敗: {e}")
            print("請確保已設定 GOOGLE_APPLICATION_CREDENTIALS 環境變數")
            return False
        except Exception as e:
            print(f"❌ 初始化客戶端失敗: {e}")
            return False

    def check_table_exists(self) -> bool:
        """檢查表格是否存在"""
        try:
            table_ref = self.client.dataset(self.dataset_id).table(self.table_id)
            table = self.client.get_table(table_ref)
            print(f"✅ 表格存在: {table.table_id}")
            return True
        except Exception as e:
            print(f"❌ 表格不存在或無法存取: {e}")
            return False

    def get_table_info(self) -> Dict[str, Any]:
        """獲取表格基本資訊（不執行查詢）"""
        try:
            table_ref = self.client.dataset(self.dataset_id).table(self.table_id)
            table = self.client.get_table(table_ref)

            table_info = {
                'table_id': table.table_id,
                'dataset_id': table.dataset_id,
                'project_id': table.project,
                'created': table.created.isoformat() if table.created else None,
                'modified': table.modified.isoformat() if table.modified else None,
                'num_rows': table.num_rows,
                'num_bytes': table.num_bytes,
                'schema': []
            }

            # 獲取 Schema 資訊
            for field in table.schema:
                table_info['schema'].append({
                    'name': field.name,
                    'type': field.field_type,
                    'mode': field.mode,
                    'description': field.description
                })

            return table_info

        except Exception as e:
            print(f"❌ 獲取表格資訊失敗: {e}")
            return {}

    def check_dataset_info(self) -> Dict[str, Any]:
        """檢查資料集資訊"""
        try:
            dataset_ref = self.client.dataset(self.dataset_id)
            dataset = self.client.get_dataset(dataset_ref)

            dataset_info = {
                'dataset_id': dataset.dataset_id,
                'project_id': dataset.project,
                'created': dataset.created.isoformat() if dataset.created else None,
                'modified': dataset.modified.isoformat() if dataset.modified else None,
                'description': dataset.description,
                'labels': dataset.labels
            }

            return dataset_info

        except Exception as e:
            print(f"❌ 獲取資料集資訊失敗: {e}")
            return {}

    def generate_basic_report(self) -> str:
        """生成基本檢查報告"""
        report = []
        report.append("=" * 80)
        report.append("家樂福離線事件資料 BigQuery 表格基本資訊檢查報告")
        report.append("=" * 80)
        report.append(f"檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"專案 ID: {self.project_id}")
        report.append(f"資料集: {self.dataset_id}")
        report.append(f"表格: {self.table_id}")
        report.append("")

        # 檢查表格是否存在
        report.append("1. 表格存在性檢查")
        report.append("-" * 40)
        if self.check_table_exists():
            report.append("✅ 表格存在且可存取")
        else:
            report.append("❌ 表格不存在或無法存取")
            report.append("")
            report.append("=" * 80)
            return "\n".join(report)
        report.append("")

        # 表格基本資訊
        report.append("2. 表格基本資訊")
        report.append("-" * 40)
        table_info = self.get_table_info()
        if table_info:
            report.append(f"表格 ID: {table_info.get('table_id', 'N/A')}")
            report.append(f"建立時間: {table_info.get('created', 'N/A')}")
            report.append(f"修改時間: {table_info.get('modified', 'N/A')}")
            report.append(f"總行數: {table_info.get('num_rows', 'N/A'):,}")
            report.append(f"總大小: {table_info.get('num_bytes', 'N/A'):,} bytes")
            report.append("")
            report.append("欄位結構:")
            for field in table_info.get('schema', []):
                mode = f" ({field['mode']})" if field['mode'] != 'NULLABLE' else ""
                report.append(f"  - {field['name']}: {field['type']}{mode}")
                if field.get('description'):
                    report.append(f"    說明: {field['description']}")
        report.append("")

        # 資料集資訊
        report.append("3. 資料集資訊")
        report.append("-" * 40)
        dataset_info = self.check_dataset_info()
        if dataset_info:
            report.append(f"資料集 ID: {dataset_info.get('dataset_id', 'N/A')}")
            report.append(f"專案 ID: {dataset_info.get('project_id', 'N/A')}")
            report.append(f"建立時間: {dataset_info.get('created', 'N/A')}")
            report.append(f"修改時間: {dataset_info.get('modified', 'N/A')}")
            if dataset_info.get('description'):
                report.append(f"描述: {dataset_info['description']}")
            if dataset_info.get('labels'):
                report.append(f"標籤: {dataset_info['labels']}")
        report.append("")

        # 建議
        report.append("4. 建議")
        report.append("-" * 40)
        if table_info and table_info.get('num_rows', 0) > 0:
            report.append("✅ 表格存在且有資料，可以進行進一步的資料分析")
            report.append("⚠️  注意：進行大量查詢前請先使用 dry_run 估算成本")
        elif table_info:
            report.append("⚠️  表格存在但沒有資料，需要檢查資料來源")
        else:
            report.append("❌ 無法獲取表格資訊，需要檢查權限設定")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)

def main():
    """主函數"""
    print("🚀 開始家樂福離線事件資料 BigQuery 表格基本資訊檢查...")
    print("=" * 70)

    # 建立檢查器
    checker = CarrefourTableChecker()

    # 進行身份驗證
    if not checker.authenticate():
        print("❌ 身份驗證失敗，程式結束")
        sys.exit(1)

    print("✅ 身份驗證成功")
    print("")

    # 生成報告
    report = checker.generate_basic_report()
    print(report)

    # 儲存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"carrefour_bigquery_basic_check_{timestamp}.txt"

    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 報告已儲存至: {report_filename}")
    except Exception as e:
        print(f"⚠️  報告儲存失敗: {e}")

if __name__ == "__main__":
    main()
