#!/usr/bin/env python3
"""
家樂福離線交易資料更新狀況檢查腳本

檢查以下兩個資料表的最新資料狀況：
- tw-eagle-prod.rmn_tagtoo.offline_transaction_day
- tw-eagle-prod.rmn_tagtoo.offline_transaction_month

使用 <EMAIL> 權限
遵循成本控制規範，使用 --dry_run 估算查詢成本

Author: AI Assistant
Date: 2025-09-03
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

class CarrefourDataUpdateChecker:
    """家樂福離線交易資料更新狀況檢查器"""

    def __init__(self):
        self.project_id = "tw-eagle-prod"
        self.dataset_id = "rmn_tagtoo"
        self.tables = {
            "daily": "offline_transaction_day",
            "monthly": "offline_transaction_month"
        }
        self.service_account = "<EMAIL>"
        self.client = None
        self.results = {}

    def authenticate(self) -> bool:
        """進行身份驗證"""
        try:
            # 使用 Application Default Credentials，但指定計費專案為 tagtoo-tracking
            credentials, project = default()
            # 使用 tagtoo-tracking 作為計費專案來查詢 tw-eagle-prod 的資料
            self.client = bigquery.Client(project="tagtoo-tracking", credentials=credentials)

            print(f"✅ 身份驗證成功")
            print(f"📋 目標專案ID: {self.project_id}")
            print(f"💰 計費專案ID: tagtoo-tracking")
            print(f"🔐 Service Account: {self.service_account}")
            return True

        except DefaultCredentialsError as e:
            print(f"❌ 身份驗證失敗: {e}")
            print("請先執行: source config/setup_auth.sh")
            return False
        except Exception as e:
            print(f"❌ 身份驗證錯誤: {e}")
            return False

    def estimate_query_cost(self, query: str) -> Tuple[bool, float, str]:
        """估算查詢成本"""
        try:
            # 使用 dry_run 估算查詢成本
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            query_job = self.client.query(query, job_config=job_config)

            # 計算預估成本 (BigQuery 定價: $5 per TB)
            bytes_processed = query_job.total_bytes_processed
            tb_processed = bytes_processed / (1024**4)  # 轉換為 TB
            estimated_cost = tb_processed * 5  # $5 per TB

            cost_info = f"預估處理資料量: {bytes_processed:,} bytes ({tb_processed:.6f} TB), 預估成本: ${estimated_cost:.4f} USD"

            # 檢查是否超過成本限制
            if estimated_cost > 10:
                return False, estimated_cost, f"⚠️ 查詢成本過高: {cost_info}"
            else:
                return True, estimated_cost, f"✅ 查詢成本合理: {cost_info}"

        except Exception as e:
            return False, 0, f"❌ 成本估算失敗: {e}"

    def check_table_basic_info(self, table_type: str) -> Dict[str, Any]:
        """檢查資料表基本資訊"""
        table_name = self.tables[table_type]
        full_table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"

        print(f"\n🔍 檢查 {table_type} 資料表: {full_table_id}")
        print("-" * 60)

        result = {
            "table_name": full_table_id,
            "exists": False,
            "row_count": 0,
            "date_range": {},
            "cost_info": {},
            "error": None
        }

        try:
            # 檢查表格是否存在
            table = self.client.get_table(full_table_id)
            result["exists"] = True
            print(f"✅ 資料表存在")
            print(f"📊 Schema 欄位數: {len(table.schema)}")

            # 準備查詢語句 - 獲取基本統計資訊，使用 event_times 欄位
            query = f"""
            SELECT
                COUNT(*) as total_rows,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date,
                COUNT(DISTINCT DATE(TIMESTAMP_SECONDS(event_times))) as unique_dates
            FROM `{full_table_id}`
            WHERE event_times IS NOT NULL
            """

            # 估算查詢成本
            can_proceed, cost, cost_msg = self.estimate_query_cost(query)
            result["cost_info"] = {
                "estimated_cost": cost,
                "message": cost_msg,
                "can_proceed": can_proceed
            }

            print(cost_msg)

            if not can_proceed:
                print("❌ 查詢成本過高，跳過資料查詢")
                return result

            # 執行查詢
            print("🔄 執行資料查詢...")
            query_job = self.client.query(query)
            rows = list(query_job.result())

            if rows:
                row = rows[0]
                result["row_count"] = row.total_rows
                result["date_range"] = {
                    "earliest": row.earliest_date.strftime("%Y-%m-%d") if row.earliest_date else None,
                    "latest": row.latest_date.strftime("%Y-%m-%d") if row.latest_date else None,
                    "unique_dates": row.unique_dates
                }

                print(f"📈 總筆數: {row.total_rows:,}")
                print(f"📅 資料時間範圍: {result['date_range']['earliest']} 至 {result['date_range']['latest']}")
                print(f"📊 唯一日期數: {row.unique_dates}")

                # 計算資料涵蓋天數
                if row.earliest_date and row.latest_date:
                    days_span = (row.latest_date - row.earliest_date).days + 1
                    print(f"⏱️ 資料涵蓋天數: {days_span} 天")
                    result["date_range"]["days_span"] = days_span

        except Exception as e:
            result["error"] = str(e)
            print(f"❌ 檢查失敗: {e}")

        return result

    def check_recent_updates(self, table_type: str) -> Dict[str, Any]:
        """檢查最近的資料更新"""
        table_name = self.tables[table_type]
        full_table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"

        print(f"\n🕐 檢查 {table_type} 資料表最近更新...")

        result = {
            "recent_dates": [],
            "last_7_days_count": 0,
            "last_30_days_count": 0,
            "error": None
        }

        try:
            # 查詢最近 30 天的資料，使用 event_times 欄位
            query = f"""
            SELECT
                DATE(TIMESTAMP_SECONDS(event_times)) as date,
                COUNT(*) as daily_count
            FROM `{full_table_id}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                AND event_times IS NOT NULL
            GROUP BY DATE(TIMESTAMP_SECONDS(event_times))
            ORDER BY DATE(TIMESTAMP_SECONDS(event_times)) DESC
            LIMIT 30
            """

            # 估算查詢成本
            can_proceed, cost, cost_msg = self.estimate_query_cost(query)
            print(cost_msg)

            if not can_proceed:
                print("❌ 查詢成本過高，跳過最近更新檢查")
                return result

            # 執行查詢
            query_job = self.client.query(query)
            rows = list(query_job.result())

            recent_dates = []
            last_7_days = 0
            last_30_days = 0
            today = datetime.now().date()

            for row in rows:
                date_str = row.date.strftime("%Y-%m-%d")
                count = row.daily_count
                recent_dates.append({"date": date_str, "count": count})

                days_ago = (today - row.date).days
                if days_ago <= 7:
                    last_7_days += count
                last_30_days += count

            result["recent_dates"] = recent_dates
            result["last_7_days_count"] = last_7_days
            result["last_30_days_count"] = last_30_days

            print(f"📊 最近 7 天資料筆數: {last_7_days:,}")
            print(f"📊 最近 30 天資料筆數: {last_30_days:,}")
            print(f"📅 最近資料日期: {recent_dates[0]['date'] if recent_dates else '無資料'}")

        except Exception as e:
            result["error"] = str(e)
            print(f"❌ 最近更新檢查失敗: {e}")

        return result

    def generate_summary_report(self) -> str:
        """生成摘要報告"""
        report = []
        report.append("=" * 80)
        report.append("🏪 家樂福離線交易資料更新狀況檢查報告")
        report.append("=" * 80)
        report.append(f"📅 檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🔐 Service Account: {self.service_account}")
        report.append("")

        for table_type in ["daily", "monthly"]:
            if table_type in self.results:
                basic_info = self.results[table_type]["basic_info"]
                recent_info = self.results[table_type]["recent_updates"]

                report.append(f"📊 {table_type.upper()} 資料表 ({basic_info['table_name']})")
                report.append("-" * 60)

                if basic_info["exists"]:
                    report.append(f"✅ 資料表存在")
                    report.append(f"📈 總筆數: {basic_info['row_count']:,}")

                    if basic_info.get("date_range") and basic_info["date_range"].get("earliest"):
                        earliest = basic_info['date_range']['earliest']
                        latest = basic_info['date_range']['latest']

                        # 檢查是否有異常的時間範圍
                        if earliest < "2020-01-01":
                            report.append(f"⚠️ 資料時間範圍: {earliest} 至 {latest} (包含異常早期資料)")
                        else:
                            report.append(f"📅 資料時間範圍: {earliest} 至 {latest}")

                        report.append(f"📊 唯一日期數: {basic_info['date_range']['unique_dates']}")

                        # 計算實際有效的資料涵蓋天數（排除異常值）
                        if earliest < "2020-01-01" and latest >= "2020-01-01":
                            report.append(f"⏱️ 資料涵蓋天數: {basic_info['date_range'].get('days_span', 'N/A')} (包含異常值)")
                            report.append(f"💡 建議: 主要資料範圍為 2024-2025 年")
                        else:
                            report.append(f"⏱️ 資料涵蓋天數: {basic_info['date_range'].get('days_span', 'N/A')}")

                    if recent_info and not recent_info.get("error"):
                        report.append(f"📊 最近 7 天資料筆數: {recent_info['last_7_days_count']:,}")
                        report.append(f"📊 最近 30 天資料筆數: {recent_info['last_30_days_count']:,}")

                        if recent_info["recent_dates"]:
                            report.append(f"📅 最新資料日期: {recent_info['recent_dates'][0]['date']}")
                        else:
                            report.append(f"📅 最新資料日期: 無最近資料")
                else:
                    report.append(f"❌ 資料表不存在或無法存取")

                if basic_info.get("error"):
                    report.append(f"❌ 錯誤: {basic_info['error']}")

                report.append("")

        # 添加分析總結
        report.append("💡 分析總結")
        report.append("-" * 40)

        daily_info = self.results.get("daily", {}).get("basic_info", {})
        monthly_info = self.results.get("monthly", {}).get("basic_info", {})

        if daily_info.get("exists") and monthly_info.get("exists"):
            daily_earliest = daily_info.get("date_range", {}).get("earliest", "")
            monthly_earliest = monthly_info.get("date_range", {}).get("earliest", "")

            if monthly_earliest < "2020-01-01":
                report.append("⚠️ Monthly 表格包含異常早期資料（可能是測試資料）")

            if daily_earliest >= "2025-07-01":
                report.append("✅ Daily 表格包含最新資料")

            if monthly_info.get("date_range", {}).get("latest", "") < "2025-08-01":
                report.append("⚠️ Monthly 表格需要更新到最新月份")

        report.append("")
        report.append("=" * 80)
        return "\n".join(report)

    def run_full_check(self) -> bool:
        """執行完整檢查"""
        print("🚀 開始家樂福離線交易資料更新狀況檢查...")
        print("=" * 80)

        # 進行身份驗證
        if not self.authenticate():
            return False

        # 檢查兩個資料表
        for table_type in ["daily", "monthly"]:
            basic_info = self.check_table_basic_info(table_type)
            recent_info = self.check_recent_updates(table_type)

            self.results[table_type] = {
                "basic_info": basic_info,
                "recent_updates": recent_info
            }

        return True

def main():
    """主函數"""
    checker = CarrefourDataUpdateChecker()

    if not checker.run_full_check():
        sys.exit(1)

    # 生成並顯示報告
    report = checker.generate_summary_report()
    print("\n" + report)

    # 儲存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"carrefour_data_update_status_{timestamp}.txt"

    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 報告已儲存至: {report_filename}")
    except Exception as e:
        print(f"⚠️ 報告儲存失敗: {e}")

if __name__ == "__main__":
    main()
