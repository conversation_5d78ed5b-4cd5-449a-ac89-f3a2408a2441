#!/usr/bin/env python3
"""
家樂福離線資料自動合併模組
整合到 Cloud Function 中，實現每月自動將月表資料智慧合併到完整表

功能：
1. 檢查月表和完整表的資料範圍
2. 自動識別需要合併的新資料（避免重複）
3. 智慧處理重疊日期
4. 執行資料合併到完整表
5. 驗證合併結果

作者：Claude Code
創建時間：2025-09-12
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from google.cloud import bigquery
from google.cloud.bigquery import QueryJobConfig

# 設定日誌
logger = logging.getLogger(__name__)


class AutomatedMonthlyMerger:
    """家樂福資料自動月度合併器"""

    def __init__(self, project_id: str = "tagtoo-tracking"):
        """
        初始化資料合併器

        Args:
            project_id: BigQuery 專案 ID
        """
        self.project_id = project_id
        self.dataset_id = "event_prod"
        self.complete_table = "carrefour_offline_transaction"
        self.month_table = "carrefour_offline_transaction_month"

        # 初始化 BigQuery 客戶端
        self.client = bigquery.Client(project=project_id)

        # ph_id_hex 富化功能配置
        self.cost_per_tb = 5.0  # BigQuery 成本 $5/TB
        self.max_enrichment_cost_usd = 2.0  # ph_id_hex 富化成本上限

        logger.info(f"初始化家樂福自動月度合併器 - 專案: {project_id}")

    def analyze_date_boundaries(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        智慧分析日期邊界和資料分佈

        Args:
            start_date: 起始分析日期 (可選)
            end_date: 結束分析日期 (可選)

        Returns:
            Dict: 包含各類型日期的分析結果
        """
        # 如果未指定範圍，分析最近3個月
        if not start_date:
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

        logger.info(f"分析日期邊界: {start_date} 到 {end_date}")

        # 查詢兩表的每日資料分佈
        boundary_query = f"""
        WITH daily_complete AS (
          SELECT
            DATE(TIMESTAMP_SECONDS(event_times)) as date,
            COUNT(*) as complete_records
          FROM `{self.project_id}.{self.dataset_id}.{self.complete_table}`
          WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
          GROUP BY date
        ),
        daily_month AS (
          SELECT
            DATE(TIMESTAMP_SECONDS(event_times)) as date,
            COUNT(*) as month_records
          FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
          WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
          GROUP BY date
        )
        SELECT
          COALESCE(c.date, m.date) as date,
          COALESCE(c.complete_records, 0) as complete_records,
          COALESCE(m.month_records, 0) as month_records
        FROM daily_complete c
        FULL OUTER JOIN daily_month m ON c.date = m.date
        ORDER BY date
        """

        try:
            results = list(self.client.query(boundary_query))

            # 分析結果
            analysis = {
                'month_only_dates': [],      # 只有月表有資料的日期
                'complete_only_dates': [],   # 只有完整表有資料的日期
                'month_dominant_dates': [],  # 月表資料更多的日期
                'complete_dominant_dates': [], # 完整表資料更多的日期
                'overlap_dates': [],         # 資料完全相同的重疊日期
                'total_month_records': 0,
                'total_complete_records': 0
            }

            for row in results:
                date_str = row.date.strftime('%Y-%m-%d')
                complete_count = row.complete_records
                month_count = row.month_records

                analysis['total_complete_records'] += complete_count
                analysis['total_month_records'] += month_count

                # 分類日期
                if month_count > 0 and complete_count == 0:
                    # 只有月表有資料
                    analysis['month_only_dates'].append({
                        'date': date_str,
                        'records': month_count
                    })
                    logger.info(f"  {date_str}: month_only (完整表: {complete_count:,}, 月表: {month_count:,})")

                elif complete_count > 0 and month_count == 0:
                    # 只有完整表有資料
                    analysis['complete_only_dates'].append({
                        'date': date_str,
                        'records': complete_count
                    })
                    logger.info(f"  {date_str}: complete_only (完整表: {complete_count:,}, 月表: {month_count:,})")

                elif month_count > complete_count:
                    # 月表資料較多
                    analysis['month_dominant_dates'].append({
                        'date': date_str,
                        'complete_records': complete_count,
                        'month_records': month_count
                    })
                    logger.info(f"  {date_str}: month_dominant (完整表: {complete_count:,}, 月表: {month_count:,})")

                elif complete_count > month_count:
                    # 完整表資料較多
                    analysis['complete_dominant_dates'].append({
                        'date': date_str,
                        'complete_records': complete_count,
                        'month_records': month_count
                    })
                    logger.info(f"  {date_str}: complete_dominant (完整表: {complete_count:,}, 月表: {month_count:,})")

                elif month_count == complete_count and month_count > 0:
                    # 資料完全相同的重疊
                    analysis['overlap_dates'].append({
                        'date': date_str,
                        'complete_records': complete_count,
                        'month_records': month_count
                    })
                    logger.info(f"  {date_str}: overlap (完整表: {complete_count:,}, 月表: {month_count:,})")

            return analysis

        except Exception as e:
            logger.error(f"分析日期邊界失敗: {e}")
            raise

    def _add_ph_id_hex_and_populate(self, target_table_id: str) -> Dict[str, Any]:
        """
        為目標表新增並填充輔助欄位 ph_id_hex (STRING)，內容為 TO_HEX(ph_id)。
        - 保持原有 ph_id (BYTES) 不變
        - 先以 ALTER TABLE 新增欄位（若不存在）
        - 再以 UPDATE 進行填充，先 dry-run 估算成本
        """
        details: Dict[str, Any] = {
            "target_table": target_table_id,
            "alter_done": False,
            "update_bytes": 0,
            "estimated_cost_usd": 0.0,
        }

        # 1) 新增欄位（若不存在）
        alter_sql = f"""
        ALTER TABLE `{target_table_id}`
        ADD COLUMN IF NOT EXISTS ph_id_hex STRING
        """
        logger.info(f"準備在目標表新增輔助欄位 ph_id_hex: {target_table_id}")
        self.client.query(alter_sql).result()
        details["alter_done"] = True

        # 2) 更新欄位值（dry-run 估算成本）
        update_sql = f"UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id) WHERE ph_id_hex IS NULL"
        dry_cfg = QueryJobConfig(dry_run=True, use_query_cache=False)
        dry_job = self.client.query(update_sql, job_config=dry_cfg)
        bytes_proc = getattr(dry_job, "total_bytes_processed", 0) or 0
        details["update_bytes"] = bytes_proc
        est_cost = bytes_proc / (1024 ** 4) * self.cost_per_tb
        details["estimated_cost_usd"] = est_cost
        logger.info(f"ph_id_hex UPDATE 預估掃描: {bytes_proc:,} bytes (約 ${est_cost:.4f})")
        if est_cost > self.max_enrichment_cost_usd:
            logger.warning(
                f"ph_id_hex 填充預估成本 ${est_cost:.4f} 超過上限 ${self.max_enrichment_cost_usd:.2f}，依需求仍將執行"
            )

        # 實際執行 UPDATE
        job = self.client.query(update_sql)
        job.result()
        logger.info("ph_id_hex 欄位填充完成")
        return details

    def merge_month_only_data(self, dates: List[Dict], dry_run: bool = True) -> bool:
        """
        合併只有月表有資料的日期
        使用直接 INSERT 方式

        Args:
            dates: 日期資料列表
            dry_run: 是否為測試模式

        Returns:
            bool: 合併是否成功
        """
        if not dates:
            logger.info("沒有需要直接插入的月表專有資料")
            return True

        # 構建日期條件
        date_list = [f"'{item['date']}'" for item in dates]
        date_conditions = ','.join(date_list)
        total_records = sum(item['records'] for item in dates)

        merge_query = f"""
        INSERT INTO `{self.project_id}.{self.dataset_id}.{self.complete_table}`
        SELECT * FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
        WHERE DATE(TIMESTAMP_SECONDS(event_times)) IN ({date_conditions})
        """

        try:
            logger.info(f"📥 直接插入策略: {len(dates)} 天, {total_records:,} 筆記錄")

            if dry_run:
                logger.info("⚠️ DRY RUN 模式 - 不會執行實際插入")
                return True

            # 執行插入
            job = self.client.query(merge_query)
            job.result()  # 等待完成

            logger.info(f"✅ 直接插入完成，處理了 {job.num_dml_affected_rows:,} 筆記錄")
            return True

        except Exception as e:
            logger.error(f"❌ 直接插入失敗: {e}")
            return False

    def merge_month_dominant_data(self, dates_data: List[Dict], dry_run: bool = True) -> bool:
        """
        合併月表資料較多的日期
        使用 DELETE + INSERT 策略確保資料一致性

        Args:
            dates_data: 日期資料列表，包含 complete_records 和 month_records
            dry_run: 是否為測試模式

        Returns:
            bool: 合併是否成功
        """
        if not dates_data:
            logger.info("沒有需要替換的月表主導資料")
            return True

        total_deleted = 0
        total_inserted = 0

        try:
            logger.info(f"🔄 替換策略: {len(dates_data)} 天需要處理")

            for date_info in dates_data:
                date_str = date_info['date']
                complete_records = date_info['complete_records']
                month_records = date_info['month_records']

                logger.info(f"  處理 {date_str}: 完整表 {complete_records:,} → 月表 {month_records:,}")

                if dry_run:
                    logger.info(f"    ⚠️ DRY RUN - 會刪除 {complete_records:,} 筆，插入 {month_records:,} 筆")
                    total_deleted += complete_records
                    total_inserted += month_records
                    continue

                # 1. 刪除完整表中的該日期資料
                delete_query = f"""
                DELETE FROM `{self.project_id}.{self.dataset_id}.{self.complete_table}`
                WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '{date_str}'
                """

                delete_job = self.client.query(delete_query)
                delete_job.result()
                deleted_rows = delete_job.num_dml_affected_rows or 0
                total_deleted += deleted_rows

                # 2. 插入月表資料
                insert_query = f"""
                INSERT INTO `{self.project_id}.{self.dataset_id}.{self.complete_table}`
                SELECT * FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
                WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '{date_str}'
                """

                insert_job = self.client.query(insert_query)
                insert_job.result()
                inserted_rows = insert_job.num_dml_affected_rows or 0
                total_inserted += inserted_rows

                logger.info(f"    ✅ 刪除 {deleted_rows:,} 筆，插入 {inserted_rows:,} 筆")

            if dry_run:
                logger.info(f"⚠️ DRY RUN 總計 - 會刪除 {total_deleted:,} 筆，插入 {total_inserted:,} 筆")
            else:
                logger.info(f"✅ 替換策略完成 - 刪除 {total_deleted:,} 筆，插入 {total_inserted:,} 筆")

            return True

        except Exception as e:
            logger.error(f"❌ 替換策略失敗: {e}")
            return False

    def run_automated_merge(self, dry_run: bool = True, max_days_back: int = 90) -> Dict[str, Any]:
        """
        執行自動化月度資料合併

        Args:
            dry_run: 是否為測試模式
            max_days_back: 最多回溯天數

        Returns:
            Dict: 執行結果
        """
        start_time = datetime.now()
        logger.info("🚀 開始執行自動化月度資料合併...")

        results = {
            'start_time': start_time.isoformat(),
            'dry_run': dry_run,
            'boundary_analysis': {},
            'merge_operations': [],
            'total_records_merged': 0,
            'success': False,
            'error_message': None
        }

        try:
            # 步驟1: 分析日期邊界
            logger.info("=== 步驟1: 分析日期邊界 ===")
            start_date = (datetime.now() - timedelta(days=max_days_back)).strftime('%Y-%m-%d')
            boundary_analysis = self.analyze_date_boundaries(start_date=start_date)
            results['boundary_analysis'] = boundary_analysis

            # 步驟2: 檢查是否有需要合併的資料
            month_only_dates = boundary_analysis['month_only_dates']
            month_dominant_dates = boundary_analysis['month_dominant_dates']

            if not month_only_dates and not month_dominant_dates:
                logger.info("✅ 沒有需要合併的新資料")
                results['success'] = True
                return results

            # 步驟3: 執行合併操作
            logger.info("=== 步驟2: 執行資料合併 ===")
            total_merged = 0

            # 3a. 處理月表專有資料（直接插入）
            if month_only_dates:
                success = self.merge_month_only_data(month_only_dates, dry_run)
                if success:
                    merged_count = sum(item['records'] for item in month_only_dates)
                    results['merge_operations'].append({
                        'operation': 'direct_insert',
                        'dates': [item['date'] for item in month_only_dates],
                        'records': merged_count,
                        'description': f"直接插入 {len(month_only_dates)} 天的月表專有資料"
                    })
                    total_merged += merged_count
                    logger.info(f"📥 月表專有資料: {merged_count:,} 筆")
                else:
                    raise RuntimeError("月表專有資料合併失敗")

            # 3b. 處理月表主導資料（替換）
            if month_dominant_dates:
                success = self.merge_month_dominant_data(month_dominant_dates, dry_run)
                if success:
                    merged_count = sum(item['month_records'] for item in month_dominant_dates)
                    results['merge_operations'].append({
                        'operation': 'replace',
                        'dates': [item['date'] for item in month_dominant_dates],
                        'records': merged_count,
                        'description': f"替換 {len(month_dominant_dates)} 天的完整表資料"
                    })
                    total_merged += merged_count
                    logger.info(f"🔄 月表主導資料: {merged_count:,} 筆")
                else:
                    raise RuntimeError("月表主導資料合併失敗")

            # 處理重疊資料（跳過）
            overlap_dates = boundary_analysis['overlap_dates']
            if overlap_dates:
                logger.info(f"⏭️ 跳過 {len(overlap_dates)} 天的完全重疊資料")
                results['merge_operations'].append({
                    'operation': 'overlap_skipped',
                    'dates': [item['date'] for item in overlap_dates],
                    'records': 0,
                    'description': f"跳過 {len(overlap_dates)} 天的重疊資料"
                })

            results['total_records_merged'] = total_merged

            # 步驟4: 新增並填充 ph_id_hex 輔助欄位（僅在有合併資料時執行）
            if total_merged > 0 and not dry_run:
                logger.info("=== 步驟4: 新增並填充 ph_id_hex 輔助欄位 ===")
                target_table_id = f"{self.project_id}.{self.dataset_id}.{self.complete_table}"
                try:
                    enrich_info = self._add_ph_id_hex_and_populate(target_table_id)
                    results['ph_id_hex_enrichment'] = enrich_info
                    logger.info("✅ ph_id_hex 輔助欄位處理完成")
                except Exception as enrich_err:
                    logger.error(f"⚠️ ph_id_hex 輔助欄位處理失敗: {enrich_err}")
                    results['ph_id_hex_enrichment_error'] = str(enrich_err)
                    # 不中斷主要合併流程，僅記錄錯誤
            elif total_merged > 0 and dry_run:
                logger.info("=== 步驟4: ph_id_hex 輔助欄位處理 (DRY RUN 模式跳過) ===")
                results['ph_id_hex_enrichment'] = {"skipped": "dry_run_mode"}

            results['success'] = True

            end_time = datetime.now()
            results['end_time'] = end_time.isoformat()
            results['duration_seconds'] = (end_time - start_time).total_seconds()

            mode_text = "測試" if dry_run else "生產"
            logger.info(f"✅ 自動化月度合併完成 ({mode_text}模式)，共合併 {total_merged:,} 筆記錄")

        except Exception as e:
            logger.error(f"❌ 自動化月度合併失敗: {e}")
            results['error_message'] = str(e)
            results['end_time'] = datetime.now().isoformat()

        return results


def execute_monthly_merge(project_id: str = "tagtoo-tracking",
                         dry_run: bool = True,
                         max_days_back: int = 90) -> Dict[str, Any]:
    """
    執行月度資料合併的便利函數
    供 Cloud Function 調用

    Args:
        project_id: BigQuery 專案 ID
        dry_run: 是否為測試模式
        max_days_back: 最多回溯天數

    Returns:
        Dict: 執行結果
    """
    merger = AutomatedMonthlyMerger(project_id=project_id)
    return merger.run_automated_merge(dry_run=dry_run, max_days_back=max_days_back)


if __name__ == '__main__':
    # 本地測試支援
    import argparse

    parser = argparse.ArgumentParser(description='家樂福自動月度資料合併工具')
    parser.add_argument('--project-id', default='tagtoo-tracking', help='BigQuery 專案 ID')
    parser.add_argument('--dry-run', action='store_true', help='測試模式')
    parser.add_argument('--max-days-back', type=int, default=90, help='最多回溯天數')

    args = parser.parse_args()

    # 執行合併
    results = execute_monthly_merge(
        project_id=args.project_id,
        dry_run=args.dry_run,
        max_days_back=args.max_days_back
    )

    # 顯示結果
    import json
    print(json.dumps(results, indent=2, ensure_ascii=False))
