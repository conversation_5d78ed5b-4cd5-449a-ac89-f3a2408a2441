#!/usr/bin/env python3
"""
驗證 carrefour-tagtoo-bigquery-view Service Account 權限

檢查 Service Account 對於家樂福資料表的存取權限
並提供權限設定建議

Author: AI Assistant
Date: 2025-09-03
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any, List
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceAccountPermissionVerifier:
    """Service Account 權限驗證器"""

    def __init__(self):
        self.service_account = "<EMAIL>"
        self.tables_to_check = {
            "tagtoo_entity": "tagtoo-tracking.event_prod.tagtoo_entity",
            "carrefour_daily": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
            "carrefour_monthly": "tagtoo-tracking.event_prod.carrefour_offline_transaction_month",
            "target_table": "tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_20250904"
        }
        self.client = None

    def authenticate(self) -> bool:
        """進行身份驗證"""
        try:
            credentials, project = default()
            self.client = bigquery.Client(project="tagtoo-tracking", credentials=credentials)

            logger.info(f"✅ 身份驗證成功")
            logger.info(f"🔐 Service Account: {self.service_account}")
            return True

        except DefaultCredentialsError as e:
            logger.error(f"❌ 身份驗證失敗: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 身份驗證錯誤: {e}")
            return False

    def check_table_access(self, table_name: str, table_id: str) -> Dict[str, Any]:
        """檢查表格存取權限"""
        result = {
            "table_name": table_name,
            "table_id": table_id,
            "exists": False,
            "readable": False,
            "queryable": False,
            "error": None
        }

        try:
            # 檢查表格是否存在
            table = self.client.get_table(table_id)
            result["exists"] = True
            result["readable"] = True

            logger.info(f"✅ {table_name}: 表格存在且可讀取")
            logger.info(f"   筆數: {table.num_rows:,}")
            logger.info(f"   大小: {table.num_bytes:,} bytes")

            # 嘗試執行簡單查詢測試
            test_query = f"SELECT COUNT(*) as row_count FROM `{table_id}` LIMIT 1"

            # 先用 dry_run 測試
            job_config = bigquery.QueryJobConfig(dry_run=True)
            job = self.client.query(test_query, job_config=job_config)

            # 如果 dry_run 成功，執行實際查詢
            job = self.client.query(test_query)
            results = list(job.result())

            if results:
                result["queryable"] = True
                logger.info(f"✅ {table_name}: 查詢權限正常")

        except Exception as e:
            result["error"] = str(e)
            if "403" in str(e):
                logger.error(f"❌ {table_name}: 權限不足 - {e}")
            elif "404" in str(e):
                logger.error(f"❌ {table_name}: 表格不存在 - {e}")
            else:
                logger.error(f"❌ {table_name}: 其他錯誤 - {e}")

        return result

    def test_write_permission(self) -> Dict[str, Any]:
        """測試寫入權限"""
        result = {
            "writable": False,
            "test_table": None,
            "error": None
        }

        # 建立測試表格名稱
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_table_id = f"tagtoo-ml-workflow.tagtoo_export_results.carrefour_permission_test_{timestamp}"

        try:
            logger.info("🧪 測試寫入權限...")

            # 建立測試資料
            test_data = [
                {
                    "permanent": "test_user_001",
                    "segment_id": "tm:c_715_pc_test",
                    "created_at": datetime.now().isoformat(),
                    "source_type": "permission_test",
                    "source_entity": "permission_verifier",
                    "execution_id": f"test_{timestamp}"
                }
            ]

            # 嘗試寫入測試資料
            job_config = bigquery.LoadJobConfig(
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
                schema=[
                    bigquery.SchemaField("permanent", "STRING"),
                    bigquery.SchemaField("segment_id", "STRING"),
                    bigquery.SchemaField("created_at", "TIMESTAMP"),
                    bigquery.SchemaField("source_type", "STRING"),
                    bigquery.SchemaField("source_entity", "STRING"),
                    bigquery.SchemaField("execution_id", "STRING"),
                ]
            )

            job = self.client.load_table_from_json(
                test_data, test_table_id, job_config=job_config
            )
            job.result()  # 等待完成

            result["writable"] = True
            result["test_table"] = test_table_id
            logger.info(f"✅ 寫入權限測試成功: {test_table_id}")

            # 清理測試表格
            self.client.delete_table(test_table_id)
            logger.info("🧹 測試表格已清理")

        except Exception as e:
            result["error"] = str(e)
            if "403" in str(e):
                logger.error(f"❌ 寫入權限不足: {e}")
            else:
                logger.error(f"❌ 寫入測試失敗: {e}")

        return result

    def generate_permission_report(self, results: List[Dict]) -> str:
        """生成權限檢查報告"""
        report = []
        report.append("=" * 80)
        report.append("🔐 Service Account 權限驗證報告")
        report.append("=" * 80)
        report.append(f"📅 檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🔐 Service Account: {self.service_account}")
        report.append("")

        # 表格權限檢查結果
        report.append("📊 表格存取權限檢查")
        report.append("-" * 60)

        all_readable = True
        all_queryable = True

        for result in results:
            if result.get("table_name"):
                table_name = result["table_name"]
                report.append(f"\n🔸 {table_name}")
                report.append(f"   表格ID: {result['table_id']}")

                if result["exists"]:
                    report.append(f"   ✅ 存在: 是")
                    report.append(f"   ✅ 可讀取: {'是' if result['readable'] else '否'}")
                    report.append(f"   ✅ 可查詢: {'是' if result['queryable'] else '否'}")

                    if not result["readable"]:
                        all_readable = False
                    if not result["queryable"]:
                        all_queryable = False
                else:
                    report.append(f"   ❌ 存在: 否")
                    all_readable = False
                    all_queryable = False

                if result.get("error"):
                    report.append(f"   ❌ 錯誤: {result['error']}")

        # 寫入權限檢查結果
        write_result = next((r for r in results if "writable" in r), None)
        if write_result:
            report.append(f"\n💾 寫入權限檢查")
            report.append("-" * 40)
            report.append(f"✅ 可寫入: {'是' if write_result['writable'] else '否'}")
            if write_result.get("error"):
                report.append(f"❌ 錯誤: {write_result['error']}")

        # 總結
        report.append(f"\n📋 權限檢查總結")
        report.append("-" * 40)

        if all_readable and all_queryable and (write_result and write_result["writable"]):
            report.append("✅ 所有權限檢查通過")
            report.append("🎯 Service Account 已正確配置")
        else:
            report.append("⚠️ 部分權限檢查失敗")

            if not all_readable:
                report.append("❌ 部分表格無法讀取")
            if not all_queryable:
                report.append("❌ 部分表格無法查詢")
            if write_result and not write_result["writable"]:
                report.append("❌ 目標專案無法寫入")

        # 建議
        report.append(f"\n💡 權限配置建議")
        report.append("-" * 40)

        if not all_readable or not all_queryable:
            report.append("1. 確認 Service Account 在 tagtoo-tracking 專案中有以下權限:")
            report.append("   - roles/bigquery.dataViewer")
            report.append("   - roles/bigquery.jobUser")

        if write_result and not write_result["writable"]:
            report.append("2. 確認 Service Account 在 tagtoo-ml-workflow 專案中有以下權限:")
            report.append("   - roles/bigquery.dataEditor")
            report.append("   - roles/bigquery.jobUser")

        if all_readable and all_queryable and (write_result and write_result["writable"]):
            report.append("✅ 權限配置正確，無需額外設定")

        report.append("")
        report.append("=" * 80)
        return "\\n".join(report)

    def run_verification(self) -> bool:
        """執行完整權限驗證"""
        logger.info("🚀 開始 Service Account 權限驗證...")

        if not self.authenticate():
            return False

        results = []

        # 檢查各表格權限
        for table_name, table_id in self.tables_to_check.items():
            result = self.check_table_access(table_name, table_id)
            results.append(result)

        # 測試寫入權限
        write_result = self.test_write_permission()
        results.append(write_result)

        # 生成報告
        report = self.generate_permission_report(results)
        print("\\n" + report)

        # 儲存報告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"service_account_permission_report_{timestamp}.txt"

        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"📄 報告已儲存至: {report_filename}")
        except Exception as e:
            logger.warning(f"⚠️ 報告儲存失敗: {e}")

        return True

def main():
    """主函數"""
    verifier = ServiceAccountPermissionVerifier()

    if not verifier.run_verification():
        sys.exit(1)

if __name__ == "__main__":
    main()
