#!/usr/bin/env python3
"""
家樂福離線資料智慧合併工具
基於資料時間邊界分析，智慧合併月表到完整表

合併策略：
1. 完全缺失的月份：直接插入月表資料
2. 部分重疊的日期：使用 MERGE 語句去重合併
3. 邊界資料：月表補充完整表缺失的時間段

作者：Claude Code
創建時間：2025-09-11
版本：v2.0 - 智慧邊界處理
"""

import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from google.cloud import bigquery
from google.cloud.bigquery import QueryJobConfig

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SmartCarrefourDataMerger:
    """智慧家樂福資料合併器"""

    def __init__(self, project_id: str = "tagtoo-tracking"):
        """
        初始化智慧資料合併器

        Args:
            project_id: BigQuery 專案 ID
        """
        self.project_id = project_id
        self.dataset_id = "event_prod"
        self.complete_table = "carrefour_offline_transaction"
        self.month_table = "carrefour_offline_transaction_month"

        # 初始化 BigQuery 客戶端
        self.client = bigquery.Client(project=project_id)

        logger.info(f"初始化智慧家樂福資料合併器 - 專案: {project_id}")

    def analyze_date_boundaries(self, start_date: str = "2025-06-01", end_date: str = "2025-09-30") -> Dict:
        """
        分析兩表的日期邊界和重疊情況

        Args:
            start_date: 分析起始日期
            end_date: 分析結束日期

        Returns:
            Dict: 包含邊界分析結果
        """
        query = f"""
        WITH complete_data AS (
          SELECT
            DATE(TIMESTAMP_SECONDS(event_times)) as event_date,
            COUNT(*) as record_count,
            MIN(TIMESTAMP_SECONDS(event_times)) as min_datetime,
            MAX(TIMESTAMP_SECONDS(event_times)) as max_datetime
          FROM `{self.project_id}.{self.dataset_id}.{self.complete_table}`
          WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
          GROUP BY event_date
        ),
        month_data AS (
          SELECT
            DATE(TIMESTAMP_SECONDS(event_times)) as event_date,
            COUNT(*) as record_count,
            MIN(TIMESTAMP_SECONDS(event_times)) as min_datetime,
            MAX(TIMESTAMP_SECONDS(event_times)) as max_datetime
          FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
          WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
          GROUP BY event_date
        )

        SELECT
          COALESCE(c.event_date, m.event_date) as event_date,
          COALESCE(c.record_count, 0) as complete_count,
          COALESCE(m.record_count, 0) as month_count,
          c.min_datetime as complete_min,
          c.max_datetime as complete_max,
          m.min_datetime as month_min,
          m.max_datetime as month_max,
          CASE
            WHEN c.event_date IS NULL THEN 'month_only'
            WHEN m.event_date IS NULL THEN 'complete_only'
            WHEN c.record_count > m.record_count * 10 THEN 'complete_dominant'
            WHEN m.record_count > c.record_count * 10 THEN 'month_dominant'
            ELSE 'overlap'
          END as boundary_type
        FROM complete_data c
        FULL OUTER JOIN month_data m ON c.event_date = m.event_date
        ORDER BY event_date
        """

        try:
            logger.info(f"分析日期邊界: {start_date} 到 {end_date}")
            results = list(self.client.query(query))

            analysis = {
                'month_only_dates': [],      # 只有月表有資料的日期
                'complete_only_dates': [],   # 只有完整表有資料的日期
                'month_dominant_dates': [],  # 月表資料量顯著較多的日期
                'complete_dominant_dates': [], # 完整表資料量顯著較多的日期
                'overlap_dates': [],         # 有重疊需要小心處理的日期
                'total_month_records': 0,
                'total_complete_records': 0
            }

            for row in results:
                date_str = row.event_date.isoformat()
                boundary_type = row.boundary_type

                logger.info(f"  {date_str}: {boundary_type} (完整表: {row.complete_count:,}, 月表: {row.month_count:,})")

                if boundary_type == 'month_only':
                    analysis['month_only_dates'].append({
                        'date': date_str,
                        'records': row.month_count,
                        'time_range': f"{row.month_min} 到 {row.month_max}"
                    })
                elif boundary_type == 'month_dominant':
                    analysis['month_dominant_dates'].append({
                        'date': date_str,
                        'complete_records': row.complete_count,
                        'month_records': row.month_count,
                        'complete_range': f"{row.complete_min} 到 {row.complete_max}",
                        'month_range': f"{row.month_min} 到 {row.month_max}"
                    })
                elif boundary_type == 'overlap':
                    analysis['overlap_dates'].append({
                        'date': date_str,
                        'complete_records': row.complete_count,
                        'month_records': row.month_count
                    })
                elif boundary_type == 'complete_dominant':
                    analysis['complete_dominant_dates'].append({
                        'date': date_str,
                        'records': row.complete_count
                    })
                elif boundary_type == 'complete_only':
                    analysis['complete_only_dates'].append({
                        'date': date_str,
                        'records': row.complete_count
                    })

                analysis['total_month_records'] += row.month_count
                analysis['total_complete_records'] += row.complete_count

            return analysis

        except Exception as e:
            logger.error(f"分析日期邊界失敗: {e}")
            return {}

    def merge_month_only_data(self, dates: List[str], dry_run: bool = True) -> bool:
        """
        合併只存在於月表的資料

        Args:
            dates: 要合併的日期列表
            dry_run: 是否為測試模式

        Returns:
            bool: 合併是否成功
        """
        if not dates:
            logger.info("沒有只存在於月表的資料需要合併")
            return True

        # 建構日期條件
        date_conditions = "', '".join(dates)

        count_query = f"""
        SELECT COUNT(*) as record_count
        FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
        WHERE DATE(TIMESTAMP_SECONDS(event_times)) IN ('{date_conditions}')
        """

        try:
            # 檢查要合併的資料量
            results = list(self.client.query(count_query))
            record_count = results[0].record_count if results else 0

            logger.info(f"準備合併只存在於月表的資料: {len(dates)} 天, {record_count:,} 筆記錄")

            if record_count == 0:
                return True

            if dry_run:
                logger.info("⚠️ DRY RUN 模式 - 不會執行實際合併")
                return True

            # 執行合併
            merge_query = f"""
            INSERT INTO `{self.project_id}.{self.dataset_id}.{self.complete_table}`
            SELECT * FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) IN ('{date_conditions}')
            """

            logger.info("執行月表專有資料合併...")
            job = self.client.query(merge_query)
            job.result()

            logger.info(f"✅ 合併完成: {job.num_dml_affected_rows:,} 筆記錄")
            return True

        except Exception as e:
            logger.error(f"合併月表專有資料失敗: {e}")
            return False

    def merge_month_dominant_data(self, dates_data: List[Dict], dry_run: bool = True) -> bool:
        """
        合併月表數據量顯著較多的日期（使用覆蓋策略）

        Args:
            dates_data: 包含日期和資料量信息的字典列表
            dry_run: 是否為測試模式

        Returns:
            bool: 合併是否成功
        """
        if not dates_data:
            logger.info("沒有月表占主導地位的資料需要合併")
            return True

        for date_info in dates_data:
            date_str = date_info['date']
            month_records = date_info['month_records']
            complete_records = date_info['complete_records']

            logger.info(f"處理月表主導日期 {date_str}: 月表 {month_records:,} 筆 vs 完整表 {complete_records:,} 筆")

            if dry_run:
                logger.info("⚠️ DRY RUN 模式 - 不會執行實際合併")
                continue

            try:
                # 先刪除完整表中該日期的資料
                delete_query = f"""
                DELETE FROM `{self.project_id}.{self.dataset_id}.{self.complete_table}`
                WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '{date_str}'
                """

                logger.info(f"刪除完整表中 {date_str} 的舊資料...")
                delete_job = self.client.query(delete_query)
                delete_job.result()

                # 插入月表中該日期的資料
                insert_query = f"""
                INSERT INTO `{self.project_id}.{self.dataset_id}.{self.complete_table}`
                SELECT * FROM `{self.project_id}.{self.dataset_id}.{self.month_table}`
                WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '{date_str}'
                """

                logger.info(f"插入月表中 {date_str} 的新資料...")
                insert_job = self.client.query(insert_query)
                insert_job.result()

                logger.info(f"✅ {date_str} 覆蓋合併完成: {insert_job.num_dml_affected_rows:,} 筆記錄")

            except Exception as e:
                logger.error(f"覆蓋合併 {date_str} 失敗: {e}")
                return False

        return True

    def merge_overlap_data(self, dates_data: List[Dict], dry_run: bool = True) -> bool:
        """
        處理有重疊的日期，使用 MERGE 語句避免重複

        Args:
            dates_data: 包含重疊日期信息的字典列表
            dry_run: 是否為測試模式

        Returns:
            bool: 合併是否成功
        """
        if not dates_data:
            logger.info("沒有重疊資料需要處理")
            return True

        logger.warning("⚠️ 發現重疊資料，需要謹慎處理:")
        for date_info in dates_data:
            logger.warning(f"  {date_info['date']}: 完整表 {date_info['complete_records']:,} 筆, 月表 {date_info['month_records']:,} 筆")

        if dry_run:
            logger.info("⚠️ DRY RUN 模式 - 建議手動檢查重疊資料")
            return True

        # 實際實作中可能需要更複雜的去重邏輯
        logger.warning("⚠️ 重疊資料處理需要手動確認，暫時跳過")
        return True

    def run_smart_merge(self, dry_run: bool = True) -> Dict:
        """
        執行智慧資料合併流程

        Args:
            dry_run: 是否為測試模式

        Returns:
            Dict: 執行結果
        """
        start_time = datetime.now()
        logger.info("開始執行智慧家樂福資料合併流程...")

        results = {
            'start_time': start_time.isoformat(),
            'dry_run': dry_run,
            'boundary_analysis': {},
            'merge_operations': [],
            'total_records_merged': 0,
            'success': False,
            'error_message': None
        }

        try:
            # 步驟1: 分析日期邊界
            logger.info("=== 步驟1: 分析日期邊界 ===")
            analysis = self.analyze_date_boundaries()
            results['boundary_analysis'] = analysis

            if not analysis:
                raise ValueError("邊界分析失敗")

            # 生成清楚的合併摘要
            logger.info("=== 📋 資料合併計劃摘要 ===")

            # 步驟2: 合併只存在於月表的資料
            month_only_dates = [item['date'] for item in analysis['month_only_dates']]
            if month_only_dates:
                total_records = sum(item['records'] for item in analysis['month_only_dates'])
                logger.info(f"🔸 新增資料：月表 → 完整表")
                logger.info(f"   日期範圍：{month_only_dates[0]} ~ {month_only_dates[-1]} ({len(month_only_dates)} 天)")
                logger.info(f"   資料量：{total_records:,} 筆記錄")
                logger.info(f"   原因：完整表完全沒有這些日期的資料")

                success = self.merge_month_only_data(month_only_dates, dry_run)
                if success:
                    results['merge_operations'].append({
                        'operation': 'month_only_insert',
                        'dates': month_only_dates,
                        'records': total_records,
                        'description': f"新增 {len(month_only_dates)} 天的月表專有資料"
                    })
                    results['total_records_merged'] += total_records
                else:
                    raise RuntimeError("月表專有資料合併失敗")

            # 步驟3: 處理月表占主導地位的資料
            month_dominant_data = analysis['month_dominant_dates']
            if month_dominant_data:
                for item in month_dominant_data:
                    logger.info(f"🔄 覆蓋資料：月表 → 完整表")
                    logger.info(f"   日期：{item['date']}")
                    logger.info(f"   月表：{item['month_records']:,} 筆 ({item['month_range']})")
                    logger.info(f"   完整表：{item['complete_records']:,} 筆 ({item['complete_range']})")
                    logger.info(f"   原因：月表有完整資料，完整表只有部分資料")

                success = self.merge_month_dominant_data(month_dominant_data, dry_run)
                if success:
                    total_records = sum(item['month_records'] for item in month_dominant_data)
                    results['merge_operations'].append({
                        'operation': 'month_dominant_replace',
                        'dates': [item['date'] for item in month_dominant_data],
                        'records': total_records,
                        'description': f"覆蓋 {len(month_dominant_data)} 天的不完整資料"
                    })
                    results['total_records_merged'] += total_records
                else:
                    raise RuntimeError("月表主導資料覆蓋失敗")

            # 步驟4: 處理重疊資料
            overlap_data = analysis['overlap_dates']
            if overlap_data:
                logger.warning("⚠️ 發現資料重疊，需要手動確認")
                for item in overlap_data:
                    logger.warning(f"   {item['date']}: 完整表 {item['complete_records']:,} 筆, 月表 {item['month_records']:,} 筆")

                success = self.merge_overlap_data(overlap_data, dry_run)
                if success:
                    results['merge_operations'].append({
                        'operation': 'overlap_handled',
                        'dates': [item['date'] for item in overlap_data],
                        'records': 0,
                        'description': f"跳過 {len(overlap_data)} 天的重疊資料"
                    })

            # 顯示保持不變的資料
            complete_only_dates = analysis['complete_only_dates']
            if complete_only_dates:
                total_complete_records = sum(item['records'] for item in complete_only_dates)
                logger.info(f"✅ 保持現有：完整表獨有資料")
                logger.info(f"   日期範圍：{complete_only_dates[0]['date']} ~ {complete_only_dates[-1]['date']} ({len(complete_only_dates)} 天)")
                logger.info(f"   資料量：{total_complete_records:,} 筆記錄")
                logger.info(f"   原因：完整表已有完整且品質良好的資料")

            results['success'] = True
            end_time = datetime.now()
            results['end_time'] = end_time.isoformat()
            results['duration_seconds'] = (end_time - start_time).total_seconds()

            logger.info(f"")
            logger.info(f"📊 合併結果摘要:")
            logger.info(f"   • 新增記錄：{results['total_records_merged']:,} 筆")
            logger.info(f"   • 執行時間：{results['duration_seconds']:.1f} 秒")
            if dry_run:
                logger.info(f"   • 模式：⚠️ 測試模式 (未實際執行)")
            else:
                logger.info(f"   • 模式：✅ 生產模式 (已實際執行)")

        except Exception as e:
            logger.error(f"智慧資料合併流程失敗: {e}")
            results['error_message'] = str(e)
            results['end_time'] = datetime.now().isoformat()

        return results


def main():
    """主程式入口"""
    import argparse

    parser = argparse.ArgumentParser(description='智慧家樂福離線資料合併工具')
    parser.add_argument('--project-id', default='tagtoo-tracking', help='BigQuery 專案 ID')
    parser.add_argument('--dry-run', action='store_true', help='測試模式，不執行實際合併')
    parser.add_argument('--force', action='store_true', help='強制執行（生產模式）')

    args = parser.parse_args()

    # 安全檢查
    if not args.dry_run and not args.force:
        logger.error("生產模式需要 --force 參數確認")
        sys.exit(1)

    # 初始化智慧合併器
    merger = SmartCarrefourDataMerger(project_id=args.project_id)

    # 執行智慧合併
    results = merger.run_smart_merge(dry_run=args.dry_run)

    # 顯示結果
    import json
    print(json.dumps(results, indent=2, ensure_ascii=False))

    if not results['success']:
        sys.exit(1)


if __name__ == '__main__':
    main()
