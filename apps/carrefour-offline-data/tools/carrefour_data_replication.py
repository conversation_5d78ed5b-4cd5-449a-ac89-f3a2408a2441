#!/usr/bin/env python3
"""
家樂福離線資料複製工具

負責將家樂福資料從 tw-eagle-prod 複製到 tagtoo-tracking
支援 Daily 和 Monthly 兩種模式

Author: AI Assistant
Date: 2025-09-03
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CarrefourDataReplicator:
    """家樂福資料複製器"""

    def __init__(self):
        self.source_project = "tw-eagle-prod"
        self.target_project = "tagtoo-tracking"
        self.dataset_id = "rmn_tagtoo"
        self.target_dataset = "event_prod"

        self.tables = {
            "daily": {
                "source": f"{self.source_project}.{self.dataset_id}.offline_transaction_day",
                "target": f"{self.target_project}.{self.target_dataset}.carrefour_offline_transaction_day"
            },
            "monthly": {
                "source": f"{self.source_project}.{self.dataset_id}.offline_transaction_month",
                "target": f"{self.target_project}.{self.target_dataset}.carrefour_offline_transaction_month"
            }
        }

        # 成本控制
        self.max_copy_cost_usd = 5.0  # 單次複製成本上限
        self.cost_per_tb = 5.0  # BigQuery 成本 $5/TB

        self.client = None

    def authenticate(self) -> bool:
        """進行身份驗證"""
        try:
            credentials, project = default()
            # 使用 target_project 作為計費專案，允許跨專案存取 tw-eagle-prod 資料
            self.client = bigquery.Client(project=self.target_project, credentials=credentials)

            logger.info(f"✅ 身份驗證成功")
            logger.info(f"📋 計費專案: {self.target_project}")
            logger.info(f"🔐 預期使用 Service Account: <EMAIL>")

            # 測試跨專案存取權限
            try:
                source_table = f"{self.source_project}.{self.dataset_id}.offline_transaction_day"
                table = self.client.get_table(source_table)
                logger.info(f"✅ 跨專案存取測試成功: {source_table}")
            except Exception as access_error:
                logger.error(f"❌ 跨專案存取測試失敗: {access_error}")
                logger.error(f"請確認 Service Account 具有對 {self.source_project} 的 bigquery.dataViewer 權限")
                return False

            return True

        except DefaultCredentialsError as e:
            logger.error(f"❌ 身份驗證失敗: {e}")
            logger.error("請確認 Cloud Function 使用正確的 Service Account")
            return False
        except Exception as e:
            logger.error(f"❌ 身份驗證錯誤: {e}")
            return False

    def check_table_modified_time(self, table_id: str) -> Optional[datetime]:
        """檢查表格最後修改時間"""
        try:
            table = self.client.get_table(table_id)
            return table.modified
        except Exception as e:
            logger.error(f"❌ 檢查表格 {table_id} 失敗: {e}")
            return None

    def estimate_copy_cost(self, source_table: str) -> float:
        """估算複製成本"""
        try:
            table = self.client.get_table(source_table)
            bytes_size = table.num_bytes
            tb_size = bytes_size / (1024**4)
            cost = tb_size * self.cost_per_tb

            logger.info(f"📊 表格大小: {bytes_size:,} bytes ({tb_size:.4f} TB)")
            logger.info(f"💰 預估複製成本: ${cost:.4f} USD")

            return cost

        except Exception as e:
            logger.error(f"❌ 成本估算失敗: {e}")
            return float('inf')

    def check_replication_needed(self, mode: str) -> Tuple[bool, str]:
        """檢查是否需要複製"""
        tables = self.tables[mode]
        source_table = tables["source"]
        target_table = tables["target"]

        logger.info(f"🔍 檢查 {mode} 模式複製需求...")

        # 檢查來源表格修改時間
        source_modified = self.check_table_modified_time(source_table)
        if not source_modified:
            return False, f"無法取得來源表格 {source_table} 的修改時間"

        # 檢查目標表格修改時間
        target_modified = self.check_table_modified_time(target_table)
        if not target_modified:
            logger.warning(f"目標表格 {target_table} 不存在或無法存取，將進行完整複製")
            return True, "目標表格不存在，需要初始複製"

        # 比較修改時間
        if source_modified > target_modified:
            time_diff = source_modified - target_modified
            return True, f"來源資料較新 (差異: {time_diff})"
        else:
            return False, f"目標資料已是最新 (來源: {source_modified}, 目標: {target_modified})"

    def execute_replication(self, mode: str, force: bool = False) -> Dict[str, Any]:
        """執行資料複製"""
        tables = self.tables[mode]
        source_table = tables["source"]
        target_table = tables["target"]

        result = {
            "mode": mode,
            "source_table": source_table,
            "target_table": target_table,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "success": False,
            "error": None,
            "stats": {}
        }

        try:
            logger.info(f"🚀 開始執行 {mode} 資料複製...")
            logger.info(f"📥 來源: {source_table}")
            logger.info(f"📤 目標: {target_table}")

            # 檢查是否需要複製
            if not force:
                need_copy, reason = self.check_replication_needed(mode)
                if not need_copy:
                    logger.info(f"⏭️ 跳過複製: {reason}")
                    result["success"] = True
                    result["skipped"] = True
                    result["reason"] = reason
                    return result
                else:
                    logger.info(f"✅ 需要複製: {reason}")

            # 估算成本
            cost = self.estimate_copy_cost(source_table)
            if cost > self.max_copy_cost_usd:
                error_msg = f"複製成本 ${cost:.4f} 超過限制 ${self.max_copy_cost_usd}"
                logger.error(f"❌ {error_msg}")
                result["error"] = error_msg
                return result

            result["stats"]["estimated_cost_usd"] = cost

            # 執行複製
            logger.info("🔄 開始複製資料...")

            copy_config = bigquery.CopyJobConfig(
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
                labels={
                    "source": "carrefour_data_replication",
                    "mode": mode,
                    "automated": "true"
                }
            )

            copy_job = self.client.copy_table(
                source_table,
                target_table,
                job_config=copy_config
            )

            # 等待完成
            copy_job.result()

            # 檢查結果
            target_table_obj = self.client.get_table(target_table)

            result["success"] = True
            result["stats"].update({
                "rows_copied": target_table_obj.num_rows,
                "bytes_copied": target_table_obj.num_bytes,
                "job_id": copy_job.job_id
            })

            logger.info(f"✅ 複製完成!")
            logger.info(f"📊 複製筆數: {target_table_obj.num_rows:,}")
            logger.info(f"📦 複製大小: {target_table_obj.num_bytes:,} bytes")
            logger.info(f"🆔 作業ID: {copy_job.job_id}")

        except Exception as e:
            error_msg = f"複製失敗: {e}"
            logger.error(f"❌ {error_msg}")
            result["error"] = error_msg

        finally:
            result["end_time"] = datetime.now(timezone.utc).isoformat()

        return result

    def generate_report(self, results: list) -> str:
        """生成複製報告"""
        report = []
        report.append("=" * 80)
        report.append("🏪 家樂福資料複製執行報告")
        report.append("=" * 80)
        report.append(f"📅 執行時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        total_cost = 0
        total_rows = 0

        for result in results:
            mode = result["mode"]
            report.append(f"📊 {mode.upper()} 模式複製結果")
            report.append("-" * 60)

            if result["success"]:
                if result.get("skipped"):
                    report.append(f"⏭️ 狀態: 跳過複製")
                    report.append(f"📝 原因: {result.get('reason', 'N/A')}")
                else:
                    stats = result["stats"]
                    report.append(f"✅ 狀態: 複製成功")
                    report.append(f"📈 複製筆數: {stats.get('rows_copied', 0):,}")
                    report.append(f"💰 預估成本: ${stats.get('estimated_cost_usd', 0):.4f} USD")
                    report.append(f"🆔 作業ID: {stats.get('job_id', 'N/A')}")

                    total_cost += stats.get('estimated_cost_usd', 0)
                    total_rows += stats.get('rows_copied', 0)
            else:
                report.append(f"❌ 狀態: 複製失敗")
                report.append(f"📝 錯誤: {result.get('error', 'N/A')}")

            report.append(f"📥 來源: {result['source_table']}")
            report.append(f"📤 目標: {result['target_table']}")
            report.append("")

        # 總結
        report.append("📋 執行總結")
        report.append("-" * 40)
        report.append(f"💰 總預估成本: ${total_cost:.4f} USD")
        report.append(f"📊 總複製筆數: {total_rows:,}")
        report.append(f"✅ 成功任務: {sum(1 for r in results if r['success'])}/{len(results)}")

        report.append("")
        report.append("=" * 80)
        return "\\n".join(report)

def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description="家樂福資料複製工具")
    parser.add_argument("--mode", choices=["daily", "monthly", "both"],
                       default="both", help="複製模式")
    parser.add_argument("--force", action="store_true",
                       help="強制複製，忽略時間檢查")
    parser.add_argument("--dry-run", action="store_true",
                       help="僅檢查狀態，不執行複製")

    args = parser.parse_args()

    # 初始化複製器
    replicator = CarrefourDataReplicator()

    if not replicator.authenticate():
        sys.exit(1)

    # 決定要執行的模式
    modes = ["daily", "monthly"] if args.mode == "both" else [args.mode]

    results = []

    for mode in modes:
        if args.dry_run:
            # DRY RUN 模式：只檢查狀態
            need_copy, reason = replicator.check_replication_needed(mode)
            logger.info(f"🔍 {mode.upper()} 模式檢查結果: {reason}")

            if need_copy:
                cost = replicator.estimate_copy_cost(replicator.tables[mode]["source"])
                logger.info(f"💰 預估複製成本: ${cost:.4f} USD")
        else:
            # 實際執行複製
            result = replicator.execute_replication(mode, force=args.force)
            results.append(result)

    # 生成報告
    if results:
        report = replicator.generate_report(results)
        print("\\n" + report)

        # 儲存報告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"carrefour_replication_report_{timestamp}.txt"

        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"📄 報告已儲存至: {report_filename}")
        except Exception as e:
            logger.warning(f"⚠️ 報告儲存失敗: {e}")

if __name__ == "__main__":
    main()
