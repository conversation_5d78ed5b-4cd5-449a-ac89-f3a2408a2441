#!/usr/bin/env python3
"""
家樂福離線事件資料驗證腳本 (v3) - 符合規格文件要求

使用 <EMAIL> 權限
檢視和驗證 tw-eagle-prod.rmn_tagtoo.offline_transaction_day 資料

完全符合 carrefour-validation-spec.md 中定義的驗證要求：
1. 權限驗證
2. 資料結構驗證
3. 資料內容驗證
4. 資料量分析

Author: AI Assistant
Date: 2025-08-15
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

# Google Cloud 相關
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

# 資料處理
import pandas as pd
import numpy as np

class CarrefourDataValidatorV3:
    """家樂福離線事件資料驗證器 (v3) - 符合規格文件要求"""

    def __init__(self, project_id: str = "tw-eagle-prod"):
        self.project_id = project_id
        self.dataset_id = "rmn_tagtoo"
        self.table_id = "offline_transaction_day"
        self.service_account = "<EMAIL>"
        self.client = None
        self.validation_results = {
            'permissions': {},
            'schema': {},
            'content': {},
            'data_volume': {},
            'recommendations': []
        }

    def authenticate(self) -> bool:
        """使用 Application Default Credentials 進行身份驗證"""
        try:
            # 使用預設認證 (應該已透過 setup_auth.sh 設定為 Service Account Impersonation)
            credentials, project = default()
            print(f"✅ 認證成功！使用專案: {project}")
            print(f"📋 預期使用 Service Account: {self.service_account}")

            # 初始化 BigQuery 客戶端
            self.client = bigquery.Client(
                project=self.project_id,
                credentials=credentials
            )

            return True

        except DefaultCredentialsError as e:
            print(f"❌ 認證失敗: {e}")
            print("請先執行認證設定腳本: ./setup_auth.sh")
            return False
        except Exception as e:
            print(f"❌ 初始化客戶端失敗: {e}")
            return False

    def validate_permissions(self) -> Dict[str, Any]:
        """1. 權限驗證 - 符合規格文件要求"""
        print("\n🔐 執行權限驗證...")
        permissions = {
            'adc_configured': False,
            'bigquery_access': False,
            'table_access': False,
            'service_account_verified': False
        }

        try:
            # 檢查 BigQuery 資料集存取權限
            dataset_ref = self.client.dataset(self.dataset_id)
            dataset = self.client.get_dataset(dataset_ref)
            permissions['bigquery_access'] = True
            print(f"✅ BigQuery 資料集存取權限正常: {self.dataset_id}")

            # 檢查表格存取權限
            table_ref = dataset_ref.table(self.table_id)
            table = self.client.get_table(table_ref)
            permissions['table_access'] = True
            print(f"✅ BigQuery 表格存取權限正常: {self.table_id}")

            # 驗證 ADC 配置
            permissions['adc_configured'] = True
            print(f"✅ Application Default Credentials 配置正常")

            # 驗證 Service Account (透過成功存取來間接驗證)
            permissions['service_account_verified'] = True
            print(f"✅ Service Account 權限驗證成功")

        except Exception as e:
            print(f"❌ 權限驗證失敗: {e}")
            print("請檢查:")
            print("  1. 是否已執行 ./setup_auth.sh")
            print("  2. Service Account 是否具有 BigQuery 讀取權限")
            print("  3. 是否具有 Service Account Token Creator 權限")

        self.validation_results['permissions'] = permissions
        return permissions

    def validate_schema(self) -> Dict[str, Any]:
        """2. 資料結構驗證 - 符合規格文件要求"""
        print("\n📋 執行資料結構驗證...")
        schema_info = {}

        try:
            table_ref = self.client.dataset(self.dataset_id).table(self.table_id)
            table = self.client.get_table(table_ref)

            schema_info = {
                'table_id': table.table_id,
                'dataset_id': table.dataset_id,
                'project_id': table.project,
                'created': table.created.isoformat() if table.created else None,
                'modified': table.modified.isoformat() if table.modified else None,
                'num_rows': table.num_rows,
                'num_bytes': table.num_bytes,
                'schema_fields': [],
                'field_count': len(table.schema),
                'validation_status': 'success'
            }

            # 檢視表格結構（Schema）
            for field in table.schema:
                field_info = {
                    'name': field.name,
                    'type': field.field_type,
                    'mode': field.mode,
                    'description': field.description or 'No description'
                }
                schema_info['schema_fields'].append(field_info)

            print(f"✅ 表格結構檢視完成")
            print(f"📊 欄位數量: {schema_info['field_count']}")
            print(f"📊 總行數: {schema_info['num_rows']:,}")
            print(f"📊 總大小: {schema_info['num_bytes']:,} bytes")

        except Exception as e:
            print(f"❌ 資料結構驗證失敗: {e}")
            schema_info['validation_status'] = 'failed'
            schema_info['error'] = str(e)

        self.validation_results['schema'] = schema_info
        return schema_info

    def estimate_query_cost(self, query: str) -> Dict[str, Any]:
        """估算查詢成本 - 遵循規格文件的成本控制原則"""
        try:
            # 使用 dry_run 估算查詢成本
            job_config = bigquery.QueryJobConfig(dry_run=True)
            query_job = self.client.query(query, job_config=job_config)

            # 計算成本（假設 $5/TB）
            bytes_processed = query_job.total_bytes_processed
            cost_usd = (bytes_processed / (1024**4)) * 5  # 轉換為 TB 並計算成本

            return {
                'bytes_processed': bytes_processed,
                'cost_usd': cost_usd,
                'query_valid': True,
                'cost_acceptable': cost_usd <= 10.0  # 規格文件要求：超過 $10 USD 需確認
            }

        except Exception as e:
            return {
                'bytes_processed': 0,
                'cost_usd': 0,
                'query_valid': False,
                'cost_acceptable': False,
                'error': str(e)
            }

    def validate_content(self) -> Dict[str, Any]:
        """3. 資料內容驗證 - 符合規格文件要求"""
        print("\n📊 執行資料內容驗證...")
        content_info = {}

        try:
            # 確認資料時間範圍（D-62 ~ D-3）
            date_range_query = f"""
            SELECT
                MIN(DATE(transaction_date)) as min_date,
                MAX(DATE(transaction_date)) as max_date,
                COUNT(*) as total_rows,
                COUNT(DISTINCT DATE(transaction_date)) as unique_dates
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            """

            # 估算成本
            cost_info = self.estimate_query_cost(date_range_query)
            print(f"📊 查詢成本估算: {cost_info['bytes_processed']:,} bytes (約 ${cost_info['cost_usd']:.4f})")

            if not cost_info['query_valid']:
                print(f"❌ 查詢成本估算失敗: {cost_info.get('error', '未知錯誤')}")
                content_info['validation_status'] = 'failed'
                content_info['error'] = cost_info.get('error', '查詢成本估算失敗')
                self.validation_results['content'] = content_info
                return content_info

            if not cost_info['cost_acceptable']:
                print(f"⚠️  查詢成本較高 (${cost_info['cost_usd']:.4f})，超過 $10 USD 限制")
                if hasattr(self, 'auto_approve_high_cost') and self.auto_approve_high_cost:
                    print("🤖 自動批准高成本查詢（CI/CD 模式）")
                else:
                    response = input("是否繼續執行查詢？(y/N): ")
                    if response.lower() != 'y':
                        content_info['validation_status'] = 'skipped_high_cost'
                        self.validation_results['content'] = content_info
                        return content_info

            # 執行查詢
            result = self.client.query(date_range_query).to_dataframe()

            if not result.empty:
                row = result.iloc[0]
                min_date = row['min_date']
                max_date = row['max_date']

                # 計算預期的時間範圍 (D-62 ~ D-3)
                today = datetime.now().date()
                expected_min = today - timedelta(days=62)
                expected_max = today - timedelta(days=3)

                content_info = {
                    'min_date': str(min_date),
                    'max_date': str(max_date),
                    'total_rows': int(row['total_rows']),
                    'unique_dates': int(row['unique_dates']),
                    'expected_min_date': str(expected_min),
                    'expected_max_date': str(expected_max),
                    'date_range_valid': expected_min <= min_date <= max_date <= expected_max,
                    'validation_status': 'success'
                }

                print(f"✅ 資料時間範圍: {min_date} ~ {max_date}")
                print(f"📅 預期範圍: {expected_min} ~ {expected_max}")
                print(f"✅ 總行數: {content_info['total_rows']:,}")
                print(f"✅ 唯一日期數: {content_info['unique_dates']}")

                if content_info['date_range_valid']:
                    print(f"✅ 資料時間範圍符合規格要求 (D-62 ~ D-3)")
                else:
                    print(f"⚠️  資料時間範圍不符合規格要求")

        except Exception as e:
            print(f"❌ 資料內容驗證失敗: {e}")
            content_info['validation_status'] = 'failed'
            content_info['error'] = str(e)

        self.validation_results['content'] = content_info
        return content_info

    def validate_data_volume(self) -> Dict[str, Any]:
        """4. 資料量分析 - 符合規格文件要求"""
        print("\n📈 執行資料量分析...")
        volume_info = {}

        try:
            # 統計每日資料筆數
            daily_stats_query = f"""
            SELECT
                DATE(transaction_date) as date,
                COUNT(*) as daily_count,
                COUNT(DISTINCT user_id) as unique_users
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            GROUP BY DATE(transaction_date)
            ORDER BY date DESC
            LIMIT 30
            """

            # 估算成本
            cost_info = self.estimate_query_cost(daily_stats_query)
            print(f"📊 查詢成本估算: {cost_info['bytes_processed']:,} bytes (約 ${cost_info['cost_usd']:.4f})")

            if not cost_info['query_valid']:
                print(f"❌ 查詢成本估算失敗: {cost_info.get('error', '未知錯誤')}")
                volume_info['validation_status'] = 'failed'
                volume_info['error'] = cost_info.get('error', '查詢成本估算失敗')
            elif not cost_info['cost_acceptable']:
                print(f"⚠️  查詢成本較高，跳過詳細分析")
                volume_info['validation_status'] = 'skipped_high_cost'
            else:
                # 執行查詢
                result = self.client.query(daily_stats_query).to_dataframe()

                if not result.empty:
                    volume_info = {
                        'daily_stats': result.to_dict('records'),
                        'avg_daily_count': float(result['daily_count'].mean()),
                        'max_daily_count': int(result['daily_count'].max()),
                        'min_daily_count': int(result['daily_count'].min()),
                        'total_days_analyzed': len(result),
                        'validation_status': 'success'
                    }

                    print(f"✅ 分析了最近 {volume_info['total_days_analyzed']} 天的資料")
                    print(f"📊 平均每日資料筆數: {volume_info['avg_daily_count']:,.0f}")
                    print(f"📊 最大每日資料筆數: {volume_info['max_daily_count']:,}")
                    print(f"📊 最小每日資料筆數: {volume_info['min_daily_count']:,}")

        except Exception as e:
            print(f"❌ 資料量分析失敗: {e}")
            volume_info['validation_status'] = 'failed'
            volume_info['error'] = str(e)

        self.validation_results['data_volume'] = volume_info
        return volume_info

    def generate_recommendations(self) -> List[str]:
        """生成建議報告 - 符合規格文件要求"""
        recommendations = []

        # 基於驗證結果生成建議
        permissions = self.validation_results.get('permissions', {})
        schema = self.validation_results.get('schema', {})
        content = self.validation_results.get('content', {})
        volume = self.validation_results.get('data_volume', {})

        # 權限相關建議
        if permissions.get('service_account_verified'):
            recommendations.append("✅ Service Account 權限驗證通過，可以正常存取資料")
        else:
            recommendations.append("❌ Service Account 權限不足，需要檢查 IAM 設定")

        # 資料結構相關建議
        if schema.get('validation_status') == 'success':
            recommendations.append("✅ 表格結構完整，建議進行資料品質分析")
            if schema.get('num_rows', 0) > 0:
                recommendations.append("✅ 資料存在，可以進行進一步分析")
            else:
                recommendations.append("⚠️  資料為空，需要檢查資料來源")
        else:
            recommendations.append("❌ 無法獲取表格結構，需要檢查權限設定")

        # 資料內容相關建議
        if content.get('validation_status') == 'success':
            if content.get('date_range_valid'):
                recommendations.append("✅ 資料時間範圍符合規格要求 (D-62 ~ D-3)")
                recommendations.append("✅ 資料規格確認完成，可以通知對方產出 monthly 資料")
            else:
                recommendations.append("⚠️  資料時間範圍不符合規格要求，需要與對方確認")
        elif content.get('validation_status') == 'skipped_high_cost':
            recommendations.append("⚠️  因成本考量跳過部分驗證，建議在必要時進行完整驗證")

        # 資料量相關建議
        if volume.get('validation_status') == 'success':
            avg_count = volume.get('avg_daily_count', 0)
            if avg_count > 1000:
                recommendations.append("✅ 資料量充足，適合進行分析")
            else:
                recommendations.append("⚠️  資料量較少，需要確認是否正常")

        # 下一步建議
        if all([
            permissions.get('service_account_verified'),
            schema.get('validation_status') == 'success',
            content.get('date_range_valid')
        ]):
            recommendations.append("🎉 所有驗證項目通過，建議進行下一步：將資料複製到我們的 BigQuery table")
        else:
            recommendations.append("⚠️  部分驗證項目未通過，建議先解決問題再進行下一步")

        self.validation_results['recommendations'] = recommendations
        return recommendations

    def generate_comprehensive_report(self) -> str:
        """生成符合規格文件要求的完整驗證報告"""
        report = []
        report.append("=" * 80)
        report.append("家樂福離線事件資料驗證報告 (v3)")
        report.append("=" * 80)
        report.append(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"專案 ID: {self.project_id}")
        report.append(f"資料集: {self.dataset_id}")
        report.append(f"表格: {self.table_id}")
        report.append(f"Service Account: {self.service_account}")
        report.append("")

        # 1. 權限驗證報告
        report.append("1. 權限驗證報告")
        report.append("-" * 40)
        permissions = self.validation_results.get('permissions', {})
        for perm, status in permissions.items():
            status_text = "✅ 正常" if status else "❌ 失敗"
            perm_name = {
                'adc_configured': 'Application Default Credentials 配置',
                'bigquery_access': 'BigQuery 存取權限',
                'table_access': '表格存取權限',
                'service_account_verified': 'Service Account 驗證'
            }.get(perm, perm)
            report.append(f"  {perm_name}: {status_text}")
        report.append("")

        # 2. 資料結構報告
        report.append("2. 資料結構報告")
        report.append("-" * 40)
        schema = self.validation_results.get('schema', {})
        if schema.get('validation_status') == 'success':
            report.append(f"表格 ID: {schema.get('table_id', 'N/A')}")
            report.append(f"建立時間: {schema.get('created', 'N/A')}")
            report.append(f"修改時間: {schema.get('modified', 'N/A')}")
            report.append(f"總行數: {schema.get('num_rows', 'N/A'):,}")
            report.append(f"總大小: {schema.get('num_bytes', 'N/A'):,} bytes")
            report.append(f"欄位數量: {schema.get('field_count', 'N/A')}")
            report.append("")
            report.append("欄位結構:")
            for field in schema.get('schema_fields', []):
                mode = f" ({field['mode']})" if field['mode'] != 'NULLABLE' else ""
                report.append(f"  - {field['name']}: {field['type']}{mode}")
                if field.get('description') and field['description'] != 'No description':
                    report.append(f"    說明: {field['description']}")
        else:
            report.append("❌ 資料結構驗證失敗")
            if 'error' in schema:
                report.append(f"錯誤: {schema['error']}")
        report.append("")

        # 3. 資料內容報告
        report.append("3. 資料內容報告")
        report.append("-" * 40)
        content = self.validation_results.get('content', {})
        if content.get('validation_status') == 'success':
            report.append(f"資料時間範圍: {content.get('min_date', 'N/A')} ~ {content.get('max_date', 'N/A')}")
            report.append(f"預期時間範圍: {content.get('expected_min_date', 'N/A')} ~ {content.get('expected_max_date', 'N/A')}")
            report.append(f"時間範圍符合規格: {'✅ 是' if content.get('date_range_valid') else '❌ 否'}")
            report.append(f"總行數: {content.get('total_rows', 'N/A'):,}")
            report.append(f"唯一日期數: {content.get('unique_dates', 'N/A')}")
        elif content.get('validation_status') == 'skipped_high_cost':
            report.append("⚠️  因成本考量跳過驗證")
        else:
            report.append("❌ 資料內容驗證失敗")
            if 'error' in content:
                report.append(f"錯誤: {content['error']}")
        report.append("")

        # 4. 資料量報告
        report.append("4. 資料量報告")
        report.append("-" * 40)
        volume = self.validation_results.get('data_volume', {})
        if volume.get('validation_status') == 'success':
            report.append(f"分析天數: {volume.get('total_days_analyzed', 'N/A')}")
            report.append(f"平均每日資料筆數: {volume.get('avg_daily_count', 'N/A'):,.0f}")
            report.append(f"最大每日資料筆數: {volume.get('max_daily_count', 'N/A'):,}")
            report.append(f"最小每日資料筆數: {volume.get('min_daily_count', 'N/A'):,}")
        elif volume.get('validation_status') == 'skipped_high_cost':
            report.append("⚠️  因成本考量跳過分析")
        else:
            report.append("❌ 資料量分析失敗")
            if 'error' in volume:
                report.append(f"錯誤: {volume['error']}")
        report.append("")

        # 5. 建議報告
        report.append("5. 建議報告")
        report.append("-" * 40)
        recommendations = self.validation_results.get('recommendations', [])
        for rec in recommendations:
            report.append(f"  {rec}")
        report.append("")

        report.append("=" * 80)
        return "\n".join(report)

    def run_full_validation(self) -> bool:
        """執行完整的驗證流程"""
        print("🚀 開始家樂福離線事件資料驗證 (v3)...")
        print("=" * 60)

        # 進行身份驗證
        if not self.authenticate():
            print("❌ 身份驗證失敗，程式結束")
            return False

        print("✅ 身份驗證成功")

        # 執行四大類驗證
        self.validate_permissions()
        self.validate_schema()
        self.validate_content()
        self.validate_data_volume()

        # 生成建議
        self.generate_recommendations()

        return True

def main():
    """主函數"""
    # 建立驗證器
    validator = CarrefourDataValidatorV3()

    # 執行完整驗證
    if not validator.run_full_validation():
        sys.exit(1)

    # 生成並顯示報告
    report = validator.generate_comprehensive_report()
    print("\n" + report)

    # 儲存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"carrefour_validation_report_v3_{timestamp}.txt"

    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 報告已儲存至: {report_filename}")
    except Exception as e:
        print(f"⚠️  報告儲存失敗: {e}")

    # 處理資料樣本顯示（支援非互動模式）
    print("")
    show_samples = os.getenv('CARREFOUR_SHOW_SAMPLES', '').lower() == 'true'
    auto_mode = os.getenv('CARREFOUR_AUTO_MODE', '').lower() == 'true'

    if not auto_mode and not show_samples:
        response = input("是否要查看資料樣本？(y/N): ")
        show_samples = response.lower() == 'y'

    if show_samples:
        print("📊 獲取資料樣本...")
        try:
            sample_query = f"""
            SELECT *
            FROM `{validator.project_id}.{validator.dataset_id}.{validator.table_id}`
            LIMIT 5
            """

            cost_info = validator.estimate_query_cost(sample_query)
            if cost_info['cost_acceptable']:
                sample_df = validator.client.query(sample_query).to_dataframe()
                if not sample_df.empty:
                    print("資料樣本:")
                    print(sample_df.to_string(index=False))
                else:
                    print("無法獲取資料樣本")
            else:
                print("樣本查詢成本過高，跳過")
        except Exception as e:
            print(f"獲取資料樣本失敗: {e}")

if __name__ == "__main__":
    main()
