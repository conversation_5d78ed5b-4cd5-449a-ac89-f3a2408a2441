#!/usr/bin/env python3
"""
家樂福離線交易資料每日增量合併自動化腳本

功能：
1. 從 carrefour_offline_transaction_day 表增量更新到 carrefour_offline_transaction 表
2. 動態檢查重疊資料，避免重複插入
3. 支援回填缺失的歷史資料
4. 提供完整的執行報告和驗證

設計原則：
- 使用 transaction_id 作為唯一性檢查
- 支援 dry-run 模式進行安全測試
- 動態計算處理日期範圍
- 提供詳細的資料分析和驗證報告
"""

import os
import sys
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import json

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CarrefourDailyMerger:
    """家樂福每日資料合併器"""

    def __init__(self,
                 project_id: str = "tagtoo-tracking",
                 source_dataset: str = "event_prod",
                 day_table: str = "carrefour_offline_transaction_day",
                 main_table: str = "carrefour_offline_transaction"):
        """
        初始化合併器

        Args:
            project_id: BigQuery 專案 ID
            source_dataset: 資料集名稱
            day_table: 每日資料表名稱
            main_table: 主要資料表名稱
        """
        self.project_id = project_id
        self.source_dataset = source_dataset
        self.day_table = day_table
        self.main_table = main_table
        self.client = bigquery.Client(project=project_id)

        # 完整表名
        self.day_table_full = f"{project_id}.{source_dataset}.{day_table}"
        self.main_table_full = f"{project_id}.{source_dataset}.{main_table}"

        self.execution_report = {
            "execution_time": datetime.now(timezone.utc).isoformat(),
            "parameters": {},
            "validation": {},
            "results": {},
            "errors": []
        }

    def calculate_target_date(self, target_date: Optional[str] = None) -> datetime:
        """
        計算目標處理日期

        Args:
            target_date: 目標日期字串 ('YYYY-MM-DD' 或 'auto')

        Returns:
            目標日期的 datetime 物件
        """
        taiwan_tz = timezone(timedelta(hours=8))
        now_taiwan = datetime.now(taiwan_tz)

        if target_date is None or target_date.lower() == 'auto':
            # 自動模式：使用昨天的日期
            target = now_taiwan - timedelta(days=1)
        else:
            # 手動指定日期
            target = datetime.strptime(target_date, '%Y-%m-%d').replace(tzinfo=taiwan_tz)

        return target.replace(hour=0, minute=0, second=0, microsecond=0)

    def check_data_overlap(self, start_date: str, end_date: str) -> List[Dict]:
        """
        檢查資料重疊情況

        Args:
            start_date: 開始日期
            end_date: 結束日期

        Returns:
            重疊資料分析結果
        """
        query = f"""
        WITH main_daily AS (
            SELECT
                DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
                COUNT(*) as existing_records_in_main
            FROM `{self.main_table_full}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
            GROUP BY DATE(TIMESTAMP_SECONDS(event_times))
        ),
        day_daily AS (
            SELECT
                DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
                COUNT(*) as available_records_in_day
            FROM `{self.day_table_full}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
            GROUP BY DATE(TIMESTAMP_SECONDS(event_times))
        )
        SELECT
            COALESCE(main_daily.transaction_date, day_daily.transaction_date) as transaction_date,
            COALESCE(main_daily.existing_records_in_main, 0) as existing_records_in_main,
            COALESCE(day_daily.available_records_in_day, 0) as available_records_in_day
        FROM main_daily
        FULL OUTER JOIN day_daily
          ON main_daily.transaction_date = day_daily.transaction_date
        ORDER BY transaction_date
        """

        try:
            results = self.client.query(query).result()
            overlap_data = []

            for row in results:
                overlap_data.append({
                    "transaction_date": row.transaction_date.isoformat(),
                    "existing_records_in_main": row.existing_records_in_main,
                    "available_records_in_day": row.available_records_in_day,
                    "missing_records": row.available_records_in_day - row.existing_records_in_main,
                    "completion_percentage": round((row.existing_records_in_main / row.available_records_in_day * 100), 2) if row.available_records_in_day > 0 else 0
                })

            return overlap_data

        except Exception as e:
            logger.error(f"資料重疊檢查失敗: {e}")
            self.execution_report["errors"].append(f"overlap_check_failed: {str(e)}")
            return []

    def identify_new_records(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        識別需要新增的記錄

        Args:
            start_date: 開始日期
            end_date: 結束日期

        Returns:
            新記錄統計資訊
        """
        # 計算新記錄統計
        stats_query = f"""
        SELECT
            COUNT(*) as new_records_count,
            COUNT(DISTINCT DATE(TIMESTAMP_SECONDS(day.event_times))) as new_days_count,
            COUNT(DISTINCT day.ph_id) as new_unique_ph_ids,
            MIN(DATE(TIMESTAMP_SECONDS(day.event_times))) as earliest_date,
            MAX(DATE(TIMESTAMP_SECONDS(day.event_times))) as latest_date
        FROM `{self.day_table_full}` day
        LEFT JOIN `{self.main_table_full}` main
          ON day.transaction_id = main.transaction_id
        WHERE DATE(TIMESTAMP_SECONDS(day.event_times)) BETWEEN '{start_date}' AND '{end_date}'
          AND main.transaction_id IS NULL
        """

        try:
            result = list(self.client.query(stats_query).result())[0]

            new_records_stats = {
                "new_records_count": result.new_records_count,
                "new_days_count": result.new_days_count,
                "new_unique_ph_ids": result.new_unique_ph_ids,
                "earliest_date": result.earliest_date.isoformat() if result.earliest_date else None,
                "latest_date": result.latest_date.isoformat() if result.latest_date else None
            }

            # 每日詳細分析
            daily_analysis_query = f"""
            SELECT
                DATE(TIMESTAMP_SECONDS(day.event_times)) as transaction_date,
                COUNT(*) as new_records_to_add,
                COUNT(DISTINCT day.ph_id) as unique_ph_ids,
                MIN(day.event_times) as earliest_timestamp,
                MAX(day.event_times) as latest_timestamp
            FROM `{self.day_table_full}` day
            LEFT JOIN `{self.main_table_full}` main
              ON day.transaction_id = main.transaction_id
            WHERE DATE(TIMESTAMP_SECONDS(day.event_times)) BETWEEN '{start_date}' AND '{end_date}'
              AND main.transaction_id IS NULL
            GROUP BY DATE(TIMESTAMP_SECONDS(day.event_times))
            ORDER BY transaction_date
            """

            daily_results = self.client.query(daily_analysis_query).result()
            daily_analysis = []

            for row in daily_results:
                daily_analysis.append({
                    "transaction_date": row.transaction_date.isoformat(),
                    "new_records_to_add": row.new_records_to_add,
                    "unique_ph_ids": row.unique_ph_ids,
                    "earliest_timestamp": row.earliest_timestamp,
                    "latest_timestamp": row.latest_timestamp
                })

            new_records_stats["daily_analysis"] = daily_analysis
            return new_records_stats

        except Exception as e:
            logger.error(f"新記錄識別失敗: {e}")
            self.execution_report["errors"].append(f"new_records_identification_failed: {str(e)}")
            return {}

    def execute_merge(self,
                     target_date: Optional[str] = None,
                     lookback_days: int = 3,
                     dry_run: bool = True,
                     max_cost_usd: float = 5.0) -> Dict[str, Any]:
        """
        執行增量合併

        Args:
            target_date: 目標日期 ('YYYY-MM-DD' 或 'auto')
            lookback_days: 回溯天數
            dry_run: 是否為測試模式
            max_cost_usd: 最大成本限制 (USD)

        Returns:
            執行結果報告
        """
        # 記錄執行參數
        self.execution_report["parameters"] = {
            "target_date": target_date,
            "lookback_days": lookback_days,
            "dry_run": dry_run,
            "max_cost_usd": max_cost_usd
        }

        try:
            # 1. 計算處理日期範圍
            target_dt = self.calculate_target_date(target_date)
            start_date = target_dt - timedelta(days=lookback_days)
            end_date = target_dt

            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            logger.info(f"處理日期範圍: {start_date_str} 到 {end_date_str}")

            # 2. 資料重疊檢查
            overlap_analysis = self.check_data_overlap(start_date_str, end_date_str)
            self.execution_report["validation"]["overlap_analysis"] = overlap_analysis

            # 3. 新記錄識別
            new_records_analysis = self.identify_new_records(start_date_str, end_date_str)
            self.execution_report["validation"]["new_records_analysis"] = new_records_analysis

            # 4. 成本估算
            if new_records_analysis.get("new_records_count", 0) > 0:
                estimated_cost = self.estimate_insertion_cost(new_records_analysis["new_records_count"])
                self.execution_report["validation"]["estimated_cost_usd"] = estimated_cost

                if estimated_cost > max_cost_usd:
                    error_msg = f"預估成本 ${estimated_cost:.2f} 超過限制 ${max_cost_usd:.2f}"
                    logger.error(error_msg)
                    self.execution_report["errors"].append(error_msg)
                    return self.execution_report

            # 5. 執行實際合併 (非 dry_run 模式)
            if not dry_run and new_records_analysis.get("new_records_count", 0) > 0:
                merge_result = self.perform_actual_merge(start_date_str, end_date_str)
                self.execution_report["results"]["merge_execution"] = merge_result
            else:
                self.execution_report["results"]["merge_execution"] = {
                    "status": "skipped",
                    "reason": "dry_run_mode" if dry_run else "no_new_records"
                }

            # 6. 生成執行摘要
            self.execution_report["results"]["summary"] = {
                "processing_date_range": f"{start_date_str} 到 {end_date_str}",
                "new_records_found": new_records_analysis.get("new_records_count", 0),
                "days_affected": new_records_analysis.get("new_days_count", 0),
                "unique_ph_ids": new_records_analysis.get("new_unique_ph_ids", 0),
                "overlap_days": len(overlap_analysis),
                "execution_mode": "dry_run" if dry_run else "live",
                "status": "completed" if not self.execution_report["errors"] else "completed_with_errors"
            }

            return self.execution_report

        except Exception as e:
            logger.error(f"合併執行失敗: {e}")
            self.execution_report["errors"].append(f"execution_failed: {str(e)}")
            return self.execution_report

    def estimate_insertion_cost(self, record_count: int) -> float:
        """
        估算插入成本

        Args:
            record_count: 記錄數量

        Returns:
            預估成本 (USD)
        """
        # 基於經驗值：每100萬筆記錄約 $0.05
        return (record_count / 1000000) * 0.05

    def perform_actual_merge(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        執行實際的資料合併

        Args:
            start_date: 開始日期
            end_date: 結束日期

        Returns:
            合併執行結果
        """
        merge_query = f"""
        INSERT INTO `{self.main_table_full}`
        SELECT
            day.event_name,
            day.event_times,
            day.store_id,
            day.store_name,
            day.gender,
            day.member_id,
            day.ph_id,
            day.transaction_id,
            day.items,
            day.total_amount,
            day.payment_method,
            TO_HEX(day.ph_id) as ph_id_hex
        FROM `{self.day_table_full}` day
        LEFT JOIN `{self.main_table_full}` main
          ON day.transaction_id = main.transaction_id
        WHERE DATE(TIMESTAMP_SECONDS(day.event_times)) BETWEEN '{start_date}' AND '{end_date}'
          AND main.transaction_id IS NULL
        """

        try:
            job = self.client.query(merge_query)
            result = job.result()

            merge_result = {
                "status": "success",
                "rows_inserted": job.num_dml_affected_rows,
                "bytes_processed": job.total_bytes_processed,
                "job_id": job.job_id,
                "execution_time_seconds": (job.ended - job.started).total_seconds()
            }

            logger.info(f"成功插入 {job.num_dml_affected_rows} 筆記錄")
            return merge_result

        except Exception as e:
            logger.error(f"資料合併失敗: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }


def main():
    """主要執行函數"""
    import argparse

    parser = argparse.ArgumentParser(description='家樂福每日資料增量合併')
    parser.add_argument('--target-date', default='auto', help='目標日期 (YYYY-MM-DD 或 auto)')
    parser.add_argument('--lookback-days', type=int, default=3, help='回溯天數')
    parser.add_argument('--dry-run', action='store_true', help='測試模式')
    parser.add_argument('--max-cost', type=float, default=5.0, help='最大成本限制 (USD)')
    parser.add_argument('--output-file', help='輸出報告檔案路徑')

    args = parser.parse_args()

    # 執行合併
    merger = CarrefourDailyMerger()
    result = merger.execute_merge(
        target_date=args.target_date,
        lookback_days=args.lookback_days,
        dry_run=args.dry_run,
        max_cost_usd=args.max_cost
    )

    # 輸出結果
    if args.output_file:
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"執行報告已儲存至: {args.output_file}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
