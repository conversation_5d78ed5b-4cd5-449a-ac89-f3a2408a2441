#!/usr/bin/env python3
"""
家樂福 integrated_event 歷史資料回補腳本

此腳本用於回補歷史的家樂福線下交易資料到 integrated_event 表。

設計原則：
1. 支援指定日期範圍的回補
2. 分批處理避免查詢過大和超時
3. 自動跳過已存在的資料
4. 詳細的進度追蹤和錯誤處理
5. 成本控制和安全檢查

使用方式：
python historical_backfill_integrated_event.py --start-date 2025-09-01 --end-date 2025-09-16 --dry-run
"""

import os
import sys
import logging
import json
import argparse
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from carrefour_integrated_event_converter import CarrefourIntegratedEventConverter

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HistoricalBackfillManager:
    """歷史資料回補管理器"""

    def __init__(self,
                 project_id: str = "tagtoo-tracking",
                 source_dataset: str = "event_prod"):
        """
        初始化回補管理器

        Args:
            project_id: BigQuery 專案 ID
            source_dataset: 資料集名稱
        """
        self.project_id = project_id
        self.source_dataset = source_dataset
        self.converter = CarrefourIntegratedEventConverter(project_id, source_dataset)

        # 回補統計
        self.backfill_stats = {
            "start_time": datetime.now(timezone.utc),
            "total_days_planned": 0,
            "days_completed": 0,
            "days_failed": 0,
            "total_transactions_processed": 0,
            "total_cost_usd": 0.0,
            "failed_dates": [],
            "detailed_results": []
        }

    def validate_date_range(self, start_date: str, end_date: str) -> Tuple[datetime, datetime]:
        """
        驗證日期範圍

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)

        Returns:
            (開始日期物件, 結束日期物件)
        """
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            if start_dt > end_dt:
                raise ValueError("開始日期不能晚於結束日期")

            # 檢查日期範圍是否合理 (不超過90天)
            date_diff = (end_dt - start_dt).days + 1
            if date_diff > 90:
                raise ValueError(f"日期範圍過大 ({date_diff} 天)，建議不超過90天")

            logger.info(f"📅 日期範圍驗證通過: {start_date} ~ {end_date} ({date_diff} 天)")
            self.backfill_stats["total_days_planned"] = date_diff

            return start_dt, end_dt

        except ValueError as e:
            logger.error(f"日期格式或範圍錯誤: {e}")
            raise

    def get_available_data_dates(self, start_date: str, end_date: str) -> List[str]:
        """
        取得指定範圍內有資料的日期清單

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)

        Returns:
            有資料的日期清單
        """
        query = f"""
        SELECT DISTINCT DATE(TIMESTAMP_SECONDS(event_times)) as data_date
        FROM `{self.converter.tables["carrefour_offline"]}`
        WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN '{start_date}' AND '{end_date}'
          AND ph_id IS NOT NULL
          AND transaction_id IS NOT NULL
        ORDER BY data_date
        """

        try:
            result = self.converter.client.query(query).result()
            available_dates = [row.data_date.strftime('%Y-%m-%d') for row in result]

            logger.info(f"📊 指定範圍內有資料的日期: {len(available_dates)} 天")
            if available_dates:
                logger.info(f"  📅 最早: {available_dates[0]}")
                logger.info(f"  📅 最晚: {available_dates[-1]}")

            return available_dates

        except Exception as e:
            logger.error(f"取得可用資料日期失敗: {e}")
            raise

    def check_existing_data(self, target_date: str) -> Dict[str, Any]:
        """
        檢查指定日期的資料是否已存在於 integrated_event

        Args:
            target_date: 目標日期 (YYYY-MM-DD)

        Returns:
            現有資料統計
        """
        query = f"""
        SELECT
          COUNT(*) as existing_count,
          COUNT(DISTINCT permanent) as existing_users
        FROM `{self.converter.tables["integrated_event"]}`
        WHERE partner_source = 'carrefour_offline'
          AND DATE(event_time) = '{target_date}'
        """

        try:
            result = self.converter.client.query(query).result()
            stats = dict(list(result)[0])

            return {
                "date": target_date,
                "existing_transactions": stats["existing_count"],
                "existing_users": stats["existing_users"],
                "has_existing_data": stats["existing_count"] > 0
            }

        except Exception as e:
            logger.error(f"檢查現有資料失敗: {e}")
            raise

    def process_single_date(self,
                           target_date: str,
                           dry_run: bool = False,
                           max_cost_usd: float = 5.0,
                           skip_existing: bool = True) -> Dict[str, Any]:
        """
        處理單一日期的資料回補

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            dry_run: 是否僅模擬執行
            max_cost_usd: 最大允許成本
            skip_existing: 是否跳過已存在的資料

        Returns:
            處理結果
        """
        logger.info(f"📅 處理日期: {target_date}")

        try:
            # 檢查現有資料
            if skip_existing:
                existing_check = self.check_existing_data(target_date)
                if existing_check["has_existing_data"]:
                    logger.info(f"⏭️ 跳過日期 {target_date}: 已存在 {existing_check['existing_transactions']} 筆資料")
                    return {
                        "success": True,
                        "date": target_date,
                        "skipped": True,
                        "existing_transactions": existing_check["existing_transactions"],
                        "message": "已存在資料，跳過處理"
                    }

            # 建立該日期的轉換器查詢
            # 修改轉換器以支援特定日期查詢
            latest_data_date = target_date

            # 暫時修改轉換器的日期取得方法
            original_get_latest = self.converter.get_latest_data_date
            self.converter.get_latest_data_date = lambda: latest_data_date

            try:
                # 執行該日期的轉換
                result = self.converter.execute_conversion(
                    lookback_days=1,  # 單日處理
                    limit=None,       # 不限制筆數
                    dry_run=dry_run,
                    max_cost_usd=max_cost_usd
                )

                # 記錄統計
                if result.get("success"):
                    self.backfill_stats["days_completed"] += 1
                    if result.get("inserted_rows", 0) > 0:
                        self.backfill_stats["total_transactions_processed"] += result["inserted_rows"]
                    if result.get("estimated_cost"):
                        self.backfill_stats["total_cost_usd"] += result["estimated_cost"]
                else:
                    self.backfill_stats["days_failed"] += 1
                    self.backfill_stats["failed_dates"].append(target_date)

                result["date"] = target_date
                result["skipped"] = False
                return result

            finally:
                # 還原原始方法
                self.converter.get_latest_data_date = original_get_latest

        except Exception as e:
            logger.error(f"❌ 處理日期 {target_date} 失敗: {e}")
            self.backfill_stats["days_failed"] += 1
            self.backfill_stats["failed_dates"].append(target_date)

            return {
                "success": False,
                "date": target_date,
                "skipped": False,
                "error": str(e),
                "message": f"處理失敗: {str(e)}"
            }

    def execute_backfill(self,
                        start_date: str,
                        end_date: str,
                        dry_run: bool = False,
                        max_cost_per_day: float = 5.0,
                        max_total_cost: float = 50.0,
                        skip_existing: bool = True) -> Dict[str, Any]:
        """
        執行歷史資料回補

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            dry_run: 是否僅模擬執行
            max_cost_per_day: 單日最大成本
            max_total_cost: 總計最大成本
            skip_existing: 是否跳過已存在的資料

        Returns:
            回補執行結果
        """
        logger.info("🚀 開始執行家樂福 integrated_event 歷史資料回補...")
        logger.info(f"📅 日期範圍: {start_date} ~ {end_date}")
        logger.info(f"💰 成本限制: 單日 ${max_cost_per_day}, 總計 ${max_total_cost}")
        logger.info(f"🧪 模式: {'DRY RUN' if dry_run else '實際執行'}")

        try:
            # 1. 驗證日期範圍
            start_dt, end_dt = self.validate_date_range(start_date, end_date)

            # 2. 取得有資料的日期
            available_dates = self.get_available_data_dates(start_date, end_date)

            if not available_dates:
                logger.warning("⚠️ 指定範圍內沒有可處理的資料")
                return {
                    "success": True,
                    "message": "指定範圍內沒有可處理的資料",
                    "stats": self.backfill_stats
                }

            logger.info(f"📊 計劃處理 {len(available_dates)} 個日期")

            # 3. 逐日處理
            for i, date in enumerate(available_dates):
                logger.info(f"\n{'='*60}")
                logger.info(f"📅 處理進度: {i+1}/{len(available_dates)} - {date}")
                logger.info(f"{'='*60}")

                # 檢查總成本限制
                if self.backfill_stats["total_cost_usd"] >= max_total_cost:
                    logger.warning(f"⚠️ 達到總成本限制 ${max_total_cost}，停止處理")
                    break

                # 處理該日期
                daily_result = self.process_single_date(
                    target_date=date,
                    dry_run=dry_run,
                    max_cost_usd=max_cost_per_day,
                    skip_existing=skip_existing
                )

                # 記錄詳細結果
                self.backfill_stats["detailed_results"].append(daily_result)

                # 記錄日誌
                if daily_result.get("success"):
                    if daily_result.get("skipped"):
                        logger.info(f"⏭️ {date}: 跳過 (已存在 {daily_result.get('existing_transactions', 0)} 筆)")
                    else:
                        inserted = daily_result.get("inserted_rows", 0) or daily_result.get("would_insert_rows", 0)
                        cost = daily_result.get("estimated_cost", 0)
                        logger.info(f"✅ {date}: {inserted:,} 筆交易, ${cost:.4f} USD")
                else:
                    logger.error(f"❌ {date}: 失敗 - {daily_result.get('message', 'Unknown error')}")

            # 4. 計算最終統計
            self.backfill_stats["end_time"] = datetime.now(timezone.utc)
            execution_time = (self.backfill_stats["end_time"] - self.backfill_stats["start_time"]).total_seconds()
            self.backfill_stats["execution_time_seconds"] = execution_time

            # 輸出最終報告
            logger.info("\n" + "="*60)
            logger.info("📊 回補執行完成 - 最終統計")
            logger.info("="*60)
            logger.info(f"⏱️ 執行時間: {execution_time:.2f} 秒")
            logger.info(f"📅 計劃天數: {self.backfill_stats['total_days_planned']} 天")
            logger.info(f"✅ 完成天數: {self.backfill_stats['days_completed']} 天")
            logger.info(f"❌ 失敗天數: {self.backfill_stats['days_failed']} 天")
            logger.info(f"📝 處理交易: {self.backfill_stats['total_transactions_processed']:,} 筆")
            logger.info(f"💰 總成本: ${self.backfill_stats['total_cost_usd']:.4f} USD")

            if self.backfill_stats["failed_dates"]:
                logger.warning(f"⚠️ 失敗日期: {', '.join(self.backfill_stats['failed_dates'])}")

            return {
                "success": True,
                "message": "回補執行完成",
                "stats": self.backfill_stats
            }

        except Exception as e:
            logger.error(f"❌ 回補執行失敗: {e}")
            self.backfill_stats["end_time"] = datetime.now(timezone.utc)

            return {
                "success": False,
                "message": f"回補執行失敗: {str(e)}",
                "error": str(e),
                "stats": self.backfill_stats
            }

# 命令列介面
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='家樂福 integrated_event 歷史資料回補')
    parser.add_argument('--start-date', required=True, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', required=True, help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--dry-run', action='store_true', help='僅模擬執行，不實際寫入')
    parser.add_argument('--max-cost-per-day', type=float, default=5.0, help='單日最大成本 (USD)')
    parser.add_argument('--max-total-cost', type=float, default=50.0, help='總計最大成本 (USD)')
    parser.add_argument('--no-skip-existing', action='store_true', help='不跳過已存在的資料')
    parser.add_argument('--project-id', default='tagtoo-tracking', help='BigQuery 專案 ID')

    args = parser.parse_args()

    # 執行回補
    manager = HistoricalBackfillManager(project_id=args.project_id)
    result = manager.execute_backfill(
        start_date=args.start_date,
        end_date=args.end_date,
        dry_run=args.dry_run,
        max_cost_per_day=args.max_cost_per_day,
        max_total_cost=args.max_total_cost,
        skip_existing=not args.no_skip_existing
    )

    # 輸出結果
    print("\n" + "="*80)
    print("家樂福 integrated_event 歷史資料回補結果")
    print("="*80)
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))