#!/usr/bin/env python3
"""
家樂福離線交易資料詳細時間分佈分析

檢查 event_times 欄位的詳細分佈狀況，特別是異常的時間範圍
使用 <EMAIL> 權限

Author: AI Assistant
Date: 2025-09-03
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

class DetailedTimeDistributionAnalyzer:
    """詳細時間分佈分析器"""

    def __init__(self):
        self.project_id = "tw-eagle-prod"
        self.dataset_id = "rmn_tagtoo"
        self.tables = {
            "daily": "offline_transaction_day",
            "monthly": "offline_transaction_month"
        }
        self.client = None

    def authenticate(self) -> bool:
        """進行身份驗證"""
        try:
            credentials, project = default()
            self.client = bigquery.Client(project="tagtoo-tracking", credentials=credentials)

            print(f"✅ 身份驗證成功")
            print(f"📋 目標專案ID: {self.project_id}")
            print(f"💰 計費專案ID: tagtoo-tracking")
            return True

        except DefaultCredentialsError as e:
            print(f"❌ 身份驗證失敗: {e}")
            return False
        except Exception as e:
            print(f"❌ 身份驗證錯誤: {e}")
            return False

    def analyze_time_distribution(self, table_type: str) -> Dict[str, Any]:
        """分析時間分佈"""
        table_name = self.tables[table_type]
        full_table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"

        print(f"\n🔍 分析 {table_type} 表格時間分佈: {full_table_id}")
        print("=" * 80)

        result = {
            "table_type": table_type,
            "yearly_distribution": [],
            "monthly_distribution": [],
            "anomaly_analysis": {},
            "sample_records": []
        }

        try:
            # 1. 年度分佈分析
            yearly_query = f"""
            SELECT
                EXTRACT(YEAR FROM DATE(TIMESTAMP_SECONDS(event_times))) as year,
                COUNT(*) as record_count,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
            FROM `{full_table_id}`
            WHERE event_times IS NOT NULL
            GROUP BY year
            ORDER BY year
            """

            print("📊 執行年度分佈查詢...")
            yearly_job = self.client.query(yearly_query)
            yearly_results = list(yearly_job.result())

            for row in yearly_results:
                year_data = {
                    "year": row.year,
                    "record_count": row.record_count,
                    "earliest_date": row.earliest_date.strftime("%Y-%m-%d"),
                    "latest_date": row.latest_date.strftime("%Y-%m-%d")
                }
                result["yearly_distribution"].append(year_data)
                print(f"  {row.year}: {row.record_count:,} 筆 ({row.earliest_date} 至 {row.latest_date})")

            # 2. 最近12個月分佈
            monthly_query = f"""
            SELECT
                FORMAT_DATE('%Y-%m', DATE(TIMESTAMP_SECONDS(event_times))) as month,
                COUNT(*) as record_count,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
            FROM `{full_table_id}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
                AND event_times IS NOT NULL
            GROUP BY month
            ORDER BY month DESC
            """

            print("\n📅 執行最近12個月分佈查詢...")
            monthly_job = self.client.query(monthly_query)
            monthly_results = list(monthly_job.result())

            for row in monthly_results:
                month_data = {
                    "month": row.month,
                    "record_count": row.record_count,
                    "earliest_date": row.earliest_date.strftime("%Y-%m-%d"),
                    "latest_date": row.latest_date.strftime("%Y-%m-%d")
                }
                result["monthly_distribution"].append(month_data)
                print(f"  {row.month}: {row.record_count:,} 筆")

            # 3. 異常值分析 - 檢查是否有明顯異常的時間
            anomaly_query = f"""
            SELECT
                'very_old' as category,
                COUNT(*) as count,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as min_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date
            FROM `{full_table_id}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) < '2020-01-01'
                AND event_times IS NOT NULL

            UNION ALL

            SELECT
                'future' as category,
                COUNT(*) as count,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as min_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date
            FROM `{full_table_id}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) > CURRENT_DATE()
                AND event_times IS NOT NULL

            UNION ALL

            SELECT
                'recent' as category,
                COUNT(*) as count,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as min_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date
            FROM `{full_table_id}`
            WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= '2020-01-01'
                AND DATE(TIMESTAMP_SECONDS(event_times)) <= CURRENT_DATE()
                AND event_times IS NOT NULL
            """

            print("\n🔍 執行異常值分析...")
            anomaly_job = self.client.query(anomaly_query)
            anomaly_results = list(anomaly_job.result())

            for row in anomaly_results:
                if row.count > 0:
                    result["anomaly_analysis"][row.category] = {
                        "count": row.count,
                        "min_date": row.min_date.strftime("%Y-%m-%d") if row.min_date else None,
                        "max_date": row.max_date.strftime("%Y-%m-%d") if row.max_date else None
                    }
                    print(f"  {row.category}: {row.count:,} 筆 ({row.min_date} 至 {row.max_date})")

            # 4. 取樣一些最早的記錄來檢查
            sample_query = f"""
            SELECT
                event_times,
                DATE(TIMESTAMP_SECONDS(event_times)) as converted_date,
                store_id,
                total_amount
            FROM `{full_table_id}`
            WHERE event_times IS NOT NULL
            ORDER BY event_times ASC
            LIMIT 10
            """

            print("\n📋 取樣最早的記錄...")
            sample_job = self.client.query(sample_query)
            sample_results = list(sample_job.result())

            for row in sample_results:
                sample_data = {
                    "event_times": row.event_times,
                    "converted_date": row.converted_date.strftime("%Y-%m-%d"),
                    "store_id": row.store_id,
                    "total_amount": float(row.total_amount) if row.total_amount else None
                }
                result["sample_records"].append(sample_data)
                print(f"  event_times: {row.event_times} → {row.converted_date} (store: {row.store_id})")

        except Exception as e:
            print(f"❌ 分析失敗: {e}")
            result["error"] = str(e)

        return result

    def generate_analysis_report(self, daily_result: Dict, monthly_result: Dict) -> str:
        """生成分析報告"""
        report = []
        report.append("=" * 100)
        report.append("🕐 家樂福離線交易資料 - 詳細時間分佈分析報告")
        report.append("=" * 100)
        report.append(f"📅 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        for table_type, result in [("DAILY", daily_result), ("MONTHLY", monthly_result)]:
            if result.get("error"):
                report.append(f"❌ {table_type} 表格分析失敗: {result['error']}")
                continue

            report.append(f"📊 {table_type} 表格時間分佈分析")
            report.append("-" * 80)

            # 年度分佈
            if result["yearly_distribution"]:
                report.append("\n🗓️ 年度分佈:")
                total_records = sum(year["record_count"] for year in result["yearly_distribution"])
                for year_data in result["yearly_distribution"]:
                    percentage = (year_data["record_count"] / total_records) * 100
                    report.append(f"  {year_data['year']}: {year_data['record_count']:,} 筆 ({percentage:.1f}%)")

            # 異常值分析
            if result["anomaly_analysis"]:
                report.append("\n🔍 異常值分析:")
                for category, data in result["anomaly_analysis"].items():
                    report.append(f"  {category}: {data['count']:,} 筆 ({data['min_date']} 至 {data['max_date']})")

            # 最近月份分佈
            if result["monthly_distribution"]:
                report.append("\n📅 最近12個月分佈:")
                for month_data in result["monthly_distribution"][:6]:  # 只顯示前6個月
                    report.append(f"  {month_data['month']}: {month_data['record_count']:,} 筆")

            # 樣本記錄
            if result["sample_records"]:
                report.append("\n📋 最早記錄樣本:")
                for i, sample in enumerate(result["sample_records"][:5]):
                    report.append(f"  {i+1}. {sample['event_times']} → {sample['converted_date']} (store: {sample['store_id']})")

            report.append("")

        # 總結分析
        report.append("💡 分析總結")
        report.append("-" * 40)

        # 檢查是否有異常的時間分佈
        has_old_data = False
        has_future_data = False

        for result in [daily_result, monthly_result]:
            if result.get("anomaly_analysis"):
                if "very_old" in result["anomaly_analysis"] and result["anomaly_analysis"]["very_old"]["count"] > 0:
                    has_old_data = True
                if "future" in result["anomaly_analysis"] and result["anomaly_analysis"]["future"]["count"] > 0:
                    has_future_data = True

        if has_old_data:
            report.append("⚠️ 發現異常早期資料（2020年前），可能是資料錯誤或測試資料")
        if has_future_data:
            report.append("⚠️ 發現未來日期資料，可能是系統時間錯誤")

        if not has_old_data and not has_future_data:
            report.append("✅ 時間分佈正常，無明顯異常")

        report.append("")
        report.append("=" * 100)
        return "\n".join(report)

    def run_analysis(self) -> bool:
        """執行完整分析"""
        print("🚀 開始詳細時間分佈分析...")
        print("=" * 80)

        if not self.authenticate():
            return False

        # 分析兩個表格
        daily_result = self.analyze_time_distribution("daily")
        monthly_result = self.analyze_time_distribution("monthly")

        # 生成報告
        report = self.generate_analysis_report(daily_result, monthly_result)
        print("\n" + report)

        # 儲存報告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"detailed_time_distribution_analysis_{timestamp}.txt"

        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"📄 詳細報告已儲存至: {report_filename}")
        except Exception as e:
            print(f"⚠️ 報告儲存失敗: {e}")

        return True

def main():
    """主函數"""
    analyzer = DetailedTimeDistributionAnalyzer()

    if not analyzer.run_analysis():
        sys.exit(1)

if __name__ == "__main__":
    main()
