#!/usr/bin/env python3
"""
家樂福離線事件資料驗證腳本 (v2)

使用 <EMAIL> 權限
檢視和驗證 tw-eagle-prod.rmn_tagtoo.offline_transaction_day 資料

Author: AI Assistant
Date: 2025-08-15
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Google Cloud 相關
from google.cloud import bigquery
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

# 資料處理
import pandas as pd
import numpy as np

class CarrefourDataValidator:
    """家樂福離線事件資料驗證器"""

    def __init__(self, project_id: str = "tw-eagle-prod"):
        self.project_id = project_id
        self.dataset_id = "rmn_tagtoo"
        self.table_id = "offline_transaction_day"
        self.client = None

    def authenticate(self) -> bool:
        """使用預設認證進行身份驗證"""
        try:
            # 嘗試使用預設認證
            credentials, project = default()
            print(f"✅ 認證成功！使用專案: {project}")

            # 初始化 BigQuery 客戶端
            self.client = bigquery.Client(
                project=self.project_id,
                credentials=credentials
            )

            return True

        except DefaultCredentialsError as e:
            print(f"❌ 認證失敗: {e}")
            print("請確保已設定 GOOGLE_APPLICATION_CREDENTIALS 環境變數")
            return False
        except Exception as e:
            print(f"❌ 初始化客戶端失敗: {e}")
            return False

    def check_permissions(self) -> Dict[str, bool]:
        """檢查權限狀態"""
        permissions = {}

        try:
            # 檢查 BigQuery 權限
            dataset_ref = self.client.dataset(self.dataset_id)
            dataset = self.client.get_dataset(dataset_ref)
            permissions['bigquery_read'] = True
            print(f"✅ BigQuery 讀取權限正常")

        except Exception as e:
            permissions['bigquery_read'] = False
            print(f"❌ BigQuery 讀取權限檢查失敗: {e}")

        return permissions

    def get_table_schema(self) -> Dict[str, Any]:
        """獲取表格結構"""
        try:
            table_ref = self.client.dataset(self.dataset_id).table(self.table_id)
            table = self.client.get_table(table_ref)

            schema_info = {
                'table_id': table.table_id,
                'dataset_id': table.dataset_id,
                'project_id': table.project,
                'created': table.created.isoformat() if table.created else None,
                'modified': table.modified.isoformat() if table.modified else None,
                'num_rows': table.num_rows,
                'num_bytes': table.num_bytes,
                'schema': []
            }

            for field in table.schema:
                schema_info['schema'].append({
                    'name': field.name,
                    'type': field.field_type,
                    'mode': field.mode,
                    'description': field.description
                })

            return schema_info

        except Exception as e:
            print(f"❌ 獲取表格結構失敗: {e}")
            return {}

    def estimate_query_cost(self, query: str) -> Dict[str, Any]:
        """估算查詢成本"""
        try:
            # 使用 dry_run 估算查詢成本
            job_config = bigquery.QueryJobConfig(dry_run=True)
            query_job = self.client.query(query, job_config=job_config)

            # 計算成本（假設 $5/TB）
            bytes_processed = query_job.total_bytes_processed
            cost_usd = (bytes_processed / (1024**4)) * 5  # 轉換為 TB 並計算成本

            return {
                'bytes_processed': bytes_processed,
                'cost_usd': cost_usd,
                'query_valid': True
            }

        except Exception as e:
            return {
                'bytes_processed': 0,
                'cost_usd': 0,
                'query_valid': False,
                'error': str(e)
            }

    def get_data_sample(self, limit: int = 10) -> pd.DataFrame:
        """獲取資料樣本"""
        try:
            query = f"""
            SELECT *
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            LIMIT {limit}
            """

            # 估算成本
            cost_info = self.estimate_query_cost(query)
            print(f"📊 查詢成本估算: {cost_info['bytes_processed']:,} bytes (約 ${cost_info['cost_usd']:.4f})")

            if cost_info['cost_usd'] > 0.01:  # 超過 1 分錢的查詢需要確認
                print("⚠️  查詢成本較高，建議確認後再執行")
                response = input("是否繼續執行查詢？(y/N): ")
                if response.lower() != 'y':
                    return pd.DataFrame()

            # 執行查詢
            df = self.client.query(query).to_dataframe()
            return df

        except Exception as e:
            print(f"❌ 獲取資料樣本失敗: {e}")
            return pd.DataFrame()

    def get_data_statistics(self) -> Dict[str, Any]:
        """獲取資料統計資訊"""
        try:
            # 檢查資料時間範圍
            date_query = f"""
            SELECT
                MIN(transaction_date) as min_date,
                MAX(transaction_date) as max_date,
                COUNT(*) as total_rows,
                COUNT(DISTINCT transaction_date) as unique_dates
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            """

            cost_info = self.estimate_query_cost(date_query)
            print(f"📊 統計查詢成本估算: {cost_info['bytes_processed']:,} bytes (約 ${cost_info['cost_usd']:.4f})")

            if cost_info['cost_usd'] > 0.01:
                response = input("統計查詢成本較高，是否繼續？(y/N): ")
                if response.lower() != 'y':
                    return {}

            result = self.client.query(date_query).to_dataframe()

            if not result.empty:
                stats = {
                    'min_date': result.iloc[0]['min_date'],
                    'max_date': result.iloc[0]['max_date'],
                    'total_rows': result.iloc[0]['total_rows'],
                    'unique_dates': result.iloc[0]['unique_dates']
                }
                return stats

            return {}

        except Exception as e:
            print(f"❌ 獲取資料統計失敗: {e}")
            return {}

    def validate_data_quality(self) -> Dict[str, Any]:
        """驗證資料品質"""
        try:
            # 檢查空值和資料完整性
            quality_query = f"""
            SELECT
                COUNT(*) as total_rows,
                COUNTIF(transaction_date IS NULL) as null_dates,
                COUNTIF(user_id IS NULL) as null_user_ids,
                COUNTIF(amount IS NULL) as null_amounts,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT transaction_date) as unique_dates
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            """

            cost_info = self.estimate_query_cost(quality_query)
            print(f"📊 品質查詢成本估算: {cost_info['bytes_processed']:,} bytes (約 ${cost_info['cost_usd']:.4f})")

            if cost_info['cost_usd'] > 0.01:
                response = input("品質查詢成本較高，是否繼續？(y/N): ")
                if response.lower() != 'y':
                    return {}

            result = self.client.query(quality_query).to_dataframe()

            if not result.empty:
                row = result.iloc[0]
                quality = {
                    'total_rows': row['total_rows'],
                    'null_dates': row['null_dates'],
                    'null_user_ids': row['null_user_ids'],
                    'null_amounts': row['null_amounts'],
                    'unique_users': row['unique_users'],
                    'unique_dates': row['unique_dates'],
                    'data_completeness': {
                        'dates': (row['total_rows'] - row['null_dates']) / row['total_rows'],
                        'user_ids': (row['total_rows'] - row['null_user_ids']) / row['total_rows'],
                        'amounts': (row['total_rows'] - row['null_amounts']) / row['total_rows']
                    }
                }
                return quality

            return {}

        except Exception as e:
            print(f"❌ 驗證資料品質失敗: {e}")
            return {}

    def generate_report(self) -> str:
        """生成驗證報告"""
        report = []
        report.append("=" * 80)
        report.append("家樂福離線事件資料驗證報告")
        report.append("=" * 80)
        report.append(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"專案 ID: {self.project_id}")
        report.append(f"資料集: {self.dataset_id}")
        report.append(f"表格: {self.table_id}")
        report.append("")

        # 權限檢查
        report.append("1. 權限檢查")
        report.append("-" * 40)
        permissions = self.check_permissions()
        for perm, status in permissions.items():
            status_text = "✅ 正常" if status else "❌ 失敗"
            report.append(f"{perm}: {status_text}")
        report.append("")

        # 表格結構
        report.append("2. 表格結構")
        report.append("-" * 40)
        schema = self.get_table_schema()
        if schema:
            report.append(f"表格 ID: {schema.get('table_id', 'N/A')}")
            report.append(f"建立時間: {schema.get('created', 'N/A')}")
            report.append(f"修改時間: {schema.get('modified', 'N/A')}")
            report.append(f"總行數: {schema.get('num_rows', 'N/A'):,}")
            report.append(f"總大小: {schema.get('num_bytes', 'N/A'):,} bytes")
            report.append("")
            report.append("欄位結構:")
            for field in schema.get('schema', []):
                mode = f" ({field['mode']})" if field['mode'] != 'NULLABLE' else ""
                report.append(f"  - {field['name']}: {field['type']}{mode}")
                if field.get('description'):
                    report.append(f"    說明: {field['description']}")
        report.append("")

        # 資料統計
        report.append("3. 資料統計")
        report.append("-" * 40)
        stats = self.get_data_statistics()
        if stats:
            report.append(f"資料時間範圍: {stats.get('min_date', 'N/A')} ~ {stats.get('max_date', 'N/A')}")
            report.append(f"總行數: {stats.get('total_rows', 'N/A'):,}")
            report.append(f"唯一日期數: {stats.get('unique_dates', 'N/A')}")
        report.append("")

        # 資料品質
        report.append("4. 資料品質")
        report.append("-" * 40)
        quality = self.validate_data_quality()
        if quality:
            report.append(f"總行數: {quality.get('total_rows', 'N/A'):,}")
            report.append(f"空日期數: {quality.get('null_dates', 'N/A'):,}")
            report.append(f"空用戶ID數: {quality.get('null_user_ids', 'N/A'):,}")
            report.append(f"空金額數: {quality.get('null_amounts', 'N/A'):,}")
            report.append(f"唯一用戶數: {quality.get('unique_users', 'N/A'):,}")
            report.append(f"唯一日期數: {quality.get('unique_dates', 'N/A'):,}")
            report.append("")
            report.append("資料完整性:")
            completeness = quality.get('data_completeness', {})
            for field, rate in completeness.items():
                report.append(f"  - {field}: {rate:.2%}")
        report.append("")

        # 建議
        report.append("5. 建議")
        report.append("-" * 40)
        if permissions.get('bigquery_read'):
            report.append("✅ 權限檢查通過，可以正常存取資料")
            if schema:
                report.append("✅ 表格結構完整，建議進行資料品質分析")
            if quality:
                total_rows = quality.get('total_rows', 0)
                if total_rows > 0:
                    report.append("✅ 資料存在，建議進行詳細分析")
                else:
                    report.append("⚠️  資料為空，需要檢查資料來源")
        else:
            report.append("❌ 權限不足，需要檢查 IAM 設定")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)

def main():
    """主函數"""
    print("🚀 開始家樂福離線事件資料驗證 (v2)...")
    print("=" * 60)

    # 建立驗證器
    validator = CarrefourDataValidator()

    # 進行身份驗證
    if not validator.authenticate():
        print("❌ 身份驗證失敗，程式結束")
        sys.exit(1)

    print("✅ 身份驗證成功")
    print("")

    # 生成報告
    report = validator.generate_report()
    print(report)

    # 儲存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"carrefour_validation_report_v2_{timestamp}.txt"

    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 報告已儲存至: {report_filename}")
    except Exception as e:
        print(f"⚠️  報告儲存失敗: {e}")

    # 詢問是否要查看資料樣本
    print("")
    response = input("是否要查看資料樣本？(y/N): ")
    if response.lower() == 'y':
        print("📊 獲取資料樣本...")
        sample_df = validator.get_data_sample(limit=5)
        if not sample_df.empty:
            print("資料樣本:")
            print(sample_df.to_string(index=False))
        else:
            print("無法獲取資料樣本")

if __name__ == "__main__":
    main()
