#!/usr/bin/env python3
"""
自動化每日資料複製腳本

專為生產環境設計的每日資料複製流程：
1. 檢查來源資料更新狀況
2. 執行必要的資料複製
3. 驗證複製結果
4. 生成執行報告

設計原則：
- 只複製 Day 表格（用於受眾媒合）
- 確保受眾媒合執行前有最新資料
- 完整的錯誤處理和重試機制
- 詳細的執行日誌和監控

Author: AI Assistant
Date: 2025-09-04
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from google.cloud import bigquery
from google.cloud.bigquery import QueryJobConfig
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AutomatedDailyReplicator:
    """自動化每日資料複製器 - 支援多表格複製"""

    def __init__(self, table_name: str = "offline_transaction_day"):
        self.source_project = "tw-eagle-prod"
        self.target_project = "tagtoo-tracking"

        # 支援的表格清單
        self.table_configs = {
            "offline_transaction_day": {
                "source": f"{self.source_project}.rmn_tagtoo.offline_transaction_day",
                "target": f"{self.target_project}.event_prod.carrefour_offline_transaction_day",
                "schedule_type": "daily"  # 每日複製
            },
            "offline_transaction_month": {
                "source": f"{self.source_project}.rmn_tagtoo.offline_transaction_month",
                "target": f"{self.target_project}.event_prod.carrefour_offline_transaction_month",
                "schedule_type": "monthly"  # 每月複製
            }
        }

        # 設定當前處理的表格
        if table_name not in self.table_configs:
            raise ValueError(f"不支援的表格: {table_name}")

        self.current_table = table_name
        self.table_config = self.table_configs[table_name]

        # 為了向後相容，設定 tables_to_replicate 屬性
        self.tables_to_replicate = [table_name]

        # 配置參數
        self.max_copy_cost_usd = 2.0  # 單次複製成本上限（調整為支援多表格）
        self.cost_per_tb = 5.0  # BigQuery 成本
        self.max_retries = 3  # 最大重試次數
        self.retry_delay_seconds = 300  # 重試間隔（5分鐘）
        # 針對 ph_id_hex 富化欄位的成本控制
        self.max_enrichment_cost_usd = 2.0

        self.client = None

    def authenticate(self) -> bool:
        """進行身份驗證"""
        try:
            credentials, project = default()
            self.client = bigquery.Client(project=self.target_project, credentials=credentials)

            logger.info(f"✅ 身份驗證成功")
            logger.info(f"📋 計費專案: {self.target_project}")
            return True

        except DefaultCredentialsError as e:
            logger.error(f"❌ 身份驗證失敗: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 身份驗證錯誤: {e}")
            return False

    def check_source_data_freshness(self, table_name: str = None) -> Dict[str, Any]:
        """檢查來源資料新鮮度"""
        if table_name is None:
            # 檢查所有表格的新鮮度
            return self._check_all_tables_freshness()

        if table_name not in self.table_configs:
            raise ValueError(f"不支援的表格: {table_name}")

        config = self.table_configs[table_name]
        source_table_id = config["source"]

        result = {
            "table_name": table_name,
            "is_fresh": False,
            "source_modified": None,
            "latest_data_date": None,
            "hours_since_update": None,
            "error": None
        }

        try:
            # 檢查表格修改時間
            source_table = self.client.get_table(source_table_id)
            result["source_modified"] = source_table.modified

            # 檢查最新資料日期
            query = f"""
            SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
            FROM `{source_table_id}`
            WHERE event_times IS NOT NULL
            """

            job = self.client.query(query)
            rows = list(job.result())
            if rows:
                result["latest_data_date"] = rows[0].latest_date

            # 計算更新時間差
            now = datetime.now(timezone.utc)
            hours_diff = (now - source_table.modified).total_seconds() / 3600
            result["hours_since_update"] = hours_diff

            # 判斷資料是否新鮮（24小時內更新）
            result["is_fresh"] = hours_diff <= 24

            logger.info(f"📊 來源資料檢查 ({table_name}):")
            logger.info(f"   修改時間: {source_table.modified}")
            logger.info(f"   最新資料日期: {result['latest_data_date']}")
            logger.info(f"   更新時間差: {hours_diff:.1f} 小時")
            logger.info(f"   資料新鮮度: {'✅ 新鮮' if result['is_fresh'] else '⚠️ 較舊'}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 來源資料檢查失敗 ({table_name}): {e}")

        return result

    def _check_all_tables_freshness(self) -> Dict[str, Any]:
        """檢查所有表格的新鮮度"""
        results = {}
        overall_fresh = True

        for table_name in self.tables_to_replicate:
            table_result = self.check_source_data_freshness(table_name)
            results[table_name] = table_result
            if not table_result.get("is_fresh", False):
                overall_fresh = False

        return {
            "tables": results,
            "overall_fresh": overall_fresh,
            "checked_tables": self.tables_to_replicate
        }

    def check_replication_needed(self, table_name: str = None) -> Dict[str, Any]:
        """檢查是否需要複製"""
        if table_name is None:
            # 檢查所有表格是否需要複製
            return self._check_all_tables_replication_needed()

        if table_name not in self.table_configs:
            raise ValueError(f"不支援的表格: {table_name}")

        config = self.table_configs[table_name]
        source_table_id = config["source"]
        target_table_id = config["target"]

        result = {
            "table_name": table_name,
            "need_replication": False,
            "reason": "",
            "source_modified": None,
            "target_modified": None,
            "target_exists": False,
            "error": None
        }

        try:
            # 檢查來源表格
            source_table = self.client.get_table(source_table_id)
            result["source_modified"] = source_table.modified

            # 檢查目標表格是否存在
            try:
                target_table = self.client.get_table(target_table_id)
                result["target_modified"] = target_table.modified
                result["target_exists"] = True

                # 比較修改時間
                if source_table.modified > target_table.modified:
                    time_diff = source_table.modified - target_table.modified
                    result["need_replication"] = True
                    result["reason"] = f"來源資料較新 (差異: {time_diff})"
                else:
                    result["reason"] = "目標資料已是最新"

            except Exception:
                # 目標表格不存在，需要建立
                result["need_replication"] = True
                result["reason"] = "目標表格不存在，需要建立"
                result["target_exists"] = False

            logger.info(f"🔍 複製需求檢查 ({table_name}): {result['reason']}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 複製需求檢查失敗 ({table_name}): {e}")

        return result

    def _check_all_tables_replication_needed(self) -> Dict[str, Any]:
        """檢查所有表格是否需要複製"""
        results = {}
        tables_need_replication = []

        for table_name in self.tables_to_replicate:
            table_result = self.check_replication_needed(table_name)
            results[table_name] = table_result
            if table_result.get("need_replication", False):
                tables_need_replication.append(table_name)

        return {
            "tables": results,
            "tables_need_replication": tables_need_replication,
            "overall_need_replication": len(tables_need_replication) > 0,
            "checked_tables": self.tables_to_replicate
        }

    def estimate_replication_cost(self, table_names: list = None) -> Dict[str, Any]:
        """估算複製成本"""
        if table_names is None:
            table_names = self.tables_to_replicate

        result = {
            "total_estimated_cost": 0,
            "total_bytes_to_copy": 0,
            "cost_acceptable": False,
            "tables": {},
            "error": None
        }

        try:
            total_cost = 0
            total_bytes = 0

            for table_name in table_names:
                if table_name not in self.table_configs:
                    continue

                config = self.table_configs[table_name]
                source_table_id = config["source"]

                try:
                    source_table = self.client.get_table(source_table_id)
                    bytes_size = source_table.num_bytes or 0
                    tb_size = bytes_size / (1024**4)
                    cost = tb_size * self.cost_per_tb

                    result["tables"][table_name] = {
                        "bytes_size": bytes_size,
                        "tb_size": tb_size,
                        "estimated_cost": cost
                    }

                    total_cost += cost
                    total_bytes += bytes_size

                    logger.info(f"💰 成本估算 ({table_name}):")
                    logger.info(f"   資料大小: {bytes_size:,} bytes ({tb_size:.4f} TB)")
                    logger.info(f"   預估成本: ${cost:.4f} USD")

                except Exception as e:
                    logger.warning(f"⚠️ 無法取得表格資訊 ({table_name}): {e}")
                    result["tables"][table_name] = {
                        "error": str(e)
                    }

            result["total_estimated_cost"] = total_cost
            result["total_bytes_to_copy"] = total_bytes
            result["cost_acceptable"] = total_cost <= self.max_copy_cost_usd

            logger.info(f"💰 總成本估算:")
            logger.info(f"   總資料大小: {total_bytes:,} bytes")
            logger.info(f"   總預估成本: ${total_cost:.4f} USD")
            logger.info(f"   成本可接受: {'✅ 是' if result['cost_acceptable'] else '❌ 否'}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 成本估算失敗: {e}")

        return result

    def _add_ph_id_hex_and_populate(self, target_table_id: str) -> Dict[str, Any]:
        """
        為目標表新增並填充輔助欄位 ph_id_hex (STRING)，內容為 TO_HEX(ph_id)。
        - 保持原有 ph_id (BYTES) 不變
        - 先以 ALTER TABLE 新增欄位（若不存在）
        - 再以 UPDATE 進行填充，先 dry-run 估算成本
        """
        details: Dict[str, Any] = {
            "target_table": target_table_id,
            "alter_done": False,
            "update_bytes": 0,
            "estimated_cost_usd": 0.0,
        }

        # 1) 新增欄位（若不存在）
        alter_sql = f"""
        ALTER TABLE `{target_table_id}`
        ADD COLUMN IF NOT EXISTS ph_id_hex STRING
        """
        logger.info(f"準備在目標表新增輔助欄位 ph_id_hex: {target_table_id}")
        self.client.query(alter_sql).result()
        details["alter_done"] = True

        # 2) 更新欄位值（dry-run 估算成本）
        update_sql = f"UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id)"
        dry_cfg = QueryJobConfig(dry_run=True, use_query_cache=False)
        dry_job = self.client.query(update_sql, job_config=dry_cfg)
        bytes_proc = getattr(dry_job, "total_bytes_processed", 0) or 0
        details["update_bytes"] = bytes_proc
        est_cost = bytes_proc / (1024 ** 4) * self.cost_per_tb
        details["estimated_cost_usd"] = est_cost
        logger.info(f"ph_id_hex UPDATE 預估掃描: {bytes_proc:,} bytes (約 ${est_cost:.4f})")
        if est_cost > self.max_enrichment_cost_usd:
            logger.warning(
                f"ph_id_hex 填充預估成本 ${est_cost:.4f} 超過上限 ${self.max_enrichment_cost_usd:.2f}，依需求仍將執行"
            )

        # 實際執行 UPDATE
        job = self.client.query(update_sql)
        job.result()
        logger.info("ph_id_hex 欄位填充完成")
        return details

    def execute_replication(self, table_names: list = None) -> Dict[str, Any]:
        """執行資料複製"""
        if table_names is None:
            table_names = self.tables_to_replicate

        result = {
            "success": False,
            "tables_processed": [],
            "tables_results": {},
            "total_rows_copied": 0,
            "total_bytes_copied": 0,
            "total_duration_seconds": 0,
            "error": None
        }

        start_time = datetime.now(timezone.utc)
        success_count = 0

        try:
            logger.info(f"🔄 開始執行多表格資料複製... ({len(table_names)} 個表格)")

            for table_name in table_names:
                if table_name not in self.table_configs:
                    logger.warning(f"⚠️ 跳過不支援的表格: {table_name}")
                    continue

                config = self.table_configs[table_name]
                source_table_id = config["source"]
                target_table_id = config["target"]

                table_result = self._execute_single_table_replication(
                    table_name, source_table_id, target_table_id
                )

                result["tables_results"][table_name] = table_result
                result["tables_processed"].append(table_name)

                if table_result["success"]:
                    success_count += 1
                    result["total_rows_copied"] += table_result.get("rows_copied", 0)
                    result["total_bytes_copied"] += table_result.get("bytes_copied", 0)
                else:
                    logger.error(f"❌ 表格 {table_name} 複製失敗: {table_result.get('error', 'N/A')}")

            end_time = datetime.now(timezone.utc)
            result["total_duration_seconds"] = (end_time - start_time).total_seconds()
            result["success"] = success_count == len(table_names)

            logger.info(f"📊 多表格複製結果:")
            logger.info(f"   成功: {success_count}/{len(table_names)}")
            logger.info(f"   總複製筆數: {result['total_rows_copied']:,}")
            logger.info(f"   總複製大小: {result['total_bytes_copied']:,} bytes")
            logger.info(f"   總執行時間: {result['total_duration_seconds']:.1f} 秒")

            if result["success"]:
                logger.info("✅ 所有表格複製完成!")
            else:
                logger.warning("⚠️ 部分表格複製失敗")

            # 複製完成後：新增並填充 ph_id_hex 欄位
            try:
                enrich_info = self._add_ph_id_hex_and_populate(target_table_id)
                result["ph_id_hex_enrichment"] = enrich_info
                logger.info("✅ ph_id_hex 輔助欄位處理完成")
            except Exception as enrich_err:
                logger.error(f"⚠️ ph_id_hex 輔助欄位處理失敗：{enrich_err}")
                # 不中斷主要流程，僅記錄錯誤
                result["ph_id_hex_enrichment_error"] = str(enrich_err)

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 多表格複製執行失敗: {e}")

        return result

    def _execute_single_table_replication(self, table_name: str, source_table_id: str, target_table_id: str) -> Dict[str, Any]:
        """執行單一表格複製"""
        result = {
            "success": False,
            "table_name": table_name,
            "job_id": None,
            "rows_copied": 0,
            "bytes_copied": 0,
            "duration_seconds": 0,
            "error": None
        }

        start_time = datetime.now(timezone.utc)

        try:
            logger.info(f"🔄 複製表格: {table_name}")
            logger.info(f"   📥 來源: {source_table_id}")
            logger.info(f"   📤 目標: {target_table_id}")

            copy_config = bigquery.CopyJobConfig(
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
                labels={
                    "source": "automated_daily_replication",
                    "environment": "production",
                    "automated": "true",
                    "table_name": table_name
                }
            )

            copy_job = self.client.copy_table(
                source_table_id,
                target_table_id,
                job_config=copy_config
            )

            # 等待完成
            copy_job.result()

            # 新增並填充 ph_id_hex 輔助欄位
            try:
                enrich_info = self._add_ph_id_hex_and_populate(target_table_id)
                result["ph_id_hex_enrichment"] = enrich_info
                logger.info(f"   ✅ ph_id_hex 輔助欄位處理完成 - 表格: {table_name}")
            except Exception as enrich_err:
                logger.error(f"   ⚠️ ph_id_hex 輔助欄位處理失敗 - 表格: {table_name}, 錯誤: {enrich_err}")
                result["ph_id_hex_enrichment_error"] = str(enrich_err)
                # 不中斷主要複製流程，僅記錄錯誤

            # 檢查結果
            target_table = self.client.get_table(target_table_id)
            end_time = datetime.now(timezone.utc)

            result["success"] = True
            result["job_id"] = copy_job.job_id
            result["rows_copied"] = target_table.num_rows or 0
            result["bytes_copied"] = target_table.num_bytes or 0
            result["duration_seconds"] = (end_time - start_time).total_seconds()

            logger.info(f"   ✅ {table_name} 複製完成!")
            logger.info(f"   作業ID: {copy_job.job_id}")
            logger.info(f"   複製筆數: {result['rows_copied']:,}")
            logger.info(f"   複製大小: {result['bytes_copied']:,} bytes")
            logger.info(f"   執行時間: {result['duration_seconds']:.1f} 秒")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"   ❌ {table_name} 複製失敗: {e}")

        return result

    def verify_replication_result(self, table_names: list = None) -> Dict[str, Any]:
        """驗證複製結果"""
        if table_names is None:
            table_names = self.tables_to_replicate

        result = {
            "overall_verification_passed": False,
            "tables_verified": 0,
            "tables_passed": 0,
            "tables": {},
            "error": None
        }

        try:
            logger.info(f"🔍 驗證多表格複製結果... ({len(table_names)} 個表格)")

            verified_count = 0
            passed_count = 0

            for table_name in table_names:
                if table_name not in self.table_configs:
                    continue

                table_result = self._verify_single_table_replication(table_name)
                result["tables"][table_name] = table_result
                verified_count += 1

                if table_result.get("verification_passed", False):
                    passed_count += 1

            result["tables_verified"] = verified_count
            result["tables_passed"] = passed_count
            result["overall_verification_passed"] = passed_count == verified_count and verified_count > 0

            logger.info(f"📊 多表格驗證結果:")
            logger.info(f"   驗證表格數: {verified_count}")
            logger.info(f"   通過表格數: {passed_count}")
            logger.info(f"   整體驗證: {'✅ 通過' if result['overall_verification_passed'] else '❌ 失敗'}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 多表格驗證失敗: {e}")

        return result

    def _verify_single_table_replication(self, table_name: str) -> Dict[str, Any]:
        """驗證單一表格複製結果"""
        result = {
            "table_name": table_name,
            "verification_passed": False,
            "source_count": 0,
            "target_count": 0,
            "count_match": False,
            "latest_data_match": False,
            "target_exists": False,
            "error": None
        }

        try:
            config = self.table_configs[table_name]
            source_table_id = config["source"]
            target_table_id = config["target"]

            logger.info(f"🔍 驗證表格: {table_name}")

            # 檢查來源表格
            source_table = self.client.get_table(source_table_id)
            result["source_count"] = source_table.num_rows or 0

            # 檢查目標表格是否存在
            try:
                target_table = self.client.get_table(target_table_id)
                result["target_exists"] = True
                result["target_count"] = target_table.num_rows or 0
                result["count_match"] = result["source_count"] == result["target_count"]

                # 檢查最新資料日期
                source_query = f"SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date FROM `{source_table_id}`"
                target_query = f"SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times))) as max_date FROM `{target_table_id}`"

                source_job = self.client.query(source_query)
                target_job = self.client.query(target_query)

                source_max = list(source_job.result())[0].max_date
                target_max = list(target_job.result())[0].max_date

                result["latest_data_match"] = source_max == target_max
                result["verification_passed"] = result["count_match"] and result["latest_data_match"]

                logger.info(f"   來源筆數: {result['source_count']:,}")
                logger.info(f"   目標筆數: {result['target_count']:,}")
                logger.info(f"   筆數匹配: {'✅ 是' if result['count_match'] else '❌ 否'}")
                logger.info(f"   最新日期匹配: {'✅ 是' if result['latest_data_match'] else '❌ 否'}")
                logger.info(f"   驗證結果: {'✅ 通過' if result['verification_passed'] else '❌ 失敗'}")

            except Exception:
                result["target_exists"] = False
                result["verification_passed"] = False
                logger.warning(f"   ⚠️ 目標表格不存在: {target_table_id}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"   ❌ {table_name} 驗證失敗: {e}")

        return result

    def generate_execution_report(self, execution_results: Dict[str, Any]) -> str:
        """生成執行報告"""
        report = []
        report.append("=" * 100)
        report.append("🏪 家樂福多表格每日資料複製執行報告")
        report.append("=" * 100)
        report.append(f"📅 執行時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 表格資訊
        tables_to_replicate = execution_results.get("tables_to_replicate", self.tables_to_replicate)
        report.append(f"📋 處理表格數量: {len(tables_to_replicate)} 個")
        for i, table_name in enumerate(tables_to_replicate, 1):
            config = self.table_configs.get(table_name, {})
            report.append(f"   {i}. {table_name}")
            report.append(f"      來源: {config.get('source', 'N/A')}")
            report.append(f"      目標: {config.get('target', 'N/A')}")
        report.append("")

        # 執行結果
        if execution_results.get("replication_executed"):
            replication = execution_results["replication_result"]
            verification = execution_results["verification_result"]

            report.append("📊 複製執行結果")
            report.append("-" * 80)

            if replication["success"]:
                report.append(f"✅ 整體複製狀態: 成功")
                report.append(f"📈 總複製筆數: {replication.get('total_rows_copied', 0):,}")
                report.append(f"💾 總複製大小: {replication.get('total_bytes_copied', 0):,} bytes")
                report.append(f"⏱️ 總執行時間: {replication.get('total_duration_seconds', 0):.1f} 秒")
                report.append("")

                # 各表格詳細結果
                for table_name, table_result in replication.get("tables_results", {}).items():
                    report.append(f"📋 {table_name}:")
                    if table_result.get("success"):
                        report.append(f"   ✅ 狀態: 成功")
                        report.append(f"   🆔 作業ID: {table_result.get('job_id', 'N/A')}")
                        report.append(f"   📈 筆數: {table_result.get('rows_copied', 0):,}")
                        report.append(f"   💾 大小: {table_result.get('bytes_copied', 0):,} bytes")
                        report.append(f"   ⏱️ 時間: {table_result.get('duration_seconds', 0):.1f} 秒")
                    else:
                        report.append(f"   ❌ 狀態: 失敗")
                        report.append(f"   📝 錯誤: {table_result.get('error', 'N/A')}")
                    report.append("")

                # 驗證結果
                report.append("🔍 驗證結果")
                report.append("-" * 60)
                if verification.get("overall_verification_passed"):
                    report.append(f"✅ 整體驗證: 通過 ({verification.get('tables_passed', 0)}/{verification.get('tables_verified', 0)})")
                else:
                    report.append(f"❌ 整體驗證: 失敗 ({verification.get('tables_passed', 0)}/{verification.get('tables_verified', 0)})")

                for table_name, table_verification in verification.get("tables", {}).items():
                    report.append(f"   📋 {table_name}: {('✅ 通過' if table_verification.get('verification_passed') else '❌ 失敗')}")
                    if not table_verification.get("verification_passed"):
                        if not table_verification.get("count_match"):
                            report.append(f"      筆數不匹配: 來源 {table_verification.get('source_count', 0):,} vs 目標 {table_verification.get('target_count', 0):,}")
                        if not table_verification.get("latest_data_match"):
                            report.append(f"      最新資料日期不匹配")
                        if not table_verification.get("target_exists"):
                            report.append(f"      目標表格不存在")

            else:
                report.append(f"❌ 複製狀態: 失敗")
                report.append(f"📝 錯誤訊息: {replication.get('error', 'N/A')}")
        else:
            report.append("⏭️ 複製狀態: 跳過（資料已是最新）")
            report.append(f"📝 原因: {execution_results.get('skip_reason', 'N/A')}")

        # 成本資訊
        if "cost_result" in execution_results:
            cost = execution_results["cost_result"]
            report.append("")
            report.append("💰 成本資訊")
            report.append("-" * 50)
            report.append(f"總預估成本: ${cost.get('total_estimated_cost', 0):.4f} USD")
            report.append(f"成本狀態: {'✅ 可接受' if cost.get('cost_acceptable', False) else '❌ 超標'}")

            # 各表格成本明細
            for table_name, table_cost in cost.get("tables", {}).items():
                if "error" not in table_cost:
                    report.append(f"   📋 {table_name}: ${table_cost.get('estimated_cost', 0):.4f} USD ({table_cost.get('tb_size', 0):.4f} TB)")

        # 資料新鮮度
        if "freshness_result" in execution_results:
            freshness = execution_results["freshness_result"]
            report.append("")
            report.append("📊 資料新鮮度")
            report.append("-" * 50)
            report.append(f"整體新鮮度: {'✅ 新鮮' if freshness.get('overall_fresh', False) else '⚠️ 有表格較舊'}")

            for table_name, table_freshness in freshness.get("tables", {}).items():
                report.append(f"   📋 {table_name}:")
                report.append(f"      最新資料日期: {table_freshness.get('latest_data_date', 'N/A')}")
                report.append(f"      更新時間差: {table_freshness.get('hours_since_update', 0):.1f} 小時")
                report.append(f"      新鮮度: {'✅ 新鮮' if table_freshness.get('is_fresh', False) else '⚠️ 較舊'}")

        report.append("")
        report.append("=" * 100)
        return "\\n".join(report)

    def run_replication(self) -> Dict[str, Any]:
        """執行單一表格複製流程"""
        execution_results = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "success": False,
            "replication_executed": False,
            "table_name": self.current_table,
            "schedule_type": self.table_config["schedule_type"],
            "error": None
        }

        try:
            logger.info(f"🚀 開始 {self.current_table} 資料複製流程...")
            logger.info(f"📋 排程類型: {self.table_config['schedule_type']}")

            # 1. 身份驗證
            if not self.authenticate():
                execution_results["error"] = "身份驗證失敗"
                return execution_results

            # 2. 檢查資料新鮮度
            freshness_result = self.check_source_data_freshness(self.current_table)
            execution_results["freshness_result"] = freshness_result

            if freshness_result.get("error"):
                execution_results["error"] = f"資料新鮮度檢查失敗: {freshness_result['error']}"
                return execution_results

            # 3. 檢查是否需要複製
            replication_check = self.check_replication_needed(self.current_table)
            execution_results["replication_check"] = replication_check

            if replication_check.get("error"):
                execution_results["error"] = f"複製需求檢查失敗: {replication_check['error']}"
                return execution_results

            if not replication_check.get("need_replication", False):
                execution_results["success"] = True
                execution_results["skip_reason"] = "表格已是最新狀態"
                logger.info(f"⏭️ 跳過複製: {execution_results['skip_reason']}")
                return execution_results

            # 4. 估算成本
            cost_result = self.estimate_replication_cost([self.current_table])
            execution_results["cost_result"] = cost_result

            if cost_result.get("error"):
                execution_results["error"] = f"成本估算失敗: {cost_result['error']}"
                return execution_results

            if not cost_result["cost_acceptable"]:
                execution_results["error"] = f"複製成本過高: ${cost_result['total_estimated_cost']:.4f} USD"
                return execution_results

            # 5. 執行複製
            replication_result = self.execute_single_table_replication()
            execution_results["replication_result"] = replication_result
            execution_results["replication_executed"] = True

            if not replication_result["success"]:
                execution_results["error"] = f"複製執行失敗: {replication_result.get('error', 'N/A')}"
                return execution_results

            # 6. 驗證結果
            verification_result = self.verify_single_table_result()
            execution_results["verification_result"] = verification_result

            if not verification_result.get("verification_passed", False):
                execution_results["error"] = f"複製驗證失敗: {verification_result.get('error', '資料不一致')}"
                return execution_results

            execution_results["success"] = True
            logger.info(f"✅ {self.current_table} 複製流程完成!")

        except Exception as e:
            execution_results["error"] = f"流程執行失敗: {e}"
            logger.error(f"❌ 流程執行失敗: {e}")

        finally:
            execution_results["end_time"] = datetime.now(timezone.utc).isoformat()

        return execution_results

    def execute_single_table_replication(self) -> Dict[str, Any]:
        """執行單一表格資料複製"""
        result = {
            "success": False,
            "table_name": self.current_table,
            "rows_copied": 0,
            "bytes_copied": 0,
            "duration_seconds": 0,
            "error": None
        }

        start_time = datetime.now(timezone.utc)

        try:
            source_table_id = self.table_config["source"]
            target_table_id = self.table_config["target"]

            logger.info(f"🔄 開始複製 {self.current_table}...")
            logger.info(f"   來源: {source_table_id}")
            logger.info(f"   目標: {target_table_id}")

            # 建立複製任務
            job_config = bigquery.CopyJobConfig()
            job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE

            job = self.client.copy_table(
                sources=source_table_id,
                destination=target_table_id,
                job_config=job_config
            )

            # 等待完成
            job.result()

            # 取得複製統計
            if job.state == "DONE":
                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                result["duration_seconds"] = duration

                # 查詢目標表格統計
                target_table = self.client.get_table(target_table_id)
                result["rows_copied"] = target_table.num_rows
                result["bytes_copied"] = target_table.num_bytes
                result["success"] = True

                logger.info(f"✅ {self.current_table} 複製完成!")
                logger.info(f"   複製行數: {result['rows_copied']:,}")
                logger.info(f"   資料大小: {result['bytes_copied']:,} bytes")
                logger.info(f"   執行時間: {result['duration_seconds']:.2f} 秒")
            else:
                result["error"] = f"複製任務失敗: {job.error_result}"

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ {self.current_table} 複製失敗: {e}")

        return result

    def verify_single_table_result(self) -> Dict[str, Any]:
        """驗證單一表格複製結果"""
        result = {
            "verification_passed": False,
            "table_name": self.current_table,
            "source_count": 0,
            "target_count": 0,
            "count_match": False,
            "error": None
        }

        try:
            source_table_id = self.table_config["source"]
            target_table_id = self.table_config["target"]

            logger.info(f"🔍 驗證 {self.current_table} 複製結果...")

            # 查詢來源表格計數
            source_query = f"SELECT COUNT(*) as count FROM `{source_table_id}`"
            source_job = self.client.query(source_query)
            source_rows = list(source_job.result())
            result["source_count"] = source_rows[0].count

            # 查詢目標表格計數
            target_query = f"SELECT COUNT(*) as count FROM `{target_table_id}`"
            target_job = self.client.query(target_query)
            target_rows = list(target_job.result())
            result["target_count"] = target_rows[0].count

            # 比較計數
            result["count_match"] = result["source_count"] == result["target_count"]
            result["verification_passed"] = result["count_match"]

            logger.info(f"📊 驗證結果 ({self.current_table}):")
            logger.info(f"   來源計數: {result['source_count']:,}")
            logger.info(f"   目標計數: {result['target_count']:,}")
            logger.info(f"   計數一致: {'✅ 是' if result['count_match'] else '❌ 否'}")

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ {self.current_table} 驗證失敗: {e}")

        return result

def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description="自動化每日資料複製")
    parser.add_argument("--dry-run", action="store_true", help="僅檢查狀態，不執行複製")
    parser.add_argument("--force", action="store_true", help="強制執行複製")

    args = parser.parse_args()

    replicator = AutomatedDailyReplicator()

    if args.dry_run:
        # DRY RUN 模式
        logger.info("🔍 DRY RUN 模式 - 僅檢查狀態")

        if not replicator.authenticate():
            sys.exit(1)

        freshness = replicator.check_source_data_freshness()
        replication_check = replicator.check_replication_needed()
        cost = replicator.estimate_replication_cost()

        print("\\n📊 檢查結果摘要:")
        print(f"資料新鮮度: {'✅ 新鮮' if freshness.get('is_fresh') else '⚠️ 較舊'}")
        print(f"需要複製: {'✅ 是' if replication_check.get('need_replication') else '❌ 否'}")
        print(f"成本可接受: {'✅ 是' if cost.get('cost_acceptable') else '❌ 否'}")

    else:
        # 實際執行
        results = replicator.run_daily_replication()

        # 生成報告
        report = replicator.generate_execution_report(results)
        print("\\n" + report)

        # 儲存報告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"daily_replication_report_{timestamp}.txt"

        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"📄 報告已儲存至: {report_filename}")
        except Exception as e:
            logger.warning(f"⚠️ 報告儲存失敗: {e}")

        # 設定退出碼
        if not results["success"]:
            logger.error(f"❌ 執行失敗: {results.get('error', 'N/A')}")
            sys.exit(1)

if __name__ == "__main__":
    main()
