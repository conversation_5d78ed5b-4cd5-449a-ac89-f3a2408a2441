#!/usr/bin/env python3
"""
家樂福每日增量合併 Cloud Function 入口點

這個 Cloud Function 負責執行家樂福離線交易資料的每日增量合併，
從 carrefour_offline_transaction_day 表增量更新到 carrefour_offline_transaction 表。

設計原則：
1. HTTP 觸發器，支援 Cloud Scheduler 排程執行
2. 完整的錯誤處理和日誌記錄
3. 參數驗證和成本控制
4. 詳細的執行報告
"""

import json
import logging
import os
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import functions_framework
from flask import Request
from automated_daily_merge import CarrefourDailyMerger

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_request_parameters(request_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    驗證請求參數

    Args:
        request_json: 請求 JSON 資料

    Returns:
        驗證後的參數字典
    """
    # 預設參數
    default_params = {
        "target_date": "auto",
        "lookback_days": 3,
        "dry_run": False,
        "max_cost_usd": 5.0,
        "operation_type": "daily_incremental_merge"
    }

    # 合併請求參數與預設值
    params = {}
    for key, default_value in default_params.items():
        if key in request_json:
            params[key] = request_json[key]
        else:
            params[key] = default_value

    # 參數驗證
    if params["lookback_days"] < 0 or params["lookback_days"] > 30:
        raise ValueError("lookback_days 必須在 0-30 之間")

    if params["max_cost_usd"] < 0 or params["max_cost_usd"] > 100:
        raise ValueError("max_cost_usd 必須在 0-100 之間")

    # 如果是測試環境，強制啟用 dry_run
    environment = os.getenv("ENVIRONMENT", "dev")
    if environment in ["dev", "test"]:
        params["dry_run"] = True
        logger.info(f"測試環境 ({environment}) 強制啟用 dry_run 模式")

    return params

def create_error_response(error_message: str, status_code: int = 500) -> Dict[str, Any]:
    """
    建立錯誤回應

    Args:
        error_message: 錯誤訊息
        status_code: HTTP 狀態碼

    Returns:
        錯誤回應字典
    """
    return {
        "success": False,
        "error": error_message,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "status_code": status_code
    }

def create_success_response(execution_report: Dict[str, Any]) -> Dict[str, Any]:
    """
    建立成功回應

    Args:
        execution_report: 執行報告

    Returns:
        成功回應字典
    """
    return {
        "success": True,
        "execution_report": execution_report,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "status_code": 200
    }

@functions_framework.http
def main(request: Request) -> Dict[str, Any]:
    """
    Cloud Function 主要入口點

    Args:
        request: HTTP 請求物件

    Returns:
        執行結果的 JSON 回應
    """
    execution_start_time = datetime.now(timezone.utc)

    try:
        # 記錄執行開始
        logger.info("=== 家樂福每日增量合併 Cloud Function 開始執行 ===")
        logger.info(f"執行時間: {execution_start_time.isoformat()}")
        logger.info(f"環境: {os.getenv('ENVIRONMENT', 'unknown')}")

        # 解析請求資料
        if request.method == 'GET':
            # 支援 GET 請求進行健康檢查
            return {
                "success": True,
                "message": "家樂福每日增量合併 Cloud Function 運行正常",
                "timestamp": execution_start_time.isoformat(),
                "environment": os.getenv("ENVIRONMENT", "unknown"),
                "operation_type": "health_check"
            }

        if request.method != 'POST':
            return create_error_response(f"不支援的請求方法: {request.method}", 405)

        # 解析 JSON 請求
        try:
            request_json = request.get_json(silent=True) or {}
        except Exception as e:
            return create_error_response(f"JSON 解析失敗: {str(e)}", 400)

        logger.info(f"請求參數: {json.dumps(request_json, ensure_ascii=False)}")

        # 驗證參數
        try:
            validated_params = validate_request_parameters(request_json)
            logger.info(f"驗證後參數: {json.dumps(validated_params, ensure_ascii=False)}")
        except ValueError as e:
            return create_error_response(f"參數驗證失敗: {str(e)}", 400)

        # 建立合併器並執行
        merger = CarrefourDailyMerger(
            project_id=os.getenv("PROJECT_ID", "tagtoo-tracking"),
            source_dataset="event_prod",
            day_table="carrefour_offline_transaction_day",
            main_table="carrefour_offline_transaction"
        )

        # 執行增量合併
        execution_report = merger.execute_merge(
            target_date=validated_params["target_date"],
            lookback_days=validated_params["lookback_days"],
            dry_run=validated_params["dry_run"],
            max_cost_usd=validated_params["max_cost_usd"]
        )

        # 計算執行時間
        execution_end_time = datetime.now(timezone.utc)
        execution_duration = (execution_end_time - execution_start_time).total_seconds()

        # 新增執行時間資訊到報告
        execution_report["execution_metadata"] = {
            "start_time": execution_start_time.isoformat(),
            "end_time": execution_end_time.isoformat(),
            "duration_seconds": execution_duration,
            "cloud_function_name": os.getenv("K_SERVICE", "carrefour-daily-merge"),
            "environment": os.getenv("ENVIRONMENT", "unknown"),
            "revision": os.getenv("K_REVISION", "unknown")
        }

        # 記錄執行結果
        if execution_report.get("errors"):
            logger.warning(f"執行完成但有錯誤: {execution_report['errors']}")
        else:
            logger.info("執行成功完成")

        logger.info(f"執行時間: {execution_duration:.2f} 秒")

        # 記錄重要指標
        summary = execution_report.get("results", {}).get("summary", {})
        if summary:
            logger.info(f"處理摘要: {json.dumps(summary, ensure_ascii=False)}")

        logger.info("=== 家樂福每日增量合併 Cloud Function 執行完成 ===")

        return create_success_response(execution_report)

    except Exception as e:
        # 記錄詳細錯誤
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "execution_time": datetime.now(timezone.utc).isoformat()
        }

        logger.error(f"Cloud Function 執行失敗: {json.dumps(error_details, ensure_ascii=False)}")

        return create_error_response(
            f"內部錯誤: {str(e)}",
            500
        )

# 本地測試支援
if __name__ == "__main__":
    """本地測試執行"""
    import argparse
    from flask import Flask, request as flask_request

    app = Flask(__name__)

    @app.route("/", methods=["GET", "POST"])
    def test_endpoint():
        return main(flask_request)

    # 命令列參數
    parser = argparse.ArgumentParser(description='本地測試家樂福每日合併 Cloud Function')
    parser.add_argument('--port', type=int, default=8080, help='本地測試埠號')
    parser.add_argument('--debug', action='store_true', help='除錯模式')

    args = parser.parse_args()

    print(f"啟動本地測試伺服器: http://localhost:{args.port}")
    print("測試 GET 請求: curl http://localhost:8080")
    print("""測試 POST 請求: curl -X POST http://localhost:8080 \\
  -H "Content-Type: application/json" \\
  -d '{"dry_run": true, "target_date": "auto", "lookback_days": 3}'""")

    app.run(host="0.0.0.0", port=args.port, debug=args.debug)
