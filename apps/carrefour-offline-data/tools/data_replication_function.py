#!/usr/bin/env python3
"""
家樂福資料複製 Cloud Function
支援 operation_type 參數處理

Author: AI Assistant
Date: 2025-09-04
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any
import functions_framework
from flask import Request

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 導入複製工具
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automated_daily_replication import AutomatedDailyReplicator
from automated_monthly_merge import execute_monthly_merge


def _serialize_datetime_objects(obj):
    """遞迴處理物件中的 datetime 和 date 物件，轉換為字串"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, __import__('datetime').date):  # 處理 date 物件
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: _serialize_datetime_objects(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_serialize_datetime_objects(item) for item in obj]
    else:
        return obj


@functions_framework.http
def main(request: Request) -> str:
    """
    Cloud Function 主要入口點

    支援的 operation_type:
    - data_replication: 執行資料複製
    - monthly_merge: 執行月度資料合併

    Args:
        request: HTTP 請求物件

    Returns:
        str: JSON 回應
    """
    try:
        logger.info("開始執行家樂福資料複製 Cloud Function...")

        # 解析請求參數
        request_data = _parse_request(request)

        # 獲取操作類型
        operation_type = request_data.get('operation_type', 'data_replication')
        execution_mode = request_data.get('execution_mode', 'scheduled')
        environment = request_data.get('environment', 'prod')
        force_copy = request_data.get('force_copy', False)

        logger.info(f"操作類型: {operation_type}")
        logger.info(f"執行模式: {execution_mode}")
        logger.info(f"環境: {environment}")
        logger.info(f"強制複製: {force_copy}")

        # 根據操作類型執行相應功能
        if operation_type == 'data_replication':
            result = _execute_data_replication(request_data)
        elif operation_type == 'monthly_merge':
            result = _execute_monthly_merge(request_data)
        else:
            raise ValueError(f"不支援的操作類型: {operation_type}")

        # 回傳成功回應 (處理 datetime 序列化)
        response_data = {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'operation_type': operation_type,
            'execution_mode': execution_mode,
            'result': _serialize_datetime_objects(result)
        }
        return json.dumps(response_data)

    except Exception as e:
        logger.error(f"Cloud Function 執行失敗: {e}")
        return json.dumps({
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 500


def _parse_request(request: Request) -> Dict[str, Any]:
    """解析 HTTP 請求"""
    try:
        # 支援 Cloud Scheduler 的 POST 請求
        if request.method == 'POST':
            request_json = request.get_json(silent=True)
            if request_json:
                return request_json

        # 支援 GET 請求的查詢參數
        return dict(request.args)

    except Exception as e:
        logger.warning(f"解析請求失敗: {e}")
        return {}


def _execute_data_replication(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """執行資料複製"""

    # 從請求中獲取要複製的表格名稱（新的單表格模式）
    table_name = request_data.get("table_name", "offline_transaction_day")
    logger.info(f"開始執行 {table_name} 資料複製...")

    # 初始化單表格複製器
    try:
        replicator = AutomatedDailyReplicator(table_name=table_name)
        logger.info(f"✅ 初始化複製器成功，表格: {table_name}")
        logger.info(f"📋 排程類型: {replicator.table_config['schedule_type']}")
    except ValueError as e:
        logger.error(f"❌ 複製器初始化失敗: {e}")
        raise

    # 執行複製流程
    try:
        results = replicator.run_replication()

        if results["success"]:
            logger.info(f"✅ {table_name} 資料複製完成")

            # 取得複製統計
            replication_result = results.get("replication_result", {})
            rows_copied = replication_result.get("rows_copied", 0)
            bytes_copied = replication_result.get("bytes_copied", 0)
            duration_seconds = replication_result.get("duration_seconds", 0)

            return {
                "operation": "single_table_data_replication",
                "success": True,
                "table_name": table_name,
                "schedule_type": results.get("schedule_type"),
                "replication_executed": results.get("replication_executed", False),
                "stats": {
                    "rows_copied": rows_copied,
                    "bytes_copied": bytes_copied,
                    "duration_seconds": duration_seconds
                },
                "freshness_check": results.get("freshness_result", {}),
                "verification": results.get("verification_result", {}),
                "cost_analysis": results.get("cost_result", {}),
                "skip_reason": results.get("skip_reason")
            }
        else:
            error_msg = results.get("error", "複製失敗")
            logger.error(f"❌ {table_name} 資料複製失敗: {error_msg}")
            raise Exception(error_msg)

    except Exception as e:
        logger.error(f"{table_name} 資料複製執行失敗: {e}")
        raise


def _execute_monthly_merge(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    執行月度資料合併

    Args:
        request_data: 請求資料，支援參數：
            - dry_run: 是否為測試模式 (預設: True)
            - max_days_back: 最多回溯天數 (預設: 90)

    Returns:
        Dict: 合併執行結果
    """
    logger.info("開始執行月度資料合併...")

    # 解析參數
    dry_run = request_data.get('dry_run', True)  # 預設為測試模式，安全優先
    max_days_back = request_data.get('max_days_back', 90)

    # 強制轉換 dry_run 為布林值
    if isinstance(dry_run, str):
        dry_run = dry_run.lower() in ('false', '0', 'no')  # 只有明確為 false 才執行生產模式
    else:
        dry_run = not bool(dry_run)  # 其他情況都轉為安全的測試模式

    logger.info(f"合併參數: dry_run={dry_run}, max_days_back={max_days_back}")

    try:
        # 執行合併
        merge_result = execute_monthly_merge(
            project_id="tagtoo-tracking",
            dry_run=dry_run,
            max_days_back=max_days_back
        )

        if merge_result.get('success', False):
            total_merged = merge_result.get('total_records_merged', 0)
            operations_count = len(merge_result.get('merge_operations', []))

            mode_text = "測試" if dry_run else "生產"
            logger.info(f"✅ 月度資料合併完成 ({mode_text}模式)，共執行 {operations_count} 個操作，合併 {total_merged:,} 筆記錄")

            return {
                "operation": "monthly_data_merge",
                "success": True,
                "mode": "dry_run" if dry_run else "production",
                "stats": {
                    "total_records_merged": total_merged,
                    "operations_executed": operations_count,
                    "duration_seconds": merge_result.get('duration_seconds', 0)
                },
                "merge_operations": merge_result.get('merge_operations', []),
                "boundary_analysis": merge_result.get('boundary_analysis', {}),
                "execution_summary": merge_result
            }
        else:
            error_msg = merge_result.get('error_message', '合併失敗')
            logger.error(f"❌ 月度資料合併失敗: {error_msg}")
            raise Exception(error_msg)

    except Exception as e:
        logger.error(f"月度資料合併執行失敗: {e}")
        raise


# 本地測試支援
if __name__ == '__main__':
    # 本地測試模式
    import argparse

    parser = argparse.ArgumentParser(description='本地測試家樂福資料複製 Cloud Function')
    parser.add_argument('--operation-type', default='data_replication',
                       choices=['data_replication', 'monthly_merge'], help='操作類型')
    parser.add_argument('--execution-mode', default='manual', help='執行模式')
    parser.add_argument('--force-copy', action='store_true', help='強制複製')
    parser.add_argument('--table-name', default='offline_transaction_month', help='表格名稱 (複製模式)')
    parser.add_argument('--dry-run', action='store_true', help='測試模式 (合併模式)')
    parser.add_argument('--max-days-back', type=int, default=90, help='最多回溯天數 (合併模式)')

    args = parser.parse_args()

    # 模擬 HTTP 請求
    class MockRequest:
        def __init__(self, data):
            self.data = data
            self.method = 'POST'

        def get_json(self, silent=True):
            return self.data

    test_data = {
        'operation_type': args.operation_type,
        'execution_mode': args.execution_mode,
        'force_copy': args.force_copy,
        'table_name': args.table_name,
        'dry_run': args.dry_run,
        'max_days_back': args.max_days_back,
        'environment': 'test'
    }

    mock_request = MockRequest(test_data)
    result = main(mock_request)
    print(result)
