#!/usr/bin/env python3
"""
多層次資料驗證工具

實作四個層次的資料驗證：
1. Level 1: 基本統計比較（總行數、欄位數、資料大小）
2. Level 2: 資料類型和 NULL 值分布驗證
3. Level 3: 隨機抽樣 1000 筆進行逐欄位比對
4. Level 4: 複雜 STRUCT 欄位（items 陣列）的深度結構驗證

符合 carrefour-validation-spec.md 的四大驗證類別

Author: AI Assistant
Date: 2025-08-15
"""

import os
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict, field

from google.cloud import bigquery
import pandas as pd

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    from gcloud_utils import get_gcloud_credentials_path

@dataclass
class ValidationResult:
    """驗證結果結構"""
    level: int
    category: str
    test_name: str
    status: str  # PASS, FAIL, WARNING, SKIP
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)

class DataValidator:
    """多層次資料驗證器"""

    def __init__(self):
        self.source_project = "tw-eagle-prod"
        self.source_dataset = "rmn_tagtoo"
        self.source_table = "offline_transaction_day"

        self.target_project = "tagtoo-tracking"
        self.target_dataset = "event_prod"
        self.target_table = "carrefour_offline_data"

        self.source_client = None
        self.target_client = None

        # 驗證結果
        self.validation_results: List[ValidationResult] = []
        self.validation_summary = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'warning_tests': 0,
            'skipped_tests': 0,
            'overall_status': 'UNKNOWN'
        }

    def authenticate(self) -> bool:
        """設定認證"""
        try:
            print("🔐 設定認證...")

            # 確保使用正確的認證配置 - 使用動態路徑
            credentials_path = get_gcloud_credentials_path()
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
            print(f"使用認證檔案: {credentials_path}")

            # 來源專案認證 (使用 Service Account Impersonation)
            self.source_client = bigquery.Client(project=self.source_project)

            # 目標專案認證
            self.target_client = bigquery.Client(project=self.target_project)

            print("✅ 認證設定成功")
            return True

        except Exception as e:
            print(f"❌ 認證失敗: {e}")
            return False

    def add_result(self, level: int, category: str, test_name: str, status: str, message: str, details: Dict[str, Any] = None):
        """添加驗證結果"""
        result = ValidationResult(
            level=level,
            category=category,
            test_name=test_name,
            status=status,
            message=message,
            details=details or {},
            timestamp=datetime.now()
        )

        self.validation_results.append(result)

        # 更新統計
        self.validation_summary['total_tests'] += 1
        if status == 'PASS':
            self.validation_summary['passed_tests'] += 1
        elif status == 'FAIL':
            self.validation_summary['failed_tests'] += 1
        elif status == 'WARNING':
            self.validation_summary['warning_tests'] += 1
        elif status == 'SKIP':
            self.validation_summary['skipped_tests'] += 1

    def level1_basic_statistics(self) -> bool:
        """Level 1: 基本統計比較"""
        print("\n📊 Level 1: 基本統計比較")
        print("-" * 40)

        try:
            # 獲取來源表格統計
            source_table = self.source_client.get_table(f"{self.source_project}.{self.source_dataset}.{self.source_table}")
            source_stats = {
                'num_rows': source_table.num_rows,
                'num_bytes': source_table.num_bytes,
                'field_count': len(source_table.schema),
                'created': source_table.created.isoformat() if source_table.created else None,
                'modified': source_table.modified.isoformat() if source_table.modified else None
            }

            # 獲取目標表格統計
            target_table = self.target_client.get_table(f"{self.target_project}.{self.target_dataset}.{self.target_table}")
            target_stats = {
                'num_rows': target_table.num_rows,
                'num_bytes': target_table.num_bytes,
                'field_count': len(target_table.schema),
                'created': target_table.created.isoformat() if target_table.created else None,
                'modified': target_table.modified.isoformat() if target_table.modified else None
            }

            # 比較行數
            if source_stats['num_rows'] == target_stats['num_rows']:
                self.add_result(1, "基本統計", "行數比較", "PASS",
                              f"行數一致: {source_stats['num_rows']:,}",
                              {'source_rows': source_stats['num_rows'], 'target_rows': target_stats['num_rows']})
            else:
                self.add_result(1, "基本統計", "行數比較", "FAIL",
                              f"行數不一致: 來源 {source_stats['num_rows']:,}, 目標 {target_stats['num_rows']:,}",
                              {'source_rows': source_stats['num_rows'], 'target_rows': target_stats['num_rows']})

            # 比較欄位數
            if source_stats['field_count'] == target_stats['field_count']:
                self.add_result(1, "基本統計", "欄位數比較", "PASS",
                              f"欄位數一致: {source_stats['field_count']}",
                              {'source_fields': source_stats['field_count'], 'target_fields': target_stats['field_count']})
            else:
                self.add_result(1, "基本統計", "欄位數比較", "FAIL",
                              f"欄位數不一致: 來源 {source_stats['field_count']}, 目標 {target_stats['field_count']}",
                              {'source_fields': source_stats['field_count'], 'target_fields': target_stats['field_count']})

            # 比較資料大小（允許一定誤差）
            size_diff_ratio = abs(source_stats['num_bytes'] - target_stats['num_bytes']) / source_stats['num_bytes'] if source_stats['num_bytes'] > 0 else 0
            if size_diff_ratio <= 0.05:  # 允許 5% 誤差
                self.add_result(1, "基本統計", "資料大小比較", "PASS",
                              f"資料大小相近: 來源 {source_stats['num_bytes']:,} bytes, 目標 {target_stats['num_bytes']:,} bytes",
                              {'source_bytes': source_stats['num_bytes'], 'target_bytes': target_stats['num_bytes'], 'diff_ratio': size_diff_ratio})
            else:
                self.add_result(1, "基本統計", "資料大小比較", "WARNING",
                              f"資料大小差異較大: 來源 {source_stats['num_bytes']:,} bytes, 目標 {target_stats['num_bytes']:,} bytes (差異 {size_diff_ratio:.2%})",
                              {'source_bytes': source_stats['num_bytes'], 'target_bytes': target_stats['num_bytes'], 'diff_ratio': size_diff_ratio})

            return True

        except Exception as e:
            self.add_result(1, "基本統計", "統計獲取", "FAIL", f"無法獲取基本統計: {e}")
            return False

    def level2_data_types_and_nulls(self) -> bool:
        """Level 2: 資料類型和 NULL 值分布驗證"""
        print("\n🔍 Level 2: 資料類型和 NULL 值分布驗證")
        print("-" * 40)

        try:
            # 檢查是否有查詢權限
            test_query = "SELECT 1 as test_value"
            try:
                self.source_client.query(test_query).result()
                has_query_permission = True
            except:
                has_query_permission = False

            if not has_query_permission:
                self.add_result(2, "資料類型", "權限檢查", "SKIP", "缺少查詢權限，跳過 Level 2 驗證")
                return False

            # 生成 NULL 值檢查查詢
            source_table_ref = f"`{self.source_project}.{self.source_dataset}.{self.source_table}`"
            target_table_ref = f"`{self.target_project}.{self.target_dataset}.{self.target_table}`"

            # 檢查主要欄位的 NULL 值分布
            main_fields = ['event_name', 'store_id', 'transaction_id', 'total_amount']

            for field in main_fields:
                # 來源表格 NULL 值統計
                source_null_query = f"""
                SELECT
                    COUNT(*) as total_count,
                    COUNTIF({field} IS NULL) as null_count,
                    COUNTIF({field} IS NULL) / COUNT(*) as null_ratio
                FROM {source_table_ref}
                """

                # 目標表格 NULL 值統計
                target_null_query = f"""
                SELECT
                    COUNT(*) as total_count,
                    COUNTIF({field} IS NULL) as null_count,
                    COUNTIF({field} IS NULL) / COUNT(*) as null_ratio
                FROM {target_table_ref}
                """

                try:
                    source_result = self.source_client.query(source_null_query).to_dataframe()
                    target_result = self.target_client.query(target_null_query).to_dataframe()

                    source_null_ratio = source_result.iloc[0]['null_ratio']
                    target_null_ratio = target_result.iloc[0]['null_ratio']

                    # 比較 NULL 值比例
                    ratio_diff = abs(source_null_ratio - target_null_ratio)
                    if ratio_diff <= 0.01:  # 允許 1% 誤差
                        self.add_result(2, "NULL 值分布", f"{field} NULL 比例", "PASS",
                                      f"{field} NULL 值比例一致: {source_null_ratio:.2%}",
                                      {'field': field, 'source_null_ratio': source_null_ratio, 'target_null_ratio': target_null_ratio})
                    else:
                        self.add_result(2, "NULL 值分布", f"{field} NULL 比例", "FAIL",
                                      f"{field} NULL 值比例不一致: 來源 {source_null_ratio:.2%}, 目標 {target_null_ratio:.2%}",
                                      {'field': field, 'source_null_ratio': source_null_ratio, 'target_null_ratio': target_null_ratio})

                except Exception as e:
                    self.add_result(2, "NULL 值分布", f"{field} NULL 檢查", "FAIL", f"檢查 {field} NULL 值失敗: {e}")

            return True

        except Exception as e:
            self.add_result(2, "資料類型", "整體檢查", "FAIL", f"Level 2 驗證失敗: {e}")
            return False

    def level3_random_sampling(self) -> bool:
        """Level 3: 隨機抽樣 1000 筆進行逐欄位比對"""
        print("\n🎲 Level 3: 隨機抽樣比對")
        print("-" * 40)

        try:
            # 檢查查詢權限
            try:
                self.source_client.query("SELECT 1").result()
                has_query_permission = True
            except:
                has_query_permission = False

            if not has_query_permission:
                self.add_result(3, "隨機抽樣", "權限檢查", "SKIP", "缺少查詢權限，跳過 Level 3 驗證")
                return False

            # 隨機抽樣查詢（使用 TABLESAMPLE）
            sample_size = 1000
            source_sample_query = f"""
            SELECT
                event_name, store_id, transaction_id, total_amount, payment_method,
                FARM_FINGERPRINT(CONCAT(CAST(transaction_id AS STRING), CAST(store_id AS STRING))) as sample_key
            FROM `{self.source_project}.{self.source_dataset}.{self.source_table}`
            TABLESAMPLE SYSTEM (0.1 PERCENT)
            ORDER BY sample_key
            LIMIT {sample_size}
            """

            target_sample_query = f"""
            SELECT
                event_name, store_id, transaction_id, total_amount, payment_method,
                FARM_FINGERPRINT(CONCAT(CAST(transaction_id AS STRING), CAST(store_id AS STRING))) as sample_key
            FROM `{self.target_project}.{self.target_dataset}.{self.target_table}`
            TABLESAMPLE SYSTEM (0.1 PERCENT)
            ORDER BY sample_key
            LIMIT {sample_size}
            """

            # 執行抽樣查詢
            source_sample = self.source_client.query(source_sample_query).to_dataframe()
            target_sample = self.target_client.query(target_sample_query).to_dataframe()

            # 比較樣本大小
            if len(source_sample) == len(target_sample):
                self.add_result(3, "隨機抽樣", "樣本大小", "PASS",
                              f"樣本大小一致: {len(source_sample)} 筆",
                              {'source_sample_size': len(source_sample), 'target_sample_size': len(target_sample)})
            else:
                self.add_result(3, "隨機抽樣", "樣本大小", "WARNING",
                              f"樣本大小不一致: 來源 {len(source_sample)} 筆, 目標 {len(target_sample)} 筆",
                              {'source_sample_size': len(source_sample), 'target_sample_size': len(target_sample)})

            # 基於 sample_key 進行匹配比對
            merged_sample = pd.merge(source_sample, target_sample, on='sample_key', suffixes=('_source', '_target'), how='inner')

            if len(merged_sample) > 0:
                # 逐欄位比對
                fields_to_compare = ['event_name', 'store_id', 'transaction_id', 'total_amount', 'payment_method']

                for field in fields_to_compare:
                    source_col = f"{field}_source"
                    target_col = f"{field}_target"

                    if source_col in merged_sample.columns and target_col in merged_sample.columns:
                        # 計算匹配率
                        matches = (merged_sample[source_col] == merged_sample[target_col]).sum()
                        total = len(merged_sample)
                        match_ratio = matches / total if total > 0 else 0

                        if match_ratio >= 0.95:  # 95% 匹配率
                            self.add_result(3, "隨機抽樣", f"{field} 匹配", "PASS",
                                          f"{field} 匹配率: {match_ratio:.2%} ({matches}/{total})",
                                          {'field': field, 'match_ratio': match_ratio, 'matches': matches, 'total': total})
                        else:
                            self.add_result(3, "隨機抽樣", f"{field} 匹配", "FAIL",
                                          f"{field} 匹配率過低: {match_ratio:.2%} ({matches}/{total})",
                                          {'field': field, 'match_ratio': match_ratio, 'matches': matches, 'total': total})

                self.add_result(3, "隨機抽樣", "整體匹配", "PASS",
                              f"成功匹配 {len(merged_sample)} 筆樣本資料",
                              {'matched_records': len(merged_sample)})
            else:
                self.add_result(3, "隨機抽樣", "整體匹配", "FAIL", "無法找到匹配的樣本資料")

            return True

        except Exception as e:
            self.add_result(3, "隨機抽樣", "整體檢查", "FAIL", f"Level 3 驗證失敗: {e}")
            return False

    def level4_struct_validation(self) -> bool:
        """Level 4: 複雜 STRUCT 欄位深度結構驗證"""
        print("\n🏗️ Level 4: STRUCT 欄位結構驗證")
        print("-" * 40)

        try:
            # 檢查查詢權限
            try:
                self.source_client.query("SELECT 1").result()
                has_query_permission = True
            except:
                has_query_permission = False

            if not has_query_permission:
                self.add_result(4, "STRUCT 驗證", "權限檢查", "SKIP", "缺少查詢權限，跳過 Level 4 驗證")
                return False

            # 驗證 items 陣列結構
            items_structure_query = f"""
            SELECT
                COUNT(*) as total_records,
                COUNTIF(items IS NOT NULL) as non_null_items,
                COUNTIF(ARRAY_LENGTH(items) > 0) as non_empty_items,
                AVG(ARRAY_LENGTH(items)) as avg_items_count,
                MAX(ARRAY_LENGTH(items)) as max_items_count
            FROM `{{project}}.{{dataset}}.{{table}}`
            WHERE items IS NOT NULL
            LIMIT 1000
            """

            # 來源表格 items 結構統計
            source_items_query = items_structure_query.format(
                project=self.source_project,
                dataset=self.source_dataset,
                table=self.source_table
            )

            # 目標表格 items 結構統計
            target_items_query = items_structure_query.format(
                project=self.target_project,
                dataset=self.target_dataset,
                table=self.target_table
            )

            source_items_stats = self.source_client.query(source_items_query).to_dataframe()
            target_items_stats = self.target_client.query(target_items_query).to_dataframe()

            if not source_items_stats.empty and not target_items_stats.empty:
                source_stats = source_items_stats.iloc[0]
                target_stats = target_items_stats.iloc[0]

                # 比較 items 陣列統計
                stats_to_compare = ['non_null_items', 'non_empty_items', 'avg_items_count', 'max_items_count']

                for stat in stats_to_compare:
                    source_val = source_stats[stat]
                    target_val = target_stats[stat]

                    # 計算相對差異
                    if source_val != 0:
                        diff_ratio = abs(source_val - target_val) / source_val
                    else:
                        diff_ratio = 0 if target_val == 0 else 1

                    if diff_ratio <= 0.05:  # 允許 5% 誤差
                        self.add_result(4, "STRUCT 驗證", f"items {stat}", "PASS",
                                      f"items {stat} 一致: 來源 {source_val}, 目標 {target_val}",
                                      {'stat': stat, 'source_value': source_val, 'target_value': target_val, 'diff_ratio': diff_ratio})
                    else:
                        self.add_result(4, "STRUCT 驗證", f"items {stat}", "FAIL",
                                      f"items {stat} 差異過大: 來源 {source_val}, 目標 {target_val} (差異 {diff_ratio:.2%})",
                                      {'stat': stat, 'source_value': source_val, 'target_value': target_val, 'diff_ratio': diff_ratio})

            return True

        except Exception as e:
            self.add_result(4, "STRUCT 驗證", "整體檢查", "FAIL", f"Level 4 驗證失敗: {e}")
            return False

    def run_all_validations(self) -> bool:
        """執行所有層次的驗證"""
        print("🚀 開始多層次資料驗證")
        print("=" * 60)

        # 執行各層次驗證
        self.level1_basic_statistics()
        self.level2_data_types_and_nulls()
        self.level3_random_sampling()
        self.level4_struct_validation()

        # 計算整體狀態
        if self.validation_summary['failed_tests'] == 0:
            if self.validation_summary['warning_tests'] == 0:
                self.validation_summary['overall_status'] = 'PASS'
            else:
                self.validation_summary['overall_status'] = 'PASS_WITH_WARNINGS'
        else:
            self.validation_summary['overall_status'] = 'FAIL'

        return self.validation_summary['overall_status'] in ['PASS', 'PASS_WITH_WARNINGS']

    def generate_reports(self):
        """生成驗證報告"""
        # JSON 格式報告
        report_data = {
            'summary': self.validation_summary,
            'results': [asdict(result) for result in self.validation_results]
        }

        with open('data_validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)

        # 人類可讀格式報告
        report_lines = [
            "# 多層次資料驗證報告",
            f"生成時間: {self.validation_summary['timestamp']}",
            "",
            "## 驗證摘要",
            f"- 總測試數: {self.validation_summary['total_tests']}",
            f"- 通過: {self.validation_summary['passed_tests']}",
            f"- 失敗: {self.validation_summary['failed_tests']}",
            f"- 警告: {self.validation_summary['warning_tests']}",
            f"- 跳過: {self.validation_summary['skipped_tests']}",
            f"- 整體狀態: {self.validation_summary['overall_status']}",
            ""
        ]

        # 按層次分組顯示結果
        for level in [1, 2, 3, 4]:
            level_results = [r for r in self.validation_results if r.level == level]
            if level_results:
                report_lines.append(f"## Level {level} 驗證結果")
                for result in level_results:
                    status_icon = {"PASS": "✅", "FAIL": "❌", "WARNING": "⚠️", "SKIP": "⏭️"}.get(result.status, "❓")
                    report_lines.append(f"- {status_icon} **{result.test_name}**: {result.message}")
                report_lines.append("")

        with open('data_validation_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print("📄 驗證報告已生成:")
        print("  - data_validation_report.json (結構化資料)")
        print("  - data_validation_report.md (人類可讀)")

def main():
    """主函數"""
    print("🔍 多層次資料驗證工具")
    print("=" * 60)

    validator = DataValidator()

    # 認證
    if not validator.authenticate():
        print("❌ 認證失敗，程式結束")
        return False

    # 執行驗證
    success = validator.run_all_validations()

    # 生成報告
    validator.generate_reports()

    # 顯示結果
    print(f"\n📊 驗證完成！")
    print(f"整體狀態: {validator.validation_summary['overall_status']}")
    print(f"通過/總數: {validator.validation_summary['passed_tests']}/{validator.validation_summary['total_tests']}")

    if success:
        print("🎉 資料驗證通過！")
    else:
        print("⚠️ 資料驗證發現問題，請檢查報告")

    return success

if __name__ == "__main__":
    main()
