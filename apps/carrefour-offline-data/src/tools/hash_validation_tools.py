#!/usr/bin/env python3
"""
SHA256 雜湊驗證工具
驗證 TO_HEX(ph_id) 與 user.ph 的對應關係正確性
實施逆向工程驗證和受控測試集驗證
"""

import os
import sys
import json
import hashlib
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any, Tuple
from google.cloud import bigquery

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    from gcloud_utils import get_gcloud_credentials_path


def setup_bigquery_client() -> bigquery.Client:
    """設定 BigQuery 客戶端"""
    credentials_path = get_gcloud_credentials_path()
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
    return bigquery.Client(project="tagtoo-tracking")


def run_query_with_cost_control(client: bigquery.Client, query: str, desc: str, max_cost_usd: float = 0.05) -> Dict[str, Any]:
    """執行查詢並控制成本"""
    try:
        # 成本估算
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=job_config)
        bytes_processed = job.total_bytes_processed
        cost_usd = (bytes_processed / (1024**4)) * 5.0  # $5/TB

        if cost_usd > max_cost_usd:
            return {
                "status": "skipped_high_cost",
                "desc": desc,
                "estimated_cost_usd": cost_usd,
                "max_cost_usd": max_cost_usd
            }

        # 執行查詢
        start_time = datetime.now()
        job = client.query(query)
        results = job.result()
        end_time = datetime.now()

        data = []
        for row in results:
            row_dict = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):
                    row_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    row_dict[key] = float(value)
                else:
                    row_dict[key] = value
            data.append(row_dict)

        return {
            "status": "success",
            "desc": desc,
            "estimated_cost_usd": cost_usd,
            "execution_time_seconds": (end_time - start_time).total_seconds(),
            "row_count": len(data),
            "data": data
        }

    except Exception as e:
        return {
            "status": "failed",
            "desc": desc,
            "error": str(e)
        }


def get_controlled_test_set(client: bigquery.Client) -> Dict[str, Any]:
    """建立受控測試集：獲取已知匹配的記錄"""
    print('🎯 建立受控測試集...')

    query = """
    WITH matched_records AS (
      SELECT
        offline.ph_id,
        TO_HEX(offline.ph_id) as ph_id_hex,
        online.user_ph,
        offline.transaction_id,
        offline.total_amount,
        DATE(TIMESTAMP_SECONDS(offline.event_times)) as transaction_date
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` offline
      JOIN `tagtoo-tracking.event_prod.tagtoo_event` online
        ON TO_HEX(offline.ph_id) = online.user_ph
      WHERE offline.ph_id IS NOT NULL
        AND online.user_ph IS NOT NULL
        AND offline.event_times IS NOT NULL
      ORDER BY RAND()
      LIMIT 150
    )
    SELECT * FROM matched_records
    """

    return run_query_with_cost_control(client, query, "受控測試集建立", max_cost_usd=0.1)


def validate_hex_conversion(test_data: List[Dict]) -> Dict[str, Any]:
    """驗證 TO_HEX(ph_id) 轉換的正確性"""
    print('🔍 驗證 HEX 轉換正確性...')

    validation_results = {
        "total_records": len(test_data),
        "successful_matches": 0,
        "failed_matches": 0,
        "match_rate": 0.0,
        "failed_cases": [],
        "sample_validations": []
    }

    for i, record in enumerate(test_data):
        ph_id_hex = record.get('ph_id_hex', '')
        user_ph = record.get('user_ph', '')

        # 驗證 HEX 轉換是否正確
        is_match = ph_id_hex.lower() == user_ph.lower()

        if is_match:
            validation_results["successful_matches"] += 1
        else:
            validation_results["failed_matches"] += 1
            validation_results["failed_cases"].append({
                "transaction_id": record.get('transaction_id'),
                "ph_id_hex": ph_id_hex,
                "user_ph": user_ph,
                "transaction_date": record.get('transaction_date')
            })

        # 保存前10個案例作為樣本
        if i < 10:
            validation_results["sample_validations"].append({
                "transaction_id": record.get('transaction_id'),
                "ph_id_hex": ph_id_hex,
                "user_ph": user_ph,
                "is_match": is_match,
                "transaction_date": record.get('transaction_date'),
                "total_amount": record.get('total_amount')
            })

    # 計算匹配率
    if validation_results["total_records"] > 0:
        validation_results["match_rate"] = (
            validation_results["successful_matches"] / validation_results["total_records"]
        ) * 100

    return validation_results


def verify_reverse_lookup(client: bigquery.Client, sample_user_ph: List[str]) -> Dict[str, Any]:
    """逆向驗證：從 user.ph 查找對應的 ph_id"""
    print('🔄 執行逆向查找驗證...')

    if not sample_user_ph:
        return {"status": "skipped", "reason": "no_sample_data"}

    # 只取前20個樣本以控制成本
    sample_list = sample_user_ph[:20]
    user_ph_str = "', '".join(sample_list)

    query = f"""
    SELECT
      online.user_ph,
      offline.ph_id,
      TO_HEX(offline.ph_id) as computed_hex,
      offline.transaction_id,
      COUNT(*) as occurrence_count
    FROM `tagtoo-tracking.event_prod.tagtoo_event` online
    JOIN `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` offline
      ON online.user_ph = TO_HEX(offline.ph_id)
    WHERE online.user_ph IN ('{user_ph_str}')
    GROUP BY online.user_ph, offline.ph_id, offline.transaction_id
    ORDER BY occurrence_count DESC
    LIMIT 50
    """

    return run_query_with_cost_control(client, query, "逆向查找驗證", max_cost_usd=0.05)


def analyze_hash_consistency(client: bigquery.Client) -> Dict[str, Any]:
    """分析雜湊一致性：檢查是否有重複或異常的雜湊值"""
    print('📊 分析雜湊一致性...')

    query = """
    WITH hash_analysis AS (
      SELECT
        TO_HEX(ph_id) as ph_id_hex,
        COUNT(DISTINCT ph_id) as unique_ph_ids,
        COUNT(*) as total_records,
        MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
        MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
      WHERE ph_id IS NOT NULL
      GROUP BY TO_HEX(ph_id)
      HAVING COUNT(DISTINCT ph_id) > 1  -- 找出可能的雜湊衝突
      ORDER BY unique_ph_ids DESC
      LIMIT 20
    )
    SELECT * FROM hash_analysis
    """

    return run_query_with_cost_control(client, query, "雜湊一致性分析", max_cost_usd=0.03)


def generate_validation_summary(validation_results: Dict, reverse_results: Dict, consistency_results: Dict) -> Dict[str, Any]:
    """生成驗證摘要報告"""
    print('📋 生成驗證摘要...')

    summary = {
        "validation_status": "PASS" if validation_results.get("match_rate", 0) >= 99.0 else "FAIL",
        "overall_assessment": "",
        "key_findings": [],
        "recommendations": [],
        "technical_details": {
            "hex_conversion_accuracy": validation_results.get("match_rate", 0),
            "test_sample_size": validation_results.get("total_records", 0),
            "failed_matches": validation_results.get("failed_matches", 0),
            "reverse_lookup_status": reverse_results.get("status", "unknown"),
            "hash_consistency_status": consistency_results.get("status", "unknown")
        }
    }

    # 評估結果
    match_rate = validation_results.get("match_rate", 0)
    if match_rate >= 99.5:
        summary["overall_assessment"] = "SHA256 雜湊驗證通過，TO_HEX(ph_id) 與 user.ph 對應關係正確"
        summary["key_findings"].append(f"HEX 轉換準確率：{match_rate:.2f}%，符合生產標準")
    elif match_rate >= 95.0:
        summary["overall_assessment"] = "SHA256 雜湊驗證基本通過，但存在少量異常需調查"
        summary["key_findings"].append(f"HEX 轉換準確率：{match_rate:.2f}%，建議調查失敗案例")
    else:
        summary["overall_assessment"] = "SHA256 雜湊驗證失敗，存在嚴重問題"
        summary["key_findings"].append(f"HEX 轉換準確率：{match_rate:.2f}%，不符合生產標準")

    # 逆向查找結果
    if reverse_results.get("status") == "success":
        reverse_count = reverse_results.get("row_count", 0)
        summary["key_findings"].append(f"逆向查找驗證成功：{reverse_count} 筆記錄確認雙向匹配")

    # 雜湊一致性結果
    if consistency_results.get("status") == "success":
        conflict_count = consistency_results.get("row_count", 0)
        if conflict_count == 0:
            summary["key_findings"].append("雜湊一致性檢查通過：無雜湊衝突")
        else:
            summary["key_findings"].append(f"發現 {conflict_count} 個潛在雜湊衝突，需進一步調查")

    # 建議
    if summary["validation_status"] == "PASS":
        summary["recommendations"] = [
            "SHA256 雜湊驗證通過，可以安全進行資料複製作業",
            "建議建立定期雜湊驗證機制，確保資料品質",
            "可以信任當前的 ph_id 與 user.ph 匹配邏輯"
        ]
    else:
        summary["recommendations"] = [
            "暫停資料複製作業，優先調查雜湊驗證失敗原因",
            "檢查 BigQuery TO_HEX() 函數的實作和資料類型",
            "與家樂福技術團隊確認 ph_id 的原始格式和編碼方式"
        ]

    return summary


def main():
    """主要執行函數"""
    print('🔐 SHA256 雜湊驗證工具')
    print('=' * 60)

    try:
        print('🔧 設定 BigQuery 客戶端...')
        client = setup_bigquery_client()
        print('✅ BigQuery 客戶端設定完成')

        validation_report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "purpose": "驗證 TO_HEX(ph_id) 與 user.ph 的對應關係正確性",
                "billing_project": "tagtoo-tracking",
                "cost_limit_usd": 0.2
            }
        }

        # 1. 建立受控測試集
        print('\n1️⃣ 建立受控測試集')
        test_set_result = get_controlled_test_set(client)
        validation_report["controlled_test_set"] = test_set_result

        if test_set_result.get("status") != "success":
            print(f"❌ 測試集建立失敗: {test_set_result.get('error', 'Unknown error')}")
            return

        test_data = test_set_result.get("data", [])
        print(f"✅ 成功建立測試集：{len(test_data)} 筆記錄")

        # 2. 驗證 HEX 轉換正確性
        print('\n2️⃣ 驗證 HEX 轉換正確性')
        hex_validation = validate_hex_conversion(test_data)
        validation_report["hex_validation"] = hex_validation

        print(f"✅ HEX 轉換驗證完成：匹配率 {hex_validation['match_rate']:.2f}%")

        # 3. 逆向查找驗證
        print('\n3️⃣ 逆向查找驗證')
        sample_user_ph = [record.get('user_ph') for record in test_data[:20] if record.get('user_ph')]
        reverse_validation = verify_reverse_lookup(client, sample_user_ph)
        validation_report["reverse_validation"] = reverse_validation

        # 4. 雜湊一致性分析
        print('\n4️⃣ 雜湊一致性分析')
        consistency_analysis = analyze_hash_consistency(client)
        validation_report["consistency_analysis"] = consistency_analysis

        # 5. 生成驗證摘要
        print('\n5️⃣ 生成驗證摘要')
        validation_summary = generate_validation_summary(
            hex_validation, reverse_validation, consistency_analysis
        )
        validation_report["validation_summary"] = validation_summary

        # 儲存驗證報告
        output_file = 'reports/hash_validation_results.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(validation_report, f, indent=2, ensure_ascii=False)

        print(f'\n✅ SHA256 雜湊驗證報告已儲存至: {output_file}')

        # 顯示關鍵結果
        display_validation_summary(validation_summary)

        # 計算總成本
        total_cost = sum([
            test_set_result.get("estimated_cost_usd", 0),
            reverse_validation.get("estimated_cost_usd", 0),
            consistency_analysis.get("estimated_cost_usd", 0)
        ])
        print(f'\n💰 總查詢成本: ${total_cost:.6f} USD')

    except Exception as e:
        print(f'❌ 執行錯誤: {e}')
        sys.exit(1)


def display_validation_summary(summary: Dict[str, Any]) -> None:
    """顯示驗證摘要"""
    print('\n🎯 SHA256 雜湊驗證摘要:')
    print(f'狀態: {summary.get("validation_status", "UNKNOWN")}')
    print(f'評估: {summary.get("overall_assessment", "")}')

    print('\n🔍 關鍵發現:')
    for finding in summary.get("key_findings", []):
        print(f'   • {finding}')

    print('\n💡 建議:')
    for rec in summary.get("recommendations", []):
        print(f'   • {rec}')


if __name__ == '__main__':
    main()
