#!/usr/bin/env python3
"""
家樂福離線資料複製到目標資料集

此腳本將驗證完成的離線資料複製到目標資料集 tagtoo-tracking.event_prod.carrefour_offline_data
包含完整的資料完整性檢查和複製作業報告。

執行方式：
    python src/copy_to_target_dataset.py

功能：
1. 資料完整性檢查
2. 執行資料複製作業
3. 驗證複製結果
4. 產出複製作業報告

作者：AI Assistant
日期：2025-08-20
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CarrefourDataCopier:
    """家樂福資料複製器"""

    def __init__(self):
        """初始化複製器"""
        self.client = bigquery.Client()
        self.source_table = "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
        self.target_table = "tagtoo-tracking.event_prod.carrefour_offline_transaction_day"
        self.reports_dir = "reports"

        # 確保報告目錄存在
        os.makedirs(self.reports_dir, exist_ok=True)

        logger.info(f"初始化資料複製器")
        logger.info(f"來源表格: {self.source_table}")
        logger.info(f"目標表格: {self.target_table}")

    def check_source_data_integrity(self) -> Dict[str, Any]:
        """檢查來源資料完整性"""
        logger.info("🔍 檢查來源資料完整性...")

        integrity_check = {
            'timestamp': datetime.now().isoformat(),
            'source_table': self.source_table,
            'checks': {}
        }

        try:
            # 1. 基本統計檢查
            basic_stats_query = f"""
            SELECT
                COUNT(*) as total_records,
                COUNT(DISTINCT transaction_id) as unique_transactions,
                COUNT(DISTINCT ph_id) as unique_ph_ids,
                COUNTIF(ph_id IS NULL) as null_ph_ids,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date,
                SUM(ARRAY_LENGTH(items)) as total_items
            FROM `{self.source_table}`
            """

            basic_stats = list(self.client.query(basic_stats_query))[0]
            integrity_check['checks']['basic_statistics'] = {
                'status': 'success',
                'data': dict(basic_stats)
            }

            # 2. 資料品質檢查
            quality_query = f"""
            SELECT
                COUNTIF(event_times IS NULL) as null_event_times,
                COUNTIF(items IS NULL OR ARRAY_LENGTH(items) = 0) as empty_items,
                COUNTIF(total_amount IS NULL) as null_total_amount,
                COUNTIF(total_amount < 0) as negative_amounts
            FROM `{self.source_table}`
            """

            quality_stats = list(self.client.query(quality_query))[0]
            integrity_check['checks']['data_quality'] = {
                'status': 'success',
                'data': dict(quality_stats)
            }

            # 3. Items 結構檢查
            items_query = f"""
            SELECT
                COUNT(*) as total_item_records,
                COUNTIF(item.item_id IS NULL) as null_item_ids,
                COUNTIF(item.item_name IS NULL) as null_item_names,
                COUNTIF(item.quantity IS NULL) as null_quantities,
                COUNTIF(item.unit_price IS NULL) as null_unit_prices
            FROM `{self.source_table}`,
            UNNEST(items) as item
            """

            items_stats = list(self.client.query(items_query))[0]
            integrity_check['checks']['items_structure'] = {
                'status': 'success',
                'data': dict(items_stats)
            }

            logger.info("✅ 來源資料完整性檢查完成")

        except Exception as e:
            logger.error(f"❌ 來源資料完整性檢查失敗: {e}")
            integrity_check['checks']['error'] = {
                'status': 'failed',
                'error': str(e)
            }

        return integrity_check

    def check_target_table_exists(self) -> bool:
        """檢查目標表格是否存在"""
        try:
            self.client.get_table(self.target_table)
            logger.info(f"✅ 目標表格 {self.target_table} 已存在")
            return True
        except NotFound:
            logger.info(f"⚠️ 目標表格 {self.target_table} 不存在，將建立新表格")
            return False

    def create_target_table(self) -> bool:
        """建立目標表格"""
        logger.info("🔧 建立目標表格...")

        try:
            # 基於來源表格結構建立目標表格
            create_query = f"""
            CREATE TABLE `{self.target_table}` AS
            SELECT * FROM `{self.source_table}`
            WHERE FALSE  -- 只建立結構，不複製資料
            """

            job = self.client.query(create_query)
            job.result()  # 等待完成

            logger.info(f"✅ 目標表格 {self.target_table} 建立成功")
            return True

        except Exception as e:
            logger.error(f"❌ 建立目標表格失敗: {e}")
            return False

    def copy_data_to_target(self) -> Dict[str, Any]:
        """複製資料到目標表格"""
        logger.info("📋 開始複製資料到目標表格...")

        copy_result = {
            'timestamp': datetime.now().isoformat(),
            'source_table': self.source_table,
            'target_table': self.target_table,
            'status': 'started'
        }

        try:
            # 檢查目標表格是否存在，不存在則建立
            if not self.check_target_table_exists():
                if not self.create_target_table():
                    copy_result['status'] = 'failed'
                    copy_result['error'] = 'Failed to create target table'
                    return copy_result

            # 執行資料複製
            copy_query = f"""
            INSERT INTO `{self.target_table}`
            SELECT * FROM `{self.source_table}`
            """

            # 估算成本
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            job = self.client.query(copy_query, job_config=job_config)
            estimated_cost = (job.total_bytes_processed / (1024**3)) * 0.005

            logger.info(f"💰 預估複製成本: ${estimated_cost:.4f} USD")

            if estimated_cost > 1.0:  # 成本控制：超過 $1 USD 需要確認
                logger.warning(f"⚠️ 複製成本較高 (${estimated_cost:.4f} USD)，請確認是否繼續")
                copy_result['status'] = 'cost_warning'
                copy_result['estimated_cost_usd'] = estimated_cost
                return copy_result

            # 執行實際複製
            job_config = bigquery.QueryJobConfig(use_query_cache=False)
            job = self.client.query(copy_query, job_config=job_config)
            job.result()  # 等待完成

            copy_result['status'] = 'completed'
            copy_result['estimated_cost_usd'] = estimated_cost
            copy_result['job_id'] = job.job_id

            logger.info("✅ 資料複製完成")

        except Exception as e:
            logger.error(f"❌ 資料複製失敗: {e}")
            copy_result['status'] = 'failed'
            copy_result['error'] = str(e)

        return copy_result

    def verify_copy_integrity(self) -> Dict[str, Any]:
        """驗證複製完整性"""
        logger.info("🔍 驗證複製完整性...")

        verification = {
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }

        try:
            # 比較記錄數量
            count_query = f"""
            SELECT
                (SELECT COUNT(*) FROM `{self.source_table}`) as source_count,
                (SELECT COUNT(*) FROM `{self.target_table}`) as target_count
            """

            counts = list(self.client.query(count_query))[0]
            verification['checks']['record_count'] = {
                'source_count': counts['source_count'],
                'target_count': counts['target_count'],
                'match': counts['source_count'] == counts['target_count']
            }

            # 比較唯一交易數量
            transaction_query = f"""
            SELECT
                (SELECT COUNT(DISTINCT transaction_id) FROM `{self.source_table}`) as source_transactions,
                (SELECT COUNT(DISTINCT transaction_id) FROM `{self.target_table}`) as target_transactions
            """

            transactions = list(self.client.query(transaction_query))[0]
            verification['checks']['transaction_count'] = {
                'source_transactions': transactions['source_transactions'],
                'target_transactions': transactions['target_transactions'],
                'match': transactions['source_transactions'] == transactions['target_transactions']
            }

            # 比較資料範圍
            range_query = f"""
            SELECT
                'source' as table_type,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
            FROM `{self.source_table}`
            UNION ALL
            SELECT
                'target' as table_type,
                MIN(DATE(TIMESTAMP_SECONDS(event_times))) as earliest_date,
                MAX(DATE(TIMESTAMP_SECONDS(event_times))) as latest_date
            FROM `{self.target_table}`
            """

            ranges = list(self.client.query(range_query))
            verification['checks']['date_range'] = {
                'source': {
                    'earliest': str(ranges[0]['earliest_date']),
                    'latest': str(ranges[0]['latest_date'])
                },
                'target': {
                    'earliest': str(ranges[1]['earliest_date']),
                    'latest': str(ranges[1]['latest_date'])
                },
                'match': (ranges[0]['earliest_date'] == ranges[1]['earliest_date'] and
                         ranges[0]['latest_date'] == ranges[1]['latest_date'])
            }

            # 整體驗證狀態
            all_checks_passed = all([
                verification['checks']['record_count']['match'],
                verification['checks']['transaction_count']['match'],
                verification['checks']['date_range']['match']
            ])

            verification['overall_status'] = 'passed' if all_checks_passed else 'failed'

            if all_checks_passed:
                logger.info("✅ 複製完整性驗證通過")
            else:
                logger.warning("⚠️ 複製完整性驗證發現問題")

        except Exception as e:
            logger.error(f"❌ 複製完整性驗證失敗: {e}")
            verification['checks']['error'] = {
                'status': 'failed',
                'error': str(e)
            }
            verification['overall_status'] = 'error'

        return verification

def main():
    """主執行函數"""
    print("🚀 家樂福離線資料複製作業")
    print("=" * 60)

    copier = CarrefourDataCopier()

    # 建立複製作業報告
    copy_report = {
        'operation': 'carrefour_data_copy',
        'timestamp': datetime.now().isoformat(),
        'source_table': copier.source_table,
        'target_table': copier.target_table,
        'steps': {}
    }

    # 步驟1：來源資料完整性檢查
    print("\n📋 步驟1：來源資料完整性檢查")
    integrity_check = copier.check_source_data_integrity()
    copy_report['steps']['integrity_check'] = integrity_check

    # 步驟2：執行資料複製
    print("\n📋 步驟2：執行資料複製")
    copy_result = copier.copy_data_to_target()
    copy_report['steps']['data_copy'] = copy_result

    if copy_result['status'] == 'cost_warning':
        print(f"⚠️ 複製成本較高: ${copy_result['estimated_cost_usd']:.4f} USD")
        print("請確認是否繼續執行複製作業")
        return

    if copy_result['status'] != 'completed':
        print(f"❌ 資料複製失敗: {copy_result.get('error', 'Unknown error')}")
        return

    # 步驟3：驗證複製完整性
    print("\n📋 步驟3：驗證複製完整性")
    verification = copier.verify_copy_integrity()
    copy_report['steps']['verification'] = verification

    # 儲存複製作業報告
    report_file = f"{copier.reports_dir}/data_copy_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(copy_report, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n📄 複製作業報告已儲存至: {report_file}")

    # 顯示總結
    print(f"\n🎯 複製作業總結:")
    if verification['overall_status'] == 'passed':
        print("   ✅ 資料複製成功完成")
        print("   ✅ 完整性驗證通過")
        print(f"   💰 複製成本: ${copy_result['estimated_cost_usd']:.4f} USD")
    else:
        print("   ❌ 複製作業存在問題")
        print("   請檢查報告詳細資訊")

if __name__ == "__main__":
    main()
