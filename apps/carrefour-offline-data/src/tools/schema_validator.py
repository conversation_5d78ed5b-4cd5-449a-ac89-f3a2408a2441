#!/usr/bin/env python3
"""
Schema 一致性檢查與修正工具

比較來源表格與目標表格的完整 schema，確保完全一致
包括欄位名稱、資料類型、mode、description 和巢狀結構

Author: AI Assistant
Date: 2025-08-15
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from google.cloud import bigquery
from google.auth import default

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    from gcloud_utils import get_gcloud_credentials_path

@dataclass
class FieldInfo:
    """欄位資訊結構"""
    name: str
    field_type: str
    mode: str
    description: Optional[str] = None
    fields: Optional[List['FieldInfo']] = None

class SchemaValidator:
    """Schema 驗證器"""

    def __init__(self):
        self.source_project = "tw-eagle-prod"
        self.source_dataset = "rmn_tagtoo"
        self.source_table = "offline_transaction_day"

        self.target_project = "tagtoo-tracking"
        self.target_dataset = "event_prod"
        self.target_table = "carrefour_offline_data"

        self.source_client = None
        self.target_client = None

        self.comparison_result = {
            'timestamp': datetime.now().isoformat(),
            'source_table': f"{self.source_project}.{self.source_dataset}.{self.source_table}",
            'target_table': f"{self.target_project}.{self.target_dataset}.{self.target_table}",
            'schema_identical': False,
            'differences': [],
            'source_schema': None,
            'target_schema': None,
            'fix_ddl': None
        }

    def authenticate(self) -> bool:
        """設定認證"""
        try:
            print("🔐 設定認證...")

            # 確保使用正確的認證配置 - 使用動態路徑
            credentials_path = get_gcloud_credentials_path()
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
            print(f"使用認證檔案: {credentials_path}")

            # 來源專案認證 (使用 Service Account Impersonation)
            print("設定來源專案認證...")
            self.source_client = bigquery.Client(project=self.source_project)

            # 目標專案認證
            print("設定目標專案認證...")
            self.target_client = bigquery.Client(project=self.target_project)

            print("✅ 認證設定成功")
            return True

        except Exception as e:
            print(f"❌ 認證失敗: {e}")
            return False

    def parse_schema_field(self, field) -> FieldInfo:
        """解析 BigQuery schema 欄位"""
        field_info = FieldInfo(
            name=field.name,
            field_type=field.field_type,
            mode=field.mode,
            description=field.description
        )

        # 處理巢狀欄位 (RECORD/STRUCT)
        if field.field_type == 'RECORD' and field.fields:
            field_info.fields = []
            for nested_field in field.fields:
                field_info.fields.append(self.parse_schema_field(nested_field))

        return field_info

    def get_table_schema(self, client: bigquery.Client, table_ref: str) -> Tuple[List[FieldInfo], Dict[str, Any]]:
        """獲取表格的完整 schema"""
        try:
            table = client.get_table(table_ref)

            # 解析 schema
            schema_fields = []
            for field in table.schema:
                schema_fields.append(self.parse_schema_field(field))

            # 表格基本資訊
            table_info = {
                'table_id': table.table_id,
                'dataset_id': table.dataset_id,
                'project_id': table.project,
                'created': table.created.isoformat() if table.created else None,
                'modified': table.modified.isoformat() if table.modified else None,
                'num_rows': table.num_rows,
                'num_bytes': table.num_bytes,
                'field_count': len(table.schema)
            }

            return schema_fields, table_info

        except Exception as e:
            print(f"❌ 獲取表格 schema 失敗: {table_ref} - {e}")
            return [], {}

    def compare_field(self, source_field: FieldInfo, target_field: FieldInfo, path: str = "") -> List[str]:
        """比較兩個欄位的差異"""
        differences = []
        current_path = f"{path}.{source_field.name}" if path else source_field.name

        # 比較基本屬性
        if source_field.name != target_field.name:
            differences.append(f"{current_path}: 欄位名稱不一致 (來源: {source_field.name}, 目標: {target_field.name})")

        if source_field.field_type != target_field.field_type:
            differences.append(f"{current_path}: 資料類型不一致 (來源: {source_field.field_type}, 目標: {target_field.field_type})")

        if source_field.mode != target_field.mode:
            differences.append(f"{current_path}: Mode 不一致 (來源: {source_field.mode}, 目標: {target_field.mode})")

        if source_field.description != target_field.description:
            differences.append(f"{current_path}: Description 不一致 (來源: {source_field.description}, 目標: {target_field.description})")

        # 比較巢狀欄位
        if source_field.fields and target_field.fields:
            if len(source_field.fields) != len(target_field.fields):
                differences.append(f"{current_path}: 巢狀欄位數量不一致 (來源: {len(source_field.fields)}, 目標: {len(target_field.fields)})")
            else:
                for i, (src_nested, tgt_nested) in enumerate(zip(source_field.fields, target_field.fields)):
                    nested_diffs = self.compare_field(src_nested, tgt_nested, current_path)
                    differences.extend(nested_diffs)
        elif source_field.fields and not target_field.fields:
            differences.append(f"{current_path}: 來源有巢狀欄位但目標沒有")
        elif not source_field.fields and target_field.fields:
            differences.append(f"{current_path}: 目標有巢狀欄位但來源沒有")

        return differences

    def compare_schemas(self, source_schema: List[FieldInfo], target_schema: List[FieldInfo]) -> List[str]:
        """比較兩個 schema 的差異"""
        differences = []

        # 比較欄位數量
        if len(source_schema) != len(target_schema):
            differences.append(f"欄位數量不一致 (來源: {len(source_schema)}, 目標: {len(target_schema)})")

        # 建立欄位名稱對應
        source_fields = {field.name: field for field in source_schema}
        target_fields = {field.name: field for field in target_schema}

        # 檢查缺少的欄位
        source_field_names = set(source_fields.keys())
        target_field_names = set(target_fields.keys())

        missing_in_target = source_field_names - target_field_names
        extra_in_target = target_field_names - source_field_names

        for field_name in missing_in_target:
            differences.append(f"目標表格缺少欄位: {field_name}")

        for field_name in extra_in_target:
            differences.append(f"目標表格多出欄位: {field_name}")

        # 比較共同欄位
        common_fields = source_field_names & target_field_names
        for field_name in common_fields:
            field_diffs = self.compare_field(source_fields[field_name], target_fields[field_name])
            differences.extend(field_diffs)

        return differences

    def generate_field_ddl(self, field: FieldInfo, indent: int = 0) -> str:
        """生成欄位的 DDL"""
        spaces = "  " * indent

        if field.field_type == 'RECORD' and field.fields:
            # 處理 STRUCT 類型
            nested_fields = []
            for nested_field in field.fields:
                nested_fields.append(self.generate_field_ddl(nested_field, indent + 1))

            nested_definition = ',\n'.join(nested_fields)
            mode_str = f' {field.mode}' if field.mode != 'NULLABLE' else ''

            if field.mode == 'REPEATED':
                return f'{spaces}{field.name} ARRAY<STRUCT<\n{nested_definition}\n{spaces}>>'
            else:
                return f'{spaces}{field.name} STRUCT<\n{nested_definition}\n{spaces}>{mode_str}'
        else:
            mode_str = f' {field.mode}' if field.mode != 'NULLABLE' else ''
            return f'{spaces}{field.name} {field.field_type}{mode_str}'

    def generate_fix_ddl(self, source_schema: List[FieldInfo]) -> str:
        """生成修正目標表格的 DDL"""
        field_definitions = []
        for field in source_schema:
            field_definitions.append(self.generate_field_ddl(field))

        field_definitions_str = ',\n'.join(field_definitions)

        ddl = f'''-- 修正目標表格 Schema
DROP TABLE IF EXISTS `{self.target_project}.{self.target_dataset}.{self.target_table}`;

CREATE TABLE `{self.target_project}.{self.target_dataset}.{self.target_table}` (
{field_definitions_str}
)
OPTIONS (
  description='家樂福離線事件資料 - 從 {self.source_project}.{self.source_dataset}.{self.source_table} 複製 (Schema 修正版)',
  labels=[('source', 'carrefour'), ('type', 'offline_transaction'), ('env', 'prod'), ('schema_version', 'v2')]
);'''

        return ddl

    def validate_schemas(self) -> bool:
        """執行完整的 schema 驗證"""
        print("\n🔍 開始 Schema 一致性檢查...")
        print("=" * 60)

        # 獲取來源表格 schema
        print("📋 獲取來源表格 schema...")
        source_schema, source_info = self.get_table_schema(
            self.source_client,
            f"{self.source_project}.{self.source_dataset}.{self.source_table}"
        )

        if not source_schema:
            print("❌ 無法獲取來源表格 schema")
            return False

        print(f"✅ 來源表格: {len(source_schema)} 個欄位")

        # 獲取目標表格 schema
        print("📋 獲取目標表格 schema...")
        target_schema, target_info = self.get_table_schema(
            self.target_client,
            f"{self.target_project}.{self.target_dataset}.{self.target_table}"
        )

        if not target_schema:
            print("❌ 無法獲取目標表格 schema")
            return False

        print(f"✅ 目標表格: {len(target_schema)} 個欄位")

        # 比較 schema
        print("\n🔍 比較 schema 差異...")
        differences = self.compare_schemas(source_schema, target_schema)

        # 更新比較結果
        self.comparison_result['source_schema'] = source_info
        self.comparison_result['target_schema'] = target_info
        self.comparison_result['differences'] = differences
        self.comparison_result['schema_identical'] = len(differences) == 0

        # 顯示結果
        if len(differences) == 0:
            print("✅ Schema 完全一致！")
            return True
        else:
            print(f"❌ 發現 {len(differences)} 個差異:")
            for i, diff in enumerate(differences, 1):
                print(f"  {i}. {diff}")

            # 生成修正 DDL
            print("\n🔧 生成修正 DDL...")
            fix_ddl = self.generate_fix_ddl(source_schema)
            self.comparison_result['fix_ddl'] = fix_ddl

            # 儲存修正 DDL
            with open('fix_target_table_schema.sql', 'w', encoding='utf-8') as f:
                f.write(fix_ddl)

            print("✅ 修正 DDL 已儲存至: fix_target_table_schema.sql")
            return False

    def save_comparison_report(self):
        """儲存比較報告"""
        # JSON 格式報告
        with open('schema_comparison_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.comparison_result, f, indent=2, ensure_ascii=False, default=str)

        # 人類可讀格式報告
        report_lines = [
            "# Schema 比較報告",
            f"生成時間: {self.comparison_result['timestamp']}",
            f"來源表格: {self.comparison_result['source_table']}",
            f"目標表格: {self.comparison_result['target_table']}",
            "",
            f"## 比較結果",
            f"Schema 一致性: {'✅ 一致' if self.comparison_result['schema_identical'] else '❌ 不一致'}",
            f"差異數量: {len(self.comparison_result['differences'])}",
            ""
        ]

        if self.comparison_result['differences']:
            report_lines.append("## 發現的差異")
            for i, diff in enumerate(self.comparison_result['differences'], 1):
                report_lines.append(f"{i}. {diff}")
            report_lines.append("")

        if self.comparison_result['source_schema']:
            source_info = self.comparison_result['source_schema']
            report_lines.extend([
                "## 來源表格資訊",
                f"- 專案: {source_info.get('project_id')}",
                f"- 資料集: {source_info.get('dataset_id')}",
                f"- 表格: {source_info.get('table_id')}",
                f"- 建立時間: {source_info.get('created')}",
                f"- 修改時間: {source_info.get('modified')}",
                f"- 行數: {source_info.get('num_rows', 0):,}",
                f"- 大小: {source_info.get('num_bytes', 0):,} bytes",
                f"- 欄位數: {source_info.get('field_count')}",
                ""
            ])

        if self.comparison_result['target_schema']:
            target_info = self.comparison_result['target_schema']
            report_lines.extend([
                "## 目標表格資訊",
                f"- 專案: {target_info.get('project_id')}",
                f"- 資料集: {target_info.get('dataset_id')}",
                f"- 表格: {target_info.get('table_id')}",
                f"- 建立時間: {target_info.get('created')}",
                f"- 修改時間: {target_info.get('modified')}",
                f"- 行數: {target_info.get('num_rows', 0):,}",
                f"- 大小: {target_info.get('num_bytes', 0):,} bytes",
                f"- 欄位數: {target_info.get('field_count')}",
                ""
            ])

        with open('schema_comparison_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print("📄 比較報告已儲存:")
        print("  - schema_comparison_report.json (結構化資料)")
        print("  - schema_comparison_report.md (人類可讀)")

def main():
    """主函數"""
    print("🔍 Schema 一致性檢查工具")
    print("=" * 60)

    validator = SchemaValidator()

    # 認證
    if not validator.authenticate():
        print("❌ 認證失敗，程式結束")
        return False

    # 執行驗證
    schema_valid = validator.validate_schemas()

    # 儲存報告
    validator.save_comparison_report()

    if schema_valid:
        print("\n🎉 Schema 驗證通過！目標表格與來源表格完全一致。")
        return True
    else:
        print("\n⚠️ Schema 驗證失敗！請檢查差異並執行修正 DDL。")
        print("修正步驟:")
        print("1. 檢視 fix_target_table_schema.sql")
        print("2. 執行: bq query --use_legacy_sql=false < fix_target_table_schema.sql")
        print("3. 重新執行此腳本驗證")
        return False

if __name__ == "__main__":
    main()
