#!/usr/bin/env python3
"""
Google Cloud Storage 上傳工具

此模組提供 GCS 檔案上傳和管理功能，專為家樂福 ph_id 直接處理流程設計。

主要功能：
1. 檔案上傳到指定的 GCS bucket
2. 檔案元數據管理
3. 上傳進度監控
4. 錯誤處理和重試機制

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

from google.cloud import storage
from google.cloud.exceptions import GoogleCloudError

logger = logging.getLogger(__name__)

class GCSUploader:
    """Google Cloud Storage 上傳器"""

    def __init__(self, bucket_name: str, project_id: Optional[str] = None):
        """
        初始化 GCS 上傳器

        Args:
            bucket_name: GCS bucket 名稱
            project_id: GCP 專案 ID
        """
        self.bucket_name = bucket_name
        self.project_id = project_id
        self.client = storage.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)

        logger.info(f"GCSUploader 初始化完成，bucket: {bucket_name}")

    def upload_file(self,
                   local_path: str,
                   gcs_path: str,
                   metadata: Optional[Dict[str, str]] = None,
                   progress_callback: Optional[Callable[[int, int], None]] = None,
                   max_retries: int = 3) -> Dict[str, Any]:
        """
        上傳檔案到 GCS

        Args:
            local_path: 本地檔案路徑
            gcs_path: GCS 目標路徑
            metadata: 檔案元數據
            progress_callback: 進度回調函數 (bytes_uploaded, total_bytes)
            max_retries: 最大重試次數

        Returns:
            上傳結果
        """
        local_file = Path(local_path)
        if not local_file.exists():
            raise FileNotFoundError(f"本地檔案不存在: {local_path}")

        file_size = local_file.stat().st_size
        logger.info(f"開始上傳檔案: {local_path} -> gs://{self.bucket_name}/{gcs_path}")
        logger.info(f"檔案大小: {file_size:,} bytes")

        blob = self.bucket.blob(gcs_path)

        # 設定元數據
        if metadata:
            blob.metadata = metadata

        # 設定內容類型
        if local_path.endswith('.avro'):
            blob.content_type = 'application/avro'
        elif local_path.endswith('.json'):
            blob.content_type = 'application/json'

        start_time = time.time()

        for attempt in range(max_retries + 1):
            try:
                # 上傳檔案
                with open(local_path, 'rb') as file_obj:
                    blob.upload_from_file(file_obj)

                upload_time = time.time() - start_time
                upload_speed = file_size / upload_time if upload_time > 0 else 0

                logger.info(f"檔案上傳成功: gs://{self.bucket_name}/{gcs_path}")
                logger.info(f"上傳耗時: {upload_time:.2f} 秒，速度: {upload_speed/1024/1024:.2f} MB/s")

                return {
                    "status": "success",
                    "gcs_uri": f"gs://{self.bucket_name}/{gcs_path}",
                    "file_size": file_size,
                    "upload_time_seconds": upload_time,
                    "upload_speed_mbps": upload_speed / 1024 / 1024,
                    "blob_name": gcs_path,
                    "bucket_name": self.bucket_name
                }

            except GoogleCloudError as e:
                if attempt < max_retries:
                    wait_time = 2 ** attempt  # 指數退避
                    logger.warning(f"上傳失敗 (嘗試 {attempt + 1}/{max_retries + 1}): {e}")
                    logger.info(f"等待 {wait_time} 秒後重試...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"檔案上傳失敗，已達最大重試次數: {e}")
                    raise

    def upload_directory(self,
                        local_dir: str,
                        gcs_prefix: str,
                        file_pattern: str = "*",
                        metadata: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        上傳整個目錄到 GCS

        Args:
            local_dir: 本地目錄路徑
            gcs_prefix: GCS 路徑前綴
            file_pattern: 檔案模式 (例如: "*.avro")
            metadata: 檔案元數據

        Returns:
            上傳結果列表
        """
        local_path = Path(local_dir)
        if not local_path.exists() or not local_path.is_dir():
            raise ValueError(f"本地目錄不存在或不是目錄: {local_dir}")

        # 找到符合模式的檔案
        files_to_upload = list(local_path.glob(file_pattern))
        if not files_to_upload:
            logger.warning(f"在 {local_dir} 中沒有找到符合 {file_pattern} 的檔案")
            return []

        logger.info(f"找到 {len(files_to_upload)} 個檔案需要上傳")

        results = []
        for file_path in files_to_upload:
            relative_path = file_path.relative_to(local_path)
            gcs_path = f"{gcs_prefix.rstrip('/')}/{relative_path}"

            try:
                result = self.upload_file(str(file_path), gcs_path, metadata)
                results.append(result)
            except Exception as e:
                logger.error(f"上傳檔案失敗: {file_path} -> {e}")
                results.append({
                    "status": "error",
                    "local_path": str(file_path),
                    "gcs_path": gcs_path,
                    "error": str(e)
                })

        successful_uploads = [r for r in results if r["status"] == "success"]
        logger.info(f"目錄上傳完成: {len(successful_uploads)}/{len(results)} 個檔案成功")

        return results

    def list_files(self, prefix: str = "", max_results: int = 1000) -> List[Dict[str, Any]]:
        """
        列出 GCS bucket 中的檔案

        Args:
            prefix: 路徑前綴
            max_results: 最大結果數量

        Returns:
            檔案列表
        """
        logger.info(f"列出 gs://{self.bucket_name}/{prefix} 下的檔案")

        blobs = self.client.list_blobs(self.bucket, prefix=prefix, max_results=max_results)

        files = []
        for blob in blobs:
            files.append({
                "name": blob.name,
                "size": blob.size,
                "created": blob.time_created.isoformat() if blob.time_created else None,
                "updated": blob.updated.isoformat() if blob.updated else None,
                "content_type": blob.content_type,
                "gcs_uri": f"gs://{self.bucket_name}/{blob.name}"
            })

        logger.info(f"找到 {len(files)} 個檔案")
        return files

    def delete_file(self, gcs_path: str) -> bool:
        """
        刪除 GCS 檔案

        Args:
            gcs_path: GCS 檔案路徑

        Returns:
            是否成功刪除
        """
        try:
            blob = self.bucket.blob(gcs_path)
            blob.delete()
            logger.info(f"檔案刪除成功: gs://{self.bucket_name}/{gcs_path}")
            return True
        except Exception as e:
            logger.error(f"檔案刪除失敗: gs://{self.bucket_name}/{gcs_path} -> {e}")
            return False

    def file_exists(self, gcs_path: str) -> bool:
        """
        檢查檔案是否存在

        Args:
            gcs_path: GCS 檔案路徑

        Returns:
            檔案是否存在
        """
        try:
            blob = self.bucket.blob(gcs_path)
            return blob.exists()
        except Exception as e:
            logger.error(f"檢查檔案存在性失敗: gs://{self.bucket_name}/{gcs_path} -> {e}")
            return False

    def get_file_info(self, gcs_path: str) -> Optional[Dict[str, Any]]:
        """
        獲取檔案資訊

        Args:
            gcs_path: GCS 檔案路徑

        Returns:
            檔案資訊
        """
        try:
            blob = self.bucket.blob(gcs_path)
            blob.reload()

            return {
                "name": blob.name,
                "size": blob.size,
                "created": blob.time_created.isoformat() if blob.time_created else None,
                "updated": blob.updated.isoformat() if blob.updated else None,
                "content_type": blob.content_type,
                "metadata": blob.metadata,
                "gcs_uri": f"gs://{self.bucket_name}/{blob.name}"
            }
        except Exception as e:
            logger.error(f"獲取檔案資訊失敗: gs://{self.bucket_name}/{gcs_path} -> {e}")
            return None

    def create_signed_url(self, gcs_path: str, expiration_hours: int = 1) -> Optional[str]:
        """
        建立簽名 URL

        Args:
            gcs_path: GCS 檔案路徑
            expiration_hours: 過期時間 (小時)

        Returns:
            簽名 URL
        """
        try:
            blob = self.bucket.blob(gcs_path)

            # 計算過期時間
            from datetime import timedelta
            expiration = datetime.utcnow() + timedelta(hours=expiration_hours)

            url = blob.generate_signed_url(expiration=expiration, method='GET')
            logger.info(f"簽名 URL 建立成功: {gcs_path} (過期: {expiration_hours} 小時)")

            return url
        except Exception as e:
            logger.error(f"建立簽名 URL 失敗: gs://{self.bucket_name}/{gcs_path} -> {e}")
            return None


def main():
    """主要執行函數 - 用於測試"""
    import argparse

    parser = argparse.ArgumentParser(description='GCS 上傳工具')
    parser.add_argument('--bucket', required=True, help='GCS bucket 名稱')
    parser.add_argument('--upload', help='上傳檔案: local_path:gcs_path')
    parser.add_argument('--list', help='列出檔案，指定前綴')
    parser.add_argument('--delete', help='刪除檔案，指定 GCS 路徑')

    args = parser.parse_args()

    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    uploader = GCSUploader(args.bucket)

    if args.upload:
        local_path, gcs_path = args.upload.split(':', 1)
        result = uploader.upload_file(local_path, gcs_path)
        print(f"上傳結果: {result}")

    elif args.list is not None:
        files = uploader.list_files(args.list)
        for file_info in files:
            print(f"{file_info['name']} ({file_info['size']} bytes)")

    elif args.delete:
        success = uploader.delete_file(args.delete)
        print(f"刪除結果: {'成功' if success else '失敗'}")


if __name__ == "__main__":
    main()
