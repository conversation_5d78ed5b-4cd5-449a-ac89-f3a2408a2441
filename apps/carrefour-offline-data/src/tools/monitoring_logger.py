#!/usr/bin/env python3
"""
監控、日誌與通知系統

提供結構化日誌記錄、效能監控、進度追蹤和通知功能

Author: AI Assistant
Date: 2025-08-15
"""

import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum

class LogLevel(Enum):
    """日誌級別"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class OperationType(Enum):
    """操作類型"""
    AUTHENTICATION = "authentication"
    SCHEMA_VALIDATION = "schema_validation"
    DATA_COPY = "data_copy"
    DATA_VALIDATION = "data_validation"
    BATCH_PROCESSING = "batch_processing"
    PERMISSION_CHECK = "permission_check"
    COST_ESTIMATION = "cost_estimation"

@dataclass
class LogEntry:
    """結構化日誌條目"""
    timestamp: str
    level: str
    operation_type: str
    operation_id: Optional[str]
    batch_id: Optional[int]
    message: str
    details: Dict[str, Any]
    duration_ms: Optional[float] = None
    status: Optional[str] = None  # SUCCESS, FAILED, IN_PROGRESS

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return asdict(self)

@dataclass
class PerformanceMetrics:
    """效能指標"""
    operation_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    rows_processed: int = 0
    bytes_processed: int = 0
    cost_usd: float = 0.0
    success: bool = False
    error_message: Optional[str] = None

    @property
    def duration_seconds(self) -> float:
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0

    @property
    def throughput_rows_per_second(self) -> float:
        if self.duration_seconds > 0:
            return self.rows_processed / self.duration_seconds
        return 0.0

class StructuredLogger:
    """結構化日誌記錄器"""

    def __init__(self, log_file: str = "carrefour_data_operations.log"):
        self.log_file = log_file
        self.operation_id = None
        self.batch_id = None

        # 設定標準日誌記錄器
        self.logger = logging.getLogger("carrefour_data_operations")
        self.logger.setLevel(logging.DEBUG)

        # 清除現有的處理器
        self.logger.handlers.clear()

        # 檔案處理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # 控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # 結構化日誌儲存
        self.structured_logs: List[LogEntry] = []

    def set_operation_context(self, operation_id: str, batch_id: Optional[int] = None):
        """設定操作上下文"""
        self.operation_id = operation_id
        self.batch_id = batch_id

    def log(self, level: LogLevel, operation_type: OperationType, message: str,
            details: Dict[str, Any] = None, duration_ms: float = None, status: str = None):
        """記錄結構化日誌"""

        log_entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level.value,
            operation_type=operation_type.value,
            operation_id=self.operation_id,
            batch_id=self.batch_id,
            message=message,
            details=details or {},
            duration_ms=duration_ms,
            status=status
        )

        # 儲存結構化日誌
        self.structured_logs.append(log_entry)

        # 記錄到標準日誌
        log_message = f"[{operation_type.value}] {message}"
        if details:
            log_message += f" | Details: {json.dumps(details, ensure_ascii=False)}"

        if level == LogLevel.DEBUG:
            self.logger.debug(log_message)
        elif level == LogLevel.INFO:
            self.logger.info(log_message)
        elif level == LogLevel.WARNING:
            self.logger.warning(log_message)
        elif level == LogLevel.ERROR:
            self.logger.error(log_message)
        elif level == LogLevel.CRITICAL:
            self.logger.critical(log_message)

    def save_structured_logs(self, filename: str = None):
        """儲存結構化日誌到 JSON 檔案"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"structured_logs_{timestamp}.json"

        logs_data = [log.to_dict() for log in self.structured_logs]

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(logs_data, f, indent=2, ensure_ascii=False, default=str)

        self.log(LogLevel.INFO, OperationType.DATA_VALIDATION,
                f"結構化日誌已儲存至: {filename}")

class PerformanceMonitor:
    """效能監控器"""

    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.metrics: List[PerformanceMetrics] = []
        self.active_operations: Dict[str, PerformanceMetrics] = {}

    def start_operation(self, operation_type: str, operation_id: str) -> str:
        """開始監控操作"""
        metric = PerformanceMetrics(
            operation_type=operation_type,
            start_time=datetime.now()
        )

        self.active_operations[operation_id] = metric

        self.logger.log(
            LogLevel.INFO,
            OperationType(operation_type) if operation_type in [op.value for op in OperationType] else OperationType.DATA_VALIDATION,
            f"開始操作: {operation_id}",
            {"operation_type": operation_type, "start_time": metric.start_time.isoformat()},
            status="IN_PROGRESS"
        )

        return operation_id

    def end_operation(self, operation_id: str, success: bool = True,
                     rows_processed: int = 0, bytes_processed: int = 0,
                     cost_usd: float = 0.0, error_message: str = None):
        """結束監控操作"""

        if operation_id not in self.active_operations:
            self.logger.log(LogLevel.WARNING, OperationType.DATA_VALIDATION,
                          f"嘗試結束不存在的操作: {operation_id}")
            return

        metric = self.active_operations[operation_id]
        metric.end_time = datetime.now()
        metric.success = success
        metric.rows_processed = rows_processed
        metric.bytes_processed = bytes_processed
        metric.cost_usd = cost_usd
        metric.error_message = error_message

        # 移動到完成的指標列表
        self.metrics.append(metric)
        del self.active_operations[operation_id]

        # 記錄日誌
        details = {
            "operation_type": metric.operation_type,
            "duration_seconds": metric.duration_seconds,
            "rows_processed": metric.rows_processed,
            "bytes_processed": metric.bytes_processed,
            "cost_usd": metric.cost_usd,
            "throughput_rows_per_second": metric.throughput_rows_per_second,
            "success": metric.success
        }

        if error_message:
            details["error_message"] = error_message

        status = "SUCCESS" if success else "FAILED"
        level = LogLevel.INFO if success else LogLevel.ERROR

        self.logger.log(
            level,
            OperationType(metric.operation_type) if metric.operation_type in [op.value for op in OperationType] else OperationType.DATA_VALIDATION,
            f"完成操作: {operation_id}",
            details,
            duration_ms=metric.duration_seconds * 1000,
            status=status
        )

    def get_operation_stats(self, operation_type: str = None) -> Dict[str, Any]:
        """獲取操作統計"""
        filtered_metrics = self.metrics
        if operation_type:
            filtered_metrics = [m for m in self.metrics if m.operation_type == operation_type]

        if not filtered_metrics:
            return {}

        successful_metrics = [m for m in filtered_metrics if m.success]

        stats = {
            "total_operations": len(filtered_metrics),
            "successful_operations": len(successful_metrics),
            "failed_operations": len(filtered_metrics) - len(successful_metrics),
            "success_rate": len(successful_metrics) / len(filtered_metrics) if filtered_metrics else 0,
            "total_rows_processed": sum(m.rows_processed for m in filtered_metrics),
            "total_bytes_processed": sum(m.bytes_processed for m in filtered_metrics),
            "total_cost_usd": sum(m.cost_usd for m in filtered_metrics),
            "total_duration_seconds": sum(m.duration_seconds for m in filtered_metrics)
        }

        if successful_metrics:
            stats.update({
                "avg_duration_seconds": sum(m.duration_seconds for m in successful_metrics) / len(successful_metrics),
                "avg_throughput_rows_per_second": sum(m.throughput_rows_per_second for m in successful_metrics) / len(successful_metrics),
                "max_throughput_rows_per_second": max(m.throughput_rows_per_second for m in successful_metrics),
                "min_throughput_rows_per_second": min(m.throughput_rows_per_second for m in successful_metrics)
            })

        return stats

class ProgressTracker:
    """進度追蹤器"""

    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.total_items = 0
        self.completed_items = 0
        self.start_time = None
        self.last_update_time = None
        self.update_interval = 30  # 30 秒更新一次

    def initialize(self, total_items: int, description: str = "處理項目"):
        """初始化進度追蹤"""
        self.total_items = total_items
        self.completed_items = 0
        self.start_time = datetime.now()
        self.last_update_time = self.start_time

        self.logger.log(
            LogLevel.INFO,
            OperationType.DATA_COPY,
            f"開始進度追蹤: {description}",
            {
                "total_items": total_items,
                "description": description,
                "start_time": self.start_time.isoformat()
            },
            status="IN_PROGRESS"
        )

    def update(self, completed_items: int, force_log: bool = False):
        """更新進度"""
        self.completed_items = completed_items
        current_time = datetime.now()

        # 檢查是否需要記錄進度
        should_log = (force_log or
                     (current_time - self.last_update_time).total_seconds() >= self.update_interval or
                     completed_items == self.total_items)

        if should_log:
            progress_percentage = (completed_items / self.total_items * 100) if self.total_items > 0 else 0
            elapsed_seconds = (current_time - self.start_time).total_seconds()

            # 計算 ETA
            if completed_items > 0 and elapsed_seconds > 0:
                items_per_second = completed_items / elapsed_seconds
                remaining_items = self.total_items - completed_items
                eta_seconds = remaining_items / items_per_second if items_per_second > 0 else 0
                eta_time = current_time + timedelta(seconds=eta_seconds)
            else:
                eta_seconds = 0
                eta_time = None

            details = {
                "completed_items": completed_items,
                "total_items": self.total_items,
                "progress_percentage": progress_percentage,
                "elapsed_seconds": elapsed_seconds,
                "eta_seconds": eta_seconds,
                "eta_time": eta_time.isoformat() if eta_time else None,
                "items_per_second": completed_items / elapsed_seconds if elapsed_seconds > 0 else 0
            }

            status = "SUCCESS" if completed_items == self.total_items else "IN_PROGRESS"

            self.logger.log(
                LogLevel.INFO,
                OperationType.DATA_COPY,
                f"進度更新: {completed_items}/{self.total_items} ({progress_percentage:.1f}%)",
                details,
                status=status
            )

            self.last_update_time = current_time

class NotificationSystem:
    """通知系統"""

    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.notification_handlers: List[Callable] = []

    def add_handler(self, handler: Callable[[str, str, Dict[str, Any]], None]):
        """添加通知處理器"""
        self.notification_handlers.append(handler)

    def send_notification(self, level: str, message: str, details: Dict[str, Any] = None):
        """發送通知"""
        for handler in self.notification_handlers:
            try:
                handler(level, message, details or {})
            except Exception as e:
                self.logger.log(
                    LogLevel.ERROR,
                    OperationType.DATA_VALIDATION,
                    f"通知發送失敗: {e}",
                    {"handler": str(handler), "error": str(e)}
                )

def console_notification_handler(level: str, message: str, details: Dict[str, Any]):
    """控制台通知處理器"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    icon = {"INFO": "ℹ️", "WARNING": "⚠️", "ERROR": "❌", "SUCCESS": "✅"}.get(level, "📢")
    print(f"[{timestamp}] {icon} {message}")
    if details:
        print(f"    詳細資訊: {json.dumps(details, ensure_ascii=False, indent=2)}")

def file_notification_handler(filename: str = "notifications.log"):
    """檔案通知處理器工廠"""
    def handler(level: str, message: str, details: Dict[str, Any]):
        timestamp = datetime.now().isoformat()
        notification_data = {
            "timestamp": timestamp,
            "level": level,
            "message": message,
            "details": details
        }

        with open(filename, 'a', encoding='utf-8') as f:
            f.write(json.dumps(notification_data, ensure_ascii=False) + '\n')

    return handler

# 全域監控實例
_global_logger = None
_global_monitor = None
_global_tracker = None
_global_notifier = None

def get_global_logger() -> StructuredLogger:
    """獲取全域日誌記錄器"""
    global _global_logger
    if _global_logger is None:
        _global_logger = StructuredLogger()
    return _global_logger

def get_global_monitor() -> PerformanceMonitor:
    """獲取全域效能監控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor(get_global_logger())
    return _global_monitor

def get_global_tracker() -> ProgressTracker:
    """獲取全域進度追蹤器"""
    global _global_tracker
    if _global_tracker is None:
        _global_tracker = ProgressTracker(get_global_logger())
    return _global_tracker

def get_global_notifier() -> NotificationSystem:
    """獲取全域通知系統"""
    global _global_notifier
    if _global_notifier is None:
        _global_notifier = NotificationSystem(get_global_logger())
        # 添加預設處理器
        _global_notifier.add_handler(console_notification_handler)
        _global_notifier.add_handler(file_notification_handler())
    return _global_notifier

def generate_monitoring_report():
    """生成監控報告"""
    logger = get_global_logger()
    monitor = get_global_monitor()

    # 生成統計報告
    all_stats = monitor.get_operation_stats()

    report_data = {
        "timestamp": datetime.now().isoformat(),
        "overall_stats": all_stats,
        "operation_type_stats": {}
    }

    # 按操作類型分組統計
    for op_type in OperationType:
        type_stats = monitor.get_operation_stats(op_type.value)
        if type_stats:
            report_data["operation_type_stats"][op_type.value] = type_stats

    # 儲存報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"monitoring_report_{timestamp}.json"

    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)

    # 儲存結構化日誌
    logger.save_structured_logs()

    logger.log(
        LogLevel.INFO,
        OperationType.DATA_VALIDATION,
        f"監控報告已生成: {report_filename}",
        {"report_file": report_filename, "total_operations": all_stats.get("total_operations", 0)}
    )

    return report_filename

if __name__ == "__main__":
    # 測試監控系統
    logger = get_global_logger()
    monitor = get_global_monitor()
    tracker = get_global_tracker()
    notifier = get_global_notifier()

    # 測試操作監控
    op_id = monitor.start_operation("test_operation", "test_001")
    time.sleep(1)
    monitor.end_operation(op_id, success=True, rows_processed=1000, cost_usd=0.01)

    # 測試進度追蹤
    tracker.initialize(100, "測試項目")
    for i in range(0, 101, 25):
        tracker.update(i, force_log=True)
        time.sleep(0.1)

    # 測試通知
    notifier.send_notification("SUCCESS", "測試完成", {"test_items": 100})

    # 生成報告
    report_file = generate_monitoring_report()
    print(f"測試完成，報告檔案: {report_file}")
