#!/usr/bin/env python3
"""
Pub/Sub 通知工具

此模組提供 Google Cloud Pub/Sub 訊息發送和接收功能，用於家樂福 ph_id 直接處理流程的事件通知。

主要功能：
1. 發送處理完成通知
2. 觸發後續分析流程
3. 錯誤通知和告警
4. 訊息格式標準化

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable

from google.cloud import pubsub_v1
from google.cloud.pubsub_v1.types import PubsubMessage
from google.api_core import retry

logger = logging.getLogger(__name__)

class PubSubNotifier:
    """Pub/Sub 通知器"""

    def __init__(self, project_id: str):
        """
        初始化 Pub/Sub 通知器

        Args:
            project_id: GCP 專案 ID
        """
        self.project_id = project_id
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()

        logger.info(f"PubSubNotifier 初始化完成，專案: {project_id}")

    def _format_topic_path(self, topic_name: str) -> str:
        """格式化 topic 路徑"""
        if topic_name.startswith('projects/'):
            return topic_name
        return self.publisher.topic_path(self.project_id, topic_name)

    def _format_subscription_path(self, subscription_name: str) -> str:
        """格式化 subscription 路徑"""
        if subscription_name.startswith('projects/'):
            return subscription_name
        return self.subscriber.subscription_path(self.project_id, subscription_name)

    def publish_message(self,
                       topic_name: str,
                       message_data: Dict[str, Any],
                       attributes: Optional[Dict[str, str]] = None,
                       max_retries: int = 3) -> str:
        """
        發送訊息到 Pub/Sub topic

        Args:
            topic_name: Topic 名稱
            message_data: 訊息資料
            attributes: 訊息屬性
            max_retries: 最大重試次數

        Returns:
            訊息 ID
        """
        topic_path = self._format_topic_path(topic_name)

        # 準備訊息
        message_json = json.dumps(message_data, ensure_ascii=False, default=str)
        message_bytes = message_json.encode('utf-8')

        # 設定預設屬性
        msg_attributes = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "carrefour-ph-id-direct",
            "version": "1.0.0"
        }
        if attributes:
            msg_attributes.update(attributes)

        logger.info(f"發送訊息到 {topic_path}")
        logger.debug(f"訊息內容: {message_json}")

        for attempt in range(max_retries + 1):
            try:
                # 發送訊息
                future = self.publisher.publish(
                    topic_path,
                    message_bytes,
                    **msg_attributes
                )
                message_id = future.result(timeout=30)

                logger.info(f"訊息發送成功: {message_id}")
                return message_id

            except Exception as e:
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"發送訊息失敗 (嘗試 {attempt + 1}/{max_retries + 1}): {e}")
                    logger.info(f"等待 {wait_time} 秒後重試...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"訊息發送失敗，已達最大重試次數: {e}")
                    raise

    def publish_processing_completion(self,
                                    topic_name: str,
                                    execution_id: str,
                                    result: Dict[str, Any],
                                    target_date: str) -> str:
        """
        發送處理完成通知

        Args:
            topic_name: Topic 名稱
            execution_id: 執行 ID
            result: 處理結果
            target_date: 目標日期

        Returns:
            訊息 ID
        """
        message_data = {
            "event_type": "ph_id_processing_completed",
            "execution_id": execution_id,
            "target_date": target_date,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "result": result,
            "metadata": {
                "service": "carrefour-offline-data",
                "operation": "ph_id_direct_processing",
                "version": "1.0.0"
            }
        }

        attributes = {
            "event_type": "processing_completed",
            "execution_id": execution_id,
            "target_date": target_date,
            "status": result.get("status", "unknown")
        }

        return self.publish_message(topic_name, message_data, attributes)

    def publish_error_notification(self,
                                 topic_name: str,
                                 execution_id: str,
                                 error_message: str,
                                 error_details: Optional[Dict[str, Any]] = None) -> str:
        """
        發送錯誤通知

        Args:
            topic_name: Topic 名稱
            execution_id: 執行 ID
            error_message: 錯誤訊息
            error_details: 錯誤詳情

        Returns:
            訊息 ID
        """
        message_data = {
            "event_type": "ph_id_processing_error",
            "execution_id": execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": {
                "message": error_message,
                "details": error_details or {}
            },
            "metadata": {
                "service": "carrefour-offline-data",
                "operation": "ph_id_direct_processing",
                "version": "1.0.0"
            }
        }

        attributes = {
            "event_type": "processing_error",
            "execution_id": execution_id,
            "severity": "error"
        }

        return self.publish_message(topic_name, message_data, attributes)

    def publish_trigger_downstream(self,
                                 topic_name: str,
                                 execution_id: str,
                                 gcs_uri: str,
                                 target_date: str,
                                 record_count: int) -> str:
        """
        發送下游處理觸發通知

        Args:
            topic_name: Topic 名稱
            execution_id: 執行 ID
            gcs_uri: GCS 檔案 URI
            target_date: 目標日期
            record_count: 記錄數量

        Returns:
            訊息 ID
        """
        message_data = {
            "event_type": "trigger_downstream_processing",
            "execution_id": execution_id,
            "target_date": target_date,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": {
                "gcs_uri": gcs_uri,
                "record_count": record_count,
                "format": "avro",
                "schema_version": "1.0.0"
            },
            "metadata": {
                "service": "carrefour-offline-data",
                "operation": "ph_id_direct_processing",
                "version": "1.0.0"
            }
        }

        attributes = {
            "event_type": "trigger_downstream",
            "execution_id": execution_id,
            "target_date": target_date,
            "data_format": "avro"
        }

        return self.publish_message(topic_name, message_data, attributes)

    def create_topic(self, topic_name: str) -> bool:
        """
        建立 Pub/Sub topic

        Args:
            topic_name: Topic 名稱

        Returns:
            是否成功建立
        """
        topic_path = self._format_topic_path(topic_name)

        try:
            topic = self.publisher.create_topic(request={"name": topic_path})
            logger.info(f"Topic 建立成功: {topic.name}")
            return True
        except Exception as e:
            if "already exists" in str(e).lower():
                logger.info(f"Topic 已存在: {topic_path}")
                return True
            else:
                logger.error(f"建立 Topic 失敗: {e}")
                return False

    def create_subscription(self,
                          subscription_name: str,
                          topic_name: str,
                          push_endpoint: Optional[str] = None) -> bool:
        """
        建立 Pub/Sub subscription

        Args:
            subscription_name: Subscription 名稱
            topic_name: Topic 名稱
            push_endpoint: Push 端點 URL (可選)

        Returns:
            是否成功建立
        """
        subscription_path = self._format_subscription_path(subscription_name)
        topic_path = self._format_topic_path(topic_name)

        try:
            request = {
                "name": subscription_path,
                "topic": topic_path
            }

            if push_endpoint:
                request["push_config"] = {"push_endpoint": push_endpoint}

            subscription = self.subscriber.create_subscription(request=request)
            logger.info(f"Subscription 建立成功: {subscription.name}")
            return True
        except Exception as e:
            if "already exists" in str(e).lower():
                logger.info(f"Subscription 已存在: {subscription_path}")
                return True
            else:
                logger.error(f"建立 Subscription 失敗: {e}")
                return False

    def pull_messages(self,
                     subscription_name: str,
                     max_messages: int = 10,
                     timeout: float = 10.0) -> List[Dict[str, Any]]:
        """
        拉取訊息

        Args:
            subscription_name: Subscription 名稱
            max_messages: 最大訊息數量
            timeout: 超時時間 (秒)

        Returns:
            訊息列表
        """
        subscription_path = self._format_subscription_path(subscription_name)

        try:
            # 拉取訊息
            response = self.subscriber.pull(
                request={
                    "subscription": subscription_path,
                    "max_messages": max_messages
                },
                timeout=timeout
            )

            messages = []
            for received_message in response.received_messages:
                try:
                    # 解析訊息
                    message_data = json.loads(received_message.message.data.decode('utf-8'))

                    messages.append({
                        "ack_id": received_message.ack_id,
                        "message_id": received_message.message.message_id,
                        "data": message_data,
                        "attributes": dict(received_message.message.attributes),
                        "publish_time": received_message.message.publish_time
                    })
                except Exception as e:
                    logger.error(f"解析訊息失敗: {e}")

            logger.info(f"拉取到 {len(messages)} 個訊息")
            return messages

        except Exception as e:
            logger.error(f"拉取訊息失敗: {e}")
            return []

    def acknowledge_messages(self, subscription_name: str, ack_ids: List[str]) -> bool:
        """
        確認訊息

        Args:
            subscription_name: Subscription 名稱
            ack_ids: 確認 ID 列表

        Returns:
            是否成功確認
        """
        subscription_path = self._format_subscription_path(subscription_name)

        try:
            self.subscriber.acknowledge(
                request={
                    "subscription": subscription_path,
                    "ack_ids": ack_ids
                }
            )
            logger.info(f"確認 {len(ack_ids)} 個訊息")
            return True
        except Exception as e:
            logger.error(f"確認訊息失敗: {e}")
            return False


def main():
    """主要執行函數 - 用於測試"""
    import argparse

    parser = argparse.ArgumentParser(description='Pub/Sub 通知工具')
    parser.add_argument('--project', required=True, help='GCP 專案 ID')
    parser.add_argument('--topic', help='Topic 名稱')
    parser.add_argument('--subscription', help='Subscription 名稱')
    parser.add_argument('--publish', help='發送測試訊息')
    parser.add_argument('--pull', action='store_true', help='拉取訊息')

    args = parser.parse_args()

    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    notifier = PubSubNotifier(args.project)

    if args.publish and args.topic:
        message_data = {"test": "message", "timestamp": datetime.now().isoformat()}
        message_id = notifier.publish_message(args.topic, message_data)
        print(f"訊息發送成功: {message_id}")

    elif args.pull and args.subscription:
        messages = notifier.pull_messages(args.subscription)
        for msg in messages:
            print(f"訊息 ID: {msg['message_id']}")
            print(f"資料: {msg['data']}")
            print("---")


if __name__ == "__main__":
    main()
