#!/usr/bin/env python3
"""
上傳分析報告到 Google Cloud Storage
"""

import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any
from google.cloud import storage

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    from gcloud_utils import get_gcloud_credentials_path


def setup_gcs_client() -> storage.Client:
    """設定 GCS 客戶端"""
    credentials_path = get_gcloud_credentials_path()
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
    return storage.Client()


def create_project_metadata() -> Dict[str, Any]:
    """建立專案元資料"""
    return {
        'project_name': 'carrefour_offline_data_analysis',
        'created_at': datetime.now().isoformat(),
        'description': '家樂福離線資料 ph_id 格式分析專案',
        'version': '1.0.0',
        'analysis_scope': 'ph_id_user_ph_overlap_validation',
        'team': 'Tagtoo Data Engineering',
        'status': 'analysis_completed',
        'files_included': [
            'ph_id_enhanced_interactive_report.json',
            'real_overlap_analysis_report.json',
            'ph_id_overlap_validation_report.json',
            'ph_id_interactive_report_v2.json',
            'project_completion_summary.md',
            'project_metadata.json'
        ],
        'key_findings': {
            'real_overlap_rate': '0.0% (100 sample size)',
            'data_quality': 'high (>98%)',
            'format_compatibility': '85% (with TO_HEX conversion)',
            'total_cost': '$0.018 USD'
        },
        'next_steps': [
            'Expand sample size for overlap analysis',
            'Implement data copy pipeline',
            'Set up automated monitoring'
        ]
    }


def upload_files_to_gcs(bucket_name: str, folder_path: str, files_to_upload: List[Dict[str, str]]) -> List[str]:
    """上傳檔案到 GCS"""
    print(f'☁️  上傳檔案到 GCS: gs://{bucket_name}/{folder_path}')
    print('=' * 60)

    try:
        client = setup_gcs_client()
        bucket = client.bucket(bucket_name)

        uploaded_files = []

        for file_info in files_to_upload:
            local_path = file_info['local_path']
            gcs_path = f"{folder_path}/{file_info['gcs_name']}"

            if not os.path.exists(local_path):
                print(f'⚠️  檔案不存在: {local_path}')
                continue

            try:
                blob = bucket.blob(gcs_path)

                # 設定適當的 content type
                if local_path.endswith('.json'):
                    blob.content_type = 'application/json; charset=utf-8'
                elif local_path.endswith('.html'):
                    blob.content_type = 'text/html; charset=utf-8'
                elif local_path.endswith('.md'):
                    blob.content_type = 'text/markdown; charset=utf-8'
                else:
                    blob.content_type = 'text/plain; charset=utf-8'

                # 上傳檔案
                blob.upload_from_filename(local_path)

                print(f'✅ 上傳成功: {local_path} -> gs://{bucket_name}/{gcs_path}')
                uploaded_files.append(f'gs://{bucket_name}/{gcs_path}')

            except Exception as e:
                print(f'❌ 上傳失敗 {local_path}: {e}')

        return uploaded_files

    except Exception as e:
        print(f'❌ GCS 連線失敗: {e}')
        return []


def create_index_html(uploaded_files: List[str], project_metadata: Dict[str, Any]) -> str:
    """建立 HTML 索引頁面"""
    html_content = f'''<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家樂福離線資料分析報告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; margin-top: 30px; }}
        .metadata {{ background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }}
        .file-list {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }}
        .file-card {{ background: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .file-card h3 {{ margin-top: 0; color: #2980b9; }}
        .file-link {{ display: inline-block; background: #3498db; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px; }}
        .file-link:hover {{ background: #2980b9; }}
        .key-findings {{ background: #e8f5e8; border-left: 4px solid #27ae60; padding: 15px; margin: 20px 0; }}
        .technical-details {{ background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; }}
        .expandable {{ cursor: pointer; }}
        .expandable-content {{ display: none; margin-top: 10px; }}
        .status {{ display: inline-block; background: #27ae60; color: white; padding: 4px 8px; border-radius: 3px; font-size: 12px; }}
    </style>
    <script>
        function toggleExpand(id) {{
            const content = document.getElementById(id);
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        }}
    </script>
</head>
<body>
    <div class="container">
        <h1>🏪 家樂福離線資料分析報告</h1>
        <span class="status">分析完成</span>

        <div class="metadata">
            <h2>📊 專案概覽</h2>
            <p><strong>專案名稱:</strong> {project_metadata['project_name']}</p>
            <p><strong>分析日期:</strong> {project_metadata['created_at'][:10]}</p>
            <p><strong>版本:</strong> {project_metadata['version']}</p>
            <p><strong>狀態:</strong> {project_metadata['status']}</p>
            <p><strong>描述:</strong> {project_metadata['description']}</p>
        </div>

        <div class="key-findings">
            <h2>🔍 關鍵發現</h2>
            <ul>
                <li><strong>真實重疊率:</strong> {project_metadata['key_findings']['real_overlap_rate']}</li>
                <li><strong>資料品質:</strong> {project_metadata['key_findings']['data_quality']}</li>
                <li><strong>格式相容性:</strong> {project_metadata['key_findings']['format_compatibility']}</li>
                <li><strong>分析成本:</strong> {project_metadata['key_findings']['total_cost']}</li>
            </ul>
        </div>

        <h2>📁 分析報告檔案</h2>
        <div class="file-list">
            <div class="file-card">
                <h3>🎯 增強版互動式報告</h3>
                <p>包含真實查詢結果和技術細節的完整分析報告</p>
                <a href="ph_id_enhanced_interactive_report.json" class="file-link">查看報告 (JSON)</a>
            </div>

            <div class="file-card">
                <h3>🔄 真實重疊分析</h3>
                <p>基於實際 BigQuery 查詢的重疊率分析結果</p>
                <a href="real_overlap_analysis_report.json" class="file-link">查看分析 (JSON)</a>
            </div>

            <div class="file-card">
                <h3>📋 專案完成總結</h3>
                <p>專案執行過程和結果的完整總結文檔</p>
                <a href="project_completion_summary.md" class="file-link">查看總結 (Markdown)</a>
            </div>

            <div class="file-card">
                <h3>📊 基礎互動式報告</h3>
                <p>初版的格式分析和視覺化資料結構</p>
                <a href="ph_id_interactive_report_v2.json" class="file-link">查看報告 (JSON)</a>
            </div>
        </div>

        <div class="technical-details">
            <h2 class="expandable" onclick="toggleExpand('tech-details')">🔧 技術細節 (點擊展開)</h2>
            <div id="tech-details" class="expandable-content">
                <h3>查詢執行統計</h3>
                <ul>
                    <li>總查詢數: 6 個</li>
                    <li>成功執行: 6 個</li>
                    <li>總成本: $0.018 USD</li>
                    <li>執行時間: 約 2 分鐘</li>
                </ul>

                <h3>資料表資訊</h3>
                <ul>
                    <li><strong>離線資料:</strong> 15,242,556 行 (19.9 GB)</li>
                    <li><strong>線上資料:</strong> 49,522,748,451 行 (26.6 TB)</li>
                    <li><strong>樣本大小:</strong> 100 筆 (小樣本驗證)</li>
                </ul>

                <h3>下一步建議</h3>
                <ul>
                    {' '.join([f'<li>{step}</li>' for step in project_metadata['next_steps']])}
                </ul>
            </div>
        </div>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 14px;">
            <p>報告生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>技術棧: Python, BigQuery, Google Cloud Platform</p>
        </div>
    </div>
</body>
</html>'''

    return html_content


def upload_web_files(bucket_name: str) -> List[str]:
    """上傳靜態網頁檔案到 web/ 目錄"""
    web_folder_path = 'carrefour_offline_data/web'

    web_files = [
        {'local_path': 'web/index.html', 'gcs_name': 'index.html'},
        {'local_path': 'web/all_in_one_analysis.html', 'gcs_name': 'all_in_one_analysis.html'}
    ]

    print(f'🌐 上傳靜態網頁到: gs://{bucket_name}/{web_folder_path}/')
    return upload_files_to_gcs(bucket_name, web_folder_path, web_files)

def upload_report_files(bucket_name: str) -> List[str]:
    """上傳 JSON 報告檔案到 reports/ 目錄"""
    reports_folder_path = 'carrefour_offline_data/reports'

    report_files = [
        {'local_path': 'reports/project_metadata.json', 'gcs_name': 'project_metadata.json'},
        {'local_path': 'reports/corrected_overlap_analysis.json', 'gcs_name': 'corrected_overlap_analysis.json'},
        {'local_path': 'reports/detailed_data_distribution_analysis.json', 'gcs_name': 'detailed_data_distribution_analysis.json'},
        {'local_path': 'reports/full_overlap_analysis_100_percent.json', 'gcs_name': 'full_overlap_analysis_100_percent.json'},
        {'local_path': 'reports/items_format_validation.json', 'gcs_name': 'items_format_validation.json'},
        {'local_path': 'reports/null_ph_id_analysis.json', 'gcs_name': 'null_ph_id_analysis.json'},
        {'local_path': 'reports/negative_values_investigation.json', 'gcs_name': 'negative_values_investigation.json'},
        {'local_path': 'reports/offline_deep_analysis.json', 'gcs_name': 'offline_deep_analysis.json'}
    ]

    print(f'📊 上傳 JSON 報告到: gs://{bucket_name}/{reports_folder_path}/')
    return upload_files_to_gcs(bucket_name, reports_folder_path, report_files)

def main():
    """主要執行函數"""
    print('☁️  上傳家樂福離線資料分析檔案到 GCS')
    print('=' * 60)

    # GCS 設定
    bucket_name = 'integrated_event'

    # 建立專案元資料
    project_metadata = create_project_metadata()

    # 儲存專案元資料
    metadata_file = 'reports/project_metadata.json'
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(project_metadata, f, indent=2, ensure_ascii=False)

    try:
        # 上傳靜態網頁檔案
        web_uploaded = upload_web_files(bucket_name)

        # 上傳 JSON 報告檔案
        reports_uploaded = upload_report_files(bucket_name)

        total_uploaded = len(web_uploaded) + len(reports_uploaded)

        if total_uploaded > 0:
            print(f'\n✅ 成功上傳 {total_uploaded} 個檔案到 GCS')
            print(f'🌐 靜態網頁: gs://{bucket_name}/carrefour_offline_data/web/')
            print(f'📊 JSON 報告: gs://{bucket_name}/carrefour_offline_data/reports/')
            print(f'🎯 主頁面: gs://{bucket_name}/carrefour_offline_data/web/index.html')

            # 顯示上傳的檔案
            if web_uploaded:
                print('\n🌐 已上傳靜態網頁:')
                for file_path in web_uploaded:
                    print(f'   {file_path}')

            if reports_uploaded:
                print('\n📊 已上傳 JSON 報告:')
                for file_path in reports_uploaded:
                    print(f'   {file_path}')
        else:
            print('❌ 沒有檔案成功上傳')

    except Exception as e:
        print(f'❌ 上傳過程發生錯誤: {e}')
        sys.exit(1)


if __name__ == '__main__':
    main()
