#!/usr/bin/env python3
"""
Google Cloud 工具模組
提供共享的 Google Cloud 相關工具函數
"""

import os
import platform
from pathlib import Path


def get_gcloud_credentials_path() -> str:
    """
    獲取 Google Cloud 認證檔案路徑

    Returns:
        str: 認證檔案的完整路徑

    Raises:
        FileNotFoundError: 當找不到認證檔案時
    """
    system = platform.system()

    if system == "Darwin":  # macOS
        base_path = Path.home() / ".config" / "gcloud"
    elif system == "Linux":
        base_path = Path.home() / ".config" / "gcloud"
    elif system == "Windows":
        base_path = Path.home() / "AppData" / "Roaming" / "gcloud"
    else:
        raise OSError(f"不支援的作業系統: {system}")

    # 可能的認證檔案路徑
    possible_paths = [
        base_path / "application_default_credentials.json",
        base_path / "legacy_credentials" / "<EMAIL>" / "adc.json",
        Path.home() / ".config" / "gcloud" / "application_default_credentials.json"
    ]

    for path in possible_paths:
        if path.exists():
            return str(path)

    raise FileNotFoundError(
        f"找不到 Google Cloud 認證檔案。請執行 'gcloud auth application-default login' 或檢查以下路徑：\n"
        + "\n".join(f"  - {path}" for path in possible_paths)
    )
