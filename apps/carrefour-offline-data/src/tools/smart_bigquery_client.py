#!/usr/bin/env python3
"""
智能 BigQuery 客戶端
提供自動表格切換和權限檢測功能
"""

import os
import subprocess
from typing import Dict, Any, Optional, Tuple
from google.cloud import bigquery
from google.api_core import exceptions

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    from gcloud_utils import get_gcloud_credentials_path


class SmartBigQueryClient:
    """智能 BigQuery 客戶端，支援自動表格切換"""

    def __init__(self):
        self.client = None
        self.current_account = None
        self.table_mappings = {
            # 原始表格 -> 備用表格
            'tw-eagle-prod.rmn_tagtoo.offline_transaction_day': 'tagtoo-tracking.event_prod.carrefour_offline_transaction_day'
        }
        self._setup_client()

    def _setup_client(self):
        """設置 BigQuery 客戶端"""
        try:
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = get_gcloud_credentials_path()
            self.client = bigquery.Client(project='tagtoo-tracking')
            self.current_account = self._get_current_account()
            print(f"✅ BigQuery 客戶端初始化成功，當前帳號: {self.current_account}")
        except Exception as e:
            print(f"❌ BigQuery 客戶端初始化失敗: {e}")
            raise

    def _get_current_account(self) -> str:
        """獲取當前 gcloud 帳號"""
        try:
            result = subprocess.run(
                ['gcloud', 'auth', 'list', '--filter=status:ACTIVE', '--format=value(account)'],
                capture_output=True, text=True, check=True
            )
            account = result.stdout.strip()
            return account if account else "Unknown"
        except Exception as e:
            print(f"⚠️ 無法獲取當前帳號: {e}")
            return "Unknown"

    def _is_personal_account(self) -> bool:
        """檢測是否為個人帳號"""
        return self.current_account and '@tagtoo.com' in self.current_account and not self.current_account.endswith('.iam.gserviceaccount.com')

    def _get_fallback_table(self, original_table: str) -> Optional[str]:
        """獲取備用表格名稱"""
        return self.table_mappings.get(original_table)

    def _test_table_access(self, table_id: str) -> Tuple[bool, Optional[str]]:
        """測試表格訪問權限"""
        try:
            # 執行簡單的 COUNT 查詢測試權限
            query = f"SELECT COUNT(*) as count FROM `{table_id}` LIMIT 1"
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            job = self.client.query(query, job_config=job_config)  # 只檢查權限，不實際執行
            return True, None
        except exceptions.Forbidden as e:
            return False, f"權限拒絕: {str(e)}"
        except exceptions.NotFound as e:
            return False, f"表格不存在: {str(e)}"
        except Exception as e:
            return False, f"其他錯誤: {str(e)}"

    def get_accessible_table(self, preferred_table: str) -> Tuple[str, str]:
        """
        獲取可訪問的表格

        Args:
            preferred_table: 首選表格名稱

        Returns:
            Tuple[str, str]: (實際使用的表格, 狀態訊息)
        """
        # 首先測試首選表格
        can_access, error = self._test_table_access(preferred_table)

        if can_access:
            return preferred_table, f"✅ 使用首選表格: {preferred_table}"

        print(f"⚠️ 無法訪問首選表格 {preferred_table}: {error}")

        # 如果是個人帳號且有備用表格，嘗試切換
        if self._is_personal_account():
            fallback_table = self._get_fallback_table(preferred_table)
            if fallback_table:
                can_access_fallback, fallback_error = self._test_table_access(fallback_table)

                if can_access_fallback:
                    print(f"🔄 檢測到個人帳號 ({self.current_account})，自動切換到備用表格")
                    return fallback_table, f"🔄 自動切換到備用表格: {fallback_table}"
                else:
                    print(f"❌ 備用表格也無法訪問: {fallback_error}")

        # 如果都無法訪問，返回原始表格和錯誤訊息
        return preferred_table, f"❌ 所有表格都無法訪問，最後錯誤: {error}"

    def query(self, query_template: str, **kwargs) -> bigquery.QueryJob:
        """
        執行查詢，自動處理表格切換

        Args:
            query_template: SQL 查詢模板
            **kwargs: 其他參數

        Returns:
            bigquery.QueryJob: 查詢作業
        """
        # 檢查查詢中是否包含需要切換的表格
        modified_query = query_template

        for original_table, fallback_table in self.table_mappings.items():
            if original_table in query_template:
                accessible_table, status_msg = self.get_accessible_table(original_table)
                print(status_msg)

                if accessible_table != original_table:
                    modified_query = modified_query.replace(original_table, accessible_table)
                    print(f"🔄 查詢中的表格已自動替換: {original_table} → {accessible_table}")

        return self.client.query(modified_query, **kwargs)

    def get_client(self) -> bigquery.Client:
        """獲取 BigQuery 客戶端"""
        return self.client


def get_smart_bigquery_client() -> SmartBigQueryClient:
    """獲取智能 BigQuery 客戶端實例"""
    return SmartBigQueryClient()
