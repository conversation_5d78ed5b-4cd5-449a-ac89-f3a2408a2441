#!/usr/bin/env python3
"""
執行環境準備與測試工具

檢查並安裝所需依賴、驗證權限、建立配置檔案

Author: AI Assistant
Date: 2025-08-15
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# 使用共享的 gcloud 工具模組（支援相對和絕對導入）
try:
    from .gcloud_utils import get_gcloud_credentials_path
except ImportError:
    # 當作為獨立模組執行時，使用絕對導入
    from gcloud_utils import get_gcloud_credentials_path


class EnvironmentSetup:
    """執行環境設定器"""

    def __init__(self):
        self.required_packages = [
            'google-cloud-bigquery>=3.0.0',
            'google-cloud-bigquery-storage>=2.0.0',
            'pandas>=1.3.0',
            'numpy>=1.20.0',
            'tqdm>=4.60.0'
        ]

        self.config = {
            'source': {
                'project': 'tw-eagle-prod',
                'dataset': 'rmn_tagtoo',
                'table': 'offline_transaction_day',
                'service_account': '<EMAIL>'
            },
            'target': {
                'project': 'tagtoo-tracking',
                'dataset': 'event_prod',
                'table': 'carrefour_offline_data'
            },
            'copy_settings': {
                'max_query_cost_usd': 10.0,
                'batch_size_gb': 2.0,
                'min_batch_size': 1000,
                'max_batch_size': 100000,
                'target_batch_duration_seconds': 300,
                'max_retries': 3,
                'base_retry_delay': 5
            },
            'validation_settings': {
                'sample_size': 1000,
                'null_ratio_tolerance': 0.01,
                'size_diff_tolerance': 0.05,
                'match_ratio_threshold': 0.95
            }
        }

        self.setup_results = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'packages_installed': [],
            'packages_failed': [],
            'permissions_checked': {},
            'config_created': False,
            'test_results': {},
            'overall_status': 'UNKNOWN'
        }

    def check_python_version(self) -> bool:
        """檢查 Python 版本"""
        print("🐍 檢查 Python 版本...")

        version_info = sys.version_info
        if version_info.major >= 3 and version_info.minor >= 8:
            print(f"✅ Python 版本: {sys.version}")
            return True
        else:
            print(f"❌ Python 版本過舊: {sys.version}")
            print("需要 Python 3.8 或更新版本")
            return False

    def install_packages(self) -> bool:
        """安裝所需套件"""
        print("\n📦 檢查並安裝所需套件...")

        all_success = True

        for package in self.required_packages:
            try:
                print(f"檢查套件: {package}")

                # 嘗試導入套件
                package_name = package.split('>=')[0].split('==')[0]

                if package_name == 'google-cloud-bigquery':
                    import google.cloud.bigquery
                    print(f"✅ {package_name} 已安裝")
                elif package_name == 'google-cloud-bigquery-storage':
                    import google.cloud.bigquery_storage
                    print(f"✅ {package_name} 已安裝")
                elif package_name == 'pandas':
                    import pandas
                    print(f"✅ {package_name} 已安裝")
                elif package_name == 'numpy':
                    import numpy
                    print(f"✅ {package_name} 已安裝")
                elif package_name == 'tqdm':
                    import tqdm
                    print(f"✅ {package_name} 已安裝")

                self.setup_results['packages_installed'].append(package_name)

            except ImportError:
                print(f"⚠️ {package_name} 未安裝，嘗試安裝...")

                try:
                    # 使用 pip 安裝
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', package
                    ], capture_output=True, text=True, check=True)

                    print(f"✅ {package_name} 安裝成功")
                    self.setup_results['packages_installed'].append(package_name)

                except subprocess.CalledProcessError as e:
                    print(f"❌ {package_name} 安裝失敗: {e}")
                    self.setup_results['packages_failed'].append(package_name)
                    all_success = False

            except Exception as e:
                print(f"❌ 檢查 {package_name} 時發生錯誤: {e}")
                self.setup_results['packages_failed'].append(package_name)
                all_success = False

        return all_success

    def check_gcloud_auth(self) -> bool:
        """檢查 gcloud 認證狀態"""
        print("\n🔐 檢查 gcloud 認證狀態...")

        try:
            # 檢查 gcloud 是否已安裝
            result = subprocess.run(['gcloud', '--version'],
                                  capture_output=True, text=True, check=True)
            print("✅ gcloud CLI 已安裝")

            # 檢查當前認證狀態
            result = subprocess.run(['gcloud', 'auth', 'list'],
                                  capture_output=True, text=True, check=True)

            if 'ACTIVE' in result.stdout:
                print("✅ gcloud 已認證")

                # 檢查當前配置
                result = subprocess.run(['gcloud', 'config', 'list'],
                                      capture_output=True, text=True, check=True)
                print("當前 gcloud 配置:")
                print(result.stdout)

                return True
            else:
                print("⚠️ gcloud 未認證")
                return False

        except subprocess.CalledProcessError as e:
            print(f"❌ gcloud 檢查失敗: {e}")
            return False
        except FileNotFoundError:
            print("❌ gcloud CLI 未安裝")
            return False

    def test_bigquery_permissions(self) -> Dict[str, bool]:
        """測試 BigQuery 權限"""
        print("\n🔍 測試 BigQuery 權限...")

        permissions = {
            'source_table_read': False,
            'source_query_create': False,
            'target_table_read': False,
            'target_table_write': False
        }

        try:
            from google.cloud import bigquery

            # 設定認證 - 使用動態路徑
            credentials_path = get_gcloud_credentials_path()
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
            print(f"使用認證檔案: {credentials_path}")

            # 測試來源表格讀取
            try:
                source_client = bigquery.Client(project=self.config['source']['project'])
                source_table_ref = f"{self.config['source']['project']}.{self.config['source']['dataset']}.{self.config['source']['table']}"
                table = source_client.get_table(source_table_ref)
                permissions['source_table_read'] = True
                print(f"✅ 來源表格讀取權限: {table.num_rows:,} 行")
            except Exception as e:
                print(f"❌ 來源表格讀取權限失敗: {e}")

            # 測試來源查詢權限
            try:
                test_query = f"SELECT COUNT(*) FROM `{source_table_ref}` LIMIT 1"
                job_config = bigquery.QueryJobConfig(dry_run=True)
                query_job = source_client.query(test_query, job_config=job_config)
                permissions['source_query_create'] = True
                print("✅ 來源查詢權限")
            except Exception as e:
                print(f"❌ 來源查詢權限失敗: {e}")

            # 測試目標表格讀取
            try:
                target_client = bigquery.Client(project=self.config['target']['project'])
                target_table_ref = f"{self.config['target']['project']}.{self.config['target']['dataset']}.{self.config['target']['table']}"
                table = target_client.get_table(target_table_ref)
                permissions['target_table_read'] = True
                print(f"✅ 目標表格讀取權限: {table.num_rows:,} 行")
            except Exception as e:
                print(f"❌ 目標表格讀取權限失敗: {e}")

            # 測試目標表格寫入權限（插入測試資料）
            try:
                test_insert_query = f"""
                INSERT INTO `{target_table_ref}`
                SELECT * FROM `{target_table_ref}` WHERE FALSE
                """
                job_config = bigquery.QueryJobConfig(dry_run=True)
                query_job = target_client.query(test_insert_query, job_config=job_config)
                permissions['target_table_write'] = True
                print("✅ 目標表格寫入權限")
            except Exception as e:
                print(f"❌ 目標表格寫入權限失敗: {e}")

        except Exception as e:
            print(f"❌ BigQuery 權限測試失敗: {e}")

        self.setup_results['permissions_checked'] = permissions
        return permissions

    def create_config_files(self) -> bool:
        """建立配置檔案"""
        print("\n📝 建立配置檔案...")

        try:
            # 建立主配置檔案
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            print("✅ config.json 已建立")

            # 建立環境變數設定腳本
            credentials_path = get_gcloud_credentials_path()
            env_script = f"""#!/bin/bash
# 家樂福資料複製環境變數設定

# Google Cloud 認證 (動態路徑)
export GOOGLE_APPLICATION_CREDENTIALS="{credentials_path}"

# 來源表格設定
export SOURCE_PROJECT="{self.config['source']['project']}"
export SOURCE_DATASET="{self.config['source']['dataset']}"
export SOURCE_TABLE="{self.config['source']['table']}"
export SERVICE_ACCOUNT="{self.config['source']['service_account']}"

# 目標表格設定
export TARGET_PROJECT="{self.config['target']['project']}"
export TARGET_DATASET="{self.config['target']['dataset']}"
export TARGET_TABLE="{self.config['target']['table']}"

# 複製設定
export MAX_QUERY_COST_USD="{self.config['copy_settings']['max_query_cost_usd']}"
export BATCH_SIZE_GB="{self.config['copy_settings']['batch_size_gb']}"

echo "✅ 環境變數已設定"
echo "來源: $SOURCE_PROJECT.$SOURCE_DATASET.$SOURCE_TABLE"
echo "目標: $TARGET_PROJECT.$TARGET_DATASET.$TARGET_TABLE"
"""

            with open('set_env.sh', 'w', encoding='utf-8') as f:
                f.write(env_script)

            # 設定執行權限
            os.chmod('set_env.sh', 0o755)

            print("✅ set_env.sh 已建立")

            self.setup_results['config_created'] = True
            return True

        except Exception as e:
            print(f"❌ 建立配置檔案失敗: {e}")
            return False

    def run_basic_tests(self) -> Dict[str, bool]:
        """執行基本功能測試"""
        print("\n🧪 執行基本功能測試...")

        test_results = {
            'schema_validator': False,
            'data_validator': False,
            'copy_script': False
        }

        # 測試 schema_validator.py
        try:
            print("測試 schema_validator.py...")
            result = subprocess.run([
                sys.executable, '-c',
                'from schema_validator import SchemaValidator; print("✅ schema_validator 可正常導入")'
            ], capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                test_results['schema_validator'] = True
                print("✅ schema_validator.py 測試通過")
            else:
                print(f"❌ schema_validator.py 測試失敗: {result.stderr}")
        except Exception as e:
            print(f"❌ schema_validator.py 測試錯誤: {e}")

        # 測試 data_validator.py
        try:
            print("測試 data_validator.py...")
            result = subprocess.run([
                sys.executable, '-c',
                'from data_validator import DataValidator; print("✅ data_validator 可正常導入")'
            ], capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                test_results['data_validator'] = True
                print("✅ data_validator.py 測試通過")
            else:
                print(f"❌ data_validator.py 測試失敗: {result.stderr}")
        except Exception as e:
            print(f"❌ data_validator.py 測試錯誤: {e}")

        # 測試 copy_carrefour_data.py
        try:
            print("測試 copy_carrefour_data.py...")
            result = subprocess.run([
                sys.executable, '-c',
                'from copy_carrefour_data import CarrefourDataCopier; print("✅ copy_carrefour_data 可正常導入")'
            ], capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                test_results['copy_script'] = True
                print("✅ copy_carrefour_data.py 測試通過")
            else:
                print(f"❌ copy_carrefour_data.py 測試失敗: {result.stderr}")
        except Exception as e:
            print(f"❌ copy_carrefour_data.py 測試錯誤: {e}")

        self.setup_results['test_results'] = test_results
        return test_results

    def generate_setup_report(self):
        """生成設定報告"""
        # 計算整體狀態
        if (len(self.setup_results['packages_failed']) == 0 and
            self.setup_results['permissions_checked'].get('source_table_read', False) and
            self.setup_results['permissions_checked'].get('target_table_read', False) and
            self.setup_results['config_created'] and
            all(self.setup_results['test_results'].values())):
            self.setup_results['overall_status'] = 'READY'
        elif (self.setup_results['permissions_checked'].get('source_table_read', False) and
              self.setup_results['permissions_checked'].get('target_table_read', False)):
            self.setup_results['overall_status'] = 'PARTIAL'
        else:
            self.setup_results['overall_status'] = 'NOT_READY'

        # JSON 報告
        with open('environment_setup_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.setup_results, f, indent=2, ensure_ascii=False, default=str)

        # 人類可讀報告
        report_lines = [
            "# 執行環境設定報告",
            f"生成時間: {self.setup_results['timestamp']}",
            f"整體狀態: {self.setup_results['overall_status']}",
            "",
            "## Python 環境",
            f"Python 版本: {self.setup_results['python_version']}",
            "",
            "## 套件安裝狀態",
            f"成功安裝: {len(self.setup_results['packages_installed'])} 個套件",
            f"安裝失敗: {len(self.setup_results['packages_failed'])} 個套件",
            ""
        ]

        if self.setup_results['packages_installed']:
            report_lines.append("### 已安裝套件")
            for package in self.setup_results['packages_installed']:
                report_lines.append(f"- ✅ {package}")
            report_lines.append("")

        if self.setup_results['packages_failed']:
            report_lines.append("### 安裝失敗套件")
            for package in self.setup_results['packages_failed']:
                report_lines.append(f"- ❌ {package}")
            report_lines.append("")

        report_lines.append("## 權限檢查結果")
        for perm, status in self.setup_results['permissions_checked'].items():
            status_icon = "✅" if status else "❌"
            report_lines.append(f"- {status_icon} {perm}")
        report_lines.append("")

        report_lines.append("## 功能測試結果")
        for test, status in self.setup_results['test_results'].items():
            status_icon = "✅" if status else "❌"
            report_lines.append(f"- {status_icon} {test}")
        report_lines.append("")

        # 下一步建議
        report_lines.append("## 下一步建議")
        if self.setup_results['overall_status'] == 'READY':
            report_lines.append("🎉 環境設定完成！可以開始執行資料複製和驗證。")
        elif self.setup_results['overall_status'] == 'PARTIAL':
            report_lines.append("⚠️ 環境部分就緒，但缺少查詢權限。")
            report_lines.append("建議申請 BigQuery Job User 權限後再執行完整流程。")
        else:
            report_lines.append("❌ 環境尚未就緒，請解決以下問題：")
            if self.setup_results['packages_failed']:
                report_lines.append("- 安裝失敗的套件")
            if not self.setup_results['permissions_checked'].get('source_table_read', False):
                report_lines.append("- 來源表格讀取權限")
            if not self.setup_results['permissions_checked'].get('target_table_read', False):
                report_lines.append("- 目標表格讀取權限")

        with open('environment_setup_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print("\n📄 設定報告已生成:")
        print("  - environment_setup_report.json")
        print("  - environment_setup_report.md")

    def run_full_setup(self) -> bool:
        """執行完整的環境設定"""
        print("🚀 開始執行環境設定")
        print("=" * 60)

        # 檢查 Python 版本
        if not self.check_python_version():
            return False

        # 安裝套件
        self.install_packages()

        # 檢查認證
        self.check_gcloud_auth()

        # 測試權限
        self.test_bigquery_permissions()

        # 建立配置檔案
        self.create_config_files()

        # 執行基本測試
        self.run_basic_tests()

        # 生成報告
        self.generate_setup_report()

        # 顯示結果
        print(f"\n📊 環境設定完成！")
        print(f"整體狀態: {self.setup_results['overall_status']}")

        return self.setup_results['overall_status'] in ['READY', 'PARTIAL']

def main():
    """主函數"""
    setup = EnvironmentSetup()
    success = setup.run_full_setup()

    if success:
        print("\n🎉 環境設定成功！")
        print("可以開始執行資料複製和驗證流程。")
    else:
        print("\n❌ 環境設定失敗！")
        print("請檢查設定報告並解決問題。")

    return success

if __name__ == "__main__":
    main()
