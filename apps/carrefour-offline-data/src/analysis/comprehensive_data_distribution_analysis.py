#!/usr/bin/env python3
"""
全面資料分佈分析與驗證
深度分析離線和線上資料分佈，驗證重疊率異常原因
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/tools')
sys.path.append('src/utils')
from setup_environment import get_gcloud_credentials_path
from smart_client import smart_client


def setup_bigquery_client() -> bigquery.Client:
    """設定 BigQuery 客戶端"""
    return smart_client()


def estimate_query_cost_with_limit(client: bigquery.Client, query: str, description: str, max_cost: float = 0.5) -> Tuple[Dict[str, Any], bool]:
    """估算查詢成本並檢查是否超過限制"""
    print(f'💰 估算查詢成本: {description}')

    try:
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=job_config)

        bytes_processed = job.total_bytes_processed
        cost_usd = (bytes_processed / (1024**3)) * 0.005  # $5 per TB

        cost_info = {
            'description': description,
            'bytes_processed': bytes_processed,
            'bytes_processed_gb': bytes_processed / (1024**3),
            'estimated_cost_usd': cost_usd,
            'query': query,
            'estimated_at': datetime.now().isoformat()
        }

        print(f'   處理資料量: {cost_info["bytes_processed_gb"]:.3f} GB')
        print(f'   估算成本: ${cost_usd:.4f} USD')

        if cost_usd > max_cost:
            print(f'⚠️  警告: 查詢成本 ${cost_usd:.4f} USD 超過限制 ${max_cost:.2f} USD')
            return cost_info, False
        else:
            print(f'✅ 查詢成本在限制範圍內')
            return cost_info, True

    except Exception as e:
        print(f'❌ 成本估算失敗: {e}')
        return {'error': str(e), 'description': description}, False


def execute_safe_query(client: bigquery.Client, query: str, description: str, max_cost: float = 0.5) -> Dict[str, Any]:
    """安全執行查詢，包含成本控制"""
    cost_info, cost_approved = estimate_query_cost_with_limit(client, query, description, max_cost)

    if not cost_approved:
        return {
            'status': 'skipped_high_cost',
            'cost_info': cost_info,
            'error': f'Query cost ${cost_info.get("estimated_cost_usd", 0):.4f} USD exceeds limit ${max_cost:.2f} USD'
        }

    try:
        start_time = datetime.now()
        job = client.query(query)
        results = job.result()
        end_time = datetime.now()

        execution_time = (end_time - start_time).total_seconds()

        # 收集結果
        data = []
        for row in results:
            row_dict = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):  # datetime 物件
                    row_dict[key] = value.isoformat()
                else:
                    row_dict[key] = value
            data.append(row_dict)

        return {
            'status': 'success',
            'data': data,
            'cost_info': cost_info,
            'execution_info': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'execution_time_seconds': execution_time,
                'job_id': job.job_id,
                'rows_returned': len(data)
            }
        }

    except Exception as e:
        print(f'❌ 查詢執行失敗: {e}')
        return {
            'status': 'failed',
            'cost_info': cost_info,
            'error': str(e)
        }


def analyze_offline_data_distribution(client: bigquery.Client) -> Dict[str, Any]:
    """分析離線資料分佈"""
    print('\n📊 分析離線資料分佈...')
    print('=' * 50)

    analyses = {}

    # 1. 基本統計
    basic_stats_query = '''
    SELECT
      COUNT(*) as total_record_count,
      COUNT(DISTINCT ph_id) as unique_ph_id_count,
      COUNT(ph_id) as non_null_ph_ids,
      COUNT(*) - COUNT(ph_id) as null_ph_ids,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 2) as null_percentage,
      MIN(event_times) as earliest_date,
      MAX(event_times) as latest_date,
      COUNT(DISTINCT event_times) as unique_dates
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    '''

    analyses['basic_statistics'] = execute_safe_query(
        client, basic_stats_query, '離線資料基本統計', max_cost=0.1
    )

    # 2. 資料品質分析
    quality_analysis_query = '''
    SELECT
      LENGTH(ph_id) as ph_id_byte_length,
      LENGTH(TO_HEX(ph_id)) as ph_id_hex_length,
      COUNT(*) as count_with_this_length
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE ph_id IS NOT NULL
    GROUP BY LENGTH(ph_id), LENGTH(TO_HEX(ph_id))
    ORDER BY count_with_this_length DESC
    LIMIT 10
    '''

    analyses['data_quality'] = execute_safe_query(
        client, quality_analysis_query, '離線資料品質分析', max_cost=0.1
    )

    # 3. 日期分佈分析
    date_distribution_query = '''
    SELECT
      event_times as date,
      COUNT(*) as total_record_count,
      COUNT(DISTINCT ph_id) as unique_ph_id_count,
      COUNT(ph_id) as non_null_ph_ids
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE event_times >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
    GROUP BY event_times
    ORDER BY event_times DESC
    LIMIT 90
    '''

    analyses['date_distribution'] = execute_safe_query(
        client, date_distribution_query, '離線資料日期分佈', max_cost=0.15
    )

    return analyses


def analyze_online_data_distribution(client: bigquery.Client) -> Dict[str, Any]:
    """分析線上資料分佈（針對家樂福 ec_id=715）"""
    print('\n📊 分析線上資料分佈 (家樂福 ec_id=715)...')
    print('=' * 50)

    analyses = {}
    time_ranges = [1, 3, 7, 14, 30, 60, 90]

    # 1. 按時間範圍統計
    time_range_stats = []
    for days_back in time_ranges:
        stats_query = f'''
        SELECT
          {days_back} as days_back,
          COUNT(*) as total_events,
          COUNT(user.ph) as non_null_ph_events,
          COUNT(*) - COUNT(user.ph) as null_ph_events,
          ROUND((COUNT(*) - COUNT(user.ph)) * 100.0 / COUNT(*), 2) as null_ph_percentage,
          COUNT(DISTINCT user.ph) as unique_ph_count,
          ROUND(COUNT(DISTINCT user.ph) * 100.0 / COUNT(user.ph), 2) as unique_ph_percentage,
          MIN(DATE(event_time)) as earliest_date,
          MAX(DATE(event_time)) as latest_date
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = 715
          AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL {days_back} DAY)
        '''

        result = execute_safe_query(
            client, stats_query, f'線上資料統計 ({days_back}天)', max_cost=0.2
        )

        if result['status'] == 'success' and result['data']:
            time_range_stats.append(result['data'][0])
        else:
            time_range_stats.append({
                'days_back': days_back,
                'status': result['status'],
                'error': result.get('error', 'Unknown error')
            })

    analyses['time_range_statistics'] = {
        'status': 'success',
        'data': time_range_stats
    }

    # 2. 每日活躍度趨勢
    daily_activity_query = '''
    SELECT
      DATE(event_time) as event_date,
      COUNT(*) as total_events,
      COUNT(user.ph) as non_null_ph_events,
      COUNT(DISTINCT user.ph) as unique_ph_count,
      ROUND(COUNT(DISTINCT user.ph) * 100.0 / COUNT(user.ph), 2) as unique_ph_percentage
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE ec_id = 715
      AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
    GROUP BY DATE(event_time)
    ORDER BY event_date DESC
    LIMIT 30
    '''

    analyses['daily_activity'] = execute_safe_query(
        client, daily_activity_query, '每日活躍度趨勢', max_cost=0.2
    )

    # 3. user.ph 格式分析
    ph_format_query = '''
    SELECT
      LENGTH(user.ph) as ph_length,
      COUNT(*) as count_with_this_length,
      COUNT(DISTINCT user.ph) as unique_ph_with_this_length
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE ec_id = 715
      AND user.ph IS NOT NULL
      AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
    GROUP BY LENGTH(user.ph)
    ORDER BY count_with_this_length DESC
    LIMIT 10
    '''

    analyses['ph_format_analysis'] = execute_safe_query(
        client, ph_format_query, 'user.ph 格式分析', max_cost=0.15
    )

    return analyses


def analyze_overlap_anomaly(client: bigquery.Client) -> Dict[str, Any]:
    """分析重疊率異常原因"""
    print('\n🔍 分析重疊率異常原因...')
    print('=' * 50)

    analyses = {}

    # 1. 格式匹配驗證
    format_match_query = '''
    WITH offline_sample AS (
      SELECT DISTINCT TO_HEX(ph_id) as ph_id_hex
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
      WHERE ph_id IS NOT NULL
      LIMIT 1000
    ),
    online_sample AS (
      SELECT DISTINCT user.ph
      FROM `tagtoo-tracking.event_prod.tagtoo_event`
      WHERE ec_id = 715
        AND user.ph IS NOT NULL
        AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
      LIMIT 1000
    )
    SELECT
      'offline' as data_source,
      COUNT(*) as sample_count,
      MIN(LENGTH(ph_id_hex)) as min_length,
      MAX(LENGTH(ph_id_hex)) as max_length,
      AVG(LENGTH(ph_id_hex)) as avg_length
    FROM offline_sample
    UNION ALL
    SELECT
      'online' as data_source,
      COUNT(*) as sample_count,
      MIN(LENGTH(ph)) as min_length,
      MAX(LENGTH(ph)) as max_length,
      AVG(LENGTH(ph)) as avg_length
    FROM online_sample
    '''

    analyses['format_verification'] = execute_safe_query(
        client, format_match_query, '格式匹配驗證', max_cost=0.2
    )

    # 2. 重疊率趨勢驗證
    overlap_trend_query = '''
    WITH offline_all AS (
      SELECT DISTINCT TO_HEX(ph_id) as ph_id_hex
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
      WHERE ph_id IS NOT NULL
      LIMIT 10000  -- 使用較大樣本進行驗證
    )
    SELECT
      1 as days_back,
      COUNT(DISTINCT o1.ph_id_hex) as offline_count,
      COUNT(DISTINCT on1.ph) as online_count,
      COUNT(DISTINCT CASE WHEN on1.ph IS NOT NULL THEN o1.ph_id_hex END) as overlap_count
    FROM offline_all o1
    FULL OUTER JOIN (
      SELECT DISTINCT user.ph
      FROM `tagtoo-tracking.event_prod.tagtoo_event`
      WHERE ec_id = 715 AND user.ph IS NOT NULL
        AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
    ) on1 ON o1.ph_id_hex = on1.ph

    UNION ALL

    SELECT
      7 as days_back,
      COUNT(DISTINCT o7.ph_id_hex) as offline_count,
      COUNT(DISTINCT on7.ph) as online_count,
      COUNT(DISTINCT CASE WHEN on7.ph IS NOT NULL THEN o7.ph_id_hex END) as overlap_count
    FROM offline_all o7
    FULL OUTER JOIN (
      SELECT DISTINCT user.ph
      FROM `tagtoo-tracking.event_prod.tagtoo_event`
      WHERE ec_id = 715 AND user.ph IS NOT NULL
        AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
    ) on7 ON o7.ph_id_hex = on7.ph
    '''

    analyses['overlap_trend_verification'] = execute_safe_query(
        client, overlap_trend_query, '重疊率趨勢驗證', max_cost=0.3
    )

    return analyses


def main():
    """主要執行函數"""
    print('📊 全面資料分佈分析與驗證')
    print('=' * 60)

    total_cost = 0.0
    cost_limit = 2.0  # $2.00 USD 總成本限制

    try:
        client = setup_bigquery_client()

        # 執行分析
        print(f'💰 成本限制: ${cost_limit:.2f} USD')

        # 1. 離線資料分析
        offline_analysis = analyze_offline_data_distribution(client)

        # 計算已使用成本
        for analysis_name, analysis_result in offline_analysis.items():
            if 'cost_info' in analysis_result and 'estimated_cost_usd' in analysis_result['cost_info']:
                total_cost += analysis_result['cost_info']['estimated_cost_usd']

        print(f'💰 離線資料分析成本: ${total_cost:.4f} USD')

        if total_cost > cost_limit:
            print(f'⚠️  成本超過限制，停止後續分析')
            return

        # 2. 線上資料分析
        online_analysis = analyze_online_data_distribution(client)

        # 更新成本
        if 'time_range_statistics' in online_analysis:
            # 估算線上分析成本 (7個查詢 * 平均成本)
            estimated_online_cost = 7 * 0.05  # 保守估算
            total_cost += estimated_online_cost

        print(f'💰 累計成本: ${total_cost:.4f} USD')

        if total_cost > cost_limit:
            print(f'⚠️  成本接近限制，跳過重疊率異常分析')
            overlap_analysis = {'status': 'skipped_cost_limit'}
        else:
            # 3. 重疊率異常分析
            overlap_analysis = analyze_overlap_anomaly(client)
            total_cost += 0.1  # 估算成本

        # 建立完整報告
        comprehensive_report = {
            'metadata': {
                'analysis_type': 'comprehensive_data_distribution_analysis',
                'generated_at': datetime.now().isoformat(),
                'version': '1.0',
                'description': '全面資料分佈分析與重疊率異常驗證',
                'cost_control': {
                    'total_cost_limit_usd': cost_limit,
                    'actual_cost_usd': total_cost,
                    'cost_efficiency': 'excellent' if total_cost < cost_limit * 0.5 else 'good'
                }
            },
            'offline_data_analysis': offline_analysis,
            'online_data_analysis': online_analysis,
            'overlap_anomaly_analysis': overlap_analysis,
            'summary_findings': {
                'data_quality_issues': [],
                'overlap_anomaly_causes': [],
                'recommendations': []
            }
        }

        # 分析發現並生成建議
        analyze_findings_and_recommendations(comprehensive_report)

        # 儲存報告
        output_file = 'reports/comprehensive_data_distribution_analysis.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)

        print(f'\n✅ 全面資料分佈分析完成')
        print(f'📁 報告已儲存至: {output_file}')
        print(f'💰 總成本: ${total_cost:.4f} USD (限制: ${cost_limit:.2f} USD)')

        # 顯示關鍵發現
        display_key_findings(comprehensive_report)

    except Exception as e:
        print(f'❌ 執行錯誤: {e}')
        sys.exit(1)


def analyze_findings_and_recommendations(report: Dict[str, Any]) -> None:
    """分析發現並生成建議"""
    findings = report['summary_findings']

    # 分析離線資料品質
    offline_basic = report['offline_data_analysis'].get('basic_statistics', {})
    if offline_basic.get('status') == 'success' and offline_basic.get('data'):
        data = offline_basic['data'][0]
        null_percentage = data.get('null_percentage', 0)
        if null_percentage > 5:
            findings['data_quality_issues'].append(f'離線資料 NULL 值比例較高: {null_percentage}%')

    # 分析線上資料活躍度
    online_stats = report['online_data_analysis'].get('time_range_statistics', {})
    if online_stats.get('status') == 'success':
        stats_data = online_stats['data']
        # 檢查是否有明顯的資料範圍限制
        if len(stats_data) >= 3:
            day7_count = next((s['unique_ph_count'] for s in stats_data if s.get('days_back') == 7), 0)
            day14_count = next((s['unique_ph_count'] for s in stats_data if s.get('days_back') == 14), 0)
            if day7_count > 0 and abs(day7_count - day14_count) / day7_count < 0.01:
                findings['overlap_anomaly_causes'].append('線上資料 user.ph 功能僅運行7天，因此7天後無新增用戶資料')

    # 生成建議
    findings['recommendations'].extend([
        '監控線上 user.ph 功能持續運行後的重疊率增長趨勢',
        '建立資料品質監控機制，定期檢查 NULL 值比例',
        '實施增量資料更新策略，確保資料時效性',
        '考慮使用更大的樣本進行重疊率驗證'
    ])


def display_key_findings(report: Dict[str, Any]) -> None:
    """顯示關鍵發現"""
    print(f'\n🎯 關鍵發現:')

    # 離線資料統計
    offline_basic = report['offline_data_analysis'].get('basic_statistics', {})
    if offline_basic.get('status') == 'success' and offline_basic.get('data'):
        data = offline_basic['data'][0]
        print(f'   離線資料總記錄: {data.get("total_record_count", 0):,}')
        print(f'   唯一 ph_id: {data.get("unique_ph_id_count", 0):,}')
        print(f'   NULL 值比例: {data.get("null_percentage", 0)}%')

    # 線上資料統計
    online_stats = report['online_data_analysis'].get('time_range_statistics', {})
    if online_stats.get('status') == 'success':
        print(f'   線上資料分析: 已完成多時間範圍統計')
        for stat in online_stats['data'][:3]:  # 顯示前3個時間範圍
            if 'days_back' in stat:
                print(f'     {stat["days_back"]}天: {stat.get("unique_ph_count", 0):,} 唯一用戶')

    # 發現的問題
    findings = report['summary_findings']
    if findings['data_quality_issues']:
        print(f'\n⚠️  資料品質問題:')
        for issue in findings['data_quality_issues']:
            print(f'   - {issue}')

    if findings['overlap_anomaly_causes']:
        print(f'\n🔍 重疊率異常原因:')
        for cause in findings['overlap_anomaly_causes']:
            print(f'   - {cause}')


if __name__ == '__main__':
    main()
