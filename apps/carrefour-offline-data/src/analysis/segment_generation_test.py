#!/usr/bin/env python3
"""
受眾標籤生成測試腳本

測試階層式 segment_id 生成邏輯的正確性和完整性

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import json
from typing import List, Dict, Set
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SegmentGenerationTester:
    """受眾標籤生成測試器"""

    def __init__(self):
        self.test_cases = []
        self.results = {}

    def generate_hierarchical_segments(self, sub_class_key: str) -> List[str]:
        """
        根據 SUB_CLASS_KEY 生成階層式 segment_id 標籤

        Args:
            sub_class_key: 小分類代碼 (例如: "10012")

        Returns:
            階層式標籤列表 (例如: ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"])
        """
        segments = []

        # 小分類標籤
        segments.append(f"tm:c_715_pc_{sub_class_key}")

        # 中分類標籤 (取前4位數字)
        if len(sub_class_key) >= 4:
            medium_key = sub_class_key[:4]
            segments.append(f"tm:c_715_pc_{medium_key}")

        # 大分類標籤 (取前3位數字)
        if len(sub_class_key) >= 3:
            large_key = sub_class_key[:3]
            segments.append(f"tm:c_715_pc_{large_key}")

        return segments

    def test_single_product_segments(self):
        """測試單一商品的標籤生成"""
        logger.info("測試單一商品標籤生成...")

        test_cases = [
            {
                "input": "10012",
                "expected": ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"],
                "description": "飲料 > 果汁 > 柳橙汁"
            },
            {
                "input": "10000",
                "expected": ["tm:c_715_pc_10000", "tm:c_715_pc_1000", "tm:c_715_pc_100"],
                "description": "飲料 > 碳酸飲料 > 可樂"
            },
            {
                "input": "31863",
                "expected": ["tm:c_715_pc_31863", "tm:c_715_pc_3186", "tm:c_715_pc_318"],
                "description": "傢俱 > 床墊 > 雙人床墊"
            },
            {
                "input": "22200",
                "expected": ["tm:c_715_pc_22200", "tm:c_715_pc_2220", "tm:c_715_pc_222"],
                "description": "電子商務 > 線上蔬菜 > 福業購物"
            }
        ]

        results = []
        for case in test_cases:
            generated = self.generate_hierarchical_segments(case["input"])
            match = generated == case["expected"]

            results.append({
                "input": case["input"],
                "description": case["description"],
                "expected": case["expected"],
                "generated": generated,
                "match": match,
                "segment_string": ",".join(generated)
            })

            status = "✅" if match else "❌"
            logger.info(f"{status} {case['input']}: {case['description']}")
            if not match:
                logger.error(f"  預期: {case['expected']}")
                logger.error(f"  實際: {generated}")

        self.results["single_product"] = results
        return results

    def test_multi_product_segments(self):
        """測試多商品購買的標籤合併"""
        logger.info("測試多商品標籤合併...")

        # 模擬一個用戶購買多個商品
        purchased_products = ["10012", "10000", "31863", "10012"]  # 包含重複商品

        # 生成所有標籤
        all_segments = set()
        product_details = []

        for product in purchased_products:
            segments = self.generate_hierarchical_segments(product)
            all_segments.update(segments)
            product_details.append({
                "product": product,
                "segments": segments
            })

        # 合併並排序
        merged_segments = sorted(all_segments)
        segment_string = ",".join(merged_segments)

        result = {
            "purchased_products": purchased_products,
            "product_details": product_details,
            "unique_segments": merged_segments,
            "segment_count": len(merged_segments),
            "segment_string": segment_string,
            "deduplication_test": len(purchased_products) != len(set(purchased_products))
        }

        self.results["multi_product"] = result

        logger.info(f"購買商品: {purchased_products}")
        logger.info(f"去重後標籤數: {len(merged_segments)}")
        logger.info(f"標籤字串: {segment_string[:100]}...")

        return result

    def test_edge_cases(self):
        """測試邊界情況"""
        logger.info("測試邊界情況...")

        edge_cases = [
            {
                "input": "1",
                "description": "單位數商品代碼",
                "expected_length": 1  # 只有小分類
            },
            {
                "input": "12",
                "description": "兩位數商品代碼",
                "expected_length": 1  # 只有小分類
            },
            {
                "input": "123",
                "description": "三位數商品代碼",
                "expected_length": 2  # 小分類 + 大分類
            },
            {
                "input": "1234",
                "description": "四位數商品代碼",
                "expected_length": 3  # 小分類 + 中分類 + 大分類
            },
            {
                "input": "12345",
                "description": "五位數商品代碼",
                "expected_length": 3  # 小分類 + 中分類 + 大分類
            }
        ]

        results = []
        for case in edge_cases:
            generated = self.generate_hierarchical_segments(case["input"])
            match = len(generated) == case["expected_length"]

            results.append({
                "input": case["input"],
                "description": case["description"],
                "expected_length": case["expected_length"],
                "generated": generated,
                "actual_length": len(generated),
                "match": match
            })

            status = "✅" if match else "❌"
            logger.info(f"{status} {case['input']} ({case['description']}): {len(generated)} 個標籤")

        self.results["edge_cases"] = results
        return results

    def test_format_validation(self):
        """測試標籤格式驗證"""
        logger.info("測試標籤格式驗證...")

        test_product = "10012"
        segments = self.generate_hierarchical_segments(test_product)

        format_tests = []
        for segment in segments:
            tests = {
                "segment": segment,
                "starts_with_tm": segment.startswith("tm:c_715_pc_"),
                "contains_only_digits_after_prefix": segment.replace("tm:c_715_pc_", "").isdigit(),
                "valid_format": segment.startswith("tm:c_715_pc_") and segment.replace("tm:c_715_pc_", "").isdigit()
            }
            format_tests.append(tests)

        all_valid = all(test["valid_format"] for test in format_tests)

        result = {
            "test_product": test_product,
            "segments": segments,
            "format_tests": format_tests,
            "all_valid": all_valid
        }

        self.results["format_validation"] = result

        status = "✅" if all_valid else "❌"
        logger.info(f"{status} 格式驗證: 所有標籤格式正確")

        return result

    def run_all_tests(self):
        """執行所有測試"""
        logger.info("開始執行受眾標籤生成測試...")

        # 執行各項測試
        self.test_single_product_segments()
        self.test_multi_product_segments()
        self.test_edge_cases()
        self.test_format_validation()

        # 生成測試報告
        self.generate_test_report()

        return self.results

    def generate_test_report(self):
        """生成測試報告"""
        logger.info("生成測試報告...")

        # 計算測試統計
        single_tests = self.results.get("single_product", [])
        edge_tests = self.results.get("edge_cases", [])

        single_passed = sum(1 for test in single_tests if test["match"])
        edge_passed = sum(1 for test in edge_tests if test["match"])
        format_passed = 1 if self.results.get("format_validation", {}).get("all_valid", False) else 0

        total_tests = len(single_tests) + len(edge_tests) + 1  # +1 for format test
        total_passed = single_passed + edge_passed + format_passed

        summary = {
            "total_tests": total_tests,
            "passed_tests": total_passed,
            "failed_tests": total_tests - total_passed,
            "success_rate": total_passed / total_tests if total_tests > 0 else 0,
            "test_breakdown": {
                "single_product": f"{single_passed}/{len(single_tests)}",
                "edge_cases": f"{edge_passed}/{len(edge_tests)}",
                "format_validation": f"{format_passed}/1"
            }
        }

        self.results["test_summary"] = summary

        # 儲存完整報告
        output_path = "reports/segment_generation_test_report.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        logger.info(f"測試報告已儲存至: {output_path}")

        # 輸出摘要
        print(f"\n=== 受眾標籤生成測試摘要 ===")
        print(f"總測試數: {total_tests}")
        print(f"通過測試: {total_passed}")
        print(f"失敗測試: {total_tests - total_passed}")
        print(f"成功率: {summary['success_rate']:.1%}")
        print(f"測試明細: {summary['test_breakdown']}")

def main():
    """主程式"""
    tester = SegmentGenerationTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
