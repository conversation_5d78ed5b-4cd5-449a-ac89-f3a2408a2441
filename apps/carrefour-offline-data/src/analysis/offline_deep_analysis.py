#!/usr/bin/env python3
"""
離線資料深度分析
- 來源: tw-eagle-prod.rmn_tagtoo.offline_transaction_day (跨專案查詢)
- Billing Project: tagtoo-tracking
- 產出: reports/offline_deep_analysis.json
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, Any
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/tools')
from setup_environment import get_gcloud_credentials_path
try:
    from smart_bigquery_client import get_smart_bigquery_client
except ImportError:
    get_smart_bigquery_client = None


def bq_client() -> bigquery.Client:
    if get_smart_bigquery_client:
        return get_smart_bigquery_client()
    else:
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = get_gcloud_credentials_path()
        return bigquery.Client(project="tagtoo-tracking")


def run_query(client: bigquery.Client, query: str, desc: str, max_cost_usd: float = 0.5) -> Dict[str, Any]:
    try:
        cfg = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=cfg)
        bytes_proc = job.total_bytes_processed
        cost = (bytes_proc / (1024**4)) * 5.0  # $5/TB
        if cost > max_cost_usd:
            return {"status": "skipped_high_cost", "desc": desc, "estimated_cost_usd": cost}
        start = datetime.now()
        job = client.query(query)
        rows = list(job.result())
        end = datetime.now()
        data = []
        for r in rows:
            d = {}
            for k, v in r.items():
                d[k] = v.isoformat() if hasattr(v, 'isoformat') else v
            data.append(d)
        return {
            "status": "success",
            "desc": desc,
            "estimated_cost_usd": cost,
            "execution_time_seconds": (end - start).total_seconds(),
            "row_count": len(data),
            "data": data,
        }
    except Exception as e:
        return {"status": "failed", "desc": desc, "error": str(e)}


def main():
    print("📊 離線資料深度分析 (billing project: tagtoo-tracking)")
    client = bq_client()

    report: Dict[str, Any] = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "billing_project": "tagtoo-tracking",
            "source_table": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day",
        }
    }

    # 1) 基礎統計與時間範圍
    q_basic = """
    SELECT
      COUNT(*) AS total_record_count,
      COUNT(DISTINCT ph_id) AS unique_ph_id_count,
      COUNT(ph_id) AS non_null_ph_ids,
      COUNT(*) - COUNT(ph_id) AS null_ph_ids,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 4) AS null_percentage,
      MIN(DATE(TIMESTAMP_SECONDS(event_times))) AS earliest_date,
      MAX(DATE(TIMESTAMP_SECONDS(event_times))) AS latest_date,
      DATE_DIFF(
        MAX(DATE(TIMESTAMP_SECONDS(event_times))),
        MIN(DATE(TIMESTAMP_SECONDS(event_times))),
        DAY
      ) AS date_range_days
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """
    report["basic"] = run_query(client, q_basic, "offline 基礎統計", max_cost_usd=0.1)

    # 2) 每日趨勢 (近 90 天)
    q_daily = """
    SELECT
      DATE(TIMESTAMP_SECONDS(event_times)) AS date,
      COUNT(*) AS total_record_count,
      COUNT(DISTINCT ph_id) AS unique_ph_id_count,
      COUNT(ph_id) AS non_null_ph_ids,
      COUNT(*) - COUNT(ph_id) AS null_ph_ids,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 2) AS null_percentage
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
    GROUP BY date
    ORDER BY date DESC
    """
    report["daily_trend_90d"] = run_query(client, q_daily, "offline 每日趨勢(90d)", max_cost_usd=0.2)

    # 3) 每月趨勢 (近 12 個月)
    q_monthly = """
    SELECT
      FORMAT_DATE('%Y-%m', DATE(TIMESTAMP_SECONDS(event_times))) AS ym,
      COUNT(*) AS total_record_count,
      COUNT(DISTINCT ph_id) AS unique_ph_id_count,
      COUNT(ph_id) AS non_null_ph_ids,
      COUNT(*) - COUNT(ph_id) AS null_ph_ids
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
    GROUP BY ym
    ORDER BY ym DESC
    """
    report["monthly_trend_12m"] = run_query(client, q_monthly, "offline 每月趨勢(12m)", max_cost_usd=0.2)

    # 4) 格式一致性 (ph_id bytes 長度/hex 長度分布)
    q_format = """
    SELECT
      LENGTH(ph_id) AS ph_id_byte_len,
      LENGTH(TO_HEX(ph_id)) AS ph_id_hex_len,
      COUNT(*) AS rows,
      COUNT(DISTINCT ph_id) AS unique_ph_id_count
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE ph_id IS NOT NULL
    GROUP BY ph_id_byte_len, ph_id_hex_len
    ORDER BY rows DESC
    """
    report["format_consistency"] = run_query(client, q_format, "offline 格式一致性", max_cost_usd=0.2)

    # 5) 用戶活躍度分佈 (近 30 天: 每個 ph_id 出現的天數分布 - 取樣避免高成本)
    q_activity = """
    WITH last30 AS (
      SELECT TO_HEX(ph_id) AS ph_hex, DATE(TIMESTAMP_SECONDS(event_times)) AS d
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
      WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
      AND ph_id IS NOT NULL
    ), per_user AS (
      SELECT ph_hex, COUNT(DISTINCT d) AS active_days
      FROM last30
      GROUP BY ph_hex
    )
    SELECT active_days, COUNT(*) AS user_count
    FROM per_user
    GROUP BY active_days
    ORDER BY active_days
    """
    report["user_activity_30d"] = run_query(client, q_activity, "offline 用戶活躍度(30d)", max_cost_usd=0.5)

    out = 'reports/offline_deep_analysis.json'
    with open(out, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    print(f"✅ 已輸出: {out}")


if __name__ == '__main__':
    main()
