#!/usr/bin/env python3
"""
完整欄位資料狀況分析腳本
分析家樂福離線資料中所有欄位的資料品質、分佈和異常狀況
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
from tools.monitoring_logger import get_global_logger

# 使用智能 BigQuery 客戶端
try:
    from tools.smart_bigquery_client import get_smart_bigquery_client
except ImportError:
    # 如果在 src/analysis 目錄執行，使用相對路徑
    sys.path.append('../tools')
    from smart_bigquery_client import get_smart_bigquery_client

def setup_bigquery_client():
    """設定智能 BigQuery 客戶端"""
    try:
        smart_client = get_smart_bigquery_client()
        return smart_client
    except Exception as e:
        print(f"❌ 智能 BigQuery 客戶端設定失敗: {e}")
        return None

def analyze_basic_field_quality(smart_client) -> Dict[str, Any]:
    """分析基本欄位的資料品質"""

    query = """
    SELECT
      COUNT(*) as total_record_count,

      -- event_name 分析
      COUNT(event_name) as event_name_non_null_count,
      COUNT(DISTINCT event_name) as event_name_unique_count,

      -- event_times 分析
      COUNT(event_times) as event_times_non_null_count,
      MIN(event_times) as earliest_event_time,
      MAX(event_times) as latest_event_time,

      -- store_id 分析
      COUNT(store_id) as store_id_non_null_count,
      COUNT(DISTINCT store_id) as store_id_unique_count,

      -- store_name 分析
      COUNT(store_name) as store_name_non_null_count,
      COUNT(DISTINCT store_name) as store_name_unique_count,

      -- gender 分析
      COUNT(gender) as gender_non_null_count,
      COUNT(DISTINCT gender) as gender_unique_count,

      -- member_id 分析
      COUNT(member_id) as member_id_non_null_count,
      COUNT(DISTINCT member_id) as member_id_unique_count,

      -- ph_id 分析
      COUNT(ph_id) as ph_id_non_null_count,
      COUNT(DISTINCT ph_id) as ph_id_unique_count,

      -- transaction_id 分析
      COUNT(transaction_id) as transaction_id_non_null_count,
      COUNT(DISTINCT transaction_id) as transaction_id_unique_count,

      -- total_amount 分析
      COUNT(total_amount) as total_amount_non_null_count,
      MIN(total_amount) as min_total_amount,
      MAX(total_amount) as max_total_amount,
      AVG(total_amount) as avg_total_amount,
      STDDEV(total_amount) as stddev_total_amount,

      -- payment_method 分析
      COUNT(payment_method) as payment_method_non_null_count,
      COUNT(DISTINCT payment_method) as payment_method_unique_count

    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """

    try:
        results = smart_client.query(query).result()
        data = list(results)[0]

        # 轉換為字典並計算百分比
        stats = {}
        for key, value in data.items():
            stats[key] = value

        total = stats['total_record_count']

        # 計算 NULL 百分比
        null_percentages = {}
        for field in ['event_name', 'event_times', 'store_id', 'store_name', 'gender',
                     'member_id', 'ph_id', 'transaction_id', 'total_amount', 'payment_method']:
            non_null_key = f'{field}_non_null_count'
            null_key = f'{field}_null_percentage'
            if non_null_key in stats:
                null_count = total - stats[non_null_key]
                null_percentages[null_key] = round(null_count / total * 100, 3)

        stats.update(null_percentages)

        return {"status": "success", "data": stats}

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_categorical_fields(smart_client) -> Dict[str, Any]:
    """分析分類欄位的值分佈"""

    analyses = {}

    # 1. event_name 分佈
    event_name_query = """
    SELECT
      event_name,
      COUNT(*) as count,
      COUNT(DISTINCT ph_id) as unique_customers
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE event_name IS NOT NULL
    GROUP BY event_name
    ORDER BY count DESC
    """

    try:
        results = smart_client.query(event_name_query).result()
        analyses['event_name_distribution'] = {
            "status": "success",
            "data": [{"value": row.event_name, "count": row.count, "unique_customers": row.unique_customers}
                    for row in results]
        }
    except Exception as e:
        analyses['event_name_distribution'] = {"status": "failed", "error": str(e)}

    # 2. payment_method 分佈
    payment_query = """
    SELECT
      payment_method,
      COUNT(*) as count,
      COUNT(DISTINCT ph_id) as unique_customers,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE payment_method IS NOT NULL
    GROUP BY payment_method
    ORDER BY count DESC
    """

    try:
        results = smart_client.query(payment_query).result()
        analyses['payment_method_distribution'] = {
            "status": "success",
            "data": [{"value": row.payment_method, "count": row.count,
                     "unique_customers": row.unique_customers,
                     "avg_amount": float(row.avg_amount) if row.avg_amount else 0}
                    for row in results]
        }
    except Exception as e:
        analyses['payment_method_distribution'] = {"status": "failed", "error": str(e)}

    return analyses

def analyze_items_field(smart_client) -> Dict[str, Any]:
    """分析 items 欄位的結構和內容"""

    # items 是 RECORD REPEATED 類型，需要特殊處理
    items_query = """
    SELECT
      COUNT(*) as total_transactions,
      COUNT(CASE WHEN ARRAY_LENGTH(items) > 0 THEN 1 END) as transactions_with_items,
      AVG(ARRAY_LENGTH(items)) as avg_items_per_transaction,
      MAX(ARRAY_LENGTH(items)) as max_items_per_transaction,
      SUM(ARRAY_LENGTH(items)) as total_items_count
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """

    try:
        results = smart_client.query(items_query).result()
        data = list(results)[0]

        stats = {
            "total_transactions": data.total_transactions,
            "transactions_with_items": data.transactions_with_items,
            "transactions_without_items": data.total_transactions - data.transactions_with_items,
            "avg_items_per_transaction": float(data.avg_items_per_transaction) if data.avg_items_per_transaction else 0,
            "max_items_per_transaction": data.max_items_per_transaction,
            "total_items_count": data.total_items_count
        }

        # 計算百分比
        total = stats["total_transactions"]
        stats["with_items_percentage"] = round(stats["transactions_with_items"] / total * 100, 2)
        stats["without_items_percentage"] = round(stats["transactions_without_items"] / total * 100, 2)

        return {"status": "success", "data": stats}

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_data_anomalies(smart_client) -> Dict[str, Any]:
    """分析資料異常狀況"""

    anomalies = {}

    # 1. 負數金額
    negative_amount_query = """
    SELECT
      COUNT(*) as negative_amount_count,
      MIN(total_amount) as min_negative_amount,
      AVG(total_amount) as avg_negative_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE total_amount < 0
    """

    try:
        results = smart_client.query(negative_amount_query).result()
        data = list(results)[0]
        anomalies['negative_amounts'] = {
            "status": "success",
            "count": data.negative_amount_count,
            "min_value": float(data.min_negative_amount) if data.min_negative_amount else 0,
            "avg_value": float(data.avg_negative_amount) if data.avg_negative_amount else 0
        }
    except Exception as e:
        anomalies['negative_amounts'] = {"status": "failed", "error": str(e)}

    # 2. 零金額交易
    zero_amount_query = """
    SELECT
      COUNT(*) as zero_amount_count,
      COUNT(DISTINCT ph_id) as unique_customers_with_zero
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE total_amount = 0
    """

    try:
        results = smart_client.query(zero_amount_query).result()
        data = list(results)[0]
        anomalies['zero_amounts'] = {
            "status": "success",
            "count": data.zero_amount_count,
            "unique_customers": data.unique_customers_with_zero
        }
    except Exception as e:
        anomalies['zero_amounts'] = {"status": "failed", "error": str(e)}

    # 3. 異常高金額交易
    high_amount_query = """
    SELECT
      COUNT(*) as high_amount_count,
      MIN(total_amount) as min_high_amount,
      MAX(total_amount) as max_high_amount,
      AVG(total_amount) as avg_high_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE total_amount > 10000
    """

    try:
        results = smart_client.query(high_amount_query).result()
        data = list(results)[0]
        anomalies['high_amounts'] = {
            "status": "success",
            "count": data.high_amount_count,
            "min_value": float(data.min_high_amount) if data.min_high_amount else 0,
            "max_value": float(data.max_high_amount) if data.max_high_amount else 0,
            "avg_value": float(data.avg_high_amount) if data.avg_high_amount else 0
        }
    except Exception as e:
        anomalies['high_amounts'] = {"status": "failed", "error": str(e)}

    # 4. 重複 transaction_id
    duplicate_transaction_query = """
    SELECT
      transaction_id,
      COUNT(*) as duplicate_count
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE transaction_id IS NOT NULL
    GROUP BY transaction_id
    HAVING COUNT(*) > 1
    ORDER BY duplicate_count DESC
    LIMIT 10
    """

    try:
        results = smart_client.query(duplicate_transaction_query).result()
        anomalies['duplicate_transactions'] = {
            "status": "success",
            "data": [{"transaction_id": row.transaction_id, "count": row.duplicate_count}
                    for row in results]
        }
    except Exception as e:
        anomalies['duplicate_transactions'] = {"status": "failed", "error": str(e)}

    return anomalies

def main():
    """主執行函數"""
    logger = get_global_logger()

    print("📊 家樂福離線資料完整欄位分析")
    print("=" * 60)

    # 設定 BigQuery 客戶端
    client = setup_bigquery_client()
    if not client:
        return

    # 執行分析
    report = {
        "metadata": {
            "analysis_type": "comprehensive_field_analysis",
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "description": "家樂福離線資料所有欄位的完整分析"
        }
    }

    print("📋 分析基本欄位品質...")
    report["field_quality"] = analyze_basic_field_quality(client)

    print("📊 分析分類欄位分佈...")
    report["categorical_analysis"] = analyze_categorical_fields(client)

    print("🛒 分析 items 欄位...")
    report["items_analysis"] = analyze_items_field(client)

    print("⚠️ 分析資料異常...")
    report["anomaly_analysis"] = analyze_data_anomalies(client)

    # 儲存報告
    output_file = 'reports/comprehensive_field_analysis.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)

    print(f"✅ 完整欄位分析完成")
    print(f"📁 報告已儲存至: {output_file}")

    # 顯示摘要
    if report["field_quality"]["status"] == "success":
        quality = report["field_quality"]["data"]
        print(f"\n🎯 欄位品質摘要:")
        print(f"   總記錄數: {quality['total_record_count']:,}")
        print(f"   ph_id NULL 比例: {quality['ph_id_null_percentage']}%")
        print(f"   gender NULL 比例: {quality['gender_null_percentage']}%")
        print(f"   payment_method NULL 比例: {quality['payment_method_null_percentage']}%")
        print(f"   唯一 store_id: {quality['store_id_unique_count']}")
        print(f"   唯一 transaction_id: {quality['transaction_id_unique_count']:,}")

if __name__ == "__main__":
    main()
