#!/usr/bin/env python3
"""
Gender 欄位分析腳本
分析家樂福離線資料中的 gender 欄位分佈和相關統計
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
from tools.monitoring_logger import get_global_logger

# 使用智能 BigQuery 客戶端
try:
    from tools.smart_bigquery_client import get_smart_bigquery_client
except ImportError:
    # 如果在 src/analysis 目錄執行，使用相對路徑
    sys.path.append('../tools')
    from smart_bigquery_client import get_smart_bigquery_client

def setup_bigquery_client():
    """設定智能 BigQuery 客戶端"""
    try:
        smart_client = get_smart_bigquery_client()
        return smart_client
    except Exception as e:
        print(f"❌ 智能 BigQuery 客戶端設定失敗: {e}")
        return None

def analyze_gender_distribution(smart_client) -> Dict[str, Any]:
    """分析 gender 欄位分佈"""

    query = """
    SELECT
      gender,
      COUNT(*) as transaction_count,
      COUNT(DISTINCT ph_id) as unique_customer_count,
      COUNT(DISTINCT store_id) as store_count,
      AVG(total_amount) as avg_amount,
      MIN(total_amount) as min_amount,
      MAX(total_amount) as max_amount,
      COUNT(CASE WHEN total_amount > 0 THEN 1 END) as positive_amount_count
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    GROUP BY gender
    ORDER BY transaction_count DESC
    """

    try:
        results = smart_client.query(query).result()

        distribution_data = []
        total_transactions = 0

        for row in results:
            data = {
                "gender": row.gender if row.gender else "NULL",
                "transaction_count": row.transaction_count,
                "unique_customer_count": row.unique_customer_count,
                "store_count": row.store_count,
                "avg_amount": float(row.avg_amount) if row.avg_amount else 0,
                "min_amount": float(row.min_amount) if row.min_amount else 0,
                "max_amount": float(row.max_amount) if row.max_amount else 0,
                "positive_amount_count": row.positive_amount_count
            }
            distribution_data.append(data)
            total_transactions += row.transaction_count

        # 計算百分比
        for data in distribution_data:
            data["transaction_percentage"] = round(data["transaction_count"] / total_transactions * 100, 2)
            data["positive_amount_percentage"] = round(data["positive_amount_count"] / data["transaction_count"] * 100, 2)

        return {
            "status": "success",
            "total_transactions": total_transactions,
            "gender_categories": len(distribution_data),
            "distribution": distribution_data
        }

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_gender_by_store(smart_client) -> Dict[str, Any]:
    """分析各店鋪的 gender 分佈"""

    query = """
    SELECT
      store_id,
      store_name,
      gender,
      COUNT(*) as transaction_count,
      COUNT(DISTINCT ph_id) as unique_customer_count,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE store_id IS NOT NULL
    GROUP BY store_id, store_name, gender
    ORDER BY store_id, transaction_count DESC
    """

    try:
        results = smart_client.query(query).result()

        store_gender_data = {}

        for row in results:
            store_id = row.store_id
            if store_id not in store_gender_data:
                store_gender_data[store_id] = {
                    "store_id": store_id,
                    "store_name": row.store_name,
                    "gender_distribution": [],
                    "total_transactions": 0
                }

            gender_data = {
                "gender": row.gender if row.gender else "NULL",
                "transaction_count": row.transaction_count,
                "unique_customer_count": row.unique_customer_count,
                "avg_amount": float(row.avg_amount) if row.avg_amount else 0
            }

            store_gender_data[store_id]["gender_distribution"].append(gender_data)
            store_gender_data[store_id]["total_transactions"] += row.transaction_count

        # 計算各店鋪內的 gender 百分比
        for store_data in store_gender_data.values():
            total = store_data["total_transactions"]
            for gender_data in store_data["gender_distribution"]:
                gender_data["percentage"] = round(gender_data["transaction_count"] / total * 100, 2)

        # 轉換為列表並按交易量排序
        store_list = list(store_gender_data.values())
        store_list.sort(key=lambda x: x["total_transactions"], reverse=True)

        return {
            "status": "success",
            "total_stores": len(store_list),
            "stores": store_list[:10]  # 只返回前10個店鋪
        }

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_gender_by_time(smart_client) -> Dict[str, Any]:
    """分析 gender 隨時間的變化趨勢"""

    query = """
    SELECT
      FORMAT_DATE('%Y-%m', DATE(TIMESTAMP_SECONDS(event_times))) as month,
      gender,
      COUNT(*) as transaction_count,
      COUNT(DISTINCT ph_id) as unique_customer_count,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE event_times IS NOT NULL
    GROUP BY month, gender
    ORDER BY month DESC, transaction_count DESC
    LIMIT 100
    """

    try:
        results = smart_client.query(query).result()

        monthly_data = {}

        for row in results:
            month = row.month
            if month not in monthly_data:
                monthly_data[month] = {
                    "month": month,
                    "gender_distribution": [],
                    "total_transactions": 0
                }

            gender_data = {
                "gender": row.gender if row.gender else "NULL",
                "transaction_count": row.transaction_count,
                "unique_customer_count": row.unique_customer_count,
                "avg_amount": float(row.avg_amount) if row.avg_amount else 0
            }

            monthly_data[month]["gender_distribution"].append(gender_data)
            monthly_data[month]["total_transactions"] += row.transaction_count

        # 計算百分比
        for month_data in monthly_data.values():
            total = month_data["total_transactions"]
            for gender_data in month_data["gender_distribution"]:
                gender_data["percentage"] = round(gender_data["transaction_count"] / total * 100, 2)

        # 轉換為列表並按月份排序
        monthly_list = list(monthly_data.values())
        monthly_list.sort(key=lambda x: x["month"], reverse=True)

        return {
            "status": "success",
            "total_months": len(monthly_list),
            "monthly_trends": monthly_list[:12]  # 只返回最近12個月
        }

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_gender_purchase_patterns(smart_client) -> Dict[str, Any]:
    """分析不同 gender 的購買模式"""

    query = """
    SELECT
      gender,
      COUNT(*) as total_transactions,
      COUNT(DISTINCT ph_id) as unique_customers,
      AVG(total_amount) as avg_transaction_amount,
      STDDEV(total_amount) as stddev_amount,
      APPROX_QUANTILES(total_amount, 2)[OFFSET(1)] as median_amount,
      COUNT(*) / COUNT(DISTINCT ph_id) as avg_transactions_per_customer,
      COUNT(CASE WHEN total_amount > 1000 THEN 1 END) as high_value_transactions,
      COUNT(CASE WHEN total_amount = 0 THEN 1 END) as zero_amount_transactions
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE total_amount IS NOT NULL
    GROUP BY gender
    ORDER BY total_transactions DESC
    """

    try:
        results = smart_client.query(query).result()

        pattern_data = []

        for row in results:
            data = {
                "gender": row.gender if row.gender else "NULL",
                "total_transactions": row.total_transactions,
                "unique_customers": row.unique_customers,
                "avg_transaction_amount": float(row.avg_transaction_amount) if row.avg_transaction_amount else 0,
                "stddev_amount": float(row.stddev_amount) if row.stddev_amount else 0,
                "median_amount": float(row.median_amount) if row.median_amount else 0,
                "avg_transactions_per_customer": float(row.avg_transactions_per_customer) if row.avg_transactions_per_customer else 0,
                "high_value_transactions": row.high_value_transactions,
                "zero_amount_transactions": row.zero_amount_transactions
            }

            # 計算百分比
            total_trans = data["total_transactions"]
            data["high_value_percentage"] = round(data["high_value_transactions"] / total_trans * 100, 2)
            data["zero_amount_percentage"] = round(data["zero_amount_transactions"] / total_trans * 100, 2)

            pattern_data.append(data)

        return {
            "status": "success",
            "patterns": pattern_data
        }

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def main():
    """主執行函數"""
    logger = get_global_logger()

    print("🚻 家樂福離線資料 Gender 分析")
    print("=" * 60)

    # 設定 BigQuery 客戶端
    client = setup_bigquery_client()
    if not client:
        return

    # 執行分析
    report = {
        "metadata": {
            "analysis_type": "gender_analysis",
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "description": "家樂福離線資料 Gender 欄位分析"
        }
    }

    print("📊 分析 gender 分佈...")
    report["gender_distribution"] = analyze_gender_distribution(client)

    print("🏪 分析各店鋪 gender 分佈...")
    report["gender_by_store"] = analyze_gender_by_store(client)

    print("📅 分析 gender 時間趨勢...")
    report["gender_by_time"] = analyze_gender_by_time(client)

    print("🛒 分析 gender 購買模式...")
    report["purchase_patterns"] = analyze_gender_purchase_patterns(client)

    # 儲存報告
    output_file = 'reports/gender_analysis.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)

    print(f"✅ Gender 分析完成")
    print(f"📁 報告已儲存至: {output_file}")

    # 顯示摘要
    if report["gender_distribution"]["status"] == "success":
        dist = report["gender_distribution"]
        print(f"\n🎯 Gender 分佈摘要:")
        print(f"   總交易數: {dist['total_transactions']:,}")
        print(f"   Gender 類別數: {dist['gender_categories']}")
        for gender_data in dist["distribution"][:5]:
            print(f"   {gender_data['gender']}: {gender_data['transaction_count']:,} 筆 ({gender_data['transaction_percentage']}%)")

if __name__ == "__main__":
    main()
