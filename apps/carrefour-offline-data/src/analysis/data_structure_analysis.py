#!/usr/bin/env python3
"""
資料結構分析腳本
分析家樂福離線資料的完整欄位結構，包含 gender 欄位探索和所有欄位的資料狀況
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/utils')
from tools.monitoring_logger import get_global_logger
from smart_client import smart_client

def setup_bigquery_client():
    """設定 BigQuery 客戶端"""
    try:
        # 使用 smart_client 進行權限切換
        return smart_client()
    except Exception as e:
        print(f"❌ BigQuery 客戶端設定失敗: {e}")
        return None

def analyze_table_schema(client: bigquery.Client) -> Dict[str, Any]:
    """分析表格 schema"""
    try:
        table_ref = client.get_table('tw-eagle-prod.rmn_tagtoo.offline_transaction_day')

        schema_info = {
            "table_name": "offline_transaction_day",
            "total_fields": len(table_ref.schema),
            "fields": []
        }

        for field in table_ref.schema:
            field_info = {
                "name": field.name,
                "type": field.field_type,
                "mode": field.mode,
                "description": field.description or ""
            }
            schema_info["fields"].append(field_info)

        return {"status": "success", "data": schema_info}

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def check_gender_fields(client: bigquery.Client) -> Dict[str, Any]:
    """檢查 gender 相關欄位"""

    # 1. 檢查直接的 gender 欄位
    schema_query = """
    SELECT
      COLUMN_NAME,
      DATA_TYPE,
      IS_NULLABLE
    FROM `tw-eagle-prod.rmn_tagtoo.INFORMATION_SCHEMA.COLUMNS`
    WHERE TABLE_NAME = 'offline_transaction_day'
      AND (LOWER(COLUMN_NAME) LIKE '%gender%'
           OR LOWER(COLUMN_NAME) LIKE '%sex%'
           OR LOWER(COLUMN_NAME) LIKE '%性別%')
    ORDER BY COLUMN_NAME
    """

    try:
        schema_results = client.query(schema_query).result()
        direct_fields = [{"name": row.COLUMN_NAME, "type": row.DATA_TYPE, "nullable": row.IS_NULLABLE}
                        for row in schema_results]

        # 2. 檢查 items 欄位中的 gender 資訊
        items_query = """
        SELECT
          items,
          COUNT(*) as record_count
        FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
        WHERE items IS NOT NULL
          AND items != ''
          AND (LOWER(items) LIKE '%gender%'
               OR LOWER(items) LIKE '%sex%'
               OR LOWER(items) LIKE '%性別%'
               OR LOWER(items) LIKE '%male%'
               OR LOWER(items) LIKE '%female%')
        GROUP BY items
        ORDER BY record_count DESC
        LIMIT 10
        """

        items_results = client.query(items_query).result()
        items_samples = [{"items": row.items[:500], "count": row.record_count}
                        for row in items_results]

        return {
            "status": "success",
            "direct_gender_fields": direct_fields,
            "items_gender_samples": items_samples,
            "has_direct_gender": len(direct_fields) > 0,
            "has_items_gender": len(items_samples) > 0
        }

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_all_fields_data_quality(client: bigquery.Client) -> Dict[str, Any]:
    """分析所有欄位的資料品質狀況"""

    # 基本統計查詢
    basic_stats_query = """
    SELECT
      COUNT(*) as total_record_count,

      -- ph_id 分析
      COUNT(ph_id) as ph_id_non_null_count,
      COUNT(DISTINCT ph_id) as ph_id_unique_count,

      -- transaction_id 分析
      COUNT(transaction_id) as transaction_id_non_null_count,
      COUNT(DISTINCT transaction_id) as transaction_id_unique_count,

      -- event_times 分析
      COUNT(event_times) as event_times_non_null_count,
      MIN(event_times) as earliest_event_time,
      MAX(event_times) as latest_event_time,

      -- items 分析
      COUNT(items) as items_non_null_count,
      COUNT(CASE WHEN items != '' THEN 1 END) as items_non_empty_count,

      -- total_amount 分析
      COUNT(total_amount) as total_amount_non_null_count,
      MIN(total_amount) as min_total_amount,
      MAX(total_amount) as max_total_amount,
      AVG(total_amount) as avg_total_amount,

      -- store_id 分析
      COUNT(store_id) as store_id_non_null_count,
      COUNT(DISTINCT store_id) as store_id_unique_count

    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """

    try:
        basic_results = client.query(basic_stats_query).result()
        basic_data = list(basic_results)[0]

        # 轉換為字典格式
        stats = {}
        for key, value in basic_data.items():
            stats[key] = value

        # 計算百分比
        total = stats['total_record_count']
        stats['ph_id_null_percentage'] = round((total - stats['ph_id_non_null_count']) / total * 100, 3)
        stats['transaction_id_null_percentage'] = round((total - stats['transaction_id_non_null_count']) / total * 100, 3)
        stats['items_null_percentage'] = round((total - stats['items_non_null_count']) / total * 100, 3)
        stats['items_empty_percentage'] = round((stats['items_non_null_count'] - stats['items_non_empty_count']) / total * 100, 3)
        stats['total_amount_null_percentage'] = round((total - stats['total_amount_non_null_count']) / total * 100, 3)
        stats['store_id_null_percentage'] = round((total - stats['store_id_non_null_count']) / total * 100, 3)

        return {"status": "success", "data": stats}

    except Exception as e:
        return {"status": "failed", "error": str(e)}

def analyze_field_value_distributions(client: bigquery.Client) -> Dict[str, Any]:
    """分析各欄位值的分佈情況"""

    analyses = {}

    # 1. store_id 分佈
    store_query = """
    SELECT
      store_id,
      COUNT(*) as transaction_count,
      COUNT(DISTINCT ph_id) as unique_customer_count,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE store_id IS NOT NULL
    GROUP BY store_id
    ORDER BY transaction_count DESC
    LIMIT 20
    """

    try:
        store_results = client.query(store_query).result()
        analyses['store_distribution'] = {
            "status": "success",
            "data": [{"store_id": row.store_id,
                     "transaction_count": row.transaction_count,
                     "unique_customer_count": row.unique_customer_count,
                     "avg_amount": float(row.avg_amount) if row.avg_amount else 0}
                    for row in store_results]
        }
    except Exception as e:
        analyses['store_distribution'] = {"status": "failed", "error": str(e)}

    # 2. total_amount 分佈
    amount_query = """
    SELECT
      CASE
        WHEN total_amount < 0 THEN 'negative'
        WHEN total_amount = 0 THEN 'zero'
        WHEN total_amount <= 100 THEN '1-100'
        WHEN total_amount <= 500 THEN '101-500'
        WHEN total_amount <= 1000 THEN '501-1000'
        WHEN total_amount <= 5000 THEN '1001-5000'
        ELSE '5000+'
      END as amount_range,
      COUNT(*) as transaction_count,
      MIN(total_amount) as min_amount,
      MAX(total_amount) as max_amount,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE total_amount IS NOT NULL
    GROUP BY amount_range
    ORDER BY
      CASE amount_range
        WHEN 'negative' THEN 1
        WHEN 'zero' THEN 2
        WHEN '1-100' THEN 3
        WHEN '101-500' THEN 4
        WHEN '501-1000' THEN 5
        WHEN '1001-5000' THEN 6
        WHEN '5000+' THEN 7
      END
    """

    try:
        amount_results = client.query(amount_query).result()
        analyses['amount_distribution'] = {
            "status": "success",
            "data": [{"range": row.amount_range,
                     "transaction_count": row.transaction_count,
                     "min_amount": float(row.min_amount) if row.min_amount else 0,
                     "max_amount": float(row.max_amount) if row.max_amount else 0,
                     "avg_amount": float(row.avg_amount) if row.avg_amount else 0}
                    for row in amount_results]
        }
    except Exception as e:
        analyses['amount_distribution'] = {"status": "failed", "error": str(e)}

    # 3. 時間分佈（按月）
    time_query = """
    SELECT
      FORMAT_DATE('%Y-%m', DATE(TIMESTAMP_SECONDS(event_times))) as month,
      COUNT(*) as transaction_count,
      COUNT(DISTINCT ph_id) as unique_customer_count,
      AVG(total_amount) as avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE event_times IS NOT NULL
    GROUP BY month
    ORDER BY month DESC
    LIMIT 12
    """

    try:
        time_results = client.query(time_query).result()
        analyses['time_distribution'] = {
            "status": "success",
            "data": [{"month": row.month,
                     "transaction_count": row.transaction_count,
                     "unique_customer_count": row.unique_customer_count,
                     "avg_amount": float(row.avg_amount) if row.avg_amount else 0}
                    for row in time_results]
        }
    except Exception as e:
        analyses['time_distribution'] = {"status": "failed", "error": str(e)}

    return analyses

def main():
    """主執行函數"""
    logger = get_global_logger()

    print("📊 家樂福離線資料結構分析")
    print("=" * 60)

    # 設定 BigQuery 客戶端
    client = setup_bigquery_client()
    if not client:
        return

    # 執行分析
    report = {
        "metadata": {
            "analysis_type": "data_structure_analysis",
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "description": "家樂福離線資料結構和欄位分析"
        }
    }

    print("🔍 分析表格 schema...")
    report["table_schema"] = analyze_table_schema(client)

    print("🔍 檢查 gender 相關欄位...")
    report["gender_analysis"] = check_gender_fields(client)

    print("📊 分析所有欄位資料品質...")
    report["data_quality"] = analyze_all_fields_data_quality(client)

    print("📈 分析欄位值分佈...")
    report["value_distributions"] = analyze_field_value_distributions(client)

    # 儲存報告
    output_file = 'reports/data_structure_analysis.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)

    print(f"✅ 資料結構分析完成")
    print(f"📁 報告已儲存至: {output_file}")

    # 顯示摘要
    if report["data_quality"]["status"] == "success":
        data = report["data_quality"]["data"]
        print(f"\n🎯 關鍵發現:")
        print(f"   總記錄數: {data['total_record_count']:,}")
        print(f"   唯一 ph_id: {data['ph_id_unique_count']:,}")
        print(f"   唯一 transaction_id: {data['transaction_id_unique_count']:,}")
        print(f"   唯一 store_id: {data['store_id_unique_count']:,}")
        print(f"   ph_id NULL 比例: {data['ph_id_null_percentage']}%")
        print(f"   items 空值比例: {data['items_null_percentage']}%")

    if report["gender_analysis"]["status"] == "success":
        gender = report["gender_analysis"]
        print(f"\n🚻 Gender 分析:")
        print(f"   直接 gender 欄位: {'有' if gender['has_direct_gender'] else '無'}")
        print(f"   items 中包含 gender: {'有' if gender['has_items_gender'] else '無'}")

if __name__ == "__main__":
    main()
