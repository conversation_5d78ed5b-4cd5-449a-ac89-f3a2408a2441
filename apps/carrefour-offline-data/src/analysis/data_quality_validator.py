#!/usr/bin/env python3
"""
家樂福受眾資料品質驗證器

驗證生成的受眾標籤資料的品質和完整性

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import json
import re
from typing import List, Dict, Set, Tuple
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataQualityValidator:
    """資料品質驗證器"""

    def __init__(self):
        self.validation_results = {}
        self.quality_metrics = {}

    def validate_audience_data(self, audience_data: List[Dict]) -> Dict:
        """
        驗證受眾資料品質

        Args:
            audience_data: 受眾標籤資料列表

        Returns:
            驗證結果
        """
        logger.info("開始驗證受眾資料品質...")

        if not audience_data:
            logger.error("沒有受眾資料可驗證")
            return {"error": "No data to validate"}

        # 執行各項驗證
        self.validate_permanent_field(audience_data)
        self.validate_segment_id_field(audience_data)
        self.validate_timestamp_fields(audience_data)
        self.validate_metadata_consistency(audience_data)
        self.calculate_quality_metrics(audience_data)

        # 生成總體評分
        self.calculate_overall_score()

        return self.validation_results

    def validate_permanent_field(self, audience_data: List[Dict]):
        """驗證 permanent 欄位"""
        logger.info("驗證 permanent 欄位...")

        permanent_validation = {
            "total_records": len(audience_data),
            "valid_permanent": 0,
            "invalid_permanent": 0,
            "duplicate_permanent": 0,
            "empty_permanent": 0,
            "format_issues": []
        }

        permanent_set = set()
        permanent_pattern = re.compile(r'^[a-f0-9]{32}$')  # 32字元16進位字串

        for i, data in enumerate(audience_data):
            permanent = data.get("permanent", "")

            # 檢查是否為空
            if not permanent:
                permanent_validation["empty_permanent"] += 1
                permanent_validation["format_issues"].append({
                    "record_index": i,
                    "issue": "empty_permanent",
                    "value": permanent
                })
                continue

            # 檢查格式
            if not permanent_pattern.match(permanent):
                permanent_validation["invalid_permanent"] += 1
                permanent_validation["format_issues"].append({
                    "record_index": i,
                    "issue": "invalid_format",
                    "value": permanent,
                    "expected": "32-character hexadecimal string"
                })
                continue

            # 檢查重複
            if permanent in permanent_set:
                permanent_validation["duplicate_permanent"] += 1
                permanent_validation["format_issues"].append({
                    "record_index": i,
                    "issue": "duplicate_permanent",
                    "value": permanent
                })
            else:
                permanent_set.add(permanent)
                permanent_validation["valid_permanent"] += 1

        # 計算品質指標
        total = permanent_validation["total_records"]
        permanent_validation["quality_score"] = permanent_validation["valid_permanent"] / total if total > 0 else 0
        permanent_validation["uniqueness_rate"] = len(permanent_set) / total if total > 0 else 0

        self.validation_results["permanent_validation"] = permanent_validation

    def validate_segment_id_field(self, audience_data: List[Dict]):
        """驗證 segment_id 欄位"""
        logger.info("驗證 segment_id 欄位...")

        segment_validation = {
            "total_records": len(audience_data),
            "valid_segments": 0,
            "invalid_segments": 0,
            "empty_segments": 0,
            "format_issues": [],
            "segment_statistics": {
                "min_segments_per_user": float('inf'),
                "max_segments_per_user": 0,
                "avg_segments_per_user": 0,
                "total_unique_segments": set()
            }
        }

        segment_pattern = re.compile(r'^tm:c_715_pc_\d+$')  # segment 格式
        total_segments = 0

        for i, data in enumerate(audience_data):
            segment_id = data.get("segment_id", "")

            # 檢查是否為空
            if not segment_id:
                segment_validation["empty_segments"] += 1
                segment_validation["format_issues"].append({
                    "record_index": i,
                    "issue": "empty_segment_id",
                    "value": segment_id
                })
                continue

            # 分割標籤
            segments = [s.strip() for s in segment_id.split(",")]
            segment_count = len(segments)
            total_segments += segment_count

            # 更新統計
            stats = segment_validation["segment_statistics"]
            stats["min_segments_per_user"] = min(stats["min_segments_per_user"], segment_count)
            stats["max_segments_per_user"] = max(stats["max_segments_per_user"], segment_count)
            stats["total_unique_segments"].update(segments)

            # 驗證每個標籤格式
            valid_segments = 0
            for j, segment in enumerate(segments):
                if segment_pattern.match(segment):
                    valid_segments += 1
                else:
                    segment_validation["format_issues"].append({
                        "record_index": i,
                        "segment_index": j,
                        "issue": "invalid_segment_format",
                        "value": segment,
                        "expected": "tm:c_715_pc_XXXXX format"
                    })

            # 判斷整體有效性
            if valid_segments == segment_count:
                segment_validation["valid_segments"] += 1
            else:
                segment_validation["invalid_segments"] += 1

        # 計算統計指標
        total = segment_validation["total_records"]
        stats = segment_validation["segment_statistics"]

        if stats["min_segments_per_user"] == float('inf'):
            stats["min_segments_per_user"] = 0

        stats["avg_segments_per_user"] = total_segments / total if total > 0 else 0
        stats["total_unique_segments"] = len(stats["total_unique_segments"])

        segment_validation["quality_score"] = segment_validation["valid_segments"] / total if total > 0 else 0

        self.validation_results["segment_validation"] = segment_validation

    def validate_timestamp_fields(self, audience_data: List[Dict]):
        """驗證時間戳欄位"""
        logger.info("驗證時間戳欄位...")

        timestamp_validation = {
            "total_records": len(audience_data),
            "valid_timestamps": 0,
            "invalid_timestamps": 0,
            "format_issues": []
        }

        for i, data in enumerate(audience_data):
            created_at = data.get("created_at")

            try:
                # 嘗試解析時間戳
                if isinstance(created_at, str):
                    datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                elif isinstance(created_at, datetime):
                    pass  # 已經是 datetime 物件
                else:
                    raise ValueError("Invalid timestamp type")

                timestamp_validation["valid_timestamps"] += 1

            except (ValueError, TypeError) as e:
                timestamp_validation["invalid_timestamps"] += 1
                timestamp_validation["format_issues"].append({
                    "record_index": i,
                    "issue": "invalid_timestamp",
                    "value": str(created_at),
                    "error": str(e)
                })

        total = timestamp_validation["total_records"]
        timestamp_validation["quality_score"] = timestamp_validation["valid_timestamps"] / total if total > 0 else 0

        self.validation_results["timestamp_validation"] = timestamp_validation

    def validate_metadata_consistency(self, audience_data: List[Dict]):
        """驗證元資料一致性"""
        logger.info("驗證元資料一致性...")

        metadata_validation = {
            "total_records": len(audience_data),
            "consistent_source_type": 0,
            "consistent_source_entity": 0,
            "consistent_execution_id": 0,
            "source_types": set(),
            "source_entities": set(),
            "execution_ids": set()
        }

        for data in audience_data:
            source_type = data.get("source_type", "")
            source_entity = data.get("source_entity", "")
            execution_id = data.get("execution_id", "")

            metadata_validation["source_types"].add(source_type)
            metadata_validation["source_entities"].add(source_entity)
            metadata_validation["execution_ids"].add(execution_id)

            # 檢查是否符合預期值
            if source_type == "carrefour-offline-purchase":
                metadata_validation["consistent_source_type"] += 1

            if source_entity == "carrefour-audience-matching":
                metadata_validation["consistent_source_entity"] += 1

            if execution_id.startswith("carrefour_"):
                metadata_validation["consistent_execution_id"] += 1

        # 轉換 set 為 list 以便 JSON 序列化
        metadata_validation["source_types"] = list(metadata_validation["source_types"])
        metadata_validation["source_entities"] = list(metadata_validation["source_entities"])
        metadata_validation["execution_ids"] = list(metadata_validation["execution_ids"])

        total = metadata_validation["total_records"]
        metadata_validation["consistency_score"] = (
            metadata_validation["consistent_source_type"] +
            metadata_validation["consistent_source_entity"] +
            metadata_validation["consistent_execution_id"]
        ) / (3 * total) if total > 0 else 0

        self.validation_results["metadata_validation"] = metadata_validation

    def calculate_quality_metrics(self, audience_data: List[Dict]):
        """計算整體品質指標"""
        logger.info("計算品質指標...")

        self.quality_metrics = {
            "total_records": len(audience_data),
            "data_completeness": self._calculate_completeness(audience_data),
            "data_accuracy": self._calculate_accuracy(),
            "data_consistency": self._calculate_consistency(),
            "data_validity": self._calculate_validity()
        }

    def _calculate_completeness(self, audience_data: List[Dict]) -> float:
        """計算資料完整性"""
        required_fields = ["permanent", "segment_id", "created_at", "source_type", "source_entity", "execution_id"]
        total_fields = len(audience_data) * len(required_fields)
        complete_fields = 0

        for data in audience_data:
            for field in required_fields:
                if data.get(field):
                    complete_fields += 1

        return complete_fields / total_fields if total_fields > 0 else 0

    def _calculate_accuracy(self) -> float:
        """計算資料準確性"""
        permanent_score = self.validation_results.get("permanent_validation", {}).get("quality_score", 0)
        segment_score = self.validation_results.get("segment_validation", {}).get("quality_score", 0)
        timestamp_score = self.validation_results.get("timestamp_validation", {}).get("quality_score", 0)

        return (permanent_score + segment_score + timestamp_score) / 3

    def _calculate_consistency(self) -> float:
        """計算資料一致性"""
        return self.validation_results.get("metadata_validation", {}).get("consistency_score", 0)

    def _calculate_validity(self) -> float:
        """計算資料有效性"""
        # 基於格式驗證的綜合評分
        permanent_score = self.validation_results.get("permanent_validation", {}).get("quality_score", 0)
        segment_score = self.validation_results.get("segment_validation", {}).get("quality_score", 0)

        return (permanent_score + segment_score) / 2

    def calculate_overall_score(self):
        """計算總體品質評分"""
        metrics = self.quality_metrics

        overall_score = (
            metrics.get("data_completeness", 0) * 0.25 +
            metrics.get("data_accuracy", 0) * 0.35 +
            metrics.get("data_consistency", 0) * 0.20 +
            metrics.get("data_validity", 0) * 0.20
        )

        self.quality_metrics["overall_score"] = overall_score

        # 評級
        if overall_score >= 0.9:
            grade = "A (優秀)"
        elif overall_score >= 0.8:
            grade = "B (良好)"
        elif overall_score >= 0.7:
            grade = "C (一般)"
        elif overall_score >= 0.6:
            grade = "D (需改進)"
        else:
            grade = "F (不合格)"

        self.quality_metrics["grade"] = grade

    def save_validation_report(self, output_path: str) -> bool:
        """儲存驗證報告"""
        try:
            report = {
                "metadata": {
                    "validation_type": "carrefour_audience_data_quality",
                    "generated_at": datetime.now().isoformat(),
                    "validator_version": "1.0.0"
                },
                "validation_results": self.validation_results,
                "quality_metrics": self.quality_metrics
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"驗證報告已儲存至: {output_path}")
            return True

        except Exception as e:
            logger.error(f"儲存驗證報告失敗: {e}")
            return False

    def print_summary(self):
        """輸出驗證摘要"""
        print("\n=== 資料品質驗證摘要 ===")

        metrics = self.quality_metrics
        print(f"總記錄數: {metrics.get('total_records', 0)}")
        print(f"資料完整性: {metrics.get('data_completeness', 0):.1%}")
        print(f"資料準確性: {metrics.get('data_accuracy', 0):.1%}")
        print(f"資料一致性: {metrics.get('data_consistency', 0):.1%}")
        print(f"資料有效性: {metrics.get('data_validity', 0):.1%}")
        print(f"總體評分: {metrics.get('overall_score', 0):.1%}")
        print(f"品質等級: {metrics.get('grade', 'N/A')}")

        # 顯示主要問題
        permanent_issues = len(self.validation_results.get("permanent_validation", {}).get("format_issues", []))
        segment_issues = len(self.validation_results.get("segment_validation", {}).get("format_issues", []))
        timestamp_issues = len(self.validation_results.get("timestamp_validation", {}).get("format_issues", []))

        if permanent_issues + segment_issues + timestamp_issues > 0:
            print(f"\n發現問題:")
            if permanent_issues > 0:
                print(f"  - Permanent 欄位問題: {permanent_issues} 個")
            if segment_issues > 0:
                print(f"  - Segment ID 欄位問題: {segment_issues} 個")
            if timestamp_issues > 0:
                print(f"  - 時間戳欄位問題: {timestamp_issues} 個")

def main():
    """主程式 - 載入測試資料並驗證"""
    # 載入最新的測試結果
    test_file = "reports/carrefour_audience_matching_test.json"

    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)

        audience_data = test_data.get("audience_data_sample", [])

        if not audience_data:
            logger.error("測試檔案中沒有受眾資料")
            return

        # 執行驗證
        validator = DataQualityValidator()
        validator.validate_audience_data(audience_data)

        # 儲存報告
        output_path = "reports/data_quality_validation_report.json"
        validator.save_validation_report(output_path)

        # 輸出摘要
        validator.print_summary()

    except FileNotFoundError:
        logger.error(f"找不到測試檔案: {test_file}")
    except Exception as e:
        logger.error(f"驗證過程發生錯誤: {e}")

if __name__ == "__main__":
    main()
