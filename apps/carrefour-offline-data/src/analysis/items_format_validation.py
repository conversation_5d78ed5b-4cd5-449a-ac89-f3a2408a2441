#!/usr/bin/env python3
"""
離線資料 items 格式驗證
分析 tw-eagle-prod.rmn_tagtoo.offline_transaction_day 表中的 items 欄位
驗證電商交易項目格式的完整性和資料品質
"""

import os
import sys
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any, Optional
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/tools')
sys.path.append('src/utils')
from setup_environment import get_gcloud_credentials_path
from smart_client import smart_client


def setup_bigquery_client() -> bigquery.Client:
    """設定 BigQuery 客戶端"""
    return smart_client()


def run_query_with_cost_control(client: bigquery.Client, query: str, desc: str, max_cost_usd: float = 0.1) -> Dict[str, Any]:
    """執行查詢並控制成本"""
    try:
        # 成本估算
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=job_config)
        bytes_processed = job.total_bytes_processed
        cost_usd = (bytes_processed / (1024**4)) * 5.0  # $5/TB

        if cost_usd > max_cost_usd:
            return {
                "status": "skipped_high_cost",
                "desc": desc,
                "estimated_cost_usd": cost_usd,
                "max_cost_usd": max_cost_usd
            }

        # 執行查詢
        start_time = datetime.now()
        job = client.query(query)
        results = job.result()
        end_time = datetime.now()

        data = []
        for row in results:
            row_dict = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):
                    row_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    row_dict[key] = float(value)
                else:
                    row_dict[key] = value
            data.append(row_dict)

        return {
            "status": "success",
            "desc": desc,
            "estimated_cost_usd": cost_usd,
            "execution_time_seconds": (end_time - start_time).total_seconds(),
            "row_count": len(data),
            "data": data
        }

    except Exception as e:
        return {
            "status": "failed",
            "desc": desc,
            "error": str(e)
        }


def analyze_items_basic_structure(client: bigquery.Client) -> Dict[str, Any]:
    """分析 items 欄位的基本結構"""
    print('📊 分析 items 欄位基本結構...')

    query = """
    SELECT
      COUNT(*) as total_records,
      COUNT(items) as non_null_items,
      COUNT(*) - COUNT(items) as null_items,
      ROUND((COUNT(*) - COUNT(items)) * 100.0 / COUNT(*), 4) as null_percentage,
      COUNT(DISTINCT ARRAY_LENGTH(items)) as unique_array_lengths,
      MIN(ARRAY_LENGTH(items)) as min_items_count,
      MAX(ARRAY_LENGTH(items)) as max_items_count,
      AVG(ARRAY_LENGTH(items)) as avg_items_count
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """

    return run_query_with_cost_control(client, query, "items 基本結構分析", max_cost_usd=0.1)


def analyze_items_field_completeness(client: bigquery.Client) -> Dict[str, Any]:
    """分析 items 內各欄位的完整性"""
    print('🔍 分析 items 內各欄位完整性...')

    query = """
    WITH items_flattened AS (
      SELECT
        item.item_id,
        item.item_name,
        item.GRP_CLASS_KEY,
        item.GRP_CLASS_DESC,
        item.CLASS_KEY,
        item.CLASS_DESC,
        item.SUB_CLASS_KEY,
        item.SUB_CLASS_DESC,
        item.quantity,
        item.unit_price,
        item.subtotal
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
      UNNEST(items) as item
      WHERE items IS NOT NULL
      LIMIT 10000  -- 限制樣本數量控制成本
    )
    SELECT
      COUNT(*) as total_items,

      -- 各欄位非 NULL 統計
      COUNT(item_id) as item_id_non_null,
      COUNT(item_name) as item_name_non_null,
      COUNT(GRP_CLASS_KEY) as grp_class_key_non_null,
      COUNT(GRP_CLASS_DESC) as grp_class_desc_non_null,
      COUNT(CLASS_KEY) as class_key_non_null,
      COUNT(CLASS_DESC) as class_desc_non_null,
      COUNT(SUB_CLASS_KEY) as sub_class_key_non_null,
      COUNT(SUB_CLASS_DESC) as sub_class_desc_non_null,
      COUNT(quantity) as quantity_non_null,
      COUNT(unit_price) as unit_price_non_null,
      COUNT(subtotal) as subtotal_non_null,

      -- 各欄位完整性百分比
      ROUND(COUNT(item_id) * 100.0 / COUNT(*), 2) as item_id_completeness_pct,
      ROUND(COUNT(item_name) * 100.0 / COUNT(*), 2) as item_name_completeness_pct,
      ROUND(COUNT(quantity) * 100.0 / COUNT(*), 2) as quantity_completeness_pct,
      ROUND(COUNT(unit_price) * 100.0 / COUNT(*), 2) as unit_price_completeness_pct,
      ROUND(COUNT(subtotal) * 100.0 / COUNT(*), 2) as subtotal_completeness_pct
    FROM items_flattened
    """

    return run_query_with_cost_control(client, query, "items 欄位完整性分析", max_cost_usd=0.1)


def analyze_items_data_types_and_ranges(client: bigquery.Client) -> Dict[str, Any]:
    """分析 items 內數值欄位的資料類型和範圍"""
    print('📈 分析 items 數值欄位資料類型和範圍...')

    query = """
    WITH items_flattened AS (
      SELECT
        item.item_id,
        item.quantity,
        item.unit_price,
        item.subtotal
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
      UNNEST(items) as item
      WHERE items IS NOT NULL
      LIMIT 5000  -- 限制樣本數量
    )
    SELECT
      -- quantity 分析
      MIN(quantity) as min_quantity,
      MAX(quantity) as max_quantity,
      AVG(quantity) as avg_quantity,
      COUNT(DISTINCT quantity) as unique_quantity_values,

      -- unit_price 分析
      MIN(unit_price) as min_unit_price,
      MAX(unit_price) as max_unit_price,
      AVG(unit_price) as avg_unit_price,

      -- subtotal 分析
      MIN(subtotal) as min_subtotal,
      MAX(subtotal) as max_subtotal,
      AVG(subtotal) as avg_subtotal,

      -- 資料品質檢查
      COUNT(CASE WHEN quantity <= 0 THEN 1 END) as invalid_quantity_count,
      COUNT(CASE WHEN unit_price <= 0 THEN 1 END) as invalid_unit_price_count,
      COUNT(CASE WHEN subtotal <= 0 THEN 1 END) as invalid_subtotal_count,

      -- 計算一致性檢查 (subtotal ≈ quantity * unit_price)
      COUNT(CASE WHEN ABS(subtotal - (quantity * unit_price)) > 0.01 THEN 1 END) as calculation_inconsistency_count
    FROM items_flattened
    WHERE quantity IS NOT NULL AND unit_price IS NOT NULL AND subtotal IS NOT NULL
    """

    return run_query_with_cost_control(client, query, "items 數值欄位分析", max_cost_usd=0.1)


def analyze_items_samples(client: bigquery.Client) -> Dict[str, Any]:
    """取得 items 樣本進行詳細檢查"""
    print('🔬 取得 items 樣本進行詳細檢查...')

    query = """
    SELECT
      transaction_id,
      ARRAY_LENGTH(items) as items_count,
      TO_JSON_STRING(items) as items_json
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE items IS NOT NULL
    ORDER BY RAND()
    LIMIT 10
    """

    return run_query_with_cost_control(client, query, "items 樣本檢查", max_cost_usd=0.05)


def validate_items_json_structure(items_samples: List[Dict[str, Any]]) -> Dict[str, Any]:
    """驗證 items JSON 結構"""
    print('🔧 驗證 items JSON 結構...')

    validation_results = {
        "total_samples": len(items_samples),
        "valid_json_count": 0,
        "invalid_json_count": 0,
        "field_presence_stats": {},
        "structure_issues": [],
        "sample_structures": []
    }

    expected_fields = [
        'item_id', 'item_name', 'GRP_CLASS_KEY', 'GRP_CLASS_DESC',
        'CLASS_KEY', 'CLASS_DESC', 'SUB_CLASS_KEY', 'SUB_CLASS_DESC',
        'quantity', 'unit_price', 'subtotal'
    ]

    field_counts = {field: 0 for field in expected_fields}

    for sample in items_samples:
        try:
            items_json = sample.get('items_json', '[]')
            items_data = json.loads(items_json)
            validation_results["valid_json_count"] += 1

            # 記錄第一個樣本的結構
            if len(validation_results["sample_structures"]) < 3 and items_data:
                validation_results["sample_structures"].append({
                    "transaction_id": sample.get('transaction_id'),
                    "items_count": len(items_data),
                    "first_item_fields": list(items_data[0].keys()) if items_data else []
                })

            # 統計欄位出現次數
            for item in items_data:
                for field in expected_fields:
                    if field in item and item[field] is not None:
                        field_counts[field] += 1

        except json.JSONDecodeError as e:
            validation_results["invalid_json_count"] += 1
            validation_results["structure_issues"].append(f"JSON 解析錯誤: {str(e)}")
        except Exception as e:
            validation_results["structure_issues"].append(f"處理錯誤: {str(e)}")

    # 計算欄位出現率
    total_items = sum(len(json.loads(sample.get('items_json', '[]')))
                     for sample in items_samples
                     if sample.get('items_json'))

    validation_results["field_presence_stats"] = {
        field: {
            "count": count,
            "percentage": round(count * 100.0 / total_items, 2) if total_items > 0 else 0
        }
        for field, count in field_counts.items()
    }

    return validation_results


def main():
    """主要執行函數"""
    print('📊 離線資料 items 格式驗證分析')
    print('=' * 60)

    try:
        client = setup_bigquery_client()

        validation_report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "purpose": "驗證離線資料 items 欄位格式和資料品質",
                "billing_project": "tagtoo-tracking",
                "source_table": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
            }
        }

        # 1. 基本結構分析
        print('\n1️⃣ items 基本結構分析')
        validation_report["basic_structure"] = analyze_items_basic_structure(client)

        # 2. 欄位完整性分析
        print('\n2️⃣ items 欄位完整性分析')
        validation_report["field_completeness"] = analyze_items_field_completeness(client)

        # 3. 數值欄位分析
        print('\n3️⃣ items 數值欄位分析')
        validation_report["data_types_ranges"] = analyze_items_data_types_and_ranges(client)

        # 4. 樣本檢查
        print('\n4️⃣ items 樣本檢查')
        samples_result = analyze_items_samples(client)
        validation_report["samples_analysis"] = samples_result

        # 5. JSON 結構驗證
        if samples_result.get("status") == "success":
            print('\n5️⃣ JSON 結構驗證')
            json_validation = validate_items_json_structure(samples_result["data"])
            validation_report["json_structure_validation"] = json_validation

        # 儲存驗證報告
        output_file = 'reports/items_format_validation.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(validation_report, f, indent=2, ensure_ascii=False)

        print(f'\n✅ items 格式驗證報告已儲存至: {output_file}')

        # 顯示關鍵發現
        display_validation_summary(validation_report)

    except Exception as e:
        print(f'❌ 執行錯誤: {e}')
        sys.exit(1)


def display_validation_summary(report: Dict[str, Any]) -> None:
    """顯示驗證摘要"""
    print(f'\n🎯 items 格式驗證摘要:')

    # 基本結構
    basic = report.get("basic_structure", {})
    if basic.get("status") == "success" and basic.get("data"):
        data = basic["data"][0]
        print(f'\n📊 基本結構:')
        print(f'   總記錄數: {data.get("total_records", 0):,}')
        print(f'   items 非 NULL: {data.get("non_null_items", 0):,}')
        print(f'   items NULL 比例: {data.get("null_percentage", 0)}%')
        print(f'   平均 items 數量: {data.get("avg_items_count", 0):.2f}')

    # 欄位完整性
    completeness = report.get("field_completeness", {})
    if completeness.get("status") == "success" and completeness.get("data"):
        data = completeness["data"][0]
        print(f'\n🔍 關鍵欄位完整性:')
        print(f'   item_id: {data.get("item_id_completeness_pct", 0)}%')
        print(f'   item_name: {data.get("item_name_completeness_pct", 0)}%')
        print(f'   quantity: {data.get("quantity_completeness_pct", 0)}%')
        print(f'   unit_price: {data.get("unit_price_completeness_pct", 0)}%')
        print(f'   subtotal: {data.get("subtotal_completeness_pct", 0)}%')

    # JSON 結構驗證
    json_validation = report.get("json_structure_validation", {})
    if json_validation:
        print(f'\n🔧 JSON 結構驗證:')
        print(f'   有效 JSON: {json_validation.get("valid_json_count", 0)}/{json_validation.get("total_samples", 0)}')
        if json_validation.get("structure_issues"):
            print(f'   結構問題: {len(json_validation["structure_issues"])} 個')


if __name__ == '__main__':
    main()
