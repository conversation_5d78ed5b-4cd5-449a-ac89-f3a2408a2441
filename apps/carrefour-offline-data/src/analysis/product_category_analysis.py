#!/usr/bin/env python3
"""
家樂福商品分類規則分析工具

此腳本分析家樂福商品分類表，理解階層關係和 segment_id 生成規則。

功能：
1. 解析商品分類 CSV 檔案
2. 分析階層關係 (大分類 → 中分類 → 小分類)
3. 生成階層式 segment_id 標籤
4. 驗證分類規則的一致性

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import pandas as pd
import json
import re
from typing import Dict, List, Set, Tuple
from pathlib import Path
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductCategoryAnalyzer:
    """家樂福商品分類分析器"""

    def __init__(self, csv_path: str):
        """
        初始化分析器

        Args:
            csv_path: 商品分類 CSV 檔案路徑
        """
        self.csv_path = Path(csv_path)
        self.df = None
        self.category_hierarchy = {}
        self.segment_mapping = {}

    def load_data(self) -> bool:
        """載入商品分類資料"""
        try:
            logger.info(f"載入商品分類檔案: {self.csv_path}")
            self.df = pd.read_csv(self.csv_path)

            # 移除空行
            self.df = self.df.dropna(subset=['Tagtoo Event 商品編號'])

            logger.info(f"成功載入 {len(self.df)} 筆商品分類資料")
            return True

        except Exception as e:
            logger.error(f"載入資料失敗: {e}")
            return False

    def analyze_hierarchy(self) -> Dict:
        """分析商品分類階層關係"""
        if self.df is None:
            logger.error("請先載入資料")
            return {}

        logger.info("分析商品分類階層關係...")

        hierarchy_analysis = {
            "total_products": len(self.df),
            "unique_categories": {},
            "hierarchy_rules": {},
            "sample_mappings": []
        }

        # 分析各層級的唯一值數量
        hierarchy_analysis["unique_categories"] = {
            "大分類": self.df['購買大分類Segment_id'].nunique(),
            "中分類": self.df['購買中分類Segment_id'].nunique(),
            "小分類": self.df['購買Segment_id'].nunique(),
            "商品編號": self.df['Tagtoo Event 商品編號'].nunique()
        }

        # 分析階層規則
        for _, row in self.df.head(10).iterrows():
            product_id = str(row['Tagtoo Event 商品編號'])
            small_cat = row['購買Segment_id']
            medium_cat = row['購買中分類Segment_id']
            large_cat = row['購買大分類Segment_id']

            # 檢查是否為有效字串
            if not all(pd.notna(x) and isinstance(x, str) for x in [small_cat, medium_cat, large_cat]):
                continue

            # 提取數字部分
            small_num = re.search(r'tm:c_715_pc_(\d+)', small_cat)
            medium_num = re.search(r'tm:c_715_pc_(\d+)', medium_cat)
            large_num = re.search(r'tm:c_715_pc_(\d+)', large_cat)

            if small_num and medium_num and large_num:
                hierarchy_analysis["sample_mappings"].append({
                    "product_id": product_id,
                    "small_category": small_num.group(1),
                    "medium_category": medium_num.group(1),
                    "large_category": large_num.group(1),
                    "small_segment": small_cat,
                    "medium_segment": medium_cat,
                    "large_segment": large_cat
                })

        # 分析階層規則模式
        hierarchy_analysis["hierarchy_rules"] = self._extract_hierarchy_rules()

        self.category_hierarchy = hierarchy_analysis
        return hierarchy_analysis

    def _extract_hierarchy_rules(self) -> Dict:
        """提取階層規則模式"""
        rules = {
            "pattern_analysis": {},
            "validation_results": {}
        }

        # 分析數字模式
        sample_data = []
        for _, row in self.df.head(100).iterrows():
            product_id = str(row['Tagtoo Event 商品編號'])
            small_cat = row['購買Segment_id']
            medium_cat = row['購買中分類Segment_id']
            large_cat = row['購買大分類Segment_id']

            # 檢查是否為有效字串
            if not all(pd.notna(x) and isinstance(x, str) for x in [small_cat, medium_cat, large_cat]):
                continue

            # 提取數字
            small_match = re.search(r'tm:c_715_pc_(\d+)', small_cat)
            medium_match = re.search(r'tm:c_715_pc_(\d+)', medium_cat)
            large_match = re.search(r'tm:c_715_pc_(\d+)', large_cat)

            if small_match and medium_match and large_match:
                small_num = small_match.group(1)
                medium_num = medium_match.group(1)
                large_num = large_match.group(1)

                sample_data.append({
                    "product_id": product_id,
                    "small": small_num,
                    "medium": medium_num,
                    "large": large_num
                })

        # 驗證階層關係
        valid_hierarchy = 0
        total_samples = len(sample_data)

        for item in sample_data:
            # 檢查是否符合階層規則 (小分類包含中分類，中分類包含大分類)
            if (item["small"].startswith(item["medium"]) and
                item["medium"].startswith(item["large"])):
                valid_hierarchy += 1

        rules["validation_results"] = {
            "total_samples": total_samples,
            "valid_hierarchy": valid_hierarchy,
            "hierarchy_accuracy": valid_hierarchy / total_samples if total_samples > 0 else 0,
            "sample_data": sample_data[:10]  # 只保留前10個樣本
        }

        return rules

    def generate_hierarchical_segments(self, sub_class_key: str) -> List[str]:
        """
        根據 SUB_CLASS_KEY 生成階層式 segment_id 標籤

        Args:
            sub_class_key: 小分類代碼 (例如: "10012")

        Returns:
            階層式標籤列表 (例如: ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"])
        """
        segments = []

        # 小分類標籤
        segments.append(f"tm:c_715_pc_{sub_class_key}")

        # 中分類標籤 (取前4位數字)
        if len(sub_class_key) >= 4:
            medium_key = sub_class_key[:4]
            segments.append(f"tm:c_715_pc_{medium_key}")

        # 大分類標籤 (取前3位數字)
        if len(sub_class_key) >= 3:
            large_key = sub_class_key[:3]
            segments.append(f"tm:c_715_pc_{large_key}")

        return segments

    def create_segment_mapping(self) -> Dict:
        """建立商品編號到 segment_id 的對應表"""
        if self.df is None:
            logger.error("請先載入資料")
            return {}

        logger.info("建立商品編號到 segment_id 對應表...")

        mapping = {}
        for _, row in self.df.iterrows():
            product_id = str(row['Tagtoo Event 商品編號'])
            if pd.notna(product_id) and product_id.strip():
                # 生成階層式標籤
                hierarchical_segments = self.generate_hierarchical_segments(product_id)
                mapping[product_id] = {
                    "segments": hierarchical_segments,
                    "segment_string": ",".join(hierarchical_segments),
                    "product_name": row.get('SUB_CLASS_DESC（塔圖線下資料商品分類）', ''),
                    "category_info": {
                        "large": row.get('GRP_CLASS_DESC', ''),
                        "medium": row.get('CLASS_DESC', ''),
                        "small": row.get('SUB_CLASS_DESC（塔圖線下資料商品分類）', '')
                    }
                }

        self.segment_mapping = mapping
        logger.info(f"建立了 {len(mapping)} 個商品的 segment_id 對應")
        return mapping

    def test_segment_generation(self, test_cases: List[str] = None) -> Dict:
        """測試 segment_id 生成邏輯"""
        if test_cases is None:
            test_cases = ["10012", "10000", "12345", "76800"]

        logger.info("測試 segment_id 生成邏輯...")

        test_results = {
            "test_cases": [],
            "validation": {}
        }

        for case in test_cases:
            segments = self.generate_hierarchical_segments(case)
            test_results["test_cases"].append({
                "input": case,
                "output_segments": segments,
                "output_string": ",".join(segments)
            })

        # 驗證與 CSV 中的對應關係
        if self.df is not None:
            csv_validation = []
            for case in test_cases:
                csv_row = self.df[self.df['Tagtoo Event 商品編號'].astype(str) == case]
                if not csv_row.empty:
                    row = csv_row.iloc[0]
                    expected_segments = [
                        row['購買Segment_id'],
                        row['購買中分類Segment_id'],
                        row['購買大分類Segment_id']
                    ]
                    generated_segments = self.generate_hierarchical_segments(case)

                    csv_validation.append({
                        "product_id": case,
                        "expected": expected_segments,
                        "generated": generated_segments,
                        "match": expected_segments == generated_segments
                    })

            test_results["validation"]["csv_comparison"] = csv_validation

        return test_results

    def save_analysis_results(self, output_path: str) -> bool:
        """儲存分析結果"""
        try:
            results = {
                "metadata": {
                    "analysis_type": "product_category_analysis",
                    "generated_at": pd.Timestamp.now().isoformat(),
                    "csv_file": str(self.csv_path),
                    "total_products": len(self.df) if self.df is not None else 0
                },
                "hierarchy_analysis": self.category_hierarchy,
                "segment_mapping_sample": dict(list(self.segment_mapping.items())[:10]) if self.segment_mapping else {},
                "test_results": self.test_segment_generation()
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logger.info(f"分析結果已儲存至: {output_path}")
            return True

        except Exception as e:
            logger.error(f"儲存結果失敗: {e}")
            return False

def main():
    """主程式"""
    # 設定檔案路徑
    csv_path = "docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv"
    output_path = "reports/product_category_analysis.json"

    # 建立分析器
    analyzer = ProductCategoryAnalyzer(csv_path)

    # 執行分析
    if analyzer.load_data():
        analyzer.analyze_hierarchy()
        analyzer.create_segment_mapping()
        analyzer.save_analysis_results(output_path)

        # 輸出摘要
        print("\n=== 商品分類分析摘要 ===")
        print(f"總商品數量: {len(analyzer.df)}")
        print(f"唯一分類數量: {analyzer.category_hierarchy.get('unique_categories', {})}")
        print(f"階層規則準確性: {analyzer.category_hierarchy.get('hierarchy_rules', {}).get('validation_results', {}).get('hierarchy_accuracy', 0):.2%}")

        # 測試範例
        test_results = analyzer.test_segment_generation(["10012", "10000"])
        print("\n=== 測試範例 ===")
        for test in test_results["test_cases"]:
            print(f"商品 {test['input']} → {test['output_string']}")

if __name__ == "__main__":
    main()
