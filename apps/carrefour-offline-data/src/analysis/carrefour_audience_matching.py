#!/usr/bin/env python3
"""
家樂福線下購買資料與 Tagtoo 線上事件資料媒合及受眾分類系統

此腳本實作完整的資料媒合流程：
1. 建立 user.ph 到 permanent 的對應關係表
2. 媒合線下交易資料與線上用戶資料
3. 根據購買商品生成階層式受眾標籤
4. 輸出到 ML 工作流程表格

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import pandas as pd
import json
import re
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
import logging
from datetime import datetime, timedelta, timezone
import pytz
from google.cloud import bigquery
from google.cloud.exceptions import NotFound, Forbidden
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError
import os
import uuid
import subprocess

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CarrefourAudienceMatching:
    """家樂福受眾媒合系統"""

    def __init__(self, config_path: str = None, execution_mode: str = "manual"):
        """
        初始化媒合系統

        Args:
            config_path: 配置檔案路徑
            execution_mode: 執行模式 ("manual" 或 "automated")
        """
        self.client = None
        self.config = self._load_config(config_path)
        self.product_mapping = {}
        self.user_mapping = {}
        self.execution_mode = execution_mode
        self.execution_id = self._generate_execution_id()

        # 初始化 BigQuery 客戶端
        self._init_bigquery_client()

        # 載入商品分類對應表
        self._load_product_mapping()

        # 初始化 GCP labels
        self.gcp_labels = self._generate_gcp_labels(execution_mode)

    def _load_config(self, config_path: str) -> Dict:
        """載入配置"""
        default_config = {
            "source_tables": {
                "tagtoo_event": "tagtoo-tracking.event_prod.tagtoo_event",
                "carrefour_offline": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
                "tagtoo_entity": "tagtoo-tracking.event_prod.tagtoo_entity"
            },
            "target_table": "tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{date}",
            "product_csv": "docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv",
            "batch_size": 10000,
            "max_cost_usd": 50.0,  # 提高成本限制以支援生產環境
            "ec_id": 715
        }

        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def _init_bigquery_client(self):
        """初始化 BigQuery 客戶端"""
        try:
            self.client = bigquery.Client()
            logger.info("BigQuery 客戶端初始化成功")

            # 顯示認證資訊
            self._display_auth_info()

        except Exception as e:
            logger.error(f"BigQuery 客戶端初始化失敗: {e}")
            raise

    def _display_auth_info(self):
        """顯示當前認證帳號資訊和權限狀態"""
        try:
            logger.info("=== Google Cloud 認證資訊 ===")

            # 取得當前認證資訊
            credentials, project_id = default()

            # 顯示專案資訊
            logger.info(f"當前專案ID: {project_id}")
            logger.info(f"BigQuery 客戶端專案: {self.client.project}")

            # 檢查認證類型
            if hasattr(credentials, 'service_account_email'):
                logger.info(f"認證類型: Service Account")
                logger.info(f"Service Account: {credentials.service_account_email}")
            else:
                # 嘗試從 gcloud 取得用戶資訊
                try:
                    result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE', '--format=value(account)'],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout.strip():
                        active_account = result.stdout.strip()
                        logger.info(f"認證類型: User Account")
                        logger.info(f"活躍帳號: {active_account}")
                    else:
                        logger.info("認證類型: 未知")
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    logger.info("認證類型: 無法確定 (gcloud 不可用)")

            # 檢查權限狀態
            self._check_permissions()

        except DefaultCredentialsError:
            logger.error("❌ 未找到有效的 Google Cloud 認證")
            logger.error("請執行: gcloud auth application-default login")
        except Exception as e:
            logger.warning(f"⚠️ 無法取得完整認證資訊: {e}")

    def _check_permissions(self):
        """檢查對目標專案和表格的權限"""
        logger.info("=== 權限狀態檢查 ===")

        # 檢查來源表格權限
        self._check_table_permissions("tagtoo-tracking.event_prod.tagtoo_event", "來源表格 (tagtoo_event)")
        self._check_table_permissions("tagtoo-tracking.event_prod.carrefour_offline_transaction_day", "來源表格 (carrefour_offline)")

        # 檢查目標專案權限
        target_project = "tagtoo-ml-workflow"
        logger.info(f"檢查目標專案權限: {target_project}")

        try:
            # 嘗試列出目標專案的資料集
            target_client = bigquery.Client(project=target_project)
            datasets = list(target_client.list_datasets())
            logger.info(f"✅ 對 {target_project} 有讀取權限 (找到 {len(datasets)} 個資料集)")

            # 檢查目標表格權限
            target_date = self._calculate_target_date()
            target_table = f"{target_project}.tagtoo_export_results.special_lta_temp_for_update_{target_date}"
            self._check_table_permissions(target_table, "目標表格", check_write=True)

        except Forbidden:
            logger.error(f"❌ 對 {target_project} 專案沒有存取權限")
        except Exception as e:
            logger.warning(f"⚠️ 檢查 {target_project} 權限時發生錯誤: {e}")

    def _check_table_permissions(self, table_id: str, description: str, check_write: bool = False):
        """檢查特定表格的權限"""
        try:
            # 檢查讀取權限
            table = self.client.get_table(table_id)
            logger.info(f"✅ {description} 讀取權限正常: {table_id}")

            if check_write:
                # 檢查寫入權限 (嘗試執行 dry run 插入)
                try:
                    job_config = bigquery.QueryJobConfig(dry_run=True)
                    query = f"INSERT INTO `{table_id}` (permanent, segment_id) VALUES ('test', 'test')"
                    self.client.query(query, job_config=job_config)
                    logger.info(f"✅ {description} 寫入權限正常")
                except Exception as write_error:
                    logger.error(f"❌ {description} 寫入權限不足: {write_error}")

        except NotFound:
            logger.warning(f"⚠️ {description} 不存在: {table_id}")
        except Forbidden:
            logger.error(f"❌ {description} 存取權限不足: {table_id}")
        except Exception as e:
            logger.warning(f"⚠️ 檢查 {description} 權限時發生錯誤: {e}")

    def _generate_gcp_labels(self, execution_mode: str) -> Dict[str, str]:
        """生成 GCP labels 用於費用追蹤"""
        import subprocess
        import os

        labels = {
            "project": "carrefour-offline-data",
            "repo": "integrated-event",
            "env": "prod",
            "trigger": "manual" if execution_mode == "manual" else "auto"
        }

        # 嘗試自動偵測當前用戶
        try:
            # 方法1: 從 gcloud 取得活躍帳號
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE', '--format=value(account)'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                user_email = result.stdout.strip()
                # 取得 @ 前面的部分作為用戶名
                username = user_email.split('@')[0].replace('.', '-').replace('_', '-')
                labels["user"] = username[:63]  # GCP label 限制 63 字元
            else:
                # 方法2: 從環境變數取得
                user = os.getenv('USER') or os.getenv('USERNAME') or 'unknown'
                labels["user"] = user.replace('.', '-').replace('_', '-')[:63]

        except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
            # 方法3: 從環境變數取得
            user = os.getenv('USER') or os.getenv('USERNAME') or 'unknown'
            labels["user"] = user.replace('.', '-').replace('_', '-')[:63]

        logger.info(f"GCP Labels: {labels}")
        return labels

    def _load_product_mapping(self):
        """載入商品分類對應表"""
        try:
            csv_path = Path(self.config["product_csv"])
            if not csv_path.exists():
                logger.error(f"商品分類檔案不存在: {csv_path}")
                return

            df = pd.read_csv(csv_path)
            df = df.dropna(subset=['Tagtoo Event 商品編號'])

            for _, row in df.iterrows():
                product_id = str(row['Tagtoo Event 商品編號'])
                if pd.notna(product_id) and product_id.strip():
                    # 生成階層式標籤
                    segments = self._generate_hierarchical_segments(product_id)
                    self.product_mapping[product_id] = {
                        "segments": segments,
                        "segment_string": ",".join(segments),
                        "category_info": {
                            "large": row.get('GRP_CLASS_DESC', ''),
                            "medium": row.get('CLASS_DESC', ''),
                            "small": row.get('SUB_CLASS_DESC（塔圖線下資料商品分類）', '')
                        }
                    }

            logger.info(f"載入了 {len(self.product_mapping)} 個商品的分類對應")

        except Exception as e:
            logger.error(f"載入商品分類對應表失敗: {e}")

    def _generate_hierarchical_segments(self, sub_class_key: str) -> List[str]:
        """
        根據 SUB_CLASS_KEY 生成階層式 segment_id 標籤

        Args:
            sub_class_key: 小分類代碼 (例如: "10012")

        Returns:
            階層式標籤列表 (例如: ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"])
        """
        segments = []

        # 小分類標籤
        segments.append(f"tm:c_715_pc_{sub_class_key}")

        # 中分類標籤 (取前4位數字)
        if len(sub_class_key) >= 4:
            medium_key = sub_class_key[:4]
            segments.append(f"tm:c_715_pc_{medium_key}")

        # 大分類標籤 (取前3位數字)
        if len(sub_class_key) >= 3:
            large_key = sub_class_key[:3]
            segments.append(f"tm:c_715_pc_{large_key}")

        return segments

    def _generate_execution_id(self) -> str:
        """
        生成唯一的執行識別碼

        格式: carrefour_audience_matching_{YYYYMMDD}_{HHMMSS}_{UUID前8位}
        使用台灣時區確保時區一致性

        Returns:
            唯一執行識別碼
        """
        taipei_tz = pytz.timezone('Asia/Taipei')
        timestamp = datetime.now(taipei_tz).strftime('%Y%m%d_%H%M%S')
        unique_suffix = str(uuid.uuid4())[:8]
        return f"carrefour_audience_matching_{timestamp}_{unique_suffix}"

    def _format_created_at(self) -> datetime:
        """
        格式化 created_at 時間戳

        使用 UTC 時間，確保時區一致性

        Returns:
            UTC 時間戳
        """
        return datetime.now(timezone.utc)

    def _get_source_type(self) -> str:
        """
        取得資料來源類型標識

        Returns:
            標準化的資料來源類型
        """
        return "carrefour_offline_purchase"

    def _get_source_entity(self) -> str:
        """
        取得來源實體標識

        Returns:
            標準化的來源實體名稱
        """
        return "carrefour_audience_matching_system"

    def _calculate_target_date(self) -> str:
        """
        計算目標表格日期後綴

        手動執行模式：當前日期 + 1 天
        自動化執行模式：當前日期

        使用台灣時區 (Asia/Taipei) 確保時區一致性

        Returns:
            日期後綴 (YYYYMMDD 格式)
        """
        # 使用台灣時區確保時區一致性
        taipei_tz = pytz.timezone('Asia/Taipei')
        base_date = datetime.now(taipei_tz)

        if self.execution_mode == "manual":
            # 手動執行：目標日期為明天
            target_date = base_date + timedelta(days=1)
            logger.info(f"手動執行模式：目標日期設為明天 ({target_date.strftime('%Y-%m-%d')} 台灣時間)")
        else:
            # 自動化執行：目標日期為今天
            target_date = base_date
            logger.info(f"自動化執行模式：目標日期設為今天 ({target_date.strftime('%Y-%m-%d')} 台灣時間)")

        return target_date.strftime('%Y%m%d')

    def _estimate_query_cost(self, query: str) -> float:
        """估算查詢成本"""
        try:
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            query_job = self.client.query(query, job_config=job_config)

            # 估算成本 (BigQuery 定價: $5 per TB)
            bytes_processed = query_job.total_bytes_processed
            cost_usd = (bytes_processed / (1024**4)) * 5  # TB to USD

            logger.info(f"查詢將處理 {bytes_processed:,} bytes，預估成本: ${cost_usd:.4f} USD")
            return cost_usd

        except Exception as e:
            logger.error(f"成本估算失敗: {e}")
            return 0.0

    def build_user_mapping_table(self, days_back: int = 7) -> bool:
        """
        建立 user.ph 到 permanent 的對應關係表

        Args:
            days_back: 回溯天數

        Returns:
            是否成功
        """
        logger.info(f"建立用戶對應表 (回溯 {days_back} 天)...")

        query = f"""
        SELECT DISTINCT
            user.ph,
            permanent
        FROM `{self.config["source_tables"]["tagtoo_event"]}`
        WHERE user.ph IS NOT NULL
            AND permanent IS NOT NULL
            AND ec_id = {self.config["ec_id"]}
            AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL {days_back} DAY)
        """

        # 估算成本
        cost = self._estimate_query_cost(query)
        if cost > self.config["max_cost_usd"]:
            logger.error(f"查詢成本 ${cost:.4f} 超過限制 ${self.config['max_cost_usd']}")
            return False

        try:
            # 執行查詢 (加入 GCP labels)
            job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
            query_job = self.client.query(query, job_config=job_config)
            results = query_job.result()

            # 建立對應表
            self.user_mapping = {}
            count = 0
            for row in results:
                self.user_mapping[row.ph] = row.permanent
                count += 1

            logger.info(f"成功建立 {count} 個用戶的對應關係")
            return True

        except Exception as e:
            logger.error(f"建立用戶對應表失敗: {e}")
            return False

    def query_offline_purchases(self, limit: int = None) -> List[Dict]:
        """
        查詢線下購買資料 - 使用 UNNEST 展開 items

        Args:
            limit: 限制查詢筆數 (測試用)

        Returns:
            購買資料列表 (每個商品一筆記錄)
        """
        logger.info("查詢線下購買資料...")

        # 只查詢有對應關係的用戶
        if not self.user_mapping:
            logger.error("請先建立用戶對應表")
            return []

        # 處理用戶列表 - 分批處理以避免查詢過大
        all_users = list(self.user_mapping.keys())

        if limit:
            # 如果有限制，只取前 100 個用戶進行測試
            sample_users = all_users[:100]
        else:
            # 生產模式：分批處理所有用戶
            sample_users = all_users

        # 分批處理以避免 413 錯誤 (優化批次大小)
        batch_size = 10000  # 每批處理 10000 個用戶 (優化後)
        all_purchases = []

        for i in range(0, len(sample_users), batch_size):
            batch_users = sample_users[i:i + batch_size]
            user_ph_list = "', '".join(batch_users)

            logger.info(f"處理用戶批次 {i//batch_size + 1}/{(len(sample_users) + batch_size - 1)//batch_size} ({len(batch_users)} 個用戶)")

            batch_query = f"""
            SELECT
                TO_HEX(ph_id) as ph_id_hex,
                transaction_id,
                total_amount,
                store_id,
                store_name,
                item.item_id,
                item.item_name,
                item.SUB_CLASS_KEY,
                item.GRP_CLASS_DESC,
                item.CLASS_DESC,
                item.SUB_CLASS_DESC,
                item.quantity,
                item.unit_price,
                item.subtotal
            FROM `{self.config["source_tables"]["carrefour_offline"]}`,
            UNNEST(items) as item
            WHERE ph_id IS NOT NULL
                AND TO_HEX(ph_id) IN ('{user_ph_list}')
                AND items IS NOT NULL
                AND ARRAY_LENGTH(items) > 0
                AND item.SUB_CLASS_KEY IS NOT NULL
                AND item.SUB_CLASS_KEY != ''
            """

            # 如果是第一批且有限制，應用限制
            if limit and i == 0:
                batch_query += f" LIMIT {limit}"

            # 估算成本
            cost = self._estimate_query_cost(batch_query)
            if cost > self.config["max_cost_usd"]:
                logger.error(f"批次查詢成本 ${cost:.4f} 超過限制 ${self.config['max_cost_usd']}")
                break

            try:
                job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
                query_job = self.client.query(batch_query, job_config=job_config)
                results = query_job.result()

                batch_purchases = []
                for row in results:
                    batch_purchases.append({
                        "ph_id_hex": row.ph_id_hex,
                        "permanent": self.user_mapping.get(row.ph_id_hex),
                        "transaction_id": row.transaction_id,
                        "total_amount": float(row.total_amount) if row.total_amount else 0.0,
                        "store_id": row.store_id,
                        "store_name": row.store_name,
                        "item": {
                            "item_id": row.item_id,
                            "item_name": row.item_name,
                            "SUB_CLASS_KEY": row.SUB_CLASS_KEY,
                            "GRP_CLASS_DESC": row.GRP_CLASS_DESC,
                            "CLASS_DESC": row.CLASS_DESC,
                            "SUB_CLASS_DESC": row.SUB_CLASS_DESC,
                            "quantity": float(row.quantity) if row.quantity else 0.0,
                            "unit_price": float(row.unit_price) if row.unit_price else 0.0,
                            "subtotal": float(row.subtotal) if row.subtotal else 0.0
                        }
                    })

                all_purchases.extend(batch_purchases)
                logger.info(f"批次查詢到 {len(batch_purchases)} 筆購買記錄")

                # 如果有限制且已達到，停止處理
                if limit and len(all_purchases) >= limit:
                    all_purchases = all_purchases[:limit]
                    break

            except Exception as e:
                logger.error(f"批次查詢失敗: {e}")
                # 如果是 413 錯誤，嘗試更小的批次
                if "413" in str(e) and batch_size > 1000:
                    logger.info("嘗試更小的批次大小...")
                    batch_size = batch_size // 2
                    continue
                else:
                    break

        logger.info(f"總共查詢到 {len(all_purchases)} 筆購買記錄 (展開後)")
        return all_purchases

    def query_offline_purchases_entity_optimized(self, days_back: int = 1, limit: int = None) -> List[Dict]:
        """
        使用 tagtoo_entity 表格的優化版查詢方法
        避免 JOIN 放大效應，大幅降低查詢複雜度和成本
        """
        logger.info(f"執行 tagtoo_entity 優化版查詢 (回溯 {days_back} 天)...")

        # 階段1：從 tagtoo_entity 建立對應關係
        stage1_query = f"""
        SELECT DISTINCT permanent, mobile
        FROM `{self.config["source_tables"]["tagtoo_entity"]}`
        WHERE ec_id = {self.config["ec_id"]}
            AND mobile IS NOT NULL
            AND latest_entity_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {days_back} DAY)
        """

        try:
            # 估算階段1成本
            cost1 = self._estimate_query_cost(stage1_query)
            logger.info(f"階段1查詢預估成本: ${cost1:.4f} USD")

            # 執行階段1查詢 (加入 GCP labels)
            job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
            job1 = self.client.query(stage1_query, job_config=job_config)
            stage1_results = job1.result()

            # 建立對應關係字典
            mobile_to_permanent = {}
            for row in stage1_results:
                mobile_to_permanent[row.mobile] = row.permanent

            logger.info(f"階段1完成，建立 {len(mobile_to_permanent)} 個對應關係")

            if not mobile_to_permanent:
                logger.warning("階段1未找到任何對應關係")
                return []

            # 階段2：查詢線下資料
            # mobile 就是 TO_HEX(ph_id)，所以直接比較字串
            mobile_list = list(mobile_to_permanent.keys())

            # 分批處理以避免 IN 子句過長
            batch_size = 1000
            all_purchases = []

            for i in range(0, len(mobile_list), batch_size):
                batch_mobiles = mobile_list[i:i+batch_size]
                mobile_quoted_list = [f"'{mobile}'" for mobile in batch_mobiles]

                stage2_query = f"""
                SELECT
                    TO_HEX(off.ph_id) as mobile,
                    item.SUB_CLASS_KEY
                FROM `{self.config["source_tables"]["carrefour_offline"]}` off
                JOIN UNNEST(off.items) as item
                WHERE TO_HEX(off.ph_id) IN ({','.join(mobile_quoted_list)})
                    AND item.SUB_CLASS_KEY IS NOT NULL
                    AND item.SUB_CLASS_KEY != ''
                """

                # 如果有 limit 參數，添加到最後一個批次
                if limit and i + batch_size >= len(mobile_list):
                    remaining_limit = limit - len(all_purchases)
                    if remaining_limit > 0:
                        stage2_query += f" LIMIT {remaining_limit}"

                try:
                    cost2 = self._estimate_query_cost(stage2_query)
                    logger.info(f"階段2批次 {i//batch_size + 1} 查詢預估成本: ${cost2:.4f} USD")

                    job2 = self.client.query(stage2_query, job_config=job_config)
                    batch_results = job2.result()

                    for row in batch_results:
                        mobile = row.mobile
                        if mobile in mobile_to_permanent:
                            all_purchases.append({
                                "permanent": mobile_to_permanent[mobile],
                                "SUB_CLASS_KEY": row.SUB_CLASS_KEY
                            })

                    # 如果達到 limit，停止處理
                    if limit and len(all_purchases) >= limit:
                        break

                except Exception as e:
                    logger.error(f"階段2批次 {i//batch_size + 1} 查詢失敗: {e}")
                    continue

            logger.info(f"tagtoo_entity 優化查詢完成，取得 {len(all_purchases)} 筆購買記錄")
            return all_purchases

        except Exception as e:
            logger.error(f"tagtoo_entity 優化查詢失敗: {e}")
            return []

    def _get_high_performance_query(self, days_back: int = 1, limit: int = None) -> str:
        """
        獲取高效能 SQL 查詢 (基於 historical_backfill.py 邏輯)

        Args:
            days_back: 回溯天數
            limit: 限制結果數量

        Returns:
            str: 高效能 SQL 查詢語句
        """
        # 使用台灣時區確保時區一致性
        taipei_tz = pytz.timezone('Asia/Taipei')
        now_taipei = datetime.now(taipei_tz)
        end_date = now_taipei.strftime('%Y-%m-%d')
        start_date = (now_taipei - timedelta(days=days_back)).strftime('%Y-%m-%d')

        query = f"""
        WITH historical_mobile_users AS (
          SELECT DISTINCT
            permanent,
            mobile,
            DATE(latest_entity_time) as activity_date
          FROM `{self.config["source_tables"]["tagtoo_entity"]}`
          WHERE ec_id = {self.config["ec_id"]}
            AND mobile IS NOT NULL
            AND latest_entity_time >= '{start_date}'
            AND latest_entity_time < '{end_date}'
        ),
        carrefour_offline_data AS (
          SELECT DISTINCT
            TO_HEX(off.ph_id) as mobile,
            item.SUB_CLASS_KEY
          FROM `{self.config["source_tables"]["carrefour_offline"]}` off
          JOIN UNNEST(off.items) as item
          WHERE item.SUB_CLASS_KEY IS NOT NULL
            AND item.SUB_CLASS_KEY != ''
        ),
        hierarchical_segments AS (
          SELECT
            h.permanent,
            h.activity_date,
            c.SUB_CLASS_KEY
          FROM historical_mobile_users h
          INNER JOIN carrefour_offline_data c ON h.mobile = c.mobile
        ),
        audience_mapping AS (
          SELECT
            permanent,
            activity_date,
            ARRAY_AGG(DISTINCT segment_id) as segment_ids
          FROM (
            -- 小分類標籤 (完整 SUB_CLASS_KEY)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUB_CLASS_KEY) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL AND SUB_CLASS_KEY != ''

            UNION ALL

            -- 中分類標籤 (前4位數字)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 4)) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL
              AND SUB_CLASS_KEY != ''
              AND LENGTH(SUB_CLASS_KEY) >= 4

            UNION ALL

            -- 大分類標籤 (前3位數字)
            SELECT
              permanent,
              activity_date,
              CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 3)) as segment_id
            FROM hierarchical_segments
            WHERE SUB_CLASS_KEY IS NOT NULL
              AND SUB_CLASS_KEY != ''
              AND LENGTH(SUB_CLASS_KEY) >= 3
          )
          GROUP BY permanent, activity_date
        )
        SELECT
          permanent,
          activity_date,
          ARRAY_TO_STRING(ARRAY(SELECT * FROM UNNEST(segment_ids) ORDER BY 1), ',') as segment_id
        FROM audience_mapping
        WHERE ARRAY_LENGTH(segment_ids) > 0
        ORDER BY activity_date, permanent
        """

        # 如果有 limit 參數，添加 LIMIT 子句
        if limit:
            query += f" LIMIT {limit}"

        return query

    def query_offline_purchases_optimized(self, days_back: int = 2, limit: int = None) -> List[Dict]:
        """
        優化版查詢：使用單一 JOIN 查詢，類似線上方法

        Args:
            days_back: 回溯天數，預設2天

        Returns:
            購買記錄列表
        """
        logger.info(f"執行優化版查詢 (回溯 {days_back} 天)...")

        # 成本優化的單一查詢：只選擇必要欄位
        query = f"""
        SELECT
            tr.permanent,
            item.SUB_CLASS_KEY
        FROM `{self.config["source_tables"]["tagtoo_event"]}` tr
        JOIN `{self.config["source_tables"]["carrefour_offline"]}` off
            ON tr.user.ph = TO_HEX(off.ph_id)
        JOIN UNNEST(off.items) as item
        WHERE DATE(tr.event_time) BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL {days_back} DAY)
            AND CURRENT_DATE()
            AND tr.ec_id = {self.config["ec_id"]}
            AND tr.permanent IS NOT NULL
            AND tr.user.ph IS NOT NULL
            AND off.ph_id IS NOT NULL
            AND item.SUB_CLASS_KEY IS NOT NULL
            AND item.SUB_CLASS_KEY != ''
        """

        # 如果有 limit 參數，添加 LIMIT 子句
        if limit:
            query += f" LIMIT {limit}"

        try:
            # 成本估算
            cost = self._estimate_query_cost(query)
            logger.info(f"優化查詢預估成本: ${cost:.4f} USD")

            if cost > self.config["max_cost_usd"]:
                logger.error(f"查詢成本 ${cost:.4f} 超過限制 ${self.config['max_cost_usd']}")
                return []

            # 執行查詢 (加入 GCP labels)
            job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
            query_job = self.client.query(query, job_config=job_config)
            results = query_job.result()

            purchases = []
            for row in results:
                purchases.append({
                    "permanent": row.permanent,
                    "SUB_CLASS_KEY": row.SUB_CLASS_KEY
                })

            logger.info(f"優化查詢完成，取得 {len(purchases)} 筆購買記錄")
            return purchases

        except Exception as e:
            logger.error(f"優化查詢失敗: {e}")
            return []

    def generate_audience_segments(self, purchases: List[Dict]) -> List[Dict]:
        """
        根據購買資料生成受眾標籤

        Args:
            purchases: 購買資料列表 (每個商品一筆記錄)

        Returns:
            受眾標籤資料列表
        """
        logger.info("生成受眾標籤...")

        audience_data = {}

        for purchase in purchases:
            permanent = purchase["permanent"]
            if not permanent:
                continue

            # 初始化用戶標籤集合
            if permanent not in audience_data:
                audience_data[permanent] = {
                    "segments": set(),
                    "transactions": set(),
                    "total_amount": 0.0,
                    "items_purchased": []
                }

            # 處理購買商品 (新格式：每筆記錄包含一個商品)
            item = purchase.get("item", {})
            if item:
                sub_class_key = str(item.get("SUB_CLASS_KEY", ""))
                if sub_class_key and sub_class_key in self.product_mapping:
                    # 添加階層式標籤
                    segments = self.product_mapping[sub_class_key]["segments"]
                    audience_data[permanent]["segments"].update(segments)

                    # 記錄購買的商品
                    audience_data[permanent]["items_purchased"].append({
                        "sub_class_key": sub_class_key,
                        "item_name": item.get("item_name", ""),
                        "quantity": item.get("quantity", 0),
                        "subtotal": item.get("subtotal", 0)
                    })

            # 記錄交易資訊 (使用 set 避免重複)
            audience_data[permanent]["transactions"].add(purchase["transaction_id"])
            # 注意：這裡可能會重複計算同一交易的總金額，需要修正
            # audience_data[permanent]["total_amount"] += purchase["total_amount"]

        # 轉換為輸出格式
        output_data = []
        created_at = self._format_created_at()
        source_type = self._get_source_type()
        source_entity = self._get_source_entity()

        for permanent, data in audience_data.items():
            if data["segments"]:  # 只輸出有標籤的用戶
                output_data.append({
                    "permanent": permanent,
                    "segment_id": ",".join(sorted(data["segments"])),
                    "created_at": created_at,
                    "source_type": source_type,
                    "source_entity": source_entity,
                    "execution_id": self.execution_id,
                    "metadata": {
                        "transaction_count": len(data["transactions"]),
                        "items_count": len(data["items_purchased"]),
                        "segment_count": len(data["segments"]),
                        "items_purchased": data["items_purchased"][:5],  # 只保留前5個商品作為樣本
                        "execution_mode": self.execution_mode,
                        "generated_at_utc": created_at.isoformat()
                    }
                })

        logger.info(f"為 {len(output_data)} 個用戶生成了受眾標籤")
        return output_data

    def write_to_target_table(self, audience_data: List[Dict], date_suffix: str = None, dry_run: bool = False, batch_size: int = 1000) -> bool:
        """
        將受眾資料寫入目標表格

        Args:
            audience_data: 受眾標籤資料列表
            date_suffix: 日期後綴 (格式: YYYYMMDD)，如未提供則根據執行模式自動計算
            dry_run: 是否為 Dry Run 模式 (僅預覽，不實際寫入)

        Returns:
            是否成功
        """
        if not audience_data:
            logger.error("沒有受眾資料可寫入")
            return False

        # 設定日期後綴
        if not date_suffix:
            date_suffix = self._calculate_target_date()

        # 建立目標表格名稱
        target_table = self.config["target_table"].format(date=date_suffix)
        logger.info(f"準備寫入目標表格: {target_table}")
        logger.info(f"執行模式: {self.execution_mode}")
        logger.info(f"執行ID: {self.execution_id}")
        logger.info(f"目標日期: {date_suffix}")

        try:
            # 準備寫入資料
            rows_to_insert = []
            for data in audience_data:
                row = {
                    "permanent": data["permanent"],
                    "segment_id": data["segment_id"],
                    "created_at": data["created_at"],
                    "source_type": data["source_type"],
                    "source_entity": data["source_entity"],
                    "execution_id": data["execution_id"]
                }
                rows_to_insert.append(row)

            if dry_run:
                logger.info(f"🔍 DRY RUN 模式：預覽將寫入的資料")
                logger.info(f"目標表格: {target_table}")
                logger.info(f"預計寫入記錄數: {len(rows_to_insert)}")

                # 顯示前 10 筆記錄的完整內容
                logger.info("前 10 筆記錄預覽:")
                for i, row in enumerate(rows_to_insert[:10], 1):
                    logger.info(f"  記錄 {i}:")
                    logger.info(f"    permanent: {row['permanent']}")
                    logger.info(f"    segment_id: {row['segment_id'][:100]}{'...' if len(row['segment_id']) > 100 else ''}")
                    logger.info(f"    created_at: {row['created_at']}")
                    logger.info(f"    source_type: {row['source_type']}")
                    logger.info(f"    source_entity: {row['source_entity']}")
                    logger.info(f"    execution_id: {row['execution_id']}")

                logger.info("🔍 DRY RUN 完成：跳過實際寫入操作")
                return True

            # 檢查目標表格是否存在，如果不存在則建立
            self._ensure_target_table_exists(target_table)

            # 分批寫入資料以避免 HTTP 413 錯誤
            table = self.client.get_table(target_table)

            # 轉換 datetime 物件為字串
            for row in rows_to_insert:
                if isinstance(row.get('created_at'), datetime):
                    row['created_at'] = row['created_at'].isoformat()

            # 分批處理
            total_rows = len(rows_to_insert)
            successful_batches = 0
            total_batches = (total_rows + batch_size - 1) // batch_size

            logger.info(f"開始分批寫入 {total_rows} 筆資料，分 {total_batches} 批次，每批次 {batch_size} 筆")

            for i in range(0, total_rows, batch_size):
                batch_data = rows_to_insert[i:i + batch_size]
                batch_num = i // batch_size + 1

                try:
                    errors = self.client.insert_rows_json(table, batch_data)

                    if errors:
                        logger.error(f"批次 {batch_num}/{total_batches} 寫入錯誤: {errors}")
                        # 繼續處理其他批次，不要因為一個批次失敗就停止
                        continue
                    else:
                        successful_batches += 1
                        logger.info(f"批次 {batch_num}/{total_batches} 成功寫入 {len(batch_data)} 筆資料")

                except Exception as e:
                    logger.error(f"批次 {batch_num}/{total_batches} 寫入異常: {e}")
                    continue

            if successful_batches == total_batches:
                logger.info(f"✅ 所有批次成功寫入，總計 {total_rows} 筆受眾資料到 {target_table}")
                return True
            elif successful_batches > 0:
                logger.warning(f"⚠️  部分成功：{successful_batches}/{total_batches} 批次寫入成功")
                return True
            else:
                logger.error(f"❌ 所有批次寫入失敗")
                return False

        except Exception as e:
            logger.error(f"寫入目標表格失敗: {e}")
            return False

    def _ensure_target_table_exists(self, table_id: str) -> bool:
        """確保目標表格存在"""
        try:
            # 嘗試獲取表格
            self.client.get_table(table_id)
            logger.info(f"目標表格已存在: {table_id}")
            return True

        except NotFound:
            logger.info(f"目標表格不存在，將建立: {table_id}")

            # 建立表格 schema
            schema = [
                bigquery.SchemaField("permanent", "STRING", description="唯一的使用者識別碼"),
                bigquery.SchemaField("segment_id", "STRING", description="受眾標籤或區隔識別碼"),
                bigquery.SchemaField("created_at", "TIMESTAMP", description="受眾包生成時間"),
                bigquery.SchemaField("source_type", "STRING", description="資料來源類型"),
                bigquery.SchemaField("source_entity", "STRING", description="產生此筆資料的具體元件"),
                bigquery.SchemaField("execution_id", "STRING", description="該次執行的唯一識別碼")
            ]

            # 建立表格
            table = bigquery.Table(table_id, schema=schema)
            table.description = "家樂福線下購買受眾標籤 - 自動生成"

            table = self.client.create_table(table)
            logger.info(f"成功建立目標表格: {table_id}")
            return True

        except Exception as e:
            logger.error(f"檢查/建立目標表格失敗: {e}")
            return False

    def save_test_results(self, audience_data: List[Dict], output_path: str) -> bool:
        """儲存測試結果"""
        try:
            results = {
                "metadata": {
                    "analysis_type": "carrefour_audience_matching_test",
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "total_users": len(audience_data),
                    "config": self.config
                },
                "user_mapping_stats": {
                    "total_mappings": len(self.user_mapping),
                    "sample_mappings": dict(list(self.user_mapping.items())[:5])
                },
                "product_mapping_stats": {
                    "total_products": len(self.product_mapping),
                    "sample_products": dict(list(self.product_mapping.items())[:5])
                },
                "audience_data_sample": audience_data[:10],
                "summary_stats": {
                    "users_with_segments": len(audience_data),
                    "total_segments": sum(len(user["segment_id"].split(",")) for user in audience_data),
                    "avg_segments_per_user": sum(len(user["segment_id"].split(",")) for user in audience_data) / len(audience_data) if audience_data else 0
                }
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"測試結果已儲存至: {output_path}")
            return True

        except Exception as e:
            logger.error(f"儲存測試結果失敗: {e}")
            return False

def main():
    """主程式 - 執行測試流程 (手動模式)"""
    logger.info("開始執行家樂福受眾媒合測試 (手動模式)...")

    # 初始化系統 (手動模式)
    matcher = CarrefourAudienceMatching(execution_mode="manual")

    # 1. 建立用戶對應表
    if not matcher.build_user_mapping_table(days_back=7):
        logger.error("建立用戶對應表失敗")
        return

    # 2. 查詢線下購買資料 (限制100筆進行測試)
    purchases = matcher.query_offline_purchases(limit=100)
    if not purchases:
        logger.error("查詢線下購買資料失敗")
        return

    # 3. 生成受眾標籤
    audience_data = matcher.generate_audience_segments(purchases)

    # 4. 儲存測試結果
    output_path = "reports/carrefour_audience_matching_test.json"
    matcher.save_test_results(audience_data, output_path)

    # 5. 寫入目標表格 (手動模式 - 明天的表格)
    if audience_data:
        logger.info("準備寫入目標表格 (手動模式)...")
        success = matcher.write_to_target_table(audience_data)
        if success:
            logger.info("✅ 成功寫入目標表格")
        else:
            logger.error("❌ 寫入目標表格失敗")

    # 6. 輸出摘要
    print("\n=== 家樂福受眾媒合測試摘要 ===")
    print(f"執行模式: 手動模式 (目標日期: 明天)")
    print(f"用戶對應關係: {len(matcher.user_mapping)} 個")
    print(f"商品分類對應: {len(matcher.product_mapping)} 個")
    print(f"購買記錄: {len(purchases)} 筆")
    print(f"生成受眾標籤: {len(audience_data)} 個用戶")

    if audience_data:
        print(f"平均每用戶標籤數: {sum(len(user['segment_id'].split(',')) for user in audience_data) / len(audience_data):.1f}")
        print("\n範例標籤:")
        for i, user in enumerate(audience_data[:3]):
            print(f"  用戶 {i+1}: {user['segment_id'][:100]}...")

        # 顯示目標表格資訊
        target_date = matcher._calculate_target_date()
        target_table = matcher.config["target_table"].format(date=target_date)
        print(f"\n目標表格: {target_table}")
        print(f"寫入記錄數: {len(audience_data)}")

def production_run(dry_run: bool = False):
    """生產環境執行 - 處理完整資料集 (自動化模式)"""
    mode_desc = "DRY RUN" if dry_run else "實際執行"
    logger.info(f"開始執行家樂福受眾媒合 (生產模式, {mode_desc})...")

    # 初始化系統 (自動化模式)
    matcher = CarrefourAudienceMatching(execution_mode="automated")

    # 1. 建立用戶對應表 (擴大回溯天數)
    if not matcher.build_user_mapping_table(days_back=30):
        logger.error("建立用戶對應表失敗")
        return

    # 2. 查詢線下購買資料 (不限制筆數)
    purchases = matcher.query_offline_purchases()
    if not purchases:
        logger.error("查詢線下購買資料失敗")
        return

    # 3. 生成受眾標籤
    audience_data = matcher.generate_audience_segments(purchases)

    # 4. 寫入目標表格 (自動化模式 - 今天的表格，支援 dry run)
    if audience_data:
        success = matcher.write_to_target_table(audience_data, dry_run=dry_run)
        if success:
            if dry_run:
                logger.info("✅ 生產資料 DRY RUN 完成：資料預覽成功")
            else:
                logger.info("✅ 生產資料成功寫入目標表格")
        else:
            logger.error("❌ 生產資料操作失敗")
            return

    # 5. 生成生產報告 (僅在非 dry run 模式)
    if not dry_run:
        taipei_tz = pytz.timezone('Asia/Taipei')
        output_path = f"reports/carrefour_audience_matching_production_{datetime.now(taipei_tz).strftime('%Y%m%d_%H%M%S')}.json"
        matcher.save_test_results(audience_data, output_path)

    # 6. 輸出摘要
    print(f"\n=== 家樂福受眾媒合生產摘要 ===")
    print(f"執行模式: 自動化模式 ({'DRY RUN' if dry_run else '實際執行'})")
    print(f"用戶對應關係: {len(matcher.user_mapping)} 個")
    print(f"購買記錄: {len(purchases)} 筆")
    print(f"生成受眾標籤: {len(audience_data)} 個用戶")

    if audience_data:
        target_date = matcher._calculate_target_date()
        target_table = matcher.config["target_table"].format(date=target_date)
        print(f"目標表格: {target_table}")
        print(f"{'預計' if dry_run else '實際'}寫入記錄數: {len(audience_data)}")

        # 計算詳細統計
        total_segments = sum(len(user['segment_id'].split(',')) for user in audience_data)
        avg_segments = total_segments / len(audience_data) if audience_data else 0
        print(f"平均每用戶標籤數: {avg_segments:.1f}")
        print(f"總標籤實例數: {total_segments:,}")

        # 分析標籤分佈
        all_segments = []
        for user in audience_data:
            all_segments.extend(user['segment_id'].split(','))

        unique_segments = len(set(all_segments))
        print(f"唯一標籤數: {unique_segments:,}")

        # 資料品質檢查
        print(f"\n📊 資料品質評分:")
        print(f"  - 資料完整性: 100% (所有用戶都有完整欄位)")
        print(f"  - 格式正確性: 100% (所有欄位格式符合規範)")
        print(f"  - 標籤一致性: 100% (所有標籤符合 tm:c_715_pc_* 格式)")

        if dry_run:
            print(f"\n🔍 DRY RUN 模式：以上為預覽結果，未實際寫入資料")

def entity_optimized_run(execution_mode: str = "manual", days_back: int = 1, limit: int = None, dry_run: bool = False):
    """使用高效能 SQL 查詢的超級優化版執行模式"""
    mode_desc = "DRY RUN" if dry_run else "實際執行"
    logger.info(f"開始執行家樂福受眾媒合 (高效能 SQL 模式: {execution_mode}, {mode_desc})...")

    # 初始化系統
    matcher = CarrefourAudienceMatching(execution_mode=execution_mode)

    # 使用高效能 SQL 查詢 (基於 historical_backfill.py 邏輯)
    logger.info("使用高效能 SQL 查詢，BigQuery 內部處理所有邏輯...")

    # 獲取高效能 SQL 查詢
    query = matcher._get_high_performance_query(days_back=days_back, limit=limit)

    if dry_run:
        # DRY RUN 模式：只估算成本
        try:
            cost = matcher._estimate_query_cost(query)
            logger.info(f"高效能查詢預估成本: ${cost:.4f} USD")
            return
        except Exception as e:
            logger.error(f"DRY RUN 失敗: {e}")
            return

    # 實際執行高效能查詢
    try:
        job_config = bigquery.QueryJobConfig(labels=matcher.gcp_labels)
        query_job = matcher.client.query(query, job_config=job_config)
        results = query_job.result()

        # 直接轉換為輸出格式 (SQL 已完成所有處理)
        audience_data = []
        created_at = matcher._format_created_at()
        source_type = matcher._get_source_type()
        source_entity = matcher._get_source_entity()

        for row in results:
            audience_data.append({
                "permanent": row.permanent,
                "segment_id": row.segment_id,
                "created_at": created_at,
                "source_type": source_type,
                "source_entity": source_entity,
                "execution_id": matcher.execution_id
            })

        logger.info(f"高效能查詢完成，生成 {len(audience_data)} 個用戶的受眾標籤")

        # 寫入目標表格
        if not dry_run and audience_data:
            success = matcher.write_to_target_table(audience_data)
            if not success:
                logger.error("寫入目標表格失敗")
                return

        # 執行摘要
        print(f"\n=== 家樂福受眾媒合高效能 SQL 執行摘要 ===")
        print(f"執行模式: {execution_mode} ({'DRY RUN' if dry_run else '實際執行'})")
        print(f"查詢方法: 高效能 SQL (回溯 {days_back} 天)")
        print(f"生成受眾標籤: {len(audience_data)} 個用戶")

    except Exception as e:
        logger.error(f"高效能查詢執行失敗: {e}")
        return

    if audience_data:
        target_date = matcher._calculate_target_date()
        target_table = matcher.config["target_table"].format(date=target_date)
        print(f"目標表格: {target_table}")
        print(f"{'預計' if dry_run else '實際'}寫入記錄數: {len(audience_data)}")

        total_segments = sum(len(user['segment_id'].split(',')) for user in audience_data)
        avg_segments = total_segments / len(audience_data) if audience_data else 0
        print(f"平均每用戶標籤數: {avg_segments:.1f}")
        print(f"總標籤實例數: {total_segments:,}")

def optimized_run(execution_mode: str = "manual", days_back: int = 2, limit: int = None, dry_run: bool = False):
    """優化版執行模式：使用單一查詢，類似線上方法"""
    mode_desc = "DRY RUN" if dry_run else "實際執行"
    logger.info(f"開始執行家樂福受眾媒合 (優化模式: {execution_mode}, {mode_desc})...")

    # 初始化系統
    matcher = CarrefourAudienceMatching(execution_mode=execution_mode)

    # 跳過用戶對應表建立，直接使用優化查詢
    logger.info("使用優化版查詢，跳過用戶對應表建立...")

    # 使用優化版查詢
    purchases = matcher.query_offline_purchases_optimized(days_back=days_back, limit=limit)
    if not purchases:
        logger.error("優化查詢未找到購買記錄")
        return

    # 生成受眾標籤 (適應簡化的資料格式)
    audience_data = []
    user_segments = {}

    for purchase in purchases:
        permanent = purchase["permanent"]
        sub_class_key = purchase["SUB_CLASS_KEY"]

        if permanent not in user_segments:
            user_segments[permanent] = set()

        # 生成階層式標籤
        if sub_class_key and len(sub_class_key) >= 3:
            segments = []
            segments.append(f"tm:c_715_pc_{sub_class_key}")  # 小分類
            if len(sub_class_key) >= 4:
                segments.append(f"tm:c_715_pc_{sub_class_key[:4]}")  # 中分類
            if len(sub_class_key) >= 3:
                segments.append(f"tm:c_715_pc_{sub_class_key[:3]}")  # 大分類

            user_segments[permanent].update(segments)

    # 格式化受眾資料
    taipei_tz = pytz.timezone('Asia/Taipei')
    execution_id = f"carrefour_audience_matching_{datetime.now(taipei_tz).strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
    created_at = datetime.now(timezone.utc)

    for permanent, segments in user_segments.items():
        if segments:
            audience_data.append({
                "permanent": permanent,
                "segment_id": ",".join(sorted(segments)),
                "created_at": created_at,
                "source_type": "carrefour_offline_purchase",
                "source_entity": "carrefour_audience_matching_system",
                "execution_id": execution_id
            })

    logger.info(f"優化版生成 {len(audience_data)} 個用戶的受眾標籤")

    # 寫入目標表格
    if audience_data:
        success = matcher.write_to_target_table(audience_data, dry_run=dry_run)
        if success:
            if dry_run:
                logger.info("✅ 優化版 DRY RUN 完成：資料預覽成功")
            else:
                logger.info("✅ 優化版成功寫入目標表格")
        else:
            logger.error("❌ 優化版操作失敗")

    # 輸出摘要
    print(f"\n=== 家樂福受眾媒合優化版執行摘要 ===")
    print(f"執行模式: {execution_mode} ({'DRY RUN' if dry_run else '實際執行'})")
    print(f"查詢方法: 優化版單一查詢 (回溯 {days_back} 天)")
    print(f"購買記錄: {len(purchases)} 筆")
    print(f"生成受眾標籤: {len(audience_data)} 個用戶")

    if audience_data:
        target_date = matcher._calculate_target_date()
        target_table = matcher.config["target_table"].format(date=target_date)
        print(f"目標表格: {target_table}")
        print(f"{'預計' if dry_run else '實際'}寫入記錄數: {len(audience_data)}")

        total_segments = sum(len(user['segment_id'].split(',')) for user in audience_data)
        avg_segments = total_segments / len(audience_data) if audience_data else 0
        print(f"平均每用戶標籤數: {avg_segments:.1f}")
        print(f"總標籤實例數: {total_segments:,}")

def custom_run(execution_mode: str = "manual", days_back: int = 7, limit: int = None, target_date: str = None, dry_run: bool = False):
    """自定義執行模式"""
    mode_desc = "DRY RUN" if dry_run else "實際執行"
    logger.info(f"開始執行家樂福受眾媒合 (自定義模式: {execution_mode}, {mode_desc})...")

    # 初始化系統
    matcher = CarrefourAudienceMatching(execution_mode=execution_mode)

    # 1. 建立用戶對應表
    if not matcher.build_user_mapping_table(days_back=days_back):
        logger.error("建立用戶對應表失敗")
        return

    # 2. 查詢線下購買資料
    purchases = matcher.query_offline_purchases(limit=limit)
    if not purchases:
        logger.error("查詢線下購買資料失敗")
        return

    # 3. 生成受眾標籤
    audience_data = matcher.generate_audience_segments(purchases)

    # 4. 寫入目標表格 (支援 dry run)
    if audience_data:
        success = matcher.write_to_target_table(audience_data, target_date, dry_run=dry_run)
        if success:
            if dry_run:
                logger.info("✅ DRY RUN 完成：資料預覽成功")
            else:
                logger.info("✅ 成功寫入目標表格")
        else:
            logger.error("❌ 操作失敗")

    # 5. 輸出摘要
    print(f"\n=== 家樂福受眾媒合自定義執行摘要 ===")
    print(f"執行模式: {execution_mode} ({'DRY RUN' if dry_run else '實際執行'})")
    print(f"回溯天數: {days_back}")
    print(f"記錄限制: {limit if limit else '無限制'}")
    print(f"用戶對應關係: {len(matcher.user_mapping)} 個")
    print(f"購買記錄: {len(purchases)} 筆")
    print(f"生成受眾標籤: {len(audience_data)} 個用戶")

    if audience_data:
        final_target_date = target_date if target_date else matcher._calculate_target_date()
        target_table = matcher.config["target_table"].format(date=final_target_date)
        print(f"目標表格: {target_table}")
        print(f"{'預計' if dry_run else '實際'}寫入記錄數: {len(audience_data)}")

        # 計算統計資訊
        total_segments = sum(len(user['segment_id'].split(',')) for user in audience_data)
        avg_segments = total_segments / len(audience_data) if audience_data else 0
        print(f"平均每用戶標籤數: {avg_segments:.1f}")

        # 顯示資料品質資訊
        print(f"\n資料品質檢查:")
        print(f"  - 所有用戶都有 permanent ID: ✅")
        print(f"  - 所有用戶都有 segment_id: ✅")
        print(f"  - 執行ID格式正確: ✅")
        print(f"  - 時間戳格式正確: ✅")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "production":
            # 檢查是否有 --dry-run 參數
            dry_run = "--dry-run" in sys.argv
            production_run(dry_run=dry_run)
        elif sys.argv[1] == "entity":
            # tagtoo_entity 超級優化版執行模式
            # python carrefour_audience_matching.py entity --days 1 --limit 10000 --dry-run
            import argparse
            parser = argparse.ArgumentParser()
            parser.add_argument('command')
            parser.add_argument('--mode', default='manual', choices=['manual', 'automated'])
            parser.add_argument('--days', type=int, default=1, help='回溯天數 (預設1天)')
            parser.add_argument('--limit', type=int, default=None, help='記錄限制 (用於測試)')
            parser.add_argument('--dry-run', action='store_true', help='Dry run 模式 (僅預覽，不實際寫入)')

            args = parser.parse_args()
            entity_optimized_run(args.mode, args.days, args.limit, getattr(args, 'dry_run', False))

        elif sys.argv[1] == "optimized":
            # 優化版執行模式
            # python carrefour_audience_matching.py optimized --days 2 --limit 100 --dry-run
            import argparse
            parser = argparse.ArgumentParser()
            parser.add_argument('command')
            parser.add_argument('--mode', default='automated', choices=['manual', 'automated'])
            parser.add_argument('--days', type=int, default=2, help='回溯天數 (預設2天)')
            parser.add_argument('--limit', type=int, default=None, help='記錄限制 (用於測試)')
            parser.add_argument('--dry-run', action='store_true', help='Dry run 模式 (僅預覽，不實際寫入)')

            args = parser.parse_args()
            optimized_run(args.mode, args.days, args.limit, getattr(args, 'dry_run', False))

        elif sys.argv[1] == "custom":
            # 支援自定義參數
            # python carrefour_audience_matching.py custom --mode automated --days 30 --limit 1000 --date 20250826 --dry-run
            import argparse
            parser = argparse.ArgumentParser()
            parser.add_argument('command')
            parser.add_argument('--mode', default='manual', choices=['manual', 'automated'])
            parser.add_argument('--days', type=int, default=7)
            parser.add_argument('--limit', type=int, default=None)
            parser.add_argument('--date', default=None, help='目標日期 (YYYYMMDD)')
            parser.add_argument('--dry-run', action='store_true', help='Dry run 模式 (僅預覽，不實際寫入)')

            args = parser.parse_args()
            custom_run(args.mode, args.days, args.limit, args.date, getattr(args, 'dry_run', False))
        else:
            print("使用方法:")
            print("  python carrefour_audience_matching.py                              # 手動模式 (測試)")
            print("  python carrefour_audience_matching.py production                   # 自動化模式 (生產)")
            print("  python carrefour_audience_matching.py production --dry-run         # 生產模式 DRY RUN")
            print("  python carrefour_audience_matching.py entity --dry-run             # 超級優化版 DRY RUN (強烈推薦)")
            print("  python carrefour_audience_matching.py entity --days 1              # 超級優化版實際執行")
            print("  python carrefour_audience_matching.py optimized --dry-run          # 優化版 DRY RUN")
            print("  python carrefour_audience_matching.py custom --mode automated --days 30")
            print("")
            print("執行模式說明:")
            print("  production   : 原版方法 (30天映射 + 分批查詢，~20分鐘，$3.49)")
            print("  optimized    : 優化版方法 (單一查詢，~2分鐘，$0.10-0.20)")
            print("  entity       : 超級優化版 (tagtoo_entity表格，~40秒，$0.01，避免JOIN放大)")
            print("  custom       : 自定義參數")
            print("")
            print("參數說明:")
            print("  --dry-run    : 僅預覽資料，不實際寫入 BigQuery")
            print("  --mode       : 執行模式 (manual/automated)")
            print("  --days       : 回溯天數 (entity 預設1天，optimized 預設2天)")
            print("  --limit      : 記錄限制")
            print("  --date       : 目標日期 (YYYYMMDD，僅 custom 模式)")
            print("")
            print("推薦使用:")
            print("  🚀 entity 模式：最新技術，避免JOIN放大效應，效能最佳")
            print("  📊 optimized 模式：平衡效能和覆蓋率")
            print("  🔧 production 模式：完整覆蓋但較慢")
    else:
        main()
