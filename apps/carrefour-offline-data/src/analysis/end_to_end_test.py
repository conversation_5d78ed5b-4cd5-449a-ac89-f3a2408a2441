#!/usr/bin/env python3
"""
家樂福受眾媒合端到端測試

完整測試從原始資料到最終輸出的整個流程

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict
import logging

# 導入自定義模組
from carrefour_audience_matching import CarrefourAudienceMatching
from data_quality_validator import DataQualityValidator
from product_category_analysis import ProductCategoryAnalyzer
from segment_generation_test import SegmentGenerationTester

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EndToEndTester:
    """端到端測試器"""

    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None

    def run_complete_test(self) -> Dict:
        """執行完整的端到端測試"""
        logger.info("開始執行家樂福受眾媒合端到端測試...")
        self.start_time = datetime.now()

        try:
            # 階段 1: 商品分類分析
            self.test_product_category_analysis()

            # 階段 2: 標籤生成測試
            self.test_segment_generation()

            # 階段 3: 受眾媒合測試
            self.test_audience_matching()

            # 階段 4: 資料品質驗證
            self.test_data_quality()

            # 階段 5: 整合驗證
            self.test_integration()

            self.end_time = datetime.now()
            self.generate_final_report()

            return self.test_results

        except Exception as e:
            logger.error(f"端到端測試失敗: {e}")
            self.test_results["error"] = str(e)
            return self.test_results

    def test_product_category_analysis(self):
        """測試商品分類分析"""
        logger.info("階段 1: 測試商品分類分析...")

        try:
            analyzer = ProductCategoryAnalyzer("docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv")

            # 載入和分析
            success = analyzer.load_data()
            if not success:
                raise Exception("載入商品分類資料失敗")

            hierarchy = analyzer.analyze_hierarchy()
            mapping = analyzer.create_segment_mapping()

            self.test_results["product_category"] = {
                "status": "success",
                "total_products": len(analyzer.df),
                "mapping_count": len(mapping),
                "hierarchy_accuracy": hierarchy.get("hierarchy_rules", {}).get("validation_results", {}).get("hierarchy_accuracy", 0),
                "unique_categories": hierarchy.get("unique_categories", {})
            }

            logger.info("✅ 商品分類分析測試通過")

        except Exception as e:
            logger.error(f"❌ 商品分類分析測試失敗: {e}")
            self.test_results["product_category"] = {
                "status": "failed",
                "error": str(e)
            }

    def test_segment_generation(self):
        """測試標籤生成"""
        logger.info("階段 2: 測試標籤生成...")

        try:
            tester = SegmentGenerationTester()
            results = tester.run_all_tests()

            summary = results.get("test_summary", {})

            self.test_results["segment_generation"] = {
                "status": "success",
                "total_tests": summary.get("total_tests", 0),
                "passed_tests": summary.get("passed_tests", 0),
                "success_rate": summary.get("success_rate", 0),
                "test_breakdown": summary.get("test_breakdown", {})
            }

            logger.info("✅ 標籤生成測試通過")

        except Exception as e:
            logger.error(f"❌ 標籤生成測試失敗: {e}")
            self.test_results["segment_generation"] = {
                "status": "failed",
                "error": str(e)
            }

    def test_audience_matching(self):
        """測試受眾媒合"""
        logger.info("階段 3: 測試受眾媒合...")

        try:
            matcher = CarrefourAudienceMatching()

            # 建立用戶對應表
            user_mapping_success = matcher.build_user_mapping_table(days_back=7)
            if not user_mapping_success:
                raise Exception("建立用戶對應表失敗")

            # 查詢購買資料
            purchases = matcher.query_offline_purchases(limit=50)  # 限制50筆進行測試
            if not purchases:
                raise Exception("查詢線下購買資料失敗")

            # 生成受眾標籤
            audience_data = matcher.generate_audience_segments(purchases)

            self.test_results["audience_matching"] = {
                "status": "success",
                "user_mappings": len(matcher.user_mapping),
                "product_mappings": len(matcher.product_mapping),
                "purchase_records": len(purchases),
                "audience_users": len(audience_data),
                "avg_segments_per_user": sum(len(user['segment_id'].split(',')) for user in audience_data) / len(audience_data) if audience_data else 0
            }

            # 儲存受眾資料供後續測試使用
            self.audience_data = audience_data

            logger.info("✅ 受眾媒合測試通過")

        except Exception as e:
            logger.error(f"❌ 受眾媒合測試失敗: {e}")
            self.test_results["audience_matching"] = {
                "status": "failed",
                "error": str(e)
            }
            self.audience_data = []

    def test_data_quality(self):
        """測試資料品質"""
        logger.info("階段 4: 測試資料品質...")

        try:
            if not hasattr(self, 'audience_data') or not self.audience_data:
                raise Exception("沒有受眾資料可驗證")

            validator = DataQualityValidator()
            validation_results = validator.validate_audience_data(self.audience_data)

            metrics = validator.quality_metrics

            self.test_results["data_quality"] = {
                "status": "success",
                "total_records": metrics.get("total_records", 0),
                "completeness": metrics.get("data_completeness", 0),
                "accuracy": metrics.get("data_accuracy", 0),
                "consistency": metrics.get("data_consistency", 0),
                "validity": metrics.get("data_validity", 0),
                "overall_score": metrics.get("overall_score", 0),
                "grade": metrics.get("grade", "N/A")
            }

            logger.info("✅ 資料品質測試通過")

        except Exception as e:
            logger.error(f"❌ 資料品質測試失敗: {e}")
            self.test_results["data_quality"] = {
                "status": "failed",
                "error": str(e)
            }

    def test_integration(self):
        """測試整合功能"""
        logger.info("階段 5: 測試整合功能...")

        try:
            integration_tests = {
                "schema_consistency": self._test_schema_consistency(),
                "data_flow_integrity": self._test_data_flow_integrity(),
                "performance_metrics": self._test_performance_metrics()
            }

            all_passed = all(test["passed"] for test in integration_tests.values())

            self.test_results["integration"] = {
                "status": "success" if all_passed else "partial",
                "tests": integration_tests,
                "overall_passed": all_passed
            }

            if all_passed:
                logger.info("✅ 整合測試通過")
            else:
                logger.warning("⚠️ 整合測試部分通過")

        except Exception as e:
            logger.error(f"❌ 整合測試失敗: {e}")
            self.test_results["integration"] = {
                "status": "failed",
                "error": str(e)
            }

    def _test_schema_consistency(self) -> Dict:
        """測試 Schema 一致性"""
        try:
            # 檢查受眾資料是否符合目標表格 schema
            required_fields = ["permanent", "segment_id", "created_at", "source_type", "source_entity", "execution_id"]

            if hasattr(self, 'audience_data') and self.audience_data:
                sample_record = self.audience_data[0]
                missing_fields = [field for field in required_fields if field not in sample_record]

                return {
                    "passed": len(missing_fields) == 0,
                    "missing_fields": missing_fields,
                    "message": "Schema 一致性檢查" + ("通過" if len(missing_fields) == 0 else f"失敗，缺少欄位: {missing_fields}")
                }
            else:
                return {
                    "passed": False,
                    "message": "沒有受眾資料可檢查"
                }

        except Exception as e:
            return {
                "passed": False,
                "error": str(e)
            }

    def _test_data_flow_integrity(self) -> Dict:
        """測試資料流完整性"""
        try:
            # 檢查資料流各階段是否正常
            stages = ["product_category", "segment_generation", "audience_matching", "data_quality"]
            failed_stages = [stage for stage in stages if self.test_results.get(stage, {}).get("status") != "success"]

            return {
                "passed": len(failed_stages) == 0,
                "failed_stages": failed_stages,
                "message": "資料流完整性檢查" + ("通過" if len(failed_stages) == 0 else f"失敗，失敗階段: {failed_stages}")
            }

        except Exception as e:
            return {
                "passed": False,
                "error": str(e)
            }

    def _test_performance_metrics(self) -> Dict:
        """測試效能指標"""
        try:
            if not self.start_time or not self.end_time:
                return {
                    "passed": False,
                    "message": "無法計算執行時間"
                }

            execution_time = (self.end_time - self.start_time).total_seconds()

            # 效能標準：整個測試應在 5 分鐘內完成
            performance_passed = execution_time < 300

            return {
                "passed": performance_passed,
                "execution_time_seconds": execution_time,
                "message": f"執行時間: {execution_time:.1f} 秒" + (" (符合效能要求)" if performance_passed else " (超過效能要求)")
            }

        except Exception as e:
            return {
                "passed": False,
                "error": str(e)
            }

    def generate_final_report(self):
        """生成最終報告"""
        logger.info("生成最終測試報告...")

        # 計算總體統計
        total_tests = 5  # 5個主要階段
        passed_tests = sum(1 for stage in ["product_category", "segment_generation", "audience_matching", "data_quality", "integration"]
                          if self.test_results.get(stage, {}).get("status") == "success")

        execution_time = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0

        summary = {
            "test_summary": {
                "total_stages": total_tests,
                "passed_stages": passed_tests,
                "success_rate": passed_tests / total_tests,
                "execution_time_seconds": execution_time,
                "overall_status": "success" if passed_tests == total_tests else "partial" if passed_tests > 0 else "failed"
            },
            "timestamp": {
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None
            }
        }

        self.test_results["summary"] = summary

        # 儲存報告
        output_path = f"reports/end_to_end_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"最終測試報告已儲存至: {output_path}")

        # 輸出摘要
        self.print_final_summary()

    def print_final_summary(self):
        """輸出最終摘要"""
        summary = self.test_results.get("summary", {}).get("test_summary", {})

        print("\n" + "="*60)
        print("家樂福受眾媒合端到端測試結果")
        print("="*60)

        print(f"總測試階段: {summary.get('total_stages', 0)}")
        print(f"通過階段: {summary.get('passed_stages', 0)}")
        print(f"成功率: {summary.get('success_rate', 0):.1%}")
        print(f"執行時間: {summary.get('execution_time_seconds', 0):.1f} 秒")
        print(f"整體狀態: {summary.get('overall_status', 'unknown').upper()}")

        print("\n階段詳情:")
        stages = ["product_category", "segment_generation", "audience_matching", "data_quality", "integration"]
        for i, stage in enumerate(stages, 1):
            result = self.test_results.get(stage, {})
            status = result.get("status", "unknown")
            emoji = "✅" if status == "success" else "⚠️" if status == "partial" else "❌"
            print(f"  {i}. {stage.replace('_', ' ').title()}: {emoji} {status.upper()}")

        # 顯示關鍵指標
        if self.test_results.get("audience_matching", {}).get("status") == "success":
            matching = self.test_results["audience_matching"]
            print(f"\n關鍵指標:")
            print(f"  - 用戶對應關係: {matching.get('user_mappings', 0):,} 個")
            print(f"  - 購買記錄: {matching.get('purchase_records', 0):,} 筆")
            print(f"  - 生成受眾標籤: {matching.get('audience_users', 0):,} 個用戶")
            print(f"  - 平均每用戶標籤數: {matching.get('avg_segments_per_user', 0):.1f}")

        if self.test_results.get("data_quality", {}).get("status") == "success":
            quality = self.test_results["data_quality"]
            print(f"  - 資料品質評分: {quality.get('overall_score', 0):.1%} ({quality.get('grade', 'N/A')})")

def main():
    """主程式"""
    tester = EndToEndTester()
    tester.run_complete_test()

if __name__ == "__main__":
    main()
