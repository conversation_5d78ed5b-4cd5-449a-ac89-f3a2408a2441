#!/usr/bin/env python3
"""
家樂福 ph_id 直接處理模組

此模組實作直接使用 ph_id 上傳到 Meta 的功能，避免透過 tagtoo_entity 媒合過程中的使用者流失。

主要功能：
1. 直接從 carrefour_offline_transaction_day 提取 ph_id 和商品分類
2. 生成去重化的 ph_id 與 segment_id 對應關係
3. 產生符合 Meta 要求的 AVRO 檔案
4. 上傳到指定的 Cloud Storage 位置
5. 觸發 Pub/Sub 通知

Author: AI Assistant
Date: 2025-09-12
Version: 1.0.0
"""

import json
import logging
import uuid
import psutil
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from functools import partial
from collections import deque
from concurrent import futures

import pytz
from google.cloud import bigquery
from google.cloud import storage
from google.cloud import pubsub_v1
from google import api_core

from tools.pubsub_notifier import PubSubNotifier

# 設定日誌
logger = logging.getLogger(__name__)


# Configure the retry settings for Pub/Sub
custom_retry = api_core.retry.Retry(
    initial=0.250,  # seconds (default: 0.1)
    maximum=90.0,  # seconds (default: 60.0)
    multiplier=1.45,  # default: 1.3
    deadline=120.0,  # seconds (default: 60.0)
    predicate=api_core.retry.if_exception_type(
        api_core.exceptions.Aborted,
        api_core.exceptions.DeadlineExceeded,
        api_core.exceptions.InternalServerError,
        api_core.exceptions.ResourceExhausted,
        api_core.exceptions.ServiceUnavailable,
        api_core.exceptions.Unknown,
        api_core.exceptions.Cancelled,
    ),
)

# Configure the batch to publish as soon as there are 1k messages
# or 3MB of data, or 1 second has passed.
batch_settings = pubsub_v1.types.BatchSettings(
    max_messages=1000,  # default 100
    max_bytes=3072,  # default 1 MiB
    max_latency=1,  # default 10 ms
)


class PublisherClient:
    """Context manageable publisher client configured with retry settings.
    Example:
        >>> with PublisherClient('[PROJECT]', '[TOPIC]') as publisher:
        >>>     for i in range(10):
        >>>         publisher.add_message(f"Message {i}")
    """
    def __init__(self, project_id, topic_name, concurrent=3000, credentials=None, *args, **kwargs):
        self._client = pubsub_v1.PublisherClient(batch_settings, credentials=credentials, *args, **kwargs)
        self.topic_path = self._client.topic_path(project_id, topic_name)
        self.tasks = []
        self.future_q = deque(maxlen=concurrent)

    def _produce_message(self):
        for task in self.tasks:
            future = task()
            self.future_q.appendleft(future)
            if len(self.future_q) == self.future_q.maxlen:
                logger.info('Future queue full, yielding control ...')
                yield

    def _consume_message(self):
        while True:
            # Blocking until all messages in queue are published
            futures.wait(self.future_q, return_when=futures.ALL_COMPLETED)
            self.future_q.clear()
            logger.info('Future queue empty, yielding control ...')
            yield

    def add_message(self, data, **kwargs):
        for k, v in kwargs.items():
            assert isinstance(v, bytes) or isinstance(v, str), (
                "Message metadata attribute should be text strings or byte strings"
            )
        # Data must be a bytestring
        data = data.encode("utf-8")
        task = partial(self._client.publish, self.topic_path, data=data, retry=custom_retry, **kwargs)
        self.tasks.append(task)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        producer = self._produce_message()
        consumer = self._consume_message()
        while True:
            try:
                next(producer)
            except StopIteration:
                break
            finally:
                next(consumer)
        if exc_type:
            logger.exception(exc_type)
        return False

class PhIdDirectProcessor:
    """ph_id 直接處理器"""

    def __init__(self, config_path: Optional[str] = None, data_range_days: int = 1):
        """
        初始化 ph_id 直接處理器

        Args:
            config_path: 配置檔案路徑
            data_range_days: 資料範圍天數，預設 1 天（記憶體優化：預估 <200K ph_id，<1800Mi 記憶體使用）
        """
        self.data_range_days = data_range_days
        self.config = self._load_config(config_path)
        self.client = bigquery.Client()
        # 與測試相容：提供 publisher 屬性（實務上仍使用本模組 PublisherClient 發送）
        self.publisher = pubsub_v1.PublisherClient()

        self.storage_client = storage.Client()

        # 生成執行 ID
        taipei_tz = pytz.timezone('Asia/Taipei')
        self.execution_id = f"carrefour_ph_id_direct_{datetime.now(taipei_tz).strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # GCP 標籤
        self.gcp_labels = {
            "service": "carrefour-offline-data",
            "operation": "ph_id_direct",
            "execution_id": self.execution_id[:63]  # GCP label 長度限制
        }

        logger.info(f"PhIdDirectProcessor 初始化完成，執行 ID: {self.execution_id}")

    def _log_memory_usage(self, step_name: str) -> Dict[str, Any]:
        """
        記錄當前記憶體使用量

        Args:
            step_name: 步驟名稱

        Returns:
            記憶體使用統計資訊
        """
        try:
            # 獲取當前進程記憶體使用
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()

            # 記憶體使用量 (MB)
            current_mb = memory_info.rss / 1024 / 1024

            # Cloud Function 記憶體限制 (4096 MB)
            limit_mb = 4096
            percentage = (current_mb / limit_mb) * 100

            # 系統記憶體資訊
            system_memory = psutil.virtual_memory()
            system_available_mb = system_memory.available / 1024 / 1024

            memory_stats = {
                "step": step_name,
                "current_mb": round(current_mb, 2),
                "limit_mb": limit_mb,
                "percentage": round(percentage, 2),
                "system_available_mb": round(system_available_mb, 2),
                "warning": percentage > 80,
                "critical": percentage > 95
            }

            # 記錄到日誌
            status = "🔴 CRITICAL" if memory_stats["critical"] else "🟡 WARNING" if memory_stats["warning"] else "🟢 OK"
            logger.info(f"記憶體使用 [{step_name}]: {current_mb:.1f} MB / {limit_mb} MB ({percentage:.1f}%) {status}")

            if memory_stats["warning"]:
                logger.warning(f"記憶體使用接近限制！當前: {current_mb:.1f} MB, 限制: {limit_mb} MB")

            return memory_stats

        except Exception as e:
            logger.error(f"記憶體監控失敗: {e}")
            return {
                "step": step_name,
                "error": str(e),
                "current_mb": 0,
                "limit_mb": 2048,
                "percentage": 0
            }

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """載入配置"""
        default_config = {
            "source_table": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
            "target_dataset": "tagtoo-tracking.event_prod",
            "gcs_bucket": "tagtoo-ml-workflow",
            "gcs_path_prefix": "carrefour_ph_id_direct",
            "pubsub_project": "tagtoo-tracking",
            "pubsub_topic": "lta-prod",
            "ec_id": "715",  # 改為字串格式，與參考實作一致
            "max_cost_usd": 10.0,
            "batch_size": 10000,
            "insert_batch_size": 5000  # 步驟2 批次插入大小（建議 2,000-5,000）
        }

        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def _calculate_target_date(self, target_date: Optional[str] = None) -> str:
        """
        計算目標日期

        Args:
            target_date: 指定的目標日期 (YYYY-MM-DD)，None 表示使用昨天

        Returns:
            目標日期字串 (YYYY-MM-DD)
        """
        if target_date and target_date != "auto":
            return target_date

        # 使用台灣時區的昨天
        taipei_tz = pytz.timezone('Asia/Taipei')
        yesterday = datetime.now(taipei_tz) - timedelta(days=1)
        return yesterday.strftime('%Y-%m-%d')

    def _estimate_query_cost(self, query: str) -> float:
        """估算查詢成本"""
        try:
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            query_job = self.client.query(query, job_config=job_config)

            # 計算成本 ($5 per TB)
            bytes_processed = query_job.total_bytes_processed
            cost_usd = (bytes_processed / (1024**4)) * 5  # TB to USD

            logger.info(f"查詢預估成本: ${cost_usd:.4f} USD ({bytes_processed:,} bytes)")
            return cost_usd

        except Exception as e:
            logger.warning(f"無法估算查詢成本: {e}")
            return 0.0

    def extract_ph_id_segments(self, target_date: str, dry_run: bool = True) -> List[Dict[str, Any]]:
        """
        提取 ph_id 和對應的商品分類標籤

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            dry_run: 是否為測試模式

        Returns:
            ph_id 和 segment_id 的對應列表
        """
        logger.info(f"開始提取 ph_id 和商品分類 (日期: {target_date}, dry_run: {dry_run})")

        # 修正查詢：處理完整資料集，在 dry_run 模式下限制結果數量
        # 使用 event_times 欄位進行日期過濾，提取最近 60 天的資料
        query = f"""
        WITH ph_id_hierarchical_segments AS (
          SELECT
            ph_id,
            item.SUB_CLASS_KEY,
            -- 生成層級標籤：例如 10000 -> [10000, 1000, 100]
            ARRAY(
              SELECT CONCAT('tm:c_715_pc_', SUBSTR(item.SUB_CLASS_KEY, 1, pos))
              FROM UNNEST(GENERATE_ARRAY(3, LENGTH(item.SUB_CLASS_KEY))) AS pos
              WHERE REGEXP_CONTAINS(SUBSTR(item.SUB_CLASS_KEY, 1, pos), r'^[0-9]+$')
            ) AS hierarchical_tags
          FROM `{self.config["source_table"]}`
          CROSS JOIN UNNEST(items) as item
          WHERE ph_id IS NOT NULL
            AND item.SUB_CLASS_KEY IS NOT NULL
            AND item.SUB_CLASS_KEY != ''
            AND event_times IS NOT NULL
            -- 基於最新資料日期提取指定天數的資料，減少記憶體使用並確保資料新鮮度
            AND DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(
              (SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times)))
               FROM `{self.config["source_table"]}`
               WHERE event_times IS NOT NULL),
              INTERVAL {self.data_range_days} DAY)
            AND REGEXP_CONTAINS(item.SUB_CLASS_KEY, r'^[0-9]+$')  -- 確保是純數字
            AND LENGTH(item.SUB_CLASS_KEY) >= 3  -- 至少 3 位數字
        ),
        ph_id_segments AS (
          SELECT
            TO_HEX(ph_id) as ph_id_hex,
            ARRAY_AGG(DISTINCT tag) as segment_ids
          FROM ph_id_hierarchical_segments
          CROSS JOIN UNNEST(hierarchical_tags) as tag
          GROUP BY ph_id
        )
        SELECT
          ph_id_hex,
          segment_ids,
          ARRAY_LENGTH(segment_ids) as segment_count
        FROM ph_id_segments
        ORDER BY segment_count DESC
        {f'LIMIT {self.config["batch_size"]}' if dry_run else ''}
        """

        # 估算成本
        cost = self._estimate_query_cost(query)
        if cost > self.config["max_cost_usd"]:
            raise ValueError(f"查詢成本 ${cost:.4f} 超過限制 ${self.config['max_cost_usd']}")

        try:
            # 執行查詢
            job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
            query_job = self.client.query(query, job_config=job_config)
            results = query_job.result()

            # 轉換結果
            ph_id_data = []
            for row in results:
                ph_id_data.append({
                    "ph_id_hex": row.ph_id_hex,
                    "segment_ids": list(row.segment_ids),
                    "segment_count": row.segment_count
                })

            logger.info(f"成功提取 {len(ph_id_data)} 個 ph_id 的商品分類資料")

            # 記錄步驟 1 完成後的記憶體使用
            self._log_memory_usage("步驟1_資料提取完成")

            return ph_id_data

        except Exception as e:
            logger.error(f"提取 ph_id 資料失敗: {e}")
            raise

    def generate_avro_export_query(self, target_date: str, output_uri: str) -> str:
        """
        生成 AVRO 檔案匯出查詢

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            output_uri: GCS 輸出路徑

        Returns:
            EXPORT DATA 查詢字串
        """
        query = f"""
        EXPORT DATA
          OPTIONS (
            uri = '{output_uri}',
            format = 'AVRO',
            OVERWRITE = TRUE,
            use_avro_logical_types = TRUE
          ) AS (

        WITH ph_id_hierarchical_segments AS (
          SELECT
            ph_id,
            item.SUB_CLASS_KEY,
            -- 生成層級標籤：例如 10000 -> [10000, 1000, 100]
            ARRAY(
              SELECT CONCAT('tm:c_715_pc_', SUBSTR(item.SUB_CLASS_KEY, 1, pos))
              FROM UNNEST(GENERATE_ARRAY(3, LENGTH(item.SUB_CLASS_KEY))) AS pos
              WHERE REGEXP_CONTAINS(SUBSTR(item.SUB_CLASS_KEY, 1, pos), r'^[0-9]+$')
            ) AS hierarchical_tags
          FROM `{self.config["source_table"]}`
          CROSS JOIN UNNEST(items) as item
          WHERE ph_id IS NOT NULL
            AND item.SUB_CLASS_KEY IS NOT NULL
            AND item.SUB_CLASS_KEY != ''
            AND event_times IS NOT NULL
            -- 修復：使用與 extract_ph_id_segments 相同的相對日期過濾邏輯
            AND DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(
              (SELECT MAX(DATE(TIMESTAMP_SECONDS(event_times)))
               FROM `{self.config["source_table"]}`
               WHERE event_times IS NOT NULL),
              INTERVAL {self.data_range_days} DAY)
            AND REGEXP_CONTAINS(item.SUB_CLASS_KEY, r'^[0-9]+$')  -- 確保是純數字
            AND LENGTH(item.SUB_CLASS_KEY) >= 3  -- 至少 3 位數字
        ),
        ph_id_aggregated AS (
          SELECT
            TO_HEX(ph_id) as ph_id_hex,
            ARRAY_AGG(DISTINCT tag) as segment_ids_array
          FROM ph_id_hierarchical_segments
          CROSS JOIN UNNEST(hierarchical_tags) as tag
          GROUP BY ph_id
        )

        SELECT
          -- group_id: Meta 要求的群組識別碼 (設為 NULL)
          CAST(NULL AS STRING) AS group_id,

          -- emails: 空的 email 陣列 (我們使用 phone 識別)
          CAST([] AS ARRAY<STRING>) AS emails,

          -- phones: 直接使用 ph_id 的 hex 格式
          [ph_id_hex] AS phones,

          -- fb_info: Facebook 相關資訊 (空陣列)
          [STRUCT(['', '', ''] AS fbp_fbc_ip)] AS fb_info,

          -- segment_id: 商品分類標籤陣列
          segment_ids_array AS segment_id

        FROM ph_id_aggregated
        );
        """

        return query

    def execute_avro_export(self, target_date: str, dry_run: bool = True) -> Dict[str, Any]:
        """
        執行 AVRO 檔案匯出

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            dry_run: 是否為測試模式

        Returns:
            執行結果
        """
        logger.info(f"開始執行 AVRO 檔案匯出 (日期: {target_date}, dry_run: {dry_run})")

        # 生成輸出路徑
        date_str = target_date.replace('-', '')
        timestamp = datetime.now().strftime('%H%M%S')
        output_uri = f"gs://{self.config['gcs_bucket']}/{self.config['gcs_path_prefix']}/{date_str}/carrefour_ph_id_export_{timestamp}_*.avro"

        # 生成查詢
        query = self.generate_avro_export_query(target_date, output_uri)

        if dry_run:
            # 測試模式：只估算成本
            cost = self._estimate_query_cost(query)
            return {
                "status": "dry_run",
                "estimated_cost_usd": cost,
                "output_uri": output_uri,
                "query": query
            }

        try:
            # 執行匯出
            self._log_memory_usage("步驟3_EXPORT_開始")
            job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
            query_job = self.client.query(query, job_config=job_config)
            query_job.result()  # 等待完成

            logger.info(f"AVRO 檔案匯出完成: {output_uri}")

            # 記錄步驟 3 完成後的記憶體使用
            self._log_memory_usage("步驟3_AVRO匯出完成")

            return {
                "status": "success",
                "output_uri": output_uri,
                "job_id": query_job.job_id,
                "execution_id": self.execution_id
            }

        except Exception as e:
            logger.error(f"AVRO 檔案匯出失敗: {e}")
            raise

    def create_temp_mapping_table(self, target_date: str, ph_id_data: List[Dict[str, Any]]) -> str:
        """
        建立暫存的 ph_id 與 segment_id 對應表

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            ph_id_data: ph_id 和 segment_id 的對應資料

        Returns:
            建立的表格 ID
        """
        date_str = target_date.replace('-', '')
        table_id = f"{self.config['target_dataset']}.carrefour_ph_id_segment_mapping_{date_str}"

        logger.info(f"建立暫存對應表: {table_id}")

        # 定義 schema
        schema = [
            bigquery.SchemaField("ph_id_hex", "STRING", description="ph_id 的 16 進位字串格式"),
            bigquery.SchemaField("segment_ids", "STRING", mode="REPEATED", description="商品分類標籤陣列"),
            bigquery.SchemaField("segment_count", "INTEGER", description="標籤數量"),
            bigquery.SchemaField("created_at", "TIMESTAMP", description="建立時間"),
            bigquery.SchemaField("execution_id", "STRING", description="執行 ID"),
        ]

        try:
            # 建立表格
            table = bigquery.Table(table_id, schema=schema)
            table.clustering_fields = ["ph_id_hex"]
            table.description = f"家樂福 ph_id 與 segment_id 對應表 ({target_date})"

            # 設定過期時間 (7 天)
            table.expires = datetime.now(timezone.utc) + timedelta(days=7)

            table = self.client.create_table(table, exists_ok=True)
            logger.info(f"表格建立成功: {table_id}")

            # 準備資料（批次插入以降低峰值記憶體）
            created_at = datetime.now(timezone.utc).isoformat()  # 轉換為 ISO 字串格式
            total_records = len(ph_id_data)
            batch_size = int(self.config.get("insert_batch_size", 5000))
            batch_count = 0

            # 步驟 2 批次插入開始 - 記憶體監控
            self._log_memory_usage("步驟2_批次插入開始")

            for start_idx in range(0, total_records, batch_size):
                end_idx = min(start_idx + batch_size, total_records)
                batch_slice = ph_id_data[start_idx:end_idx]

                rows_to_insert = [
                    {
                        "ph_id_hex": item["ph_id_hex"],
                        "segment_ids": item["segment_ids"],
                        "segment_count": item["segment_count"],
                        "created_at": created_at,
                        "execution_id": self.execution_id,
                    }
                    for item in batch_slice
                ]

                if rows_to_insert:
                    errors = self.client.insert_rows_json(table, rows_to_insert)
                    if errors:
                        raise Exception(f"插入資料時發生錯誤: {errors}")

                batch_count += 1
                if batch_count % 5 == 0:
                    self._log_memory_usage(f"步驟2_批次{batch_count}_完成")

                # 釋放該批次的記憶體
                del rows_to_insert
                del batch_slice
                try:
                    import gc as _gc
                    _gc.collect()
                except Exception:
                    pass

            logger.info(
                f"成功插入 {total_records} 筆資料到 {table_id}（批次：{batch_count}，每批 {batch_size}）"
            )

            # 步驟 2 批次插入完成 - 記憶體監控
            self._log_memory_usage("步驟2_批次插入完成")

            return table_id

        except Exception as e:
            logger.error(f"建立暫存對應表失敗: {e}")
            raise

    def publish_avro_files_to_pubsub(self, avro_file_paths: List[str]) -> None:
        """
        發送 AVRO 檔案路徑到 Pub/Sub，與參考實作完全一致

        Args:
            avro_file_paths: AVRO 檔案路徑列表
        """
        try:
            project_id = self.config["pubsub_project"]
            topic_name = self.config["pubsub_topic"]
            ec_id = self.config["ec_id"]

            logger.info(f"準備發送 {len(avro_file_paths)} 個 AVRO 檔案路徑到 Pub/Sub")
            logger.info(f"目標 Topic: projects/{project_id}/topics/{topic_name}")

            with PublisherClient(project_id=project_id, topic_name=topic_name) as publisher:
                for file_path in avro_file_paths:
                    publisher.add_message(
                        '',  # 空的 data，檔案路徑放在 attributes 中
                        file_name=file_path,
                        version='v1',
                        ec_id=ec_id,
                    )
                    logger.info(f"訊息發佈 for file: {file_path}")

            logger.info(f"所有訊息成功發佈到 {project_id} 專案的 {topic_name} 主題")

        except Exception as e:
            logger.error(f"發送 Pub/Sub 通知失敗: {e}")
            raise

    def publish_completion_notification(self, result: Dict[str, Any], target_date: str) -> Dict[str, Any]:
        """
        發送處理完成通知（供測試與可選通知使用）

        Args:
            result: 本次處理結果摘要
            target_date: 目標日期 (YYYY-MM-DD)

        Returns:
            包含 message_id 或錯誤資訊的字典
        """
        try:
            project_id = self.config.get("pubsub_project")
            topic_name = self.config.get("pubsub_topic")
            if not project_id or not topic_name:
                return {"status": "skipped", "reason": "missing_pubsub_config"}

            notifier = PubSubNotifier(project_id)
            message_id = notifier.publish_processing_completion(
                topic_name=topic_name,
                execution_id=self.execution_id,
                result=result,
                target_date=target_date,
            )
            logger.info(f"處理完成通知已發送，message_id={message_id}")
            return {"status": "success", "message_id": message_id}
        except Exception as e:
            logger.error(f"發送處理完成通知失敗: {e}")
            return {"status": "error", "error": str(e)}

    def get_avro_file_paths(self, gcs_directory: str) -> List[str]:
        """
        獲取指定 GCS 目錄下的所有 AVRO 檔案路徑

        Args:
            gcs_directory: GCS 目錄路徑 (例如 "gs://bucket/path/to/dir/")

        Returns:
            AVRO 檔案路徑列表
        """
        try:
            logger.info(f"正在列出 GCS 目錄 '{gcs_directory}' 中的 .avro 檔案...")

            if not gcs_directory.startswith("gs://"):
                raise ValueError("無效的 GCS 路徑格式，應以 gs:// 開頭")

            # 從目錄路徑解析 bucket 和 prefix
            parts = gcs_directory[5:].split("/")
            bucket_name = parts[0]
            prefix = "/".join(parts[1:])

            # 列出所有檔案
            bucket = self.storage_client.bucket(bucket_name)
            blobs = bucket.list_blobs(prefix=prefix)

            # 篩選出 .avro 檔案
            avro_files = []
            for blob in blobs:
                if blob.name.endswith('.avro'):
                    avro_files.append(f"gs://{bucket_name}/{blob.name}")

            if not avro_files:
                logger.warning("在指定的 GCS 路徑中找不到任何 .avro 檔案。")
            else:
                logger.info(f"成功找到 {len(avro_files)} 個 .avro 檔案。")

            return avro_files

        except Exception as e:
            logger.error(f"列出 GCS 檔案時發生錯誤: {e}")
            raise

    def _generate_category_statistics(self, ph_id_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成三碼大分類統計報告

        Args:
            ph_id_data: ph_id 資料列表，包含 segment_ids

        Returns:
            統計結果字典
        """
        import re
        from collections import Counter

        logger.info("開始生成三碼大分類統計報告")

        # 類型檢查：確保 ph_id_data 是列表格式
        if not isinstance(ph_id_data, list):
            logger.warning(f"ph_id_data 應該是列表格式，但收到 {type(ph_id_data)}，返回空統計")
            return {
                "total_ph_id_records": 0,
                "total_segment_instances": 0,
                "total_category_instances": 0,
                "unique_categories_count": 0,
                "category_breakdown": []
            }

        # 統計三碼大分類
        category_counter = Counter()
        total_records = len(ph_id_data)
        total_segments = 0

        for record in ph_id_data:
            # 確保 record 是字典格式
            if not isinstance(record, dict):
                logger.warning(f"記錄應該是字典格式，但收到 {type(record)}，跳過此記錄")
                continue

            segment_ids = record.get('segment_ids', [])
            # 處理 None 值的情況
            if segment_ids is None:
                segment_ids = []
            total_segments += len(segment_ids)

            for segment_id in segment_ids:
                # 從 tm:c_715_pc_XXX 格式中提取三碼大分類
                match = re.search(r'tm:c_715_pc_(\d{3})', str(segment_id))
                if match:
                    three_digit_category = match.group(1)
                    category_counter[three_digit_category] += 1

        # 計算統計結果
        total_category_instances = sum(category_counter.values())

        # 排序並格式化結果
        sorted_categories = category_counter.most_common()

        # 建立統計報告
        statistics = {
            "total_ph_id_records": total_records,
            "total_segment_instances": total_segments,
            "total_category_instances": total_category_instances,
            "unique_categories_count": len(category_counter),
            "category_breakdown": []
        }

        # 生成詳細分類統計
        for category, count in sorted_categories:
            percentage = (count / total_category_instances * 100) if total_category_instances > 0 else 0
            statistics["category_breakdown"].append({
                "category": category,
                "count": count,
                "percentage": round(percentage, 2)
            })

        # 記錄統計報告到日誌
        self._log_category_statistics(statistics)

        logger.info(f"三碼大分類統計完成: {len(category_counter)} 個分類")
        return statistics

    def _log_category_statistics(self, statistics: Dict[str, Any]) -> None:
        """
        將統計結果記錄到日誌中，格式化輸出

        Args:
            statistics: 統計結果字典
        """
        logger.info("=" * 60)
        logger.info("📊 三碼大分類統計報告")
        logger.info("=" * 60)
        logger.info(f"總處理筆數: {statistics['total_ph_id_records']:,}")
        logger.info(f"總標籤實例數: {statistics['total_segment_instances']:,}")
        logger.info(f"總分類實例數: {statistics['total_category_instances']:,}")
        logger.info(f"唯一分類數: {statistics['unique_categories_count']}")
        logger.info("-" * 60)

        # 顯示前20名分類
        top_categories = statistics["category_breakdown"][:20]
        for item in top_categories:
            category = item["category"]
            count = item["count"]
            percentage = item["percentage"]
            logger.info(f"   - {category}: {count:,} 筆 ({percentage:.1f}%)")

        if len(statistics["category_breakdown"]) > 20:
            remaining = len(statistics["category_breakdown"]) - 20
            logger.info(f"   ... 還有 {remaining} 個分類")

        logger.info("=" * 60)

    def generate_detailed_report(self, statistics: Dict[str, Any], target_date: str,
                               execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成詳細的 JSON 報告

        Args:
            statistics: 三碼分類統計結果
            target_date: 目標日期
            execution_result: 執行結果

        Returns:
            詳細報告資料
        """
        report = {
            "metadata": {
                "report_type": "carrefour_ph_id_direct_analytics",
                "report_version": "1.0",
                "generated_at": datetime.now().isoformat(),
                "target_date": target_date,
                "execution_id": self.execution_id,
                "ec_id": self.config.get("ec_id", 715)
            },
            "execution_summary": {
                "status": execution_result.get("status", "unknown"),
                "start_time": execution_result.get("start_time"),
                "end_time": execution_result.get("end_time"),
                "execution_time_seconds": execution_result.get("execution_time_seconds"),
                "dry_run": execution_result.get("dry_run", False)
            },
            "data_statistics": {
                "total_ph_id_records": statistics.get("total_ph_id_records", 0),
                "total_segment_instances": statistics.get("total_segment_instances", 0),
                "total_category_instances": statistics.get("total_category_instances", 0),
                "unique_categories_count": statistics.get("unique_categories_count", 0),
                "average_segments_per_ph_id": round(
                    statistics.get("total_segment_instances", 0) / max(statistics.get("total_ph_id_records", 1), 1), 2
                ),
                "average_categories_per_ph_id": round(
                    statistics.get("total_category_instances", 0) / max(statistics.get("total_ph_id_records", 1), 1), 2
                )
            },
            "category_breakdown": statistics.get("category_breakdown", []),
            "top_10_categories": statistics.get("category_breakdown", [])[:10],
            "processing_steps": execution_result.get("steps", {}),
            "performance_metrics": {
                "memory_usage": execution_result.get("memory_usage", {}),
                "query_costs_estimated": execution_result.get("estimated_costs", {}),
                "files_generated": execution_result.get("files_generated", {})
            }
        }

        return report

    def generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """
        生成 HTML 視覺化報告

        Args:
            report_data: 詳細報告資料

        Returns:
            HTML 內容字串
        """
        metadata = report_data["metadata"]
        execution = report_data["execution_summary"]
        stats = report_data["data_statistics"]
        all_categories = report_data["category_breakdown"]
        top_10_categories = report_data["top_10_categories"]

        # 生成 HTML 內容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家樂福 Ph_ID 直接處理分析報告 - {metadata["target_date"]}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }}
        .header .subtitle {{
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1rem;
        }}
        .content {{
            padding: 30px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .metric-card {{
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            border-radius: 0 8px 8px 0;
        }}
        .metric-value {{
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .category-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        .category-table th,
        .category-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .category-table th {{
            background-color: #667eea;
            color: white;
            font-weight: 500;
        }}
        .category-table tr:hover {{
            background-color: #f5f5f5;
        }}
        .progress-bar {{
            background-color: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 5px;
        }}
        .progress-fill {{
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }}
        .status {{
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }}
        .status.success {{
            background-color: #d4edda;
            color: #155724;
        }}
        .status.error {{
            background-color: #f8d7da;
            color: #721c24;
        }}
        .footer {{
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }}
        .expandable-section {{
            margin-top: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }}
        .expand-button {{
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-size: 1rem;
            font-weight: 500;
            color: #495057;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }}
        .expand-button:hover {{
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }}
        .expand-content {{
            display: none;
            max-height: 500px;
            overflow-y: auto;
            background: white;
        }}
        .expand-content.show {{
            display: block;
        }}
        .summary-stats {{
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.9rem;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 家樂福 Ph_ID 直接處理分析報告</h1>
            <div class="subtitle">
                資料日期: {metadata["target_date"]} |
                執行時間: {execution.get("execution_time_seconds") or 0:.2f} 秒 |
                <span class="status {'success' if execution["status"] == 'success' else 'error'}">{execution["status"]}</span>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📈 核心指標</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{stats["total_ph_id_records"]:,}</div>
                        <div class="metric-label">總用戶數</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{stats["total_segment_instances"]:,}</div>
                        <div class="metric-label">總標籤實例數</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{stats["unique_categories_count"]}</div>
                        <div class="metric-label">唯一分類數</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{stats["average_segments_per_ph_id"]}</div>
                        <div class="metric-label">平均標籤數/用戶</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🏆 三碼大分類統計</h2>

                <!-- 前10名熱門分類 -->
                <h3>📈 前10名熱門分類</h3>
                <table class="category-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>分類代碼</th>
                            <th>用戶數</th>
                            <th>佔比</th>
                            <th>分布視覺化</th>
                        </tr>
                    </thead>
                    <tbody>"""

        # 加入前10名分類的表格行
        for idx, category in enumerate(top_10_categories, 1):
            html_content += f"""
                        <tr>
                            <td>{idx}</td>
                            <td><strong>{category["category"]}</strong></td>
                            <td>{category["count"]:,}</td>
                            <td>{category["percentage"]:.1f}%</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {category["percentage"]}%"></div>
                                </div>
                            </td>
                        </tr>"""

        html_content += f"""
                    </tbody>
                </table>

                <!-- 完整分類列表 -->
                <div class="expandable-section">
                    <button class="expand-button" onclick="toggleExpand()">
                        <span>📋 檢視完整分類列表（共 {len(all_categories)} 個分類）</span>
                        <span id="expand-icon">▼</span>
                    </button>
                    <div class="expand-content" id="full-categories">
                        <div class="summary-stats">
                            📊 顯示全部 {len(all_categories)} 個三碼大分類，按用戶數量排序
                        </div>
                        <table class="category-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>分類代碼</th>
                                    <th>用戶數</th>
                                    <th>佔比</th>
                                    <th>分布視覺化</th>
                                </tr>
                            </thead>
                            <tbody>"""

        # 加入所有分類的表格行
        for idx, category in enumerate(all_categories, 1):
            html_content += f"""
                                <tr>
                                    <td>{idx}</td>
                                    <td><strong>{category["category"]}</strong></td>
                                    <td>{category["count"]:,}</td>
                                    <td>{category["percentage"]:.1f}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {min(category["percentage"] * 2, 100)}%"></div>
                                        </div>
                                    </td>
                                </tr>"""

        html_content += f"""
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>⚙️ 執行詳細資訊</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{metadata["execution_id"][:8]}...</div>
                        <div class="metric-label">執行 ID</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{metadata["generated_at"][:16]}</div>
                        <div class="metric-label">報告生成時間</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">EC-{metadata["ec_id"]}</div>
                        <div class="metric-label">企業客戶 ID</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">v{metadata["report_version"]}</div>
                        <div class="metric-label">報告版本</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>此報告由 Tagtoo 資料工程團隊自動生成 | 報告時間: {metadata["generated_at"]}</p>
            <p>資料來源: BigQuery tagtoo-tracking.event_prod | 輸出位置: gs://tagtoo-ml-workflow/carrefour_ph_id_direct</p>
        </div>
    </div>

    <script>
        function toggleExpand() {{
            const content = document.getElementById('full-categories');
            const icon = document.getElementById('expand-icon');

            if (content.classList.contains('show')) {{
                content.classList.remove('show');
                icon.textContent = '▼';
            }} else {{
                content.classList.add('show');
                icon.textContent = '▲';
            }}
        }}

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('家樂福 Ph_ID 分析報告載入完成');
            console.log('分類統計數量: {len(all_categories)}');
        }});
    </script>
</body>
</html>"""

        return html_content

    def upload_reports_to_gcs(self, target_date: str, json_report: Dict[str, Any],
                            html_content: str) -> Dict[str, str]:
        """
        上傳報告檔案到 GCS

        Args:
            target_date: 目標日期 (YYYY-MM-DD)
            json_report: JSON 報告資料
            html_content: HTML 內容

        Returns:
            上傳結果，包含檔案 URI
        """
        from datetime import datetime
        import tempfile
        import json

        # 格式化日期為 YYYYMMDD
        date_str = target_date.replace("-", "")
        timestamp = datetime.now().strftime("%H%M%S")

        # 產生檔案名稱
        json_filename = f"carrefour_ph_id_analytics_{date_str}_{timestamp}.json"
        html_filename = f"carrefour_ph_id_report_{date_str}_{timestamp}.html"

        # GCS 路徑
        gcs_base_path = f"{self.config['gcs_path_prefix']}/{date_str}"
        json_gcs_path = f"{gcs_base_path}/{json_filename}"
        html_gcs_path = f"{gcs_base_path}/{html_filename}"

        try:
            # 建立臨時檔案並上傳 JSON
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_json:
                json.dump(json_report, temp_json, ensure_ascii=False, indent=2)
                temp_json_path = temp_json.name

            # 建立臨時檔案並上傳 HTML
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_html:
                temp_html.write(html_content)
                temp_html_path = temp_html.name

            # 使用 GCS 客戶端上傳檔案
            bucket = self.storage_client.bucket(self.config["gcs_bucket"])

            # 上傳 JSON 檔案
            json_blob = bucket.blob(json_gcs_path)
            json_blob.upload_from_filename(temp_json_path, content_type='application/json')

            # 上傳 HTML 檔案
            html_blob = bucket.blob(html_gcs_path)
            html_blob.upload_from_filename(temp_html_path, content_type='text/html')

            # 清理臨時檔案
            import os
            os.unlink(temp_json_path)
            os.unlink(temp_html_path)

            # 產生簽名 URL（1小時有效期）
            json_signed_url = json_blob.generate_signed_url(
                version="v4",
                expiration=datetime.now() + timedelta(hours=1),
                method="GET"
            )

            html_signed_url = html_blob.generate_signed_url(
                version="v4",
                expiration=datetime.now() + timedelta(hours=1),
                method="GET"
            )

            result = {
                "json_uri": f"gs://{self.config['gcs_bucket']}/{json_gcs_path}",
                "html_uri": f"gs://{self.config['gcs_bucket']}/{html_gcs_path}",
                "json_signed_url": json_signed_url,
                "html_signed_url": html_signed_url,
                "upload_status": "success"
            }

            logger.info(f"報告檔案上傳成功:")
            logger.info(f"  JSON: {result['json_uri']}")
            logger.info(f"  HTML: {result['html_uri']}")
            logger.info(f"  HTML 預覽 (1小時內有效): {html_signed_url}")

            return result

        except Exception as e:
            logger.error(f"報告檔案上傳失敗: {e}")
            return {
                "upload_status": "error",
                "error": str(e)
            }

    def run_complete_pipeline(self, target_date: Optional[str] = None, dry_run: bool = True) -> Dict[str, Any]:
        """
        執行完整的 ph_id 直接處理流程

        Args:
            target_date: 目標日期 (YYYY-MM-DD)，None 表示使用昨天
            dry_run: 是否為測試模式

        Returns:
            執行結果
        """
        start_time = datetime.now()
        target_date = self._calculate_target_date(target_date)

        logger.info(f"開始執行完整的 ph_id 直接處理流程")
        logger.info(f"目標日期: {target_date}")
        logger.info(f"執行模式: {'DRY RUN' if dry_run else '生產模式'}")
        logger.info(f"執行 ID: {self.execution_id}")

        # 記錄初始記憶體使用
        initial_memory = self._log_memory_usage("流程開始")

        result = {
            "execution_id": self.execution_id,
            "target_date": target_date,
            "dry_run": dry_run,
            "start_time": start_time.isoformat(),
            "steps": {}
        }

        try:
            # 步驟 1: 提取 ph_id 和商品分類
            logger.info("步驟 1: 提取 ph_id 和商品分類")
            ph_id_data = self.extract_ph_id_segments(target_date, dry_run)
            result["steps"]["extract_data"] = {
                "status": "success",
                "record_count": len(ph_id_data)
            }

            # 步驟 1.5: 生成三碼大分類統計報告
            if ph_id_data:
                logger.info("步驟 1.5: 生成三碼大分類統計報告")
                category_statistics = self._generate_category_statistics(ph_id_data)
                result["steps"]["category_statistics"] = {
                    "status": "success",
                    "statistics": category_statistics
                }

                # 步驟 1.6: 生成詳細分析報告 (JSON 和 HTML)
                logger.info("步驟 1.6: 生成詳細分析報告")
                try:
                    # 建立臨時結果物件 (供報告生成使用)
                    temp_result = {
                        "status": "success",
                        "start_time": start_time.isoformat(),
                        "target_date": target_date,
                        "dry_run": dry_run,
                        "steps": result.get("steps", {}),
                        "memory_usage": {},
                        "estimated_costs": {},
                        "files_generated": {}
                    }

                    # 生成詳細 JSON 報告
                    detailed_report = self.generate_detailed_report(
                        category_statistics, target_date, temp_result
                    )

                    # 生成 HTML 視覺化報告
                    html_report = self.generate_html_report(detailed_report)

                    # 上傳報告到 GCS
                    upload_result = self.upload_reports_to_gcs(
                        target_date, detailed_report, html_report
                    )

                    result["steps"]["detailed_reports"] = {
                        "status": "success",
                        "json_uri": upload_result.get("json_uri"),
                        "html_uri": upload_result.get("html_uri"),
                        "html_preview_url": upload_result.get("html_signed_url"),
                        "upload_status": upload_result.get("upload_status")
                    }

                    logger.info("✅ 詳細分析報告生成完成")
                    if upload_result.get("html_signed_url"):
                        logger.info(f"🌐 HTML 報告預覽 (1小時內有效): {upload_result['html_signed_url']}")

                except Exception as e:
                    logger.error(f"生成詳細報告時發生錯誤: {e}")
                    result["steps"]["detailed_reports"] = {
                        "status": "error",
                        "error": str(e)
                    }

            else:
                logger.warning("無資料可統計，跳過三碼大分類統計和詳細報告生成")
                result["steps"]["category_statistics"] = {
                    "status": "skipped",
                    "reason": "no_data"
                }
                result["steps"]["detailed_reports"] = {
                    "status": "skipped",
                    "reason": "no_data"
                }

            if not dry_run and ph_id_data:
                # 步驟 2: 建立暫存對應表
                logger.info("步驟 2: 建立暫存對應表")
                mapping_table_id = self.create_temp_mapping_table(target_date, ph_id_data)
                result["steps"]["create_mapping_table"] = {
                    "status": "success",
                    "table_id": mapping_table_id
                }

            # 步驟 3: 執行 AVRO 檔案匯出
            logger.info("步驟 3: 執行 AVRO 檔案匯出")
            avro_result = self.execute_avro_export(target_date, dry_run)
            result["steps"]["avro_export"] = avro_result

            if not dry_run:
                # 步驟 4: 獲取 AVRO 檔案路徑並發送到 Pub/Sub
                logger.info("步驟 4: 獲取 AVRO 檔案路徑並發送到 Pub/Sub")

                # 從 AVRO 匯出結果中獲取 GCS 目錄
                output_uri = avro_result.get("output_uri", "")
                if output_uri:
                    # 將 wildcard URI 轉換為可用於 prefix 的路徑
                    # 例如: gs://bucket/.../carrefour_ph_id_export_040036_*.avro -> gs://bucket/.../carrefour_ph_id_export_040036_
                    gcs_directory = output_uri.replace("*.avro", "")

                    # 獲取實際的 AVRO 檔案路徑
                    avro_file_paths = self.get_avro_file_paths(gcs_directory)

                    if avro_file_paths:
                        # 發送到 Pub/Sub
                        self.publish_avro_files_to_pubsub(avro_file_paths)
                        result["steps"]["pubsub_notification"] = {
                            "status": "success",
                            "file_count": len(avro_file_paths),
                            "files": avro_file_paths
                        }
                    else:
                        logger.warning("未找到 AVRO 檔案，跳過 Pub/Sub 發送")
                        result["steps"]["pubsub_notification"] = {
                            "status": "skipped",
                            "reason": "no_avro_files_found"
                        }
                else:
                    logger.warning("未獲取到 AVRO 輸出 URI，跳過 Pub/Sub 發送")
                    result["steps"]["pubsub_notification"] = {
                        "status": "skipped",
                        "reason": "no_output_uri"
                    }

            # 計算執行時間
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            result["end_time"] = end_time.isoformat()
            result["execution_time_seconds"] = execution_time
            result["status"] = "success"

            logger.info(f"ph_id 直接處理流程完成 (耗時: {execution_time:.2f} 秒)")
            return result

        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            result["end_time"] = end_time.isoformat()
            result["execution_time_seconds"] = execution_time
            result["status"] = "error"
            result["error"] = str(e)

            logger.error(f"ph_id 直接處理流程失敗: {e}")
            raise


def main():
    """主要執行函數 - 用於測試"""
    import argparse

    parser = argparse.ArgumentParser(description='家樂福 ph_id 直接處理工具')
    parser.add_argument('--target-date', help='目標日期 (YYYY-MM-DD)')
    parser.add_argument('--dry-run', action='store_true', help='測試模式')
    parser.add_argument('--config', help='配置檔案路徑')

    args = parser.parse_args()

    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        processor = PhIdDirectProcessor(args.config)
        result = processor.run_complete_pipeline(args.target_date, args.dry_run)

        print("\n=== 執行結果 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        logger.error(f"執行失敗: {e}")
        exit(1)


if __name__ == "__main__":
    main()
