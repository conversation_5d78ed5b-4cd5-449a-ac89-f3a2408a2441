#!/usr/bin/env python3
"""
負數資料專項調查
深度分析 items 中負數量和負小計的具體交易記錄
分析負數出現的業務原因（退貨、折扣、系統錯誤等）
"""

import os
import sys
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/tools')
sys.path.append('src/utils')
from setup_environment import get_gcloud_credentials_path
from smart_client import smart_client


def setup_bigquery_client() -> bigquery.Client:
    """設定 BigQuery 客戶端"""
    return smart_client()


def run_query_with_cost_control(client: bigquery.Client, query: str, desc: str, max_cost_usd: float = 0.1) -> Dict[str, Any]:
    """執行查詢並控制成本"""
    try:
        # 成本估算
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=job_config)
        bytes_processed = job.total_bytes_processed
        cost_usd = (bytes_processed / (1024**4)) * 5.0  # $5/TB

        if cost_usd > max_cost_usd:
            return {
                "status": "skipped_high_cost",
                "desc": desc,
                "estimated_cost_usd": cost_usd,
                "max_cost_usd": max_cost_usd
            }

        # 執行查詢
        start_time = datetime.now()
        job = client.query(query)
        results = job.result()
        end_time = datetime.now()

        data = []
        for row in results:
            row_dict = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):
                    row_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    row_dict[key] = float(value)
                else:
                    row_dict[key] = value
            data.append(row_dict)

        return {
            "status": "success",
            "desc": desc,
            "estimated_cost_usd": cost_usd,
            "execution_time_seconds": (end_time - start_time).total_seconds(),
            "row_count": len(data),
            "data": data
        }

    except Exception as e:
        return {
            "status": "failed",
            "desc": desc,
            "error": str(e)
        }


def investigate_negative_quantities(client: bigquery.Client) -> Dict[str, Any]:
    """調查負數量的具體交易記錄"""
    print('🔍 調查負數量交易記錄...')

    query = """
    SELECT
      transaction_id,
      DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
      TIMESTAMP_SECONDS(event_times) as transaction_timestamp,
      total_amount,
      item.item_id,
      item.item_name,
      item.quantity,
      item.unit_price,
      item.subtotal,
      item.GRP_CLASS_DESC,
      item.CLASS_DESC,
      item.SUB_CLASS_DESC
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
    UNNEST(items) as item
    WHERE item.quantity <= 0
    ORDER BY transaction_date DESC, transaction_id
    LIMIT 50
    """

    return run_query_with_cost_control(client, query, "負數量交易記錄調查", max_cost_usd=0.15)


def investigate_negative_subtotals(client: bigquery.Client) -> Dict[str, Any]:
    """調查負小計的具體交易記錄"""
    print('💰 調查負小計交易記錄...')

    query = """
    SELECT
      transaction_id,
      DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
      TIMESTAMP_SECONDS(event_times) as transaction_timestamp,
      total_amount,
      item.item_id,
      item.item_name,
      item.quantity,
      item.unit_price,
      item.subtotal,
      item.GRP_CLASS_DESC,
      item.CLASS_DESC,
      item.SUB_CLASS_DESC,
      -- 計算期望的小計
      item.quantity * item.unit_price as expected_subtotal,
      -- 計算差異
      item.subtotal - (item.quantity * item.unit_price) as subtotal_difference
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
    UNNEST(items) as item
    WHERE item.subtotal <= 0
    ORDER BY transaction_date DESC, transaction_id
    LIMIT 50
    """

    return run_query_with_cost_control(client, query, "負小計交易記錄調查", max_cost_usd=0.15)


def analyze_negative_patterns(client: bigquery.Client) -> Dict[str, Any]:
    """分析負數值的模式和分佈"""
    print('📊 分析負數值模式...')

    query = """
    WITH negative_analysis AS (
      SELECT
        DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
        item.quantity,
        item.unit_price,
        item.subtotal,
        item.GRP_CLASS_DESC,
        item.CLASS_DESC,
        CASE
          WHEN item.quantity <= 0 THEN 'negative_quantity'
          WHEN item.subtotal <= 0 THEN 'negative_subtotal'
          WHEN ABS(item.subtotal - (item.quantity * item.unit_price)) > 0.01 THEN 'calculation_mismatch'
          ELSE 'normal'
        END as issue_type
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
      UNNEST(items) as item
      WHERE item.quantity <= 0 OR item.subtotal <= 0 OR ABS(item.subtotal - (item.quantity * item.unit_price)) > 0.01
      LIMIT 1000
    )
    SELECT
      issue_type,
      COUNT(*) as count,
      MIN(quantity) as min_quantity,
      MAX(quantity) as max_quantity,
      AVG(quantity) as avg_quantity,
      MIN(subtotal) as min_subtotal,
      MAX(subtotal) as max_subtotal,
      AVG(subtotal) as avg_subtotal,
      COUNT(DISTINCT GRP_CLASS_DESC) as unique_categories,
      COUNT(DISTINCT transaction_date) as unique_dates
    FROM negative_analysis
    GROUP BY issue_type
    ORDER BY count DESC
    """

    return run_query_with_cost_control(client, query, "負數值模式分析", max_cost_usd=0.1)


def investigate_category_patterns(client: bigquery.Client) -> Dict[str, Any]:
    """調查負數值在不同商品類別中的分佈"""
    print('🏷️ 調查商品類別分佈...')

    query = """
    WITH category_analysis AS (
      SELECT
        item.GRP_CLASS_DESC,
        item.CLASS_DESC,
        COUNT(CASE WHEN item.quantity <= 0 THEN 1 END) as negative_quantity_count,
        COUNT(CASE WHEN item.subtotal <= 0 THEN 1 END) as negative_subtotal_count,
        COUNT(*) as total_items,
        AVG(item.quantity) as avg_quantity,
        AVG(item.subtotal) as avg_subtotal
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
      UNNEST(items) as item
      WHERE item.quantity <= 0 OR item.subtotal <= 0
      GROUP BY item.GRP_CLASS_DESC, item.CLASS_DESC
      HAVING COUNT(*) > 0
      ORDER BY negative_quantity_count + negative_subtotal_count DESC
      LIMIT 20
    )
    SELECT * FROM category_analysis
    """

    return run_query_with_cost_control(client, query, "商品類別負數分佈", max_cost_usd=0.1)


def analyze_business_reasons(negative_qty_data: List[Dict], negative_subtotal_data: List[Dict]) -> Dict[str, Any]:
    """分析負數出現的可能業務原因"""
    print('🔬 分析業務原因...')

    analysis = {
        "potential_reasons": [],
        "quantity_patterns": {},
        "subtotal_patterns": {},
        "category_insights": [],
        "recommendations": []
    }

    # 分析數量模式
    if negative_qty_data:
        quantities = [item.get('quantity', 0) for item in negative_qty_data]
        analysis["quantity_patterns"] = {
            "min_quantity": min(quantities),
            "max_quantity": max(quantities),
            "most_common": -1 if quantities.count(-1) > len(quantities) * 0.5 else "varied",
            "total_records": len(quantities)
        }

        # 檢查是否主要是 -1（可能是退貨）
        if quantities.count(-1) > len(quantities) * 0.7:
            analysis["potential_reasons"].append("退貨處理：大部分負數量為 -1，可能代表退貨或取消訂單")

    # 分析小計模式
    if negative_subtotal_data:
        subtotals = [item.get('subtotal', 0) for item in negative_subtotal_data]
        analysis["subtotal_patterns"] = {
            "min_subtotal": min(subtotals),
            "max_subtotal": max(subtotals),
            "total_records": len(subtotals)
        }

        # 檢查是否有折扣模式
        negative_subtotals = [s for s in subtotals if s < 0]
        if negative_subtotals:
            analysis["potential_reasons"].append("折扣或優惠：負小計可能代表折扣、優惠券或促銷活動")

    # 商品類別分析
    if negative_qty_data:
        categories = {}
        for item in negative_qty_data:
            cat = item.get('GRP_CLASS_DESC', 'Unknown')
            categories[cat] = categories.get(cat, 0) + 1

        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:5]
        analysis["category_insights"] = [
            f"{cat}: {count} 筆負數量記錄" for cat, count in top_categories
        ]

    # 業務建議
    analysis["recommendations"] = [
        "建議與家樂福確認退貨處理流程是否使用負數量記錄",
        "檢查折扣和促銷活動是否正確記錄為負小計",
        "建立資料驗證規則，確保業務邏輯的一致性",
        "考慮在資料處理時區分正常交易、退貨、折扣等不同類型",
        "建議增加交易類型欄位來明確區分不同的業務場景"
    ]

    return analysis


def main():
    """主要執行函數"""
    print('🔍 負數資料專項調查')
    print('=' * 60)

    try:
        client = setup_bigquery_client()

        investigation_report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "purpose": "深度調查 items 中負數量和負小計的業務原因",
                "billing_project": "tagtoo-tracking",
                "source_table": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
            }
        }

        # 1. 調查負數量記錄
        print('\n1️⃣ 調查負數量記錄')
        investigation_report["negative_quantities"] = investigate_negative_quantities(client)

        # 2. 調查負小計記錄
        print('\n2️⃣ 調查負小計記錄')
        investigation_report["negative_subtotals"] = investigate_negative_subtotals(client)

        # 3. 分析負數值模式
        print('\n3️⃣ 分析負數值模式')
        investigation_report["negative_patterns"] = analyze_negative_patterns(client)

        # 4. 調查商品類別分佈
        print('\n4️⃣ 調查商品類別分佈')
        investigation_report["category_patterns"] = investigate_category_patterns(client)

        # 5. 業務原因分析
        print('\n5️⃣ 業務原因分析')
        neg_qty_data = investigation_report["negative_quantities"].get("data", [])
        neg_subtotal_data = investigation_report["negative_subtotals"].get("data", [])
        investigation_report["business_analysis"] = analyze_business_reasons(neg_qty_data, neg_subtotal_data)

        # 儲存調查報告
        output_file = 'reports/negative_values_investigation.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(investigation_report, f, indent=2, ensure_ascii=False)

        print(f'\n✅ 負數資料調查報告已儲存至: {output_file}')

        # 顯示關鍵發現
        display_investigation_summary(investigation_report)

    except Exception as e:
        print(f'❌ 執行錯誤: {e}')
        sys.exit(1)


def display_investigation_summary(report: Dict[str, Any]) -> None:
    """顯示調查摘要"""
    print('\n🎯 負數資料調查摘要:')

    # 負數量調查
    neg_qty = report.get("negative_quantities", {})
    if neg_qty.get("status") == "success":
        print(f'\n📉 負數量記錄: {neg_qty.get("row_count", 0)} 筆詳細記錄')

    # 負小計調查
    neg_subtotal = report.get("negative_subtotals", {})
    if neg_subtotal.get("status") == "success":
        print(f'💰 負小計記錄: {neg_subtotal.get("row_count", 0)} 筆詳細記錄')

    # 業務分析
    business = report.get("business_analysis", {})
    if business.get("potential_reasons"):
        print(f'\n🔬 可能的業務原因:')
        for reason in business["potential_reasons"]:
            print(f'   • {reason}')

    # 建議
    if business.get("recommendations"):
        print(f'\n💡 建議:')
        for rec in business["recommendations"][:3]:  # 只顯示前3個建議
            print(f'   • {rec}')


if __name__ == '__main__':
    main()
