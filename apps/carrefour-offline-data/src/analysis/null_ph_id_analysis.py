#!/usr/bin/env python3
"""
NULL ph_id 資料詳細分析
查詢所有 ph_id 為 NULL 的記錄，分析分佈模式和原因
產出與家樂福技術團隊溝通用的詳細報告
"""

import os
import sys
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any
from google.cloud import bigquery

# 添加 src 目錄到路徑
sys.path.append('src')
sys.path.append('src/tools')
sys.path.append('src/utils')
from setup_environment import get_gcloud_credentials_path
from smart_client import smart_client


def setup_bigquery_client() -> bigquery.Client:
    """設定 BigQuery 客戶端"""
    return smart_client()


def run_query_with_cost_control(client: bigquery.Client, query: str, desc: str, max_cost_usd: float = 0.1) -> Dict[str, Any]:
    """執行查詢並控制成本"""
    try:
        # 成本估算
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        job = client.query(query, job_config=job_config)
        bytes_processed = job.total_bytes_processed
        cost_usd = (bytes_processed / (1024**4)) * 5.0  # $5/TB

        if cost_usd > max_cost_usd:
            return {
                "status": "skipped_high_cost",
                "desc": desc,
                "estimated_cost_usd": cost_usd,
                "max_cost_usd": max_cost_usd
            }

        # 執行查詢
        start_time = datetime.now()
        job = client.query(query)
        results = job.result()
        end_time = datetime.now()

        data = []
        for row in results:
            row_dict = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):
                    row_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    row_dict[key] = float(value)
                else:
                    row_dict[key] = value
            data.append(row_dict)

        return {
            "status": "success",
            "desc": desc,
            "estimated_cost_usd": cost_usd,
            "execution_time_seconds": (end_time - start_time).total_seconds(),
            "row_count": len(data),
            "data": data
        }

    except Exception as e:
        return {
            "status": "failed",
            "desc": desc,
            "error": str(e)
        }


def analyze_null_ph_id_basic_stats(client: bigquery.Client) -> Dict[str, Any]:
    """分析 NULL ph_id 的基本統計"""
    print('📊 分析 NULL ph_id 基本統計...')

    query = """
    SELECT
      COUNT(*) as total_records,
      COUNT(ph_id) as non_null_ph_id,
      COUNT(*) - COUNT(ph_id) as null_ph_id,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 4) as null_percentage,

      -- NULL ph_id 記錄的時間範圍
      MIN(CASE WHEN ph_id IS NULL THEN DATE(TIMESTAMP_SECONDS(event_times)) END) as null_earliest_date,
      MAX(CASE WHEN ph_id IS NULL THEN DATE(TIMESTAMP_SECONDS(event_times)) END) as null_latest_date,

      -- NULL ph_id 記錄的金額統計
      COUNT(CASE WHEN ph_id IS NULL THEN 1 END) as null_records_count,
      MIN(CASE WHEN ph_id IS NULL THEN total_amount END) as null_min_amount,
      MAX(CASE WHEN ph_id IS NULL THEN total_amount END) as null_max_amount,
      AVG(CASE WHEN ph_id IS NULL THEN total_amount END) as null_avg_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 基本統計", max_cost_usd=0.05)


def analyze_null_ph_id_daily_distribution(client: bigquery.Client) -> Dict[str, Any]:
    """分析 NULL ph_id 的每日分佈"""
    print('📅 分析 NULL ph_id 每日分佈...')

    query = """
    SELECT
      DATE(TIMESTAMP_SECONDS(event_times)) as date,
      COUNT(*) as total_records,
      COUNT(ph_id) as non_null_ph_id,
      COUNT(*) - COUNT(ph_id) as null_ph_id,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 2) as null_percentage
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
    GROUP BY date
    ORDER BY date DESC
    LIMIT 30
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 每日分佈", max_cost_usd=0.1)


def get_null_ph_id_sample_records(client: bigquery.Client) -> Dict[str, Any]:
    """取得 NULL ph_id 的樣本記錄"""
    print('🔍 取得 NULL ph_id 樣本記錄...')

    query = """
    SELECT
      transaction_id,
      DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
      TIMESTAMP_SECONDS(event_times) as transaction_timestamp,
      total_amount,
      ARRAY_LENGTH(items) as items_count,
      -- 取得第一個商品的資訊作為樣本
      items[OFFSET(0)].item_id as first_item_id,
      items[OFFSET(0)].item_name as first_item_name,
      items[OFFSET(0)].quantity as first_item_quantity,
      items[OFFSET(0)].unit_price as first_item_price
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE ph_id IS NULL
    ORDER BY RAND()
    LIMIT 20
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 樣本記錄", max_cost_usd=0.1)


def analyze_null_ph_id_transaction_patterns(client: bigquery.Client) -> Dict[str, Any]:
    """分析 NULL ph_id 交易模式"""
    print('💰 分析 NULL ph_id 交易模式...')

    query = """
    WITH null_transactions AS (
      SELECT
        transaction_id,
        total_amount,
        ARRAY_LENGTH(items) as items_count,
        DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date
      FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
      WHERE ph_id IS NULL
    )
    SELECT
      COUNT(*) as total_null_transactions,

      -- 金額分佈
      MIN(total_amount) as min_amount,
      MAX(total_amount) as max_amount,
      AVG(total_amount) as avg_amount,
      APPROX_QUANTILES(total_amount, 4)[OFFSET(1)] as q1_amount,
      APPROX_QUANTILES(total_amount, 4)[OFFSET(2)] as median_amount,
      APPROX_QUANTILES(total_amount, 4)[OFFSET(3)] as q3_amount,

      -- 商品數量分佈
      MIN(items_count) as min_items,
      MAX(items_count) as max_items,
      AVG(items_count) as avg_items,

      -- 異常值統計
      COUNT(CASE WHEN total_amount <= 0 THEN 1 END) as zero_or_negative_amount,
      COUNT(CASE WHEN items_count = 0 THEN 1 END) as zero_items_count,
      COUNT(CASE WHEN total_amount > 10000 THEN 1 END) as high_amount_count
    FROM null_transactions
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 交易模式", max_cost_usd=0.1)


def get_null_ph_id_transaction_ids(client: bigquery.Client) -> Dict[str, Any]:
    """取得 NULL ph_id 的交易編號清單（用於與家樂福溝通）"""
    print('📝 取得 NULL ph_id 交易編號清單...')

    query = """
    SELECT
      transaction_id,
      DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
      total_amount
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE ph_id IS NULL
    ORDER BY TIMESTAMP_SECONDS(event_times) DESC
    LIMIT 100  -- 限制數量避免過大
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 交易編號清單", max_cost_usd=0.1)


def get_null_ph_id_samples(client: bigquery.Client) -> Dict[str, Any]:
    """獲取 NULL ph_id 的具體樣本案例"""
    print('📋 獲取 NULL ph_id 樣本案例...')

    query = """
    SELECT
      transaction_id,
      DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
      TIMESTAMP_SECONDS(event_times) as transaction_timestamp,
      total_amount,
      ARRAY_LENGTH(items) as items_count,
      -- 取得第一個 item 的資訊作為範例
      items[OFFSET(0)].item_name as first_item_name,
      items[OFFSET(0)].quantity as first_item_quantity,
      items[OFFSET(0)].unit_price as first_item_price
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE ph_id IS NULL AND items IS NOT NULL AND ARRAY_LENGTH(items) > 0
    ORDER BY RAND()
    LIMIT 20
    """

    return run_query_with_cost_control(client, query, "NULL ph_id 樣本案例", max_cost_usd=0.1)


def main():
    """主要執行函數"""
    print('🔍 NULL ph_id 資料詳細分析')
    print('=' * 60)

    try:
        client = setup_bigquery_client()

        analysis_report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
                "purpose": "分析 NULL ph_id 記錄的分佈模式和原因",
                "billing_project": "tagtoo-tracking",
                "source_table": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
            }
        }

        # 1. 基本統計
        print('\n1️⃣ NULL ph_id 基本統計')
        analysis_report["basic_statistics"] = analyze_null_ph_id_basic_stats(client)

        # 2. 每日分佈
        print('\n2️⃣ NULL ph_id 每日分佈')
        analysis_report["daily_distribution"] = analyze_null_ph_id_daily_distribution(client)

        # 3. 樣本記錄
        print('\n3️⃣ NULL ph_id 樣本記錄')
        analysis_report["sample_records"] = get_null_ph_id_sample_records(client)

        # 4. 交易模式
        print('\n4️⃣ NULL ph_id 交易模式')
        analysis_report["transaction_patterns"] = analyze_null_ph_id_transaction_patterns(client)

        # 5. 交易編號清單
        print('\n5️⃣ NULL ph_id 交易編號清單')
        analysis_report["transaction_ids"] = get_null_ph_id_transaction_ids(client)

        # 6. 樣本案例
        print('\n6️⃣ NULL ph_id 樣本案例')
        analysis_report["samples"] = get_null_ph_id_samples(client)

        # 儲存分析報告
        output_file = 'reports/null_ph_id_analysis.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)

        print(f'\n✅ NULL ph_id 分析報告已儲存至: {output_file}')

        # 顯示關鍵發現
        display_analysis_summary(analysis_report)

        # 產生與家樂福溝通用的摘要
        generate_communication_summary(analysis_report)

    except Exception as e:
        print(f'❌ 執行錯誤: {e}')
        sys.exit(1)


def display_analysis_summary(report: Dict[str, Any]) -> None:
    """顯示分析摘要"""
    print('\n🎯 NULL ph_id 分析摘要:')

    # 基本統計
    basic = report.get("basic_statistics", {})
    if basic.get("status") == "success" and basic.get("data"):
        data = basic["data"][0]
        print('\n📊 基本統計:')
        print(f'   總記錄數: {data.get("total_records", 0):,}')
        print(f'   NULL ph_id 記錄數: {data.get("null_ph_id", 0):,}')
        print(f'   NULL 比例: {data.get("null_percentage", 0)}%')
        print(f'   NULL 記錄時間範圍: {data.get("null_earliest_date")} ~ {data.get("null_latest_date")}')
        print(f'   NULL 記錄平均金額: ${data.get("null_avg_amount", 0):.2f}')

    # 交易模式
    patterns = report.get("transaction_patterns", {})
    if patterns.get("status") == "success" and patterns.get("data"):
        data = patterns["data"][0]
        print('\n💰 交易模式:')
        print(f'   金額範圍: ${data.get("min_amount", 0):.2f} ~ ${data.get("max_amount", 0):.2f}')
        print(f'   平均金額: ${data.get("avg_amount", 0):.2f}')
        print(f'   中位數金額: ${data.get("median_amount", 0):.2f}')
        print(f'   異常金額記錄: {data.get("zero_or_negative_amount", 0)} 筆 (≤0)')
        print(f'   高額交易: {data.get("high_amount_count", 0)} 筆 (>$10,000)')


def generate_communication_summary(report: Dict[str, Any]) -> None:
    """產生與家樂福技術團隊溝通用的摘要"""
    print('\n📋 產生與家樂福技術團隊溝通用的摘要...')

    summary = {
        "communication_summary": {
            "generated_at": datetime.now().isoformat(),
            "purpose": "與家樂福技術團隊溝通 NULL ph_id 問題",
            "key_findings": [],
            "sample_transaction_ids": [],
            "recommendations": []
        }
    }

    # 提取關鍵發現
    basic = report.get("basic_statistics", {})
    if basic.get("status") == "success" and basic.get("data"):
        data = basic["data"][0]
        summary["communication_summary"]["key_findings"].extend([
            f"NULL ph_id 記錄數量：{data.get('null_ph_id', 0):,} 筆",
            f"NULL 比例：{data.get('null_percentage', 0)}%",
            f"時間範圍：{data.get('null_earliest_date')} ~ {data.get('null_latest_date')}",
            f"平均交易金額：${data.get('null_avg_amount', 0):.2f}"
        ])

    # 提取樣本交易編號
    transaction_ids = report.get("transaction_ids", {})
    if transaction_ids.get("status") == "success" and transaction_ids.get("data"):
        sample_ids = [record.get("transaction_id") for record in transaction_ids["data"][:10]]
        summary["communication_summary"]["sample_transaction_ids"] = sample_ids

    # 建議
    summary["communication_summary"]["recommendations"] = [
        "請檢查這些交易記錄在家樂福系統中的 ph_id 欄位狀態",
        "確認是否為系統處理問題或資料來源問題",
        "建議建立 ph_id 欄位的資料品質監控機制",
        "考慮實施 ph_id 必填驗證以避免未來出現 NULL 值"
    ]

    # 儲存溝通摘要
    output_file = 'reports/null_ph_id_communication_summary.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f'✅ 溝通摘要已儲存至: {output_file}')


if __name__ == '__main__':
    main()
