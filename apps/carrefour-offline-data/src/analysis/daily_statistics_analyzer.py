#!/usr/bin/env python3
"""
家樂福受眾資料每日統計分析腳本

分析每日寫入 special_lta_temp_for_update 表格的資料狀態，
包括 permanent 對應 segment_id 的分佈、商品購買統計等

作者: Tagtoo Data Team
日期: 2025-08-25
"""

import pandas as pd
import json
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DailyStatisticsAnalyzer:
    """每日統計分析器"""

    def __init__(self, project_id: str = "tagtoo-ml-workflow"):
        """
        初始化分析器

        Args:
            project_id: BigQuery 專案 ID
        """
        self.client = bigquery.Client(project=project_id)
        self.project_id = project_id
        self.analysis_results = {}

    def analyze_daily_data(self, target_date: str = None) -> Dict:
        """
        分析指定日期的資料

        Args:
            target_date: 目標日期 (YYYYMMDD)，預設為今天

        Returns:
            分析結果字典
        """
        if not target_date:
            target_date = datetime.now().strftime('%Y%m%d')

        logger.info(f"開始分析 {target_date} 的資料...")

        table_name = f"tagtoo_export_results.special_lta_temp_for_update_{target_date}"

        try:
            # 檢查表格是否存在
            table_ref = self.client.get_table(f"{self.project_id}.{table_name}")
            logger.info(f"找到目標表格: {table_name}")

            # 執行各項分析
            self.analysis_results = {
                "metadata": {
                    "analysis_date": datetime.now().isoformat(),
                    "target_date": target_date,
                    "table_name": table_name
                },
                "basic_statistics": self._analyze_basic_statistics(table_name),
                "segment_distribution": self._analyze_segment_distribution(table_name),
                "product_category_stats": self._analyze_product_categories(table_name),
                "execution_analysis": self._analyze_execution_info(table_name),
                "data_quality_metrics": self._analyze_data_quality(table_name)
            }

            return self.analysis_results

        except Exception as e:
            logger.error(f"分析失敗: {e}")
            return {"error": str(e)}

    def _analyze_basic_statistics(self, table_name: str) -> Dict:
        """分析基本統計資訊"""
        logger.info("分析基本統計資訊...")

        query = f"""
        SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT permanent) as unique_users,
            COUNT(DISTINCT segment_id) as unique_segment_combinations,
            MIN(created_at) as earliest_created,
            MAX(created_at) as latest_created,
            COUNT(DISTINCT source_entity) as unique_sources,
            COUNT(DISTINCT execution_id) as unique_executions
        FROM `{self.project_id}.{table_name}`
        """

        result = self.client.query(query).to_dataframe()

        if not result.empty:
            row = result.iloc[0]
            return {
                "total_records": int(row['total_records']),
                "unique_users": int(row['unique_users']),
                "unique_segment_combinations": int(row['unique_segment_combinations']),
                "earliest_created": row['earliest_created'].isoformat() if pd.notna(row['earliest_created']) else None,
                "latest_created": row['latest_created'].isoformat() if pd.notna(row['latest_created']) else None,
                "unique_sources": int(row['unique_sources']),
                "unique_executions": int(row['unique_executions']),
                "avg_records_per_user": float(row['total_records']) / float(row['unique_users']) if row['unique_users'] > 0 else 0
            }

        return {}

    def _analyze_segment_distribution(self, table_name: str) -> Dict:
        """分析 segment_id 分佈"""
        logger.info("分析 segment_id 分佈...")

        # 查詢所有 segment_id
        query = f"""
        SELECT segment_id
        FROM `{self.project_id}.{table_name}`
        """

        df = self.client.query(query).to_dataframe()

        if df.empty:
            return {}

        # 分析標籤分佈
        all_segments = []
        segment_counts = []

        for segment_id in df['segment_id']:
            segments = [s.strip() for s in segment_id.split(',')]
            all_segments.extend(segments)
            segment_counts.append(len(segments))

        segment_counter = Counter(all_segments)

        # 分析標籤層級
        level_stats = {"small": 0, "medium": 0, "large": 0, "other": 0}

        for segment in all_segments:
            if re.match(r'tm:c_715_pc_\d{5}', segment):
                level_stats["small"] += 1
            elif re.match(r'tm:c_715_pc_\d{4}', segment):
                level_stats["medium"] += 1
            elif re.match(r'tm:c_715_pc_\d{3}', segment):
                level_stats["large"] += 1
            else:
                level_stats["other"] += 1

        return {
            "total_segment_instances": len(all_segments),
            "unique_segments": len(segment_counter),
            "avg_segments_per_user": sum(segment_counts) / len(segment_counts) if segment_counts else 0,
            "min_segments_per_user": min(segment_counts) if segment_counts else 0,
            "max_segments_per_user": max(segment_counts) if segment_counts else 0,
            "top_10_segments": dict(segment_counter.most_common(10)),
            "level_distribution": level_stats,
            "segment_count_distribution": dict(Counter(segment_counts))
        }

    def _analyze_product_categories(self, table_name: str) -> Dict:
        """分析商品分類統計"""
        logger.info("分析商品分類統計...")

        query = f"""
        SELECT segment_id
        FROM `{self.project_id}.{table_name}`
        """

        df = self.client.query(query).to_dataframe()

        if df.empty:
            return {}

        # 提取大分類統計
        large_categories = set()
        medium_categories = set()
        small_categories = set()

        for segment_id in df['segment_id']:
            segments = [s.strip() for s in segment_id.split(',')]

            for segment in segments:
                match = re.match(r'tm:c_715_pc_(\d+)', segment)
                if match:
                    code = match.group(1)
                    if len(code) == 5:
                        small_categories.add(code)
                        medium_categories.add(code[:4])
                        large_categories.add(code[:3])
                    elif len(code) == 4:
                        medium_categories.add(code)
                        large_categories.add(code[:3])
                    elif len(code) == 3:
                        large_categories.add(code)

        return {
            "unique_large_categories": len(large_categories),
            "unique_medium_categories": len(medium_categories),
            "unique_small_categories": len(small_categories),
            "large_category_list": sorted(list(large_categories)),
            "coverage_stats": {
                "large_category_coverage": len(large_categories),
                "medium_category_coverage": len(medium_categories),
                "small_category_coverage": len(small_categories)
            }
        }

    def _analyze_execution_info(self, table_name: str) -> Dict:
        """分析執行資訊"""
        logger.info("分析執行資訊...")

        query = f"""
        SELECT
            source_type,
            source_entity,
            execution_id,
            COUNT(*) as record_count,
            MIN(created_at) as start_time,
            MAX(created_at) as end_time
        FROM `{self.project_id}.{table_name}`
        GROUP BY source_type, source_entity, execution_id
        ORDER BY start_time DESC
        """

        df = self.client.query(query).to_dataframe()

        if df.empty:
            return {}

        executions = []
        for _, row in df.iterrows():
            duration = None
            if pd.notna(row['start_time']) and pd.notna(row['end_time']):
                duration = (row['end_time'] - row['start_time']).total_seconds()

            executions.append({
                "source_type": row['source_type'],
                "source_entity": row['source_entity'],
                "execution_id": row['execution_id'],
                "record_count": int(row['record_count']),
                "start_time": row['start_time'].isoformat() if pd.notna(row['start_time']) else None,
                "end_time": row['end_time'].isoformat() if pd.notna(row['end_time']) else None,
                "duration_seconds": duration
            })

        return {
            "total_executions": len(executions),
            "executions": executions,
            "source_types": df['source_type'].unique().tolist(),
            "source_entities": df['source_entity'].unique().tolist()
        }

    def _analyze_data_quality(self, table_name: str) -> Dict:
        """分析資料品質"""
        logger.info("分析資料品質...")

        query = f"""
        SELECT
            COUNT(*) as total_records,
            COUNT(CASE WHEN permanent IS NULL OR permanent = '' THEN 1 END) as null_permanent,
            COUNT(CASE WHEN segment_id IS NULL OR segment_id = '' THEN 1 END) as null_segment_id,
            COUNT(CASE WHEN created_at IS NULL THEN 1 END) as null_created_at,
            COUNT(CASE WHEN REGEXP_CONTAINS(permanent, r'^[a-f0-9]{{32}}$') THEN 1 END) as valid_permanent_format,
            COUNT(CASE WHEN REGEXP_CONTAINS(segment_id, r'^tm:c_715_pc_') THEN 1 END) as valid_segment_format
        FROM `{self.project_id}.{table_name}`
        """

        result = self.client.query(query).to_dataframe()

        if not result.empty:
            row = result.iloc[0]
            total = int(row['total_records'])

            return {
                "total_records": total,
                "data_completeness": {
                    "permanent_completeness": 1 - (int(row['null_permanent']) / total) if total > 0 else 0,
                    "segment_id_completeness": 1 - (int(row['null_segment_id']) / total) if total > 0 else 0,
                    "created_at_completeness": 1 - (int(row['null_created_at']) / total) if total > 0 else 0
                },
                "format_validity": {
                    "permanent_format_validity": int(row['valid_permanent_format']) / total if total > 0 else 0,
                    "segment_format_validity": int(row['valid_segment_format']) / total if total > 0 else 0
                },
                "quality_issues": {
                    "null_permanent": int(row['null_permanent']),
                    "null_segment_id": int(row['null_segment_id']),
                    "null_created_at": int(row['null_created_at']),
                    "invalid_permanent_format": total - int(row['valid_permanent_format']),
                    "invalid_segment_format": total - int(row['valid_segment_format'])
                }
            }

        return {}

    def save_analysis_report(self, output_path: str) -> bool:
        """儲存分析報告"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"分析報告已儲存至: {output_path}")
            return True

        except Exception as e:
            logger.error(f"儲存報告失敗: {e}")
            return False

    def print_summary(self):
        """輸出分析摘要"""
        if not self.analysis_results:
            print("沒有分析結果可顯示")
            return

        basic = self.analysis_results.get("basic_statistics", {})
        segment = self.analysis_results.get("segment_distribution", {})
        category = self.analysis_results.get("product_category_stats", {})
        quality = self.analysis_results.get("data_quality_metrics", {})

        print(f"\n=== 家樂福受眾資料每日統計分析 ===")
        print(f"分析日期: {self.analysis_results['metadata']['target_date']}")
        print(f"表格名稱: {self.analysis_results['metadata']['table_name']}")

        print(f"\n📊 基本統計:")
        print(f"  總記錄數: {basic.get('total_records', 0):,}")
        print(f"  唯一用戶數: {basic.get('unique_users', 0):,}")
        print(f"  平均每用戶記錄數: {basic.get('avg_records_per_user', 0):.1f}")
        print(f"  唯一標籤組合數: {basic.get('unique_segment_combinations', 0):,}")

        print(f"\n🏷️ 標籤分佈:")
        print(f"  平均每用戶標籤數: {segment.get('avg_segments_per_user', 0):.1f}")
        print(f"  唯一標籤數: {segment.get('unique_segments', 0):,}")
        print(f"  標籤層級分佈:")
        level_dist = segment.get('level_distribution', {})
        print(f"    大分類: {level_dist.get('large', 0):,}")
        print(f"    中分類: {level_dist.get('medium', 0):,}")
        print(f"    小分類: {level_dist.get('small', 0):,}")

        print(f"\n🛍️ 商品分類覆蓋:")
        print(f"  大分類覆蓋: {category.get('unique_large_categories', 0)} 個")
        print(f"  中分類覆蓋: {category.get('unique_medium_categories', 0)} 個")
        print(f"  小分類覆蓋: {category.get('unique_small_categories', 0)} 個")

        print(f"\n✅ 資料品質:")
        completeness = quality.get('data_completeness', {})
        validity = quality.get('format_validity', {})
        print(f"  資料完整性: {completeness.get('permanent_completeness', 0):.1%}")
        print(f"  格式有效性: {validity.get('permanent_format_validity', 0):.1%}")

def main():
    """主程式"""
    import sys

    # 解析命令列參數
    target_date = sys.argv[1] if len(sys.argv) > 1 else None

    # 執行分析
    analyzer = DailyStatisticsAnalyzer()
    results = analyzer.analyze_daily_data(target_date)

    if "error" in results:
        logger.error(f"分析失敗: {results['error']}")
        return

    # 儲存報告
    date_str = target_date or datetime.now().strftime('%Y%m%d')
    output_path = f"reports/daily_statistics_{date_str}.json"
    analyzer.save_analysis_report(output_path)

    # 輸出摘要
    analyzer.print_summary()

if __name__ == "__main__":
    main()
