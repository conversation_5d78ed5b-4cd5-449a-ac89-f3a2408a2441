#!/usr/bin/env python3
"""
調試線下資料中 items 欄位的結構

檢查實際的 items 資料格式，了解 SUB_CLASS_KEY 的結構
"""

import json
from google.cloud import bigquery
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_items_structure():
    """調試 items 欄位結構"""
    client = bigquery.Client()

    # 查詢少量資料來檢查結構 - 使用 UNNEST 展開 items
    query = """
    SELECT
        TO_HEX(ph_id) as ph_id_hex,
        transaction_id,
        item.item_id,
        item.item_name,
        item.GRP_CLASS_KEY,
        item.GRP_CLASS_DESC,
        item.CLASS_KEY,
        item.CLASS_DESC,
        item.SUB_CLASS_KEY,
        item.SUB_CLASS_DESC,
        item.quantity,
        item.unit_price,
        item.subtotal
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`,
    UNNEST(items) as item
    WHERE ph_id IS NOT NULL
        AND items IS NOT NULL
        AND ARRAY_LENGTH(items) > 0
    LIMIT 20
    """

    try:
        logger.info("查詢 items 結構...")
        query_job = client.query(query)
        results = query_job.result()

        debug_data = []
        for row in results:
            item_dict = {
                "ph_id_hex": row.ph_id_hex,
                "transaction_id": row.transaction_id,
                "item_id": row.item_id,
                "item_name": row.item_name,
                "GRP_CLASS_KEY": row.GRP_CLASS_KEY,
                "GRP_CLASS_DESC": row.GRP_CLASS_DESC,
                "CLASS_KEY": row.CLASS_KEY,
                "CLASS_DESC": row.CLASS_DESC,
                "SUB_CLASS_KEY": row.SUB_CLASS_KEY,
                "SUB_CLASS_DESC": row.SUB_CLASS_DESC,
                "quantity": float(row.quantity) if row.quantity else None,
                "unit_price": float(row.unit_price) if row.unit_price else None,
                "subtotal": float(row.subtotal) if row.subtotal else None
            }
            debug_data.append(item_dict)

        # 儲存調試結果
        output_path = "reports/debug_items_structure.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"調試結果已儲存至: {output_path}")

        # 輸出摘要
        print("\n=== Items 結構調試摘要 ===")
        for i, item in enumerate(debug_data[:5]):  # 只顯示前5個商品
            print(f"\n商品 {i+1}:")
            print(f"  交易ID: {item['transaction_id']}")
            print(f"  SUB_CLASS_KEY: {item['SUB_CLASS_KEY']}")
            print(f"  商品名稱: {item['item_name']}")
            print(f"  分類: {item['GRP_CLASS_DESC']} > {item['CLASS_DESC']} > {item['SUB_CLASS_DESC']}")

        return debug_data

    except Exception as e:
        logger.error(f"調試失敗: {e}")
        return []

def check_product_mapping_match():
    """檢查商品對應表的匹配情況"""
    import pandas as pd

    # 載入商品分類表
    csv_path = "docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv"
    df = pd.read_csv(csv_path)
    df = df.dropna(subset=['Tagtoo Event 商品編號'])

    product_ids = set(str(row['Tagtoo Event 商品編號']) for _, row in df.iterrows())

    print(f"\n=== 商品對應表檢查 ===")
    print(f"商品分類表中的商品數量: {len(product_ids)}")
    print(f"商品ID範例: {list(product_ids)[:10]}")

    return product_ids

def main():
    """主程式"""
    # 1. 調試 items 結構
    debug_data = debug_items_structure()

    # 2. 檢查商品對應表
    product_ids = check_product_mapping_match()

    # 3. 檢查匹配情況
    if debug_data and product_ids:
        print(f"\n=== 匹配檢查 ===")
        found_matches = 0
        total_items = len(debug_data)

        for item in debug_data:
            sub_class_key = str(item['SUB_CLASS_KEY']) if item['SUB_CLASS_KEY'] else None
            if sub_class_key and sub_class_key in product_ids:
                found_matches += 1
                print(f"✅ 找到匹配: {sub_class_key} - {item['item_name']}")
            else:
                print(f"❌ 未找到匹配: {sub_class_key} - {item['item_name']}")

        print(f"\n匹配統計:")
        print(f"總商品數: {total_items}")
        print(f"匹配數: {found_matches}")
        print(f"匹配率: {found_matches/total_items*100:.1f}%" if total_items > 0 else "0%")

if __name__ == "__main__":
    main()
