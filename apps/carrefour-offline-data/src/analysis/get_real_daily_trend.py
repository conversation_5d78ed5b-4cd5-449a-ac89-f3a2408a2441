#!/usr/bin/env python3
"""
獲取真實的離線資料每日趨勢數據
用於替換第3頁圖表中的模擬數據
"""

import json
import os
import sys
from datetime import datetime
from google.cloud import bigquery
from typing import Dict, Any, List

# 添加 tools 目錄到路徑
sys.path.append('src')
from tools.monitoring_logger import get_global_logger

# 使用智能 BigQuery 客戶端
try:
    from tools.smart_bigquery_client import get_smart_bigquery_client
except ImportError:
    # 如果在 src/analysis 目錄執行，使用相對路徑
    sys.path.append('../tools')
    from smart_bigquery_client import get_smart_bigquery_client


def setup_bigquery_client():
    """設定智能 BigQuery 客戶端"""
    try:
        smart_client = get_smart_bigquery_client()
        return smart_client
    except Exception as e:
        print(f"❌ 智能 BigQuery 客戶端設定失敗: {e}")
        return None


def get_real_daily_trend(smart_client) -> Dict[str, Any]:
    """獲取真實的每日趨勢數據"""

    # 查詢過去60天的每日趨勢
    query = """
    SELECT
      DATE(TIMESTAMP_SECONDS(event_times)) AS date,
      COUNT(*) AS total_record_count,
      COUNT(DISTINCT ph_id) AS unique_ph_id_count,
      COUNT(ph_id) AS non_null_ph_ids,
      COUNT(*) - COUNT(ph_id) AS null_ph_ids,
      ROUND((COUNT(*) - COUNT(ph_id)) * 100.0 / COUNT(*), 2) AS null_percentage
    FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
    WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 60 DAY)
    GROUP BY date
    ORDER BY date DESC
    LIMIT 60
    """

    print("🔍 執行真實每日趨勢查詢...")
    print(f"查詢: {query}")

    try:
        # 使用智能客戶端直接執行查詢
        results = smart_client.query(query).result()

        # 轉換結果
        daily_trend = []
        for row in results:
            daily_trend.append({
                'date': row.date.strftime('%Y-%m-%d'),
                'total_record_count': int(row.total_record_count),
                'unique_ph_id_count': int(row.unique_ph_id_count),
                'non_null_ph_ids': int(row.non_null_ph_ids),
                'null_ph_ids': int(row.null_ph_ids),
                'null_percentage': float(row.null_percentage)
            })

        print(f"✅ 成功獲取 {len(daily_trend)} 天的真實數據")

        return {
            'status': 'success',
            'data': daily_trend
        }

    except Exception as e:
        print(f"❌ 查詢失敗: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e),
            'data': []
        }


def save_real_daily_trend_report(daily_trend_result: Dict[str, Any]) -> str:
    """儲存真實每日趨勢報告"""

    report = {
        'metadata': {
            'analysis_type': 'real_daily_trend_analysis',
            'generated_at': datetime.now().isoformat(),
            'version': '1.0',
            'description': '真實的離線資料每日趨勢分析 - 用於替換模擬數據'
        },
        'daily_trend_analysis': daily_trend_result
    }

    # 儲存報告
    reports_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'reports')
    os.makedirs(reports_dir, exist_ok=True)

    report_path = os.path.join(reports_dir, 'real_daily_trend_analysis.json')

    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"📄 報告已儲存: {report_path}")
    return report_path


def main():
    """主函數"""
    print("🚀 開始獲取真實的離線資料每日趨勢...")
    print("=" * 60)

    # 設定智能 BigQuery 客戶端
    smart_client = setup_bigquery_client()
    if smart_client is None:
        print("❌ 無法設定 BigQuery 客戶端，退出")
        return

    # 獲取真實每日趨勢數據
    daily_trend_result = get_real_daily_trend(smart_client)

    # 儲存報告
    report_path = save_real_daily_trend_report(daily_trend_result)

    # 顯示結果摘要
    print("\n📊 結果摘要:")
    print(f"   狀態: {daily_trend_result['status']}")

    if daily_trend_result['status'] == 'success':
        data = daily_trend_result['data']
        print(f"   數據點數量: {len(data)}")
        if data:
            print(f"   日期範圍: {data[-1]['date']} ~ {data[0]['date']}")
            print(f"   最新一天唯一 ph_id: {data[0]['unique_ph_id_count']:,}")
    elif daily_trend_result['status'] == 'failed':
        print(f"   錯誤: {daily_trend_result['error']}")

    print(f"\n📄 完整報告: {report_path}")
    print("✅ 分析完成！")


if __name__ == '__main__':
    main()
