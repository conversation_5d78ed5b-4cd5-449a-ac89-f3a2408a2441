#!/usr/bin/env python3
"""
統一主控腳本 - 家樂福離線資料完整分析報告生成器

此腳本整合所有分析腳本的執行順序，自動生成統一的完整投影片報告。
取代目前分散的執行方式，提供單一入口點執行完整分析流程。

執行方式：
    python src/generate_complete_report.py

功能：
1. 按正確順序執行所有分析腳本
2. 監控每個步驟的執行狀態
3. 生成統一的完整投影片報告
4. 提供詳細的執行日誌和錯誤處理

作者：AI Assistant
日期：2025-08-20
"""

import os
import sys
import json
import subprocess
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CarrefourReportGenerator:
    """家樂福完整報告生成器"""

    def __init__(self):
        """初始化生成器"""
        self.src_dir = Path("src")
        self.reports_dir = Path("reports")
        self.web_dir = Path("web")
        self.docs_dir = Path("docs")

        # 確保目錄存在
        for directory in [self.reports_dir, self.web_dir, self.docs_dir]:
            directory.mkdir(exist_ok=True)

        # 分析腳本執行順序（按依賴關係排序）
        self.analysis_scripts = [
            {
                'name': 'analysis/offline_deep_analysis.py',
                'description': '離線資料深度分析',
                'output': 'reports/offline_deep_analysis.json',
                'required': True
            },
            {
                'name': 'analysis/items_format_validation.py',
                'description': 'Items 格式驗證',
                'output': 'reports/items_format_validation.json',
                'required': True
            },
            {
                'name': 'analysis/null_ph_id_analysis.py',
                'description': 'NULL ph_id 分析',
                'output': 'reports/null_ph_id_analysis.json',
                'required': True
            },
            {
                'name': 'analysis/negative_values_investigation.py',
                'description': '負數資料調查',
                'output': 'reports/negative_values_investigation.json',
                'required': True
            },
            {
                'name': 'analysis/comprehensive_data_distribution_analysis.py',
                'description': '綜合資料分佈分析',
                'output': 'reports/detailed_data_distribution_analysis.json',
                'required': True
            },
            {
                'name': 'analysis/create_corrected_analysis.py',
                'description': '修正分析報告',
                'output': 'reports/corrected_overlap_analysis.json',
                'required': True
            }
        ]

        # 整合腳本（按執行順序）
        self.integration_scripts = [
            {
                'name': 'presentation/create_all_in_one_presentation.py',
                'description': '生成統一投影片報告',
                'output': 'web/all_in_one_analysis.html',
                'required': True
            }
        ]

        self.execution_log = []

    def run_script(self, script_name: str, description: str) -> Dict[str, Any]:
        """執行單個腳本"""
        script_path = self.src_dir / script_name

        if not script_path.exists():
            logger.warning(f"腳本不存在: {script_path}")
            return {
                'script': script_name,
                'status': 'skipped',
                'reason': 'script_not_found',
                'execution_time': 0
            }

        logger.info(f"🔄 執行: {description} ({script_name})")
        start_time = datetime.now()

        try:
            # 執行腳本
            result = subprocess.run(
                [sys.executable, str(script_path)],
                capture_output=True,
                text=True,
                timeout=300  # 5分鐘超時
            )

            execution_time = (datetime.now() - start_time).total_seconds()

            if result.returncode == 0:
                logger.info(f"✅ 完成: {description} ({execution_time:.1f}s)")
                return {
                    'script': script_name,
                    'status': 'success',
                    'execution_time': execution_time,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            else:
                logger.error(f"❌ 失敗: {description}")
                logger.error(f"錯誤輸出: {result.stderr}")
                return {
                    'script': script_name,
                    'status': 'failed',
                    'execution_time': execution_time,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'return_code': result.returncode
                }

        except subprocess.TimeoutExpired:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"⏰ 超時: {description} (>{execution_time:.1f}s)")
            return {
                'script': script_name,
                'status': 'timeout',
                'execution_time': execution_time,
                'reason': 'execution_timeout'
            }
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"💥 異常: {description} - {e}")
            return {
                'script': script_name,
                'status': 'error',
                'execution_time': execution_time,
                'error': str(e)
            }

    def check_output_file(self, output_path: str) -> bool:
        """檢查輸出檔案是否存在"""
        return Path(output_path).exists()

    def run_analysis_phase(self) -> Dict[str, Any]:
        """執行分析階段"""
        logger.info("📊 開始執行分析階段...")

        phase_results = {
            'phase': 'analysis',
            'start_time': datetime.now().isoformat(),
            'scripts': [],
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0
        }

        for script_config in self.analysis_scripts:
            result = self.run_script(
                script_config['name'],
                script_config['description']
            )

            # 檢查輸出檔案
            if result['status'] == 'success':
                output_exists = self.check_output_file(script_config['output'])
                result['output_exists'] = output_exists
                if not output_exists:
                    logger.warning(f"⚠️ 輸出檔案不存在: {script_config['output']}")

            phase_results['scripts'].append(result)

            # 統計結果
            if result['status'] == 'success':
                phase_results['success_count'] += 1
            elif result['status'] == 'skipped':
                phase_results['skipped_count'] += 1
            else:
                phase_results['failed_count'] += 1

                # 如果是必需的腳本失敗，記錄警告
                if script_config['required']:
                    logger.warning(f"⚠️ 必需腳本執行失敗: {script_config['name']}")

        phase_results['end_time'] = datetime.now().isoformat()
        phase_results['total_time'] = sum(s['execution_time'] for s in phase_results['scripts'])

        logger.info(f"📊 分析階段完成: {phase_results['success_count']} 成功, "
                   f"{phase_results['failed_count']} 失敗, {phase_results['skipped_count']} 跳過")

        return phase_results

    def run_integration_phase(self) -> Dict[str, Any]:
        """執行整合階段"""
        logger.info("🔗 開始執行整合階段...")

        phase_results = {
            'phase': 'integration',
            'start_time': datetime.now().isoformat(),
            'scripts': [],
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0
        }

        for script_config in self.integration_scripts:
            result = self.run_script(
                script_config['name'],
                script_config['description']
            )

            # 檢查輸出檔案
            if result['status'] == 'success':
                output_exists = self.check_output_file(script_config['output'])
                result['output_exists'] = output_exists
                if not output_exists:
                    logger.warning(f"⚠️ 輸出檔案不存在: {script_config['output']}")

            phase_results['scripts'].append(result)

            # 統計結果
            if result['status'] == 'success':
                phase_results['success_count'] += 1
            elif result['status'] == 'skipped':
                phase_results['skipped_count'] += 1
            else:
                phase_results['failed_count'] += 1

                # 如果是必需的腳本失敗，記錄警告
                if script_config['required']:
                    logger.warning(f"⚠️ 必需腳本執行失敗: {script_config['name']}")

        phase_results['end_time'] = datetime.now().isoformat()
        phase_results['total_time'] = sum(s['execution_time'] for s in phase_results['scripts'])

        logger.info(f"🔗 整合階段完成: {phase_results['success_count']} 成功, "
                   f"{phase_results['failed_count']} 失敗, {phase_results['skipped_count']} 跳過")

        return phase_results

def main():
    """主執行函數"""
    print("🚀 家樂福離線資料完整分析報告生成器")
    print("=" * 60)

    generator = CarrefourReportGenerator()

    # 建立執行報告
    execution_report = {
        'operation': 'complete_report_generation',
        'start_time': datetime.now().isoformat(),
        'phases': {}
    }

    try:
        # 階段1：執行分析腳本
        analysis_results = generator.run_analysis_phase()
        execution_report['phases']['analysis'] = analysis_results

        # 階段2：執行整合腳本
        integration_results = generator.run_integration_phase()
        execution_report['phases']['integration'] = integration_results

        # 計算總體統計
        total_success = analysis_results['success_count'] + integration_results['success_count']
        total_failed = analysis_results['failed_count'] + integration_results['failed_count']
        total_skipped = analysis_results['skipped_count'] + integration_results['skipped_count']
        total_time = analysis_results['total_time'] + integration_results['total_time']

        execution_report['end_time'] = datetime.now().isoformat()
        execution_report['summary'] = {
            'total_scripts': total_success + total_failed + total_skipped,
            'success_count': total_success,
            'failed_count': total_failed,
            'skipped_count': total_skipped,
            'total_execution_time': total_time,
            'overall_status': 'success' if total_failed == 0 else 'partial_success'
        }

        # 儲存執行報告
        report_file = generator.reports_dir / "complete_report_generation.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(execution_report, f, ensure_ascii=False, indent=2, default=str)

        # 顯示總結
        print(f"\n🎯 完整報告生成總結:")
        print(f"   ✅ 成功執行: {total_success} 個腳本")
        print(f"   ❌ 執行失敗: {total_failed} 個腳本")
        print(f"   ⏭️ 跳過執行: {total_skipped} 個腳本")
        print(f"   ⏱️ 總執行時間: {total_time:.1f} 秒")
        print(f"   📄 執行報告: {report_file}")

        if total_failed == 0:
            print(f"\n🎉 所有腳本執行成功！完整報告已生成。")
        else:
            print(f"\n⚠️ 部分腳本執行失敗，請檢查執行報告了解詳情。")

        # 檢查主要輸出檔案
        main_output = generator.web_dir / "all_in_one_analysis.html"
        if main_output.exists():
            print(f"🌐 主要報告: {main_output}")
        else:
            print(f"❌ 主要報告未生成: {main_output}")

    except Exception as e:
        logger.error(f"💥 執行過程發生異常: {e}")
        execution_report['end_time'] = datetime.now().isoformat()
        execution_report['error'] = str(e)
        execution_report['summary'] = {'overall_status': 'failed'}

        # 儲存錯誤報告
        report_file = generator.reports_dir / "complete_report_generation_error.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(execution_report, f, ensure_ascii=False, indent=2, default=str)

        print(f"❌ 執行失敗，錯誤報告: {report_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
