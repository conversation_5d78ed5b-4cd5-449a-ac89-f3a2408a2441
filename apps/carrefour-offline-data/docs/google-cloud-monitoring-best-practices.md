# Google Cloud Monitoring Alert Policy 最佳實務指南

## 📋 概述

本文檔記錄了在家樂福離線資料專案中使用 Google Cloud Monitoring Alert Policy 的最佳實務和常見問題解決方案。

## 🚨 Alert Policy Filter 語法修正

### 問題描述

在 Terraform 配置 `google_monitoring_alert_policy` 時遭遇 filter 語法錯誤：

```
錯誤訊息：Alert Policy 的 filter 語法無效
問題 filter：severity>="ERROR"
錯誤原因：左側必須使用 {group, metadata, metric, project, resource} 前綴
```

### 根本原因分析

1. **語法混淆**：Cloud Logging 和 Cloud Monitoring 使用不同的 filter 語法
2. **條件類型錯誤**：監控日誌條目應使用 `condition_matched_log`，而非 `condition_threshold`
3. **指標 vs 日誌**：`condition_threshold` 用於指標監控，`condition_matched_log` 用於日誌監控

### 正確解決方案

#### ❌ 錯誤配置（修正前）

```hcl
conditions {
  display_name = "Cloud Scheduler 執行失敗"

  condition_threshold {
    filter = "resource.type=\"cloud_scheduler_job\" AND severity>=\"ERROR\""
    # 這會導致語法錯誤
  }
}
```

#### ✅ 正確配置（修正後）

```hcl
conditions {
  display_name = "Cloud Scheduler 執行失敗"

  condition_matched_log {
    # 直接監控日誌條目，支援 severity 和 HTTP 狀態碼過濾
    filter = "resource.type=\"cloud_scheduler_job\" AND resource.labels.job_id=\"${local.service_full_name}-schedule\" AND (severity=\"ERROR\" OR httpRequest.status>=400)"

    # 可選：提取標籤用於通知詳細資訊
    label_extractors = {
      "job_id"      = "EXTRACT(resource.labels.job_id)"
      "status_code" = "EXTRACT(httpRequest.status)"
      "error_msg"   = "EXTRACT(textPayload)"
    }
  }
}
```

## 📚 Alert Policy 條件類型選擇指南

### condition_threshold

- **用途**：監控指標（metrics）
- **適用場景**：CPU 使用率、記憶體使用量、執行次數等
- **語法特點**：需要 `metric.type` 和聚合配置

```hcl
condition_threshold {
  filter = "metric.type=\"cloudfunctions.googleapis.com/function/execution_count\""
  comparison = "COMPARISON_GT"
  threshold_value = 0

  aggregations {
    alignment_period = "300s"
    per_series_aligner = "ALIGN_RATE"
  }
}
```

### condition_matched_log

- **用途**：監控日誌條目
- **適用場景**：錯誤日誌、特定事件、HTTP 狀態碼等
- **語法特點**：直接使用 Cloud Logging filter 語法
- **⚠️ 重要**：必須配置 `notification_rate_limit`

```hcl
condition_matched_log {
  filter = "resource.type=\"cloud_scheduler_job\" AND severity=\"ERROR\""

  label_extractors = {
    "error_details" = "EXTRACT(textPayload)"
  }
}

# 必須配置 alert_strategy.notification_rate_limit
alert_strategy {
  notification_rate_limit {
    period = "300s"  # 必須指定，避免過度通知
  }
}
```

### condition_sql

- **用途**：使用 SQL 查詢日誌
- **適用場景**：複雜的日誌分析和聚合
- **語法特點**：使用 GoogleSQL 語法

```hcl
condition_sql {
  query = "SELECT severity, resource FROM my_project.global._Default._AllLogs WHERE severity = 'ERROR'"

  minutes {
    periodicity = 5
  }

  row_count_test {
    comparison = "COMPARISON_GT"
    threshold = "0"
  }
}
```

## 🔍 Cloud Scheduler 監控最佳實務

### 1. 日誌級別過濾

```hcl
# 監控所有錯誤級別
filter = "severity=\"ERROR\""

# 監控 HTTP 錯誤狀態碼
filter = "httpRequest.status>=400"

# 組合條件：錯誤級別或 HTTP 錯誤
filter = "severity=\"ERROR\" OR httpRequest.status>=400"
```

### 2. 資源標籤過濾

```hcl
# 特定 Scheduler Job
filter = "resource.type=\"cloud_scheduler_job\" AND resource.labels.job_id=\"my-job\""

# 特定專案
filter = "resource.labels.project_id=\"my-project\""
```

### 3. 標籤提取器

```hcl
label_extractors = {
  "job_id"        = "EXTRACT(resource.labels.job_id)"
  "status_code"   = "EXTRACT(httpRequest.status)"
  "error_message" = "EXTRACT(textPayload)"
  "timestamp"     = "EXTRACT(timestamp)"
}
```

## 🛠️ 常見問題排除

### 問題 1：severity 語法錯誤

**錯誤**：`severity>="ERROR"` 不被接受
**解決**：使用 `severity="ERROR"` 或 `severity IN ("ERROR", "CRITICAL")`

### 問題 2：Log-based Alert Policy 缺少 notification_rate_limit

**錯誤**：`log-based alert policies need to specify a notification rate limit`
**解決**：在 `alert_strategy` 中添加 `notification_rate_limit`

```hcl
alert_strategy {
  notification_rate_limit {
    period = "300s"  # 必須指定
  }
}
```

### 問題 3：指標不存在

**錯誤**：`metric.type` 指向不存在的指標
**解決**：使用 `gcloud logging metrics list` 確認可用指標

### 問題 4：過度觸發告警

**錯誤**：正常的 INFO 日誌也觸發告警
**解決**：精確過濾條件，排除正常狀態

```hcl
# 排除成功狀態
filter = "severity=\"ERROR\" AND NOT (textPayload:\"success\" OR httpRequest.status=200)"
```

## 📊 監控策略建議

### 1. 分層監控

- **Level 1**：基礎設施監控（CPU、記憶體、網路）
- **Level 2**：應用程式監控（執行次數、錯誤率、延遲）
- **Level 3**：業務邏輯監控（資料處理量、業務指標）

### 2. 告警閾值設定

- **執行失敗**：任何錯誤立即告警（threshold = 0）
- **執行時間**：95% 分位數超過正常值 2 倍
- **記憶體使用**：超過配置的 90%

### 3. 通知策略

- **Critical**：立即通知（電子郵件 + SMS）
- **Warning**：延遲通知（僅電子郵件）
- **Info**：日報彙整

## 🔧 Terraform 配置範本

### 完整的 Alert Policy 範本

```hcl
resource "google_monitoring_alert_policy" "scheduler_failure" {
  display_name = "${var.service_name}-scheduler-failure"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Scheduler 執行失敗"

    condition_matched_log {
      filter = join(" AND ", [
        "resource.type=\"cloud_scheduler_job\"",
        "resource.labels.job_id=\"${var.service_name}-schedule\"",
        "(severity=\"ERROR\" OR httpRequest.status>=400)"
      ])

      label_extractors = {
        "job_id"      = "EXTRACT(resource.labels.job_id)"
        "status_code" = "EXTRACT(httpRequest.status)"
        "error_msg"   = "EXTRACT(textPayload)"
      }
    }
  }

  notification_channels = var.notification_channels

  alert_strategy {
    auto_close = "3600s"

    notification_rate_limit {
      period = "300s"  # 最多每 5 分鐘一次通知
    }
  }

  documentation {
    content = "Cloud Scheduler 執行失敗告警。檢查步驟：1) 查看 Scheduler 日誌 2) 確認 Function 狀態 3) 檢查權限配置"
    mime_type = "text/markdown"
  }
}
```

## 📝 維護指南

### 1. 定期檢查

- 每月檢查告警觸發頻率
- 季度檢查閾值合理性
- 年度檢查監控策略有效性

### 2. 文檔更新

- 新增監控項目時更新文檔
- 修正配置時記錄變更原因
- 定期檢查最佳實務是否過時

### 3. 測試驗證

- 定期測試告警觸發機制
- 驗證通知渠道有效性
- 確認故障排除步驟正確性

---

**最後更新**：2025-09-03
**適用版本**：Terraform Google Provider v4.0+
**維護者**：家樂福離線資料團隊
