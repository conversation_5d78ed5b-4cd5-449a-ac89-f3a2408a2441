# ph_id_hex 輔助欄位功能說明

> 版本: v8.1.0
> 更新日期: 2025-09-15
> 狀態: ✅ 已實作並測試完成

## 📋 功能概述

ph_id_hex 輔助欄位功能是為了改善團隊同事在查詢家樂福離線資料時的使用體驗而開發的功能。此功能會自動為所有複製的 BigQuery 表格新增一個 `ph_id_hex` STRING 欄位，內容為原始 `ph_id` BYTES 欄位的十六進制字串表示。

## 🎯 解決的問題

### 原有問題

在 ph_id_hex 功能實作前，團隊同事在查詢時必須使用以下方式：

```sql
-- 原有的查詢方式（不便）
SELECT
    TO_HEX(ph_id) as ph_id_hex,
    event_times,
    transaction_amount
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE TO_HEX(ph_id) = 'ABC123DEF456'
```

### 改善後

現在可以直接使用 ph_id_hex 欄位：

```sql
-- 新的查詢方式（便利）
SELECT
    ph_id_hex,
    event_times,
    transaction_amount
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE ph_id_hex = 'ABC123DEF456'
```

## 🏗️ 技術實作

### 涵蓋的表格

此功能會自動應用於以下三個 BigQuery 複製排程任務：

1. **carrefour-daily-replication-prod** (每日複製)

   - 目標表格: `carrefour_offline_transaction_day`

2. **carrefour-monthly-replication-prod** (每月複製)

   - 目標表格: `carrefour_offline_transaction_month`

3. **carrefour-monthly-merge-prod** (每月合併)
   - 目標表格: `carrefour_offline_transaction`

### 實作流程

#### 1. 日常複製流程 (automated_daily_replication.py)

```python
def _add_ph_id_hex_and_populate(self, target_table_id: str) -> Dict[str, Any]:
    # 1. 新增欄位（若不存在）
    ALTER TABLE `{target_table_id}`
    ADD COLUMN IF NOT EXISTS ph_id_hex STRING

    # 2. 成本估算（dry-run）
    UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id)

    # 3. 實際執行填充
    UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id)
```

#### 2. 月度合併流程 (automated_monthly_merge.py)

```python
def _add_ph_id_hex_and_populate(self, target_table_id: str) -> Dict[str, Any]:
    # 1. 新增欄位（若不存在）
    ALTER TABLE `{target_table_id}`
    ADD COLUMN IF NOT EXISTS ph_id_hex STRING

    # 2. 成本估算（dry-run）
    UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id) WHERE ph_id_hex IS NULL

    # 3. 實際執行填充（僅填充空值）
    UPDATE `{target_table_id}` SET ph_id_hex = TO_HEX(ph_id) WHERE ph_id_hex IS NULL
```

### 關鍵特性

#### 🔒 向後相容性

- 保持原有 `ph_id` (BYTES) 欄位完全不變
- 僅新增 `ph_id_hex` (STRING) 輔助欄位
- 現有查詢和應用程式不受影響

#### 💰 成本控制機制

- 內建 dry-run 成本估算功能
- 預估成本上限: $2.00 USD
- 超過上限時會記錄警告但仍會執行（依業務需求）
- 使用 BigQuery 標準定價: $5.00/TB

#### 🛡️ 錯誤處理

- ph_id_hex 處理失敗不會中斷主要複製流程
- 完整的錯誤日誌記錄
- 非阻塞式錯誤處理機制

#### ⚡ 效能最佳化

- 使用 `ADD COLUMN IF NOT EXISTS` 確保冪等性
- 月度合併使用 `WHERE ph_id_hex IS NULL` 避免重複處理
- 批次處理，避免逐筆更新

## 🧪 測試驗證

### 測試套件

完整的測試套件位於 `tests/test_ph_id_hex_functionality.py`，包含：

1. **單元測試**

   - 方法存在性驗證
   - ALTER TABLE SQL 正確性
   - UPDATE SQL 正確性
   - 成本控制機制
   - 錯誤處理機制
   - 配置參數一致性

2. **整合測試**
   - 完整流程測試（需要實際 BigQuery 環境）

### 執行測試

```bash
# 在 venv 環境中執行
cd /path/to/carrefour-offline-data
source venv/bin/activate

# 執行語法檢查和單元測試
python run_ph_id_hex_tests.py --syntax --unit

# 執行所有測試
python run_ph_id_hex_tests.py --all
```

## 📊 監控和日誌

### 日誌訊息範例

#### 成功案例

```
2025-09-15 10:30:15 - INFO - 準備在目標表新增輔助欄位 ph_id_hex: tagtoo-tracking.event_prod.carrefour_offline_transaction_day
2025-09-15 10:30:16 - INFO - ph_id_hex UPDATE 預估掃描: 1,048,576 bytes (約 $0.0000)
2025-09-15 10:30:18 - INFO - ph_id_hex 欄位填充完成
2025-09-15 10:30:18 - INFO - ✅ ph_id_hex 輔助欄位處理完成 - 表格: offline_transaction_day
```

#### 成本警告案例

```
2025-09-15 10:30:16 - WARNING - ph_id_hex 填充預估成本 $3.5000 超過上限 $2.00，依需求仍將執行
```

#### 錯誤處理案例

```
2025-09-15 10:30:16 - ERROR - ⚠️ ph_id_hex 輔助欄位處理失敗 - 表格: offline_transaction_day, 錯誤: Connection timeout
```

### 監控指標

- 成功率: ph_id_hex 處理成功的比例
- 執行時間: ph_id_hex 處理所需時間
- 成本消耗: 實際 BigQuery 查詢成本
- 錯誤率: ph_id_hex 處理失敗的比例

## 🚀 部署狀態

### 當前狀態

- ✅ 程式碼實作完成
- ✅ 單元測試通過
- ✅ 語法檢查通過
- ✅ 文檔更新完成
- 🔄 等待生產環境部署

### 部署計畫

1. **階段一**: 更新 Cloud Function 程式碼
2. **階段二**: 驗證 dry-run 模式運作正常
3. **階段三**: 啟用生產模式
4. **階段四**: 監控和驗證結果

## 📚 使用指南

### 查詢範例

#### 基本查詢

```sql
SELECT
    ph_id_hex,
    event_times,
    transaction_amount,
    store_id
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE ph_id_hex = 'ABC123DEF456789'
LIMIT 10;
```

#### 聚合查詢

```sql
SELECT
    ph_id_hex,
    COUNT(*) as transaction_count,
    SUM(transaction_amount) as total_amount
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '2025-09-15'
GROUP BY ph_id_hex
ORDER BY total_amount DESC
LIMIT 100;
```

#### 與原始欄位比較（驗證用）

```sql
SELECT
    ph_id,
    ph_id_hex,
    TO_HEX(ph_id) as computed_hex,
    ph_id_hex = TO_HEX(ph_id) as is_consistent
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
LIMIT 10;
```

### 最佳實務

1. **優先使用 ph_id_hex**: 在新的查詢中優先使用 ph_id_hex 欄位
2. **保持相容性**: 現有使用 TO_HEX(ph_id) 的查詢可以繼續使用
3. **效能考量**: ph_id_hex 為 STRING 類型，在某些情況下可能比 BYTES 類型查詢更快
4. **資料驗證**: 可以使用 `ph_id_hex = TO_HEX(ph_id)` 來驗證資料一致性

## 🔧 故障排除

### 常見問題

#### Q: ph_id_hex 欄位不存在

**A**: 檢查表格是否為最新複製的版本，舊的表格可能尚未包含此欄位。

#### Q: ph_id_hex 欄位為空值

**A**: 可能是 ph_id_hex 填充過程失敗，檢查 Cloud Function 日誌。

#### Q: ph_id_hex 與 TO_HEX(ph_id) 不一致

**A**: 這是嚴重問題，請立即聯絡技術支援團隊。

### 手動修復

如果需要手動為表格新增 ph_id_hex 欄位：

```sql
-- 1. 新增欄位
ALTER TABLE `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
ADD COLUMN IF NOT EXISTS ph_id_hex STRING;

-- 2. 填充資料
UPDATE `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
SET ph_id_hex = TO_HEX(ph_id)
WHERE ph_id_hex IS NULL;
```

## 📞 技術支援

**功能負責人**: Frank Zheng
**聯絡方式**: <EMAIL>
**技術文檔**: 本文檔及相關程式碼註解
**測試環境**: 可使用 dry-run 模式進行安全測試
