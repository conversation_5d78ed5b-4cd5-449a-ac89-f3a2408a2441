# 家樂福離線資料分析 - 開發建議與下一步

> **文件目的**：內部開發團隊參考，記錄重疊率異常調查計畫和技術建議
>
> **最後更新**：2025-08-19
>
> **狀態**：待調查異常 - 7天後重疊率未增加原因不明

## 🔍 調查計畫與建議

### 1. 深度調查

**問題**：為什麼 7天後 unique user.ph 數量不再增加？

**調查方向**：

- 檢查線上資料 `tagtoo-tracking.event_prod.tagtoo_event` 的資料保留政策
- 驗證不同時間範圍的 user.ph 唯一值分佈
- 分析是否存在資料清理或歸檔機制

**執行腳本**：

```bash
# 建議建立的調查腳本
src/investigate_overlap_anomaly.py
```

### 2. 雜湊驗證

**目標**：建立 SHA256 逆向驗證機制確認匹配準確性

**技術方案**：

- 實施受控的雜湊值驗證
- 建立已知 ph_id 與 user.ph 對應關係的測試集
- 驗證 `TO_HEX(ph_id)` 與 `user.ph` 的一致性

**執行腳本**：

```bash
# 建議建立的驗證工具
src/hash_validation_tools.py
docs/hash_validation_plan.md
```

### 3. 資料驗證

**範圍**：檢查 BigQuery 查詢邏輯和資料處理流程

**檢查項目**：

- 重疊率計算邏輯的正確性
- 時間範圍篩選條件
- 資料去重和聚合邏輯
- 成本估算和查詢最佳化

### 4. 替代策略

**評估其他用戶識別匹配方法**：

**選項 A**：基於交易模式匹配

- 利用交易金額、時間、商品類別等特徵
- 建立機器學習模型進行用戶匹配

**選項 B**：多重識別碼策略

- 結合 ph_id、交易時間、金額等多個維度
- 提高匹配準確性和覆蓋率

**選項 C**：增量匹配機制

- 建立即時或準即時的匹配流程
- 減少對歷史資料回溯的依賴

### 5. 暫緩決策

**原則**：在找出真實原因前，避免基於假設做業務決策

**具體措施**：

- 不基於當前 2.379% 重疊率做最終業務評估
- 保持現有資料收集和分析流程
- 等待技術調查結果再制定長期策略

## 📊 當前分析結果摘要

### 重疊率分析

- **最高重疊率**：2.3793%（第7天）
- **重疊用戶數**：71,014 人
- **平台期現象**：第7天後重疊率不再增長
- **分析成本**：$0.1028 USD（可接受範圍）

### 資料品質評估

- **離線資料**：15,242,556 筆記錄，NULL ph_id 比例 4.756%
- **線上資料**：8,834,918 筆事件，87,594 唯一 user.ph
- **格式匹配**：TO_HEX(ph_id) vs user.ph 格式正確
- **時間範圍**：2025-06-14 ~ 2025-08-12（59天）

### Items 資料品質

- **平均 items 數量**：5.43 個/交易
- **欄位完整性**：100%（所有關鍵欄位）
- **資料品質問題**：3,161 筆（負數量、負小計、計算不一致）
- **業務原因**：主要為退貨處理和折扣優惠

### NULL ph_id 問題

- **NULL 記錄數**：724,938 筆（4.756%）
- **平均交易金額**：$632.37
- **時間分佈**：2025-06-14 ~ 2025-08-11
- **具體案例**：已提供 20 筆交易ID供技術團隊查證

## 🛠️ 技術債務與改進

### 成本最佳化

- 當前查詢成本控制良好（< $0.5 USD）
- 建議建立自動化成本監控機制
- 考慮使用 BigQuery 預留容量降低成本

### 資料管道改進

- 建立即時資料品質監控
- 實施自動化異常檢測
- 建立資料血緣追蹤機制

### 報告自動化

- 整合所有分析腳本到統一工作流程
- 建立定期報告生成機制
- 實施變更通知和警報系統

## 📋 行動項目清單

### 短期（1-2週）

- [ ] 建立 `investigate_overlap_anomaly.py` 腳本
- [ ] 實施 SHA256 驗證機制
- [ ] 深度調查線上資料保留政策
- [ ] 與家樂福技術團隊溝通 NULL ph_id 問題

### 中期（1個月）

- [ ] 評估替代匹配策略
- [ ] 建立自動化監控機制
- [ ] 最佳化查詢成本和效能
- [ ] 建立完整的資料血緣文檔

### 長期（3個月）

- [ ] 實施即時匹配機制
- [ ] 建立機器學習匹配模型
- [ ] 整合到生產資料管道
- [ ] 建立業務 KPI 監控儀表板

## 📞 聯絡資訊

**技術負責人**：開發團隊
**業務負責人**：資料分析團隊
**專案狀態**：調查階段
**下次檢討**：待技術調查完成

---

_本文件為內部開發參考，不對外分享。所有技術決策需基於進一步調查結果。_
