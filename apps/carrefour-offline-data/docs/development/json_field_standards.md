# JSON 欄位命名標準規範

> 建立日期：2025-08-20
> 版本：1.0
> 適用範圍：家樂福離線資料分析專案所有 JSON 輸出檔案

## 📋 **命名原則**

### **1. 基本原則**

- **使用 snake_case**：所有欄位名稱使用小寫字母和底線
- **語義清晰**：欄位名稱應清楚表達其含義
- **一致性**：相同概念在所有檔案中使用相同命名
- **英文命名**：所有欄位名稱使用英文

### **2. 複數形式規則**

- **計數欄位使用單數**：`unique_ph_id_count`（而非 `unique_ph_ids`）
- **集合欄位使用複數**：`ph_ids`（當欄位包含 ph_id 列表時）
- **統計欄位使用單數**：`total_record_count`（而非 `total_records`）

## 🏷️ **標準欄位命名規範**

### **核心資料統計欄位**

| 概念               | 標準欄位名稱                | 說明                           | 範例值   |
| ------------------ | --------------------------- | ------------------------------ | -------- |
| 總記錄數           | `total_record_count`        | 資料集中的總記錄數量           | 15242556 |
| 唯一 ph_id 數量    | `unique_ph_id_count`        | 不重複的 ph_id 數量            | 2984689  |
| NULL ph_id 數量    | `null_ph_id_count`          | ph_id 為 NULL 的記錄數量       | 725436   |
| NULL ph_id 比例    | `null_ph_id_percentage`     | NULL ph_id 佔總記錄的百分比    | 4.756    |
| 非 NULL ph_id 數量 | `non_null_ph_id_count`      | ph_id 非 NULL 的記錄數量       | 14517120 |
| 非 NULL ph_id 比例 | `non_null_ph_id_percentage` | 非 NULL ph_id 佔總記錄的百分比 | 95.244   |

### **時間相關欄位**

| 概念           | 標準欄位名稱             | 說明                 | 範例值                 |
| -------------- | ------------------------ | -------------------- | ---------------------- |
| 執行時間（秒） | `execution_time_seconds` | 查詢或分析的執行時間 | 2.308496               |
| 開始日期       | `start_date`             | 資料範圍的開始日期   | "2025-06-14"           |
| 結束日期       | `end_date`               | 資料範圍的結束日期   | "2025-08-12"           |
| 總天數         | `total_day_count`        | 資料範圍涵蓋的天數   | 59                     |
| 生成時間       | `generated_at`           | 報告生成的時間戳     | "2025-08-20T10:30:00Z" |

### **重疊率分析欄位**

| 概念       | 標準欄位名稱         | 說明                       | 範例值  |
| ---------- | -------------------- | -------------------------- | ------- |
| 重疊率     | `overlap_percentage` | 重疊用戶佔離線用戶的百分比 | 2.379   |
| 重疊用戶數 | `overlap_user_count` | 重疊的用戶數量             | 71014   |
| 離線用戶數 | `offline_user_count` | 離線資料中的唯一用戶數     | 2984689 |
| 線上用戶數 | `online_user_count`  | 線上資料中的唯一用戶數     | 94778   |

### **成本和效能欄位**

| 概念                   | 標準欄位名稱         | 說明                    | 範例值    |
| ---------------------- | -------------------- | ----------------------- | --------- |
| 估計成本（美元）       | `estimated_cost_usd` | BigQuery 查詢的估計成本 | 0.0025    |
| 處理的資料量（位元組） | `bytes_processed`    | 查詢處理的資料量        | 1048576   |
| 查詢狀態               | `query_status`       | 查詢執行狀態            | "SUCCESS" |
| 資料列數               | `row_count`          | 查詢結果的資料列數      | 1000      |

### **元資料欄位**

| 概念     | 標準欄位名稱    | 說明             | 範例值                                             |
| -------- | --------------- | ---------------- | -------------------------------------------------- |
| 版本     | `version`       | 分析版本號       | "1.0.0"                                            |
| 描述     | `description`   | 分析或報告的描述 | "離線資料深度分析"                                 |
| 分析類型 | `analysis_type` | 分析的類型或類別 | "data_distribution"                                |
| 資料來源 | `data_source`   | 資料的來源表格   | "tw-eagle-prod.rmn_tagtoo.offline_transaction_day" |

## 🚫 **避免使用的命名模式**

### **不推薦的命名**

- ❌ `unique_ph_ids`（複數形式用於計數）
- ❌ `execution_sec`（縮寫不清楚）
- ❌ `total_records`（缺少 count 後綴）
- ❌ `ph_count`（不夠具體）
- ❌ `percentage`（缺少具體說明）

### **推薦的替代命名**

- ✅ `unique_ph_id_count`
- ✅ `execution_time_seconds`
- ✅ `total_record_count`
- ✅ `unique_ph_id_count`
- ✅ `null_ph_id_percentage`

## 📊 **結構化規範**

### **標準 metadata 結構**

```json
{
  "metadata": {
    "version": "1.0.0",
    "generated_at": "2025-08-20T10:30:00Z",
    "description": "分析描述",
    "analysis_type": "分析類型",
    "data_source": "資料來源表格"
  }
}
```

### **標準執行資訊結構**

```json
{
  "execution_info": {
    "execution_time_seconds": 2.308496,
    "estimated_cost_usd": 0.0025,
    "query_status": "SUCCESS",
    "row_count": 1000,
    "bytes_processed": 1048576
  }
}
```

### **標準統計資訊結構**

```json
{
  "basic_statistics": {
    "total_record_count": 15242556,
    "unique_ph_id_count": 2984689,
    "null_ph_id_count": 725436,
    "null_ph_id_percentage": 4.756,
    "non_null_ph_id_count": 14517120,
    "non_null_ph_id_percentage": 95.244,
    "start_date": "2025-06-14",
    "end_date": "2025-08-12",
    "total_day_count": 59
  }
}
```

## 🔄 **遷移策略**

### **階段一：識別不一致欄位**

1. 掃描所有 JSON 檔案識別不符合標準的欄位
2. 建立欄位對應表（舊名稱 → 新名稱）
3. 評估影響範圍

### **階段二：更新分析腳本**

1. 修改 `src/analysis/` 目錄中的所有分析腳本
2. 確保輸出符合新的命名標準
3. 更新單元測試

### **階段三：更新整合腳本**

1. 修改 `src/presentation/create_all_in_one_presentation.py`
2. 更新所有讀取 JSON 的邏輯
3. 確保網頁生成功能正常

### **階段四：驗證和測試**

1. 重新執行所有分析腳本
2. 驗證 JSON 輸出符合標準
3. 測試網頁生成功能
4. 確認 GCS 上傳正常

## 📝 **維護指南**

### **新增分析腳本時**

1. 參考本文檔的命名標準
2. 使用標準的 metadata 和 execution_info 結構
3. 確保欄位命名一致性

### **修改現有腳本時**

1. 檢查是否符合最新的命名標準
2. 更新不符合標準的欄位名稱
3. 測試相關的整合功能

---

_建立日期：2025-08-20_
_最後更新：2025-08-20_
_維護者：AI Assistant_
