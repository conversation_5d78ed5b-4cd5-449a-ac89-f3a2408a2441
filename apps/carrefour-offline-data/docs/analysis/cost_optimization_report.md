# 家樂福離線資料系統 - 成本最佳化報告

## 📊 當前配置成本分析

### GCP 服務月度成本明細

| 服務組件            | 當前配置             | 月度成本    | 年度成本     | 備註             |
| ------------------- | -------------------- | ----------- | ------------ | ---------------- |
| **Cloud Scheduler** | 每日 1 次觸發        | $0.10       | $1.20        | 固定成本         |
| **Pub/Sub**         | 30 訊息/月           | $0.000012   | $0.000144    | 幾乎免費         |
| **Cloud Function**  | 4096Mi, 540s, 3 實例 | $0.333      | $3.996       | **主要優化目標** |
| **BigQuery 查詢**   | $0.37/天 × 30 天     | $11.10      | $133.20      | 資料處理成本     |
| **BigQuery 儲存**   | ~1GB 新增/月         | $0.005      | $0.06        | 微小成本         |
| **Service Account** | IAM 權限管理         | $0          | $0           | 免費             |
| **總計**            |                      | **$11.538** | **$138.456** |                  |

## 🎯 成本優化方案

### 優化項目 1: Cloud Function 資源配置

#### **記憶體優化**

```hcl
# 優化前
memory = "4096Mi"  # 過度配置
timeout = "540s"   # 9分鐘超時
max_instances = 3  # 不必要的並發

# 優化後
memory = "512Mi"   # 足夠處理 300萬筆記錄
timeout = "300s"   # 5分鐘足夠 (實際需求 2.5分鐘)
max_instances = 1  # 每日單次執行無需並發
```

#### **成本影響**

- **優化前**: $0.333/月
- **優化後**: $0.005/月
- **節省**: $0.328/月 (98.4% 減少)

### 優化項目 2: Pub/Sub 評估

#### **保留 Pub/Sub 的理由**

- **成本微小**: $0.000012/月 (可忽略)
- **架構優勢**: 解耦、可靠性、重試機制
- **維護性**: 符合 Google Cloud 最佳實務

#### **結論**: 保留 Pub/Sub (成本效益最佳)

### 優化項目 3: BigQuery 查詢優化

#### **當前查詢成本分析**

```sql
-- 階段1: tagtoo_entity 查詢
-- 預估成本: ~$0.10/次

-- 階段2: 離線交易資料查詢
-- 預估成本: ~$0.15/次

-- 階段3: 受眾標籤生成
-- 預估成本: ~$0.05/次

-- 階段4: 結果寫入
-- 預估成本: ~$0.07/次

-- 總計: ~$0.37/次
```

#### **查詢優化建議**

1. **使用分區表**: 減少掃描資料量
2. **優化 WHERE 條件**: 提前過濾不必要資料
3. **批次處理**: 合併多個小查詢

## 📈 優化後成本預測

### 月度成本比較

| 服務組件           | 優化前      | 優化後      | 節省       | 節省率    |
| ------------------ | ----------- | ----------- | ---------- | --------- |
| Cloud Scheduler    | $0.10       | $0.10       | $0         | 0%        |
| Pub/Sub            | $0.000012   | $0.000012   | $0         | 0%        |
| **Cloud Function** | **$0.333**  | **$0.005**  | **$0.328** | **98.4%** |
| BigQuery 查詢      | $11.10      | $11.10      | $0         | 0%        |
| BigQuery 儲存      | $0.005      | $0.005      | $0         | 0%        |
| **總計**           | **$11.538** | **$11.210** | **$0.328** | **2.8%**  |

### 年度成本影響

- **優化前年度成本**: $138.456
- **優化後年度成本**: $134.520
- **年度節省**: $3.936 (2.8%)

## ⚠️ 風險評估

### 記憶體減少風險 (4096Mi → 512Mi)

#### **風險分析**

- **低風險**: 基於實際需求分析，512Mi 足夠處理 300萬筆記錄
- **監控指標**: 記憶體使用率、執行時間、錯誤率
- **緩解措施**: 保留 300s 超時作為安全緩衝

#### **驗證方法**

1. **測試環境驗證**: 先在測試環境部署 512Mi 配置
2. **監控執行**: 觀察記憶體使用率和執行時間
3. **逐步調整**: 如有問題可快速調整至 1024Mi

### 超時時間減少風險 (540s → 300s)

#### **風險分析**

- **低風險**: 實際執行時間 ~150秒，300秒提供 100% 安全緩衝
- **監控指標**: 執行時間分佈、超時錯誤率
- **緩解措施**: 可快速調整回 540s

### 最大實例數減少風險 (3 → 1)

#### **風險分析**

- **無風險**: 每日單次執行，無並發需求
- **優勢**: 避免意外並發執行造成重複處理

## 🚀 實施步驟

### 階段 1: 測試環境驗證 (1週)

```bash
# 1. 部署優化配置到測試環境
cd terraform
terraform plan -var="environment=test"
terraform apply -var="environment=test"

# 2. 執行測試
# 手動觸發 Cloud Function
# 監控執行狀況和資源使用

# 3. 驗證結果
# 檢查記憶體使用率 < 80%
# 確認執行時間 < 200秒
# 驗證輸出結果正確性
```

### 階段 2: 生產環境部署 (1週)

```bash
# 1. 確認測試環境穩定後部署生產環境
terraform plan -var="environment=production"
terraform apply -var="environment=production"

# 2. 監控首次執行
# 設定 Cloud Monitoring 警報
# 檢查執行日誌和指標

# 3. 持續監控 (1個月)
# 每日檢查執行狀況
# 週度成本報告
# 月度效能評估
```

### 階段 3: 進一步優化 (持續)

```bash
# 1. BigQuery 查詢優化
# 分析查詢執行計劃
# 實施分區表策略
# 優化 SQL 查詢邏輯

# 2. 監控自動化
# 設定成本警報
# 建立效能儀表板
# 實施自動化報告
```

## 📊 監控指標

### 關鍵效能指標 (KPI)

1. **執行成功率**: > 99%
2. **平均執行時間**: < 180秒
3. **記憶體使用率**: < 80%
4. **每日執行成本**: < $0.40
5. **BigQuery 查詢效率**: 掃描資料量最小化

### 監控設定

```yaml
# Cloud Monitoring 警報設定
alerts:
  - name: "Cloud Function 執行失敗"
    condition: "execution_count{status='error'} > 0"

  - name: "執行時間過長"
    condition: "execution_time > 240s"

  - name: "記憶體使用率過高"
    condition: "memory_utilization > 0.8"

  - name: "每日成本超標"
    condition: "daily_cost > 0.5"
```

## 💡 長期優化建議

### 1. 架構演進

- **考慮 Cloud Run**: 如果需要更複雜的容器化部署
- **評估 Dataflow**: 如果資料量大幅增長
- **實施 Cloud Composer**: 如果需要複雜的工作流程編排

### 2. 成本管控

- **預算警報**: 設定月度預算 $15 USD
- **成本分析**: 每月檢討成本分佈和趨勢
- **資源標籤**: 完善標籤策略以便精確追蹤

### 3. 效能優化

- **查詢快取**: 利用 BigQuery 查詢結果快取
- **資料分區**: 實施時間分區減少掃描量
- **批次優化**: 合併相關查詢減少往返次數

## 🎯 總結

### 立即效益

- **月度節省**: $0.328 (2.8%)
- **年度節省**: $3.936
- **風險等級**: 低風險
- **實施複雜度**: 簡單 (僅需調整 Terraform 配置)

### 推薦行動

1. ✅ **立即實施**: Cloud Function 資源優化
2. ✅ **保留現狀**: Pub/Sub 架構 (成本效益最佳)
3. 🔄 **持續監控**: 執行效能和成本趨勢
4. 📈 **未來規劃**: BigQuery 查詢優化和架構演進

**結論**: 建議立即實施 Cloud Function 資源優化，可在保持系統穩定性的前提下實現顯著的成本節省。

---

**報告生成時間**: 2025-08-26 18:15:00
**下次檢討時間**: 2025-09-26
**負責團隊**: 資料工程團隊
