# 家樂福離線資料系統 IAM 權限指南

## 📋 權限配置概覽

### Service Account 資訊

**主要 Service Account**：

- **Email**: `<EMAIL>`
- **專案**: `tagtoo-tracking`
- **用途**: 家樂福離線資料受眾媒合系統專用
- **管理方式**: 部分權限由 Terraform 管理，部分為預先配置

## 🔐 權限清單

### BigQuery 權限

#### tagtoo-tracking 專案

```yaml
角色：
- roles/bigquery.dataEditor
用途：讀取來源表格資料
範圍：整個專案

- roles/bigquery.jobUser
用途：執行 BigQuery 查詢 Job
範圍：整個專案

來源表格：
- tagtoo-tracking.event_prod.tagtoo_event
- tagtoo-tracking.event_prod.carrefour_offline_transaction_day
```

#### tagtoo-ml-workflow 專案

```yaml
資料集層級權限：
- 資料集：tagtoo_export_results
- 角色：WRITER
- 用途：寫入受眾標籤資料

目標表格：
- tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_YYYYMMDD
```

### Cloud Storage 權限

```yaml
角色：
- roles/storage.objectUser
用途：存取 Cloud Function 原始碼和暫存檔案
範圍：tagtoo-tracking 專案
```

### Cloud Function 權限

```yaml
角色：
- roles/cloudfunctions.invoker
用途：允許 Cloud Scheduler 觸發 Cloud Function
範圍：特定 Cloud Function
管理：Terraform 自動配置
```

## 🛠️ Terraform 管理的權限

### 自動配置的權限

```hcl
# Cloud Function 觸發權限
resource "google_cloudfunctions2_function_iam_binding" "invoker" {
  project        = local.shared_outputs.project_id
  location       = local.shared_outputs.region
  cloud_function = google_cloudfunctions2_function.carrefour_offline_data.name
  role           = "roles/cloudfunctions.invoker"

  members = [
    "serviceAccount:<EMAIL>"
  ]
}
```

### 預先配置的權限

以下權限需要手動配置或已預先配置：

1. **BigQuery 專案層級權限**
2. **BigQuery 資料集層級權限**
3. **Cloud Storage 權限**

## 🔍 權限驗證

### 自動驗證腳本

使用內建的權限驗證腳本：

```bash
cd /path/to/carrefour-offline-data
source venv/bin/activate
python scripts/verify_permissions.py
```

### 預期驗證結果

```
============================================================
🔐 家樂福離線資料系統權限驗證
============================================================

📋 Service Account 資訊:
  專案ID: tagtoo-tracking
  Service Account: <EMAIL>
  認證類型: Credentials

📖 BigQuery 讀取權限測試:
  ✅ 成功讀取表格 schema，共 12 個欄位
  ✅ 成功讀取表格 schema，共 11 個欄位

📝 BigQuery 寫入權限測試:
  ✅ 成功讀取目標表格
  ✅ 寫入權限驗證成功 (dry run)

⚙️ BigQuery Job 執行權限測試:
  ✅ 查詢執行權限正常

============================================================
📊 權限驗證結果: 5/5 項測試通過
🎉 所有權限驗證通過！系統可以正常運行。
```

## 🔧 故障排除

### 常見權限問題

**1. BigQuery 讀取權限不足**

```bash
# 檢查專案層級權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

**2. BigQuery 寫入權限不足**

```bash
# 檢查資料集層級權限
bq show --format=prettyjson tagtoo-ml-workflow:tagtoo_export_results | jq '.access[]'
```

**3. Cloud Function 觸發權限不足**

```bash
# 檢查 Cloud Function IAM 權限
gcloud functions get-iam-policy carrefour-offline-data-prod --region=asia-east1
```

### 權限修復

**如果權限驗證失敗**：

1. **重新執行 Terraform**：

   ```bash
   cd terraform/
   terraform plan -var-file="environments/prod.tfvars"
   terraform apply -var-file="environments/prod.tfvars"
   ```

2. **手動檢查預先配置的權限**：

   - 確認 Service Account 存在
   - 確認 BigQuery 專案層級權限
   - 確認 BigQuery 資料集層級權限

3. **聯絡管理員**：
   - 如果需要修改專案層級權限
   - 如果需要修改資料集層級權限

## 📊 安全最佳實務

### 最小權限原則

```yaml
實施策略：
- 僅授予執行任務所需的最小權限
- 定期審查和更新權限
- 使用專用 Service Account
- 避免使用過於寬泛的權限

權限範圍：
- BigQuery：僅限必要的專案和資料集
- Cloud Storage：僅限必要的 bucket
- Cloud Function：僅限特定函數的觸發權限
```

### 定期審查

```yaml
審查頻率：
- 每季度：檢查權限是否仍然必要
- 每半年：執行完整的權限驗證
- 系統變更後：立即驗證權限狀態

審查項目：
- Service Account 使用狀況
- 權限範圍是否適當
- 是否有未使用的權限
- 安全性配置是否最新
```

### 監控和警報

```yaml
監控項目：
- Service Account 使用情況
- 權限變更記錄
- 異常存取模式
- 失敗的權限檢查

警報設定：
- 權限驗證失敗
- 異常的 BigQuery 查詢
- Service Account 權限變更
- 未授權的存取嘗試
```

---

**文檔版本**：v1.0
**最後更新**：2025-08-27
**權限驗證狀態**：✅ 所有權限已驗證
**維護團隊**：資料工程團隊
