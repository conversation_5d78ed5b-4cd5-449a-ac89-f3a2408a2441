# 權限開通後執行檢查清單

## 📋 執行前檢查清單

### ✅ 環境準備確認

- [ ] **Python 環境**

  - [ ] Python 3.8+ 已安裝
  - [ ] 虛擬環境已啟動 (`source venv/bin/activate`)
  - [ ] 所有必要套件已安裝（執行 `python3 setup_environment.py` 確認）

- [ ] **gcloud 認證**

  - [ ] gcloud CLI 已安裝
  - [ ] 已登入正確的 Google 帳號 (`gcloud auth list`)
  - [ ] 已啟動 tagtoo-tracking 配置 (`gcloud config configurations activate tagtoo-tracking`)

- [ ] **Service Account 設定**
  - [ ] Service Account Impersonation 已設定
  - [ ] 可以成功獲取 access token
  ```bash
  gcloud auth application-default print-access-token --impersonate-service-account=<EMAIL>
  ```

### ✅ 權限驗證確認

- [ ] **來源表格權限**

  - [ ] 可以讀取表格 metadata (`bigquery.tables.get`)
  - [ ] 可以建立查詢 jobs (`bigquery.jobs.create`) ⚠️ **關鍵權限**
  - [ ] 可以執行 dry run 查詢

- [ ] **目標表格權限**
  - [ ] 可以讀取表格 metadata
  - [ ] 可以寫入資料 (`bigquery.tables.updateData`)
  - [ ] 可以執行 INSERT 查詢

**權限驗證指令**：

```bash
python3 -c "
from copy_carrefour_data import CarrefourDataCopier
copier = CarrefourDataCopier()
copier.authenticate()
permissions = copier.check_permissions()
required = ['source_table_read', 'source_query_create', 'target_table_read', 'target_table_write']
all_good = all(permissions.get(p, False) for p in required)
print('✅ 所有權限正常' if all_good else '❌ 權限不足')
print('詳細狀態:', permissions)
"
```

### ✅ Schema 一致性確認

- [ ] **Schema 驗證**
  - [ ] 執行 `python3 schema_validator.py`
  - [ ] 確認 Schema 完全一致
  - [ ] 如有差異，已執行修正 DDL

**預期結果**：

```
✅ Schema 完全一致！目標表格與來源表格完全一致。
```

## 🚀 執行階段檢查清單

### 階段 1: 成本估算與計畫確認

- [ ] **成本估算**

  - [ ] 執行成本估算查詢
  - [ ] 確認總成本 < $10 USD（預期 < $1 USD）
  - [ ] 批次計畫合理（預期 10-50 個批次）

- [ ] **資源準備**
  - [ ] 確認有足夠的磁碟空間（用於日誌）
  - [ ] 確認網路連線穩定
  - [ ] 確認 BigQuery 配額充足

### 階段 2: 資料複製執行

- [ ] **複製前準備**

  - [ ] 目標表格已清空（腳本會自動執行）
  - [ ] 監控系統已啟動
  - [ ] 日誌記錄正常

- [ ] **執行複製**

  ```bash
  python3 copy_carrefour_data.py
  ```

- [ ] **監控指標**
  - [ ] 批次成功率 > 95%
  - [ ] 吞吐量 2,000-5,000 行/秒
  - [ ] 成本控制在預算內
  - [ ] 無重大錯誤

**預期執行時間**：1-2 小時

### 階段 3: 資料驗證

- [ ] **基本驗證**

  - [ ] 總行數一致
  - [ ] 表格大小相近（允許 5% 誤差）
  - [ ] 欄位數量一致

- [ ] **深度驗證**
  ```bash
  python3 data_validator.py
  ```
  - [ ] Level 1: 基本統計 ✅
  - [ ] Level 2: NULL 值分布 ✅
  - [ ] Level 3: 隨機抽樣比對 ✅
  - [ ] Level 4: STRUCT 結構驗證 ✅

### 階段 4: 最終報告生成

- [ ] **規格驗證報告**

  ```bash
  python3 validate_carrefour_data_v3.py
  ```

  - [ ] 權限驗證報告
  - [ ] 資料結構報告
  - [ ] 資料內容報告
  - [ ] 資料量報告
  - [ ] 建議報告

- [ ] **監控報告**
  ```bash
  python3 -c "from monitoring_logger import generate_monitoring_report; generate_monitoring_report()"
  ```

## 📊 成功標準檢查清單

### ✅ 資料完整性

- [ ] **行數驗證**

  - [ ] 來源表格行數：15,242,556
  - [ ] 目標表格行數：15,242,556
  - [ ] 差異：0 行

- [ ] **資料品質**

  - [ ] 主要欄位 NULL 值比例一致
  - [ ] 隨機抽樣匹配率 > 95%
  - [ ] STRUCT 欄位結構完整

- [ ] **Schema 一致性**
  - [ ] 所有欄位名稱一致
  - [ ] 所有資料類型一致
  - [ ] 所有 mode 設定一致

### ✅ 效能指標

- [ ] **執行效能**

  - [ ] 總執行時間 < 3 小時
  - [ ] 平均吞吐量 > 2,000 行/秒
  - [ ] 批次成功率 > 95%

- [ ] **成本控制**
  - [ ] 總查詢成本 < $1 USD
  - [ ] 單次查詢成本 < $10 USD
  - [ ] 無異常高成本查詢

### ✅ 報告完整性

- [ ] **必要報告檔案**

  - [ ] `schema_comparison_report.md`
  - [ ] `data_validation_report.md`
  - [ ] `carrefour_validation_report_v3_[timestamp].txt`
  - [ ] `monitoring_report_[timestamp].json`
  - [ ] `structured_logs_[timestamp].json`

- [ ] **報告內容**
  - [ ] 所有驗證項目都有結果
  - [ ] 無重大失敗項目
  - [ ] 建議清單完整

## 🚨 異常情況處理檢查清單

### ❌ 如果權限仍然不足

- [ ] **確認權限申請狀態**

  - [ ] 聯繫家樂福技術團隊
  - [ ] 確認 IAM 政策已更新
  - [ ] 等待權限生效（可能需要幾分鐘）

- [ ] **重新測試權限**

  ```bash
  # 重新設定認證
  gcloud auth application-default login --impersonate-service-account=<EMAIL>

  # 測試權限
  python3 -c "from copy_carrefour_data import CarrefourDataCopier; copier = CarrefourDataCopier(); copier.authenticate(); print(copier.check_permissions())"
  ```

### ❌ 如果複製過程中斷

- [ ] **保留現場**

  - [ ] 不要刪除任何檔案
  - [ ] 保留日誌檔案
  - [ ] 記錄中斷時間和狀態

- [ ] **評估狀態**

  - [ ] 檢查已複製的資料量
  - [ ] 確認目標表格狀態
  - [ ] 檢查錯誤日誌

- [ ] **恢復策略**
  - [ ] 系統支援斷點續傳
  - [ ] 重新執行複製腳本
  - [ ] 監控恢復進度

### ❌ 如果驗證失敗

- [ ] **分析失敗原因**

  - [ ] 檢查驗證報告詳細資訊
  - [ ] 確認是否為已知問題
  - [ ] 評估影響範圍

- [ ] **決定處理方式**
  - [ ] 如果是輕微差異（< 1%），記錄並繼續
  - [ ] 如果是重大差異，停止並調查
  - [ ] 聯繫技術團隊協助

## 📞 緊急聯繫

### 🆘 如需立即協助

1. **保留所有日誌檔案**
2. **記錄錯誤訊息和截圖**
3. **聯繫 Tagtoo 技術團隊**
4. **提供以下資訊**：
   - 執行時間
   - 錯誤訊息
   - 日誌檔案
   - 當前狀態

### 📋 報告模板

```
問題報告：家樂福資料複製

時間：[YYYY-MM-DD HH:MM:SS]
階段：[環境準備/權限驗證/資料複製/資料驗證]
問題：[簡短描述]
錯誤訊息：[完整錯誤訊息]
已嘗試解決方案：[列出已嘗試的方法]
附件：[日誌檔案、截圖等]
```

---

**檢查清單版本**：1.0
**最後更新**：2025-08-15
**使用說明**：請按順序檢查每個項目，確保所有 ✅ 都已完成
