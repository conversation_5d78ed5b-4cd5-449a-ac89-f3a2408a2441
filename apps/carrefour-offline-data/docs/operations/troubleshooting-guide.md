# 家樂福資料複製系統故障排除指南

## 📋 概覽

本指南提供家樂福資料複製系統常見問題的診斷和解決方案，涵蓋 `carrefour-data-replication-prod` 和 `carrefour-offline-data-prod` 兩個主要 Cloud Function。

## 🚨 常見問題與解決方案

### 1. JSON 序列化錯誤

**錯誤訊息**:

```
Object of type datetime is not JSON serializable
Object of type date is not JSON serializable
```

**診斷步驟**:

```bash
# 檢查最近的執行日誌
gcloud logging read 'resource.type="cloud_run_revision" AND
  resource.labels.service_name="carrefour-data-replication-prod" AND
  textPayload:"JSON serializable"' --limit=5
```

**解決方案**:

- **已修復** (2025-09-05): 在 `data_replication_function.py` 中新增 `_serialize_datetime_objects()` 函數
- 確保所有回應資料都經過序列化處理
- 如果重複發生，檢查是否有新的 datetime/date 欄位未處理

### 2. Service Account 權限問題

**錯誤訊息**:

```
PERMISSION_DENIED: User does not have permission
403 Forbidden
```

**診斷步驟**:

```bash
# 檢查當前使用的 Service Account
gcloud functions describe carrefour-data-replication-prod --region=asia-east1 \
  --format="value(serviceConfig.serviceAccountEmail)"

# 驗證 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:*carrefour*" \
  --format="table(bindings.role,bindings.members)"
```

**解決方案**:

1. 確認使用正確的 Service Account: `<EMAIL>`
2. 驗證跨專案權限 (tw-eagle-prod → tagtoo-tracking)
3. 檢查 BigQuery 資料集層級權限

### 3. 資料複製失敗

**錯誤訊息**:

```
BigQuery copy job failed
Table not found
```

**診斷步驟**:

```bash
# 檢查來源表格是否存在
bq show tw-eagle-prod:rmn_tagtoo.offline_transaction_day

# 檢查目標表格狀態
bq show tagtoo-tracking:event_prod.carrefour_offline_transaction_day

# 查看複製作業狀態
gcloud logging read 'resource.type="cloud_run_revision" AND
  resource.labels.service_name="carrefour-data-replication-prod"' --limit=20
```

**解決方案**:

1. 驗證來源資料可用性
2. 檢查網路連線和跨專案存取
3. 確認目標表格 schema 相容性

### 4. Cloud Scheduler 觸發失敗

**錯誤訊息**:

```
HTTP 403: PERMISSION_DENIED
HTTP 500: INTERNAL ERROR
```

**診斷步驟**:

```bash
# 檢查 Scheduler Job 狀態
gcloud scheduler jobs describe carrefour-data-replication-prod --location=asia-east1

# 查看最近的 Scheduler 執行記錄
gcloud logging read 'resource.type="cloud_scheduler_job" AND
  resource.labels.job_id="carrefour-data-replication-prod"' --limit=5
```

**解決方案**:

1. 確認 Cloud Scheduler 使用正確的 Service Account
2. 驗證 OIDC Token 配置
3. 檢查 Cloud Function 的觸發器設定

## 🔧 診斷工具和指令

### 快速健康檢查

```bash
# 一鍵健康檢查腳本
#!/bin/bash

echo "🔍 家樂福資料複製系統健康檢查"
echo "================================"

echo "📅 Cloud Scheduler 狀態:"
gcloud scheduler jobs describe carrefour-data-replication-prod \
  --location=asia-east1 --format="value(state,schedule,timeZone)"

echo "🔧 Cloud Function 狀態:"
gcloud functions describe carrefour-data-replication-prod \
  --region=asia-east1 --format="value(status,serviceConfig.serviceAccountEmail)"

echo "📊 最近執行結果:"
gcloud logging read 'resource.type="cloud_run_revision" AND
  resource.labels.service_name="carrefour-data-replication-prod"' \
  --limit=3 --format="value(timestamp,severity,textPayload)"
```

### 手動測試執行

```bash
# 手動觸發資料複製
gcloud scheduler jobs run carrefour-data-replication-prod --location=asia-east1

# 監控執行進度 (每 30 秒檢查一次)
watch -n 30 'gcloud logging read "resource.type=\"cloud_run_revision\" AND
  resource.labels.service_name=\"carrefour-data-replication-prod\" AND
  timestamp>=\"$(date -d \"5 minutes ago\" --iso-8601)\"" --limit=5'
```

### 深度診斷

```bash
# 檢查 BigQuery 存取權限
bq ls tw-eagle-prod:rmn_tagtoo 2>&1 | head -5

# 驗證跨專案資料存取
bq query --use_legacy_sql=false --max_rows=1 \
  "SELECT COUNT(*) FROM \`tw-eagle-prod.rmn_tagtoo.offline_transaction_day\` LIMIT 1"

# 檢查目標表格最近更新時間
bq show --format=json tagtoo-tracking:event_prod.carrefour_offline_transaction_day | \
  jq -r '.lastModifiedTime'
```

## 📊 監控和警報

### 重要監控指標

1. **執行成功率**: > 95%
2. **執行時間**: < 180 秒
3. **錯誤率**: < 1%
4. **資料新鮮度**: < 24 小時

### 警報觸發條件

- 連續 2 次執行失敗
- 執行時間超過 300 秒
- 記憶體使用率 > 90%
- BigQuery 查詢成本 > $0.5

### 警報回應程序

1. **立即檢查**: 查看 Cloud Console 和 Logging
2. **識別問題**: 使用本指南的診斷步驟
3. **暫時修復**: 手動觸發或重新部署
4. **根本修復**: 更新程式碼並測試
5. **文檔更新**: 記錄問題和解決方案

## 🏥 緊急修復程序

### 系統完全失效時

1. **評估影響**:

   ```bash
   # 檢查資料延遲
   bq query --use_legacy_sql=false \
     "SELECT DATETIME_DIFF(CURRENT_DATETIME(),
      DATETIME(TIMESTAMP_MILLIS(CAST(lastModifiedTime AS INT64))), HOUR) as hours_behind
      FROM \`tagtoo-tracking.event_prod.__TABLES__\`
      WHERE table_id = 'carrefour_offline_transaction_day'"
   ```

2. **臨時手動執行**:

   ```bash
   # 在 localhost 執行核心邏輯
   cd /apps/carrefour-offline-data
   source venv/bin/activate
   python tools/automated_daily_replication.py
   ```

3. **快速回滾**:

   ```bash
   # 檢視最近的 commits
   git log --oneline -5

   # 回滾到穩定版本
   git revert <commit-hash>
   cd terraform
   terraform apply -var-file="environments/prod.tfvars"
   ```

## 📞 escalation 路徑

### 技術支援層級

1. **L1 - 即時監控** (自動警報)

   - Cloud Monitoring 警報
   - 基本日誌檢查

2. **L2 - 初級技術支援**

   - 使用本故障排除指南
   - 執行標準診斷程序
   - 嘗試重新部署

3. **L3 - 高級技術支援**

   - 深度程式碼分析
   - 架構層級問題解決
   - 權限和設定修復

4. **L4 - 系統架構師**
   - 系統重新設計
   - 跨專案權限協調
   - 災難復原計畫執行

### 聯絡資訊

- **主要負責人**: Frank Zheng (<EMAIL>)
- **備援支援**: Data Engineering Team
- **緊急聯絡**: Google Cloud Monitoring 警報系統

## 📚 相關文檔

- [recent-changes-log.md](recent-changes-log.md) - 最近變更記錄
- [operations-manual.md](operations-manual.md) - 完整操作手冊
- [iam-permissions-guide.md](iam-permissions-guide.md) - 權限配置指南

---

**文檔建立**: 2025-09-05
**版本**: v1.0
**適用系統**: carrefour-data-replication-prod, carrefour-offline-data-prod
