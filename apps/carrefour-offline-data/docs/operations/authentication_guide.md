# Service Account 認證配置指南

## 📋 概述

本指南提供安全且不影響現有 gcloud 配置的 Service Account 認證設定方式。

## 🎯 認證目標

- **Service Account**: `<EMAIL>`
- **專案**: `tw-eagle-prod` (來源) 和 `tagtoo-tracking` (目標)
- **認證方式**: Service Account Impersonation

## 🚀 推薦方式：直接 Impersonation

### 優點

- ✅ 不修改使用者的全域 gcloud 配置
- ✅ 不影響其他專案的認證設定
- ✅ 簡單直接，一條指令完成
- ✅ 使用完畢後自動恢復原有設定

### 設定指令

```bash
# 一鍵設定 Service Account Impersonation
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

### 驗證設定

```bash
# 測試認證是否正常
gcloud auth application-default print-access-token --impersonate-service-account=<EMAIL>
```

## 🔧 替代方式：使用 Configurations

### 適用情況

- 需要長期使用此專案
- 希望保存專案特定的配置
- 需要在多個專案間快速切換

### 設定步驟

```bash
# 1. 建立專案專用 configuration
gcloud config configurations create carrefour-offline-data

# 2. 啟動新 configuration
gcloud config configurations activate carrefour-offline-data

# 3. 設定專案和 Service Account
gcloud config set project tagtoo-tracking
gcloud config set auth/impersonate_service_account <EMAIL>

# 4. 設定應用程式預設認證
gcloud auth application-default login

# 5. 使用完畢後切換回預設 configuration
gcloud config configurations activate default
```

### Configuration 管理

```bash
# 查看所有 configurations
gcloud config configurations list

# 切換到家樂福專案
gcloud config configurations activate carrefour-offline-data

# 切換回預設設定
gcloud config configurations activate default

# 刪除專案 configuration（如不再需要）
gcloud config configurations delete carrefour-offline-data
```

## ⚠️ 避免的做法

### ❌ 不推薦：修改全域設定

```bash
# 這會永久修改使用者的全域 gcloud 配置
gcloud config set auth/impersonate_service_account <EMAIL>
gcloud auth application-default login
```

**問題**：

- 影響所有其他專案的認證
- 可能導致其他工作流程失敗
- 需要手動恢復原有設定

## 🔍 故障排除

### 問題 1: 權限不足

**錯誤訊息**：

```
ERROR: (gcloud.auth.application-default.login) There was a problem with the service account impersonation
```

**解決方案**：

1. 確認您有 Service Account Token Creator 權限
2. 聯絡管理員授予權限：
   ```bash
   gcloud iam service-accounts add-iam-policy-binding <EMAIL> \
     --member="user:<EMAIL>" \
     --role="roles/iam.serviceAccountTokenCreator"
   ```

### 問題 2: 認證檔案衝突

**錯誤訊息**：

```
WARNING: Cannot find a quota project to add to ADC
```

**解決方案**：

```bash
# 清除現有 ADC 並重新設定
gcloud auth application-default revoke
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

### 問題 3: Configuration 衝突

**解決方案**：

```bash
# 檢查當前 configuration
gcloud config configurations list

# 確保使用正確的 configuration
gcloud config configurations activate carrefour-offline-data
```

## 🛡️ 安全最佳實踐

### 1. 最小權限原則

- 只授予必要的 BigQuery 權限
- 定期檢查和清理不需要的權限

### 2. 認證隔離

- 使用 configurations 隔離不同專案的設定
- 避免修改全域 gcloud 配置

### 3. 定期檢查

```bash
# 檢查當前認證狀態
gcloud auth list

# 檢查 Service Account 權限
gcloud iam service-accounts get-iam-policy <EMAIL>
```

## 📚 相關文檔

- [Google Cloud Service Account Impersonation](https://cloud.google.com/docs/authentication/external/workforce#impersonation)
- [gcloud configurations 管理](https://cloud.google.com/sdk/gcloud/reference/config/configurations)
- [Application Default Credentials](https://cloud.google.com/docs/authentication/application-default-credentials)

## ✅ 檢查清單

完成認證設定後，請確認：

- [ ] 可以成功獲取 access token
- [ ] BigQuery 連線正常
- [ ] 不影響其他專案的認證
- [ ] 了解如何切換回原有設定

---

**文檔版本**: 1.0
**最後更新**: 2025-08-15
**適用範圍**: 家樂福離線事件資料專案認證設定
