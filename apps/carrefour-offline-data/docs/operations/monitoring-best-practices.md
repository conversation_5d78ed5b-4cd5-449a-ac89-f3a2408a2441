# 🔔 Google Cloud Monitoring 最佳實務指南

> **基於家樂福離線資料系統的企業級監控配置經驗**
> 最後更新：2025-09-01
> 適用範圍：Tagtoo 所有 Google Cloud 專案

## 📋 **概述**

本文檔記錄了基於家樂福離線資料系統實際部署經驗的 Google Cloud Monitoring 最佳實務，包含通知頻道架構設計、BigQuery 費用追蹤、警報策略配置等企業級監控標準。

## 🏗️ **通知頻道架構設計**

### **核心原則**

基於深度技術分析和實際驗證，我們確立以下架構原則：

#### **技術事實**

- ✅ **一個警報策略支援多個通知頻道**
- ❌ **一個通知頻道只能對應一個 email 地址**（Google Cloud 技術限制）
- ✅ **通知頻道建立和使用完全免費**
- ✅ **多收件人需透過多個通知頻道實現**

#### **架構選擇：專案獨立通知頻道**

```yaml
推薦架構: Project-Specific Notification Channels
設計理念: 每個專案建立專用的通知頻道
成本影響: 零額外成本（通知頻道完全免費）
```

### **實施標準**

#### **1. 命名規範**

```
格式: "{專案名稱} - {環境} - {收件人}"
範例:
  - "家樂福離線資料系統 - prod - <EMAIL>"
  - "ReURL整合系統 - prod - <EMAIL>"
  - "Shopify Webhook - prod - <EMAIL>"
```

#### **2. Terraform 配置範例**

```hcl
# 通知頻道資源定義
resource "google_monitoring_notification_channel" "email" {
  count        = length(var.notification_emails)
  display_name = "${var.project_name} - ${var.environment} - ${var.notification_emails[count.index]}"
  type         = "email"

  labels = {
    email_address = var.notification_emails[count.index]
  }

  # 標準化用戶標籤，便於管理和分類
  user_labels = {
    project     = var.project_name
    environment = var.environment
    purpose     = "project-specific-alerts"
    owner       = split("@", var.notification_emails[count.index])[0]
  }

  enabled = var.enable_monitoring
}

# 警報策略中的多通知頻道綁定
resource "google_monitoring_alert_policy" "example" {
  display_name = "${var.project_name}-alert"

  notification_channels = [
    for channel in google_monitoring_notification_channel.email : channel.name
  ]

  # 警報條件配置...
}
```

#### **3. 多收件人擴展範例**

```hcl
# variables.tf
variable "notification_emails" {
  description = "List of email addresses for notifications"
  type        = list(string)
  default     = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}
```

### **架構優勢分析**

#### **✅ 專案隔離的優勢**

1. **安全邊界**：每個專案的警報完全隔離
2. **權限控制**：可針對不同專案設定不同存取權限
3. **故障排除**：從通知頻道名稱即可識別警報來源
4. **未來擴展**：支援團隊成長和專案擴展
5. **合規要求**：符合企業級審計和治理標準

#### **📊 成本效益**

- **通知頻道**：完全免費，無數量限制
- **警報策略**：前 100 個免費（遠超一般需求）
- **Email 通知**：完全免費
- **維護成本**：標準化配置，易於管理

## 💰 **BigQuery 費用追蹤最佳實務**

### **Labels 標準配置**

家樂福專案已實施完整的 BigQuery labels 機制：

```python
def _generate_gcp_labels(self, execution_mode: str) -> Dict[str, str]:
    """生成 GCP labels 用於費用追蹤"""
    labels = {
        "project": "carrefour-offline-data",
        "repo": "integrated-event",
        "env": "prod",
        "trigger": "manual" if execution_mode == "manual" else "auto",
        "user": self._detect_current_user()  # 自動偵測當前用戶
    }
    return labels

# 所有 BigQuery 查詢都使用 labels
job_config = bigquery.QueryJobConfig(labels=self.gcp_labels)
query_job = self.client.query(query, job_config=job_config)
```

### **費用追蹤能力**

透過這些 labels，可在 BigQuery billing 中精確追蹤：

- **專案別**：`project=carrefour-offline-data`
- **環境別**：`env=prod`
- **觸發方式**：`trigger=auto/manual`
- **用戶別**：`user=frank`
- **代碼庫**：`repo=integrated-event`

### **實施檢查清單**

- [ ] 所有 BigQuery 查詢都使用 `job_config.labels`
- [ ] Labels 包含必要的分類資訊（project, env, user）
- [ ] 自動偵測用戶機制正常運作
- [ ] 費用報告可按 labels 分類查看

## 🎯 **警報策略配置標準**

### **監控覆蓋範圍**

基於家樂福專案的 5 個警報策略，建議標準覆蓋：

1. **執行時間監控**：超過預期執行時間觸發
2. **錯誤率監控**：任何執行失敗立即通知
3. **記憶體使用率監控**：資源使用異常警報
4. **執行頻率監控**：異常執行頻率檢測
5. **調度器失敗監控**：Cloud Scheduler 失敗通知

### **監控間隔最佳化**

基於成本效益分析：

- **推薦間隔**：300 秒（5 分鐘）
- **理由**：平衡檢測速度與成本效益
- **避免誤報**：適應 Cloud Function 冷啟動時間

## 📚 **團隊實施指南**

### **新專案設置步驟**

1. **複製 Terraform 模組**

   ```bash
   cp -r apps/carrefour-offline-data/terraform/monitoring.tf new-project/terraform/
   ```

2. **調整變數配置**

   ```hcl
   # 更新專案名稱和收件人
   project_name = "your-new-project"
   notification_emails = ["<EMAIL>"]
   ```

3. **部署監控配置**
   ```bash
   terraform plan -var-file="environments/prod.tfvars"
   terraform apply -var-file="environments/prod.tfvars"
   ```

### **驗證檢查清單**

- [ ] 通知頻道命名符合標準格式
- [ ] 用戶標籤正確設定
- [ ] 警報策略綁定正確的通知頻道
- [ ] BigQuery 查詢包含 labels
- [ ] 測試警報通知正常運作

## 🔗 **相關文檔**

- [家樂福離線資料系統 README.md](../README.md)
- [監控優化報告](./monitoring-optimization-report.md)
- [Terraform 部署指南](../deployment/terraform_deployment_guide.md)
- [IAM 權限指南](./iam_permissions_guide.md)

---

**維護者**：Frank Zheng (<EMAIL>)
**審核者**：Data Engineering Team
**版本**：v1.0 (2025-09-01)
