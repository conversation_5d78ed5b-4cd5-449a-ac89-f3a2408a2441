# 家樂福離線事件資料複製與驗證操作手冊

## 📋 概述

本手冊提供家樂福離線事件資料複製和驗證的完整操作流程，包括環境設定、權限配置、資料複製、驗證檢查和問題排除。

**目標**：將 `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` 的資料複製到 `tagtoo-tracking.event_prod.carrefour_offline_data` 並進行完整驗證。

## 🚀 快速開始

### 前置條件檢查清單

- [ ] Python 3.8+ 已安裝
- [ ] gcloud CLI 已安裝並認證
- [ ] 虛擬環境已建立並啟動
- [ ] 必要的 Python 套件已安裝
- [ ] Service Account 權限已設定

### 一鍵執行（權限開通後）

```bash
# 1. 啟動虛擬環境
source venv/bin/activate

# 2. 設定環境變數
source set_env.sh

# 3. 執行完整流程
python3 run_full_pipeline.py
```

## 📚 詳細操作流程

### 步驟 1: 環境準備

#### 1.1 檢查 Python 環境

```bash
python3 --version  # 需要 3.8+
```

#### 1.2 建立並啟動虛擬環境

```bash
python3 -m venv venv
source venv/bin/activate
```

#### 1.3 執行環境設定

```bash
python3 setup_environment.py
```

**預期輸出**：

- ✅ Python 版本檢查通過
- ✅ 所有必要套件已安裝
- ✅ gcloud 認證狀態正常
- ⚠️ 部分權限可能缺失（等待開通）

### 步驟 2: 認證設定

#### 2.1 確認 gcloud 配置

```bash
gcloud config configurations list
gcloud config configurations activate tagtoo-tracking
```

#### 2.2 設定 Service Account Impersonation

**推薦方式（不影響全域設定）**：

```bash
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

**替代方式（使用 configurations 隔離）**：

```bash
# 建立專案專用 configuration
gcloud config configurations create carrefour-offline-data
gcloud config configurations activate carrefour-offline-data
gcloud config set project tagtoo-tracking
gcloud config set auth/impersonate_service_account <EMAIL>
gcloud auth application-default login
```

#### 2.3 驗證認證狀態

```bash
gcloud auth application-default print-access-token --impersonate-service-account=<EMAIL>
```

### 步驟 3: Schema 驗證

#### 3.1 執行 Schema 一致性檢查

```bash
python3 schema_validator.py
```

**預期結果**：

- ✅ Schema 完全一致
- 📄 生成 `schema_comparison_report.md`

**如果發現差異**：

1. 檢視 `fix_target_table_schema.sql`
2. 執行修正 DDL：
   ```bash
   bq query --use_legacy_sql=false < fix_target_table_schema.sql
   ```
3. 重新執行 Schema 驗證

### 步驟 4: 權限驗證（等待開通後）

#### 4.1 檢查必要權限

```bash
python3 -c "
from copy_carrefour_data import CarrefourDataCopier
copier = CarrefourDataCopier()
copier.authenticate()
permissions = copier.check_permissions()
print('權限狀態:', permissions)
"
```

**必要權限**：

- ✅ `source_table_read`: 來源表格讀取
- ✅ `source_query_create`: 來源查詢建立 ⚠️ **需要開通**
- ✅ `target_table_read`: 目標表格讀取
- ✅ `target_table_write`: 目標表格寫入

### 步驟 5: 資料複製（權限開通後）

#### 5.1 執行資料複製

```bash
python3 copy_carrefour_data.py
```

**執行過程**：

1. 🔐 認證檢查
2. 💰 成本估算（預期 < $1 USD）
3. 📋 智慧批次計畫
4. 🚀 分批複製執行
5. 📊 即時進度追蹤
6. ✅ 完成驗證

**預期時間**：約 1-2 小時（1500+ 萬筆資料）

#### 5.2 監控複製進度

複製過程中會顯示：

- 📊 當前批次進度
- ⏱️ 預估剩餘時間
- 💰 累計成本
- 🚀 吞吐量統計

### 步驟 6: 資料驗證

#### 6.1 執行多層次驗證

```bash
python3 data_validator.py
```

**驗證層次**：

1. **Level 1**: 基本統計比較（行數、欄位數、大小）
2. **Level 2**: 資料類型和 NULL 值分布
3. **Level 3**: 隨機抽樣 1000 筆逐欄位比對
4. **Level 4**: 複雜 STRUCT 欄位結構驗證

#### 6.2 檢視驗證報告

```bash
cat data_validation_report.md
```

### 步驟 7: 生成最終報告

#### 7.1 執行完整驗證（符合規格要求）

```bash
python3 validate_carrefour_data_v3.py
```

**生成報告**：

- 權限驗證報告
- 資料結構報告
- 資料內容報告
- 資料量報告
- 建議報告

## 🔧 常見問題排除

### 問題 1: 認證失敗

**症狀**：

```
❌ 認證失敗: 403 Access Denied
```

**解決方案**：

1. 檢查 gcloud 認證狀態：
   ```bash
   gcloud auth list
   ```
2. 重新設定 Service Account Impersonation：
   ```bash
   ./setup_auth.sh
   ```
3. 確認 Service Account 權限是否正確

### 問題 2: 權限不足

**症狀**：

```
❌ User does not have bigquery.jobs.create permission
```

**解決方案**：

1. 確認是否已申請 BigQuery Job User 權限
2. 聯繫家樂福技術團隊開通權限：
   ```bash
   gcloud projects add-iam-policy-binding tw-eagle-prod \
       --member="serviceAccount:<EMAIL>" \
       --role="roles/bigquery.jobUser"
   ```

### 問題 3: 成本超過限制

**症狀**：

```
⚠️ 查詢成本較高 ($X.XX)，超過 $10 USD 限制
```

**解決方案**：

1. 檢查查詢是否合理
2. 考慮分批處理
3. 如確實需要，手動確認繼續執行

### 問題 4: 批次複製失敗

**症狀**：

```
❌ 批次 X 失敗: [錯誤訊息]
```

**解決方案**：

1. 檢查網路連線
2. 確認目標表格狀態
3. 使用重試機制（自動執行）
4. 如持續失敗，檢查日誌檔案

### 問題 5: Schema 不一致

**症狀**：

```
❌ 發現 X 個差異
```

**解決方案**：

1. 檢視 `schema_comparison_report.md`
2. 執行修正 DDL：
   ```bash
   bq query --use_legacy_sql=false < fix_target_table_schema.sql
   ```
3. 重新執行 Schema 驗證

## 📊 效能基準

### 預期效能指標

| 指標     | 預期值            | 說明                 |
| -------- | ----------------- | -------------------- |
| 總資料量 | 15,242,556 行     | 約 18.63 GB          |
| 複製時間 | 1-2 小時          | 取決於網路和系統負載 |
| 吞吐量   | 2,000-5,000 行/秒 | 平均處理速度         |
| 總成本   | < $1 USD          | BigQuery 查詢成本    |
| 成功率   | > 99%             | 批次成功率           |

### 效能優化建議

1. **批次大小調整**：系統會自動調整，通常 10,000-50,000 行/批次
2. **網路優化**：確保穩定的網路連線
3. **並行處理**：避免同時執行其他大型查詢
4. **監控資源**：注意 BigQuery 配額使用情況

## 🚨 緊急處理程序

### 資料複製中斷

1. **不要重新啟動**：系統支援斷點續傳
2. **檢查日誌**：查看 `carrefour_data_operations.log`
3. **評估狀態**：確認已複製的資料量
4. **聯繫支援**：如需協助，提供日誌檔案

### 資料驗證失敗

1. **保留現場**：不要刪除任何檔案
2. **收集資訊**：
   - 驗證報告
   - 日誌檔案
   - 錯誤訊息截圖
3. **分析原因**：檢查是否為已知問題
4. **上報問題**：聯繫技術團隊

### 成本異常

1. **立即停止**：如發現成本異常高
2. **檢查查詢**：確認是否有異常查詢
3. **聯繫管理員**：報告成本異常情況
4. **調整設定**：降低批次大小或查詢頻率

## 📞 聯繫資訊

### 技術支援

- **主要聯繫人**：Tagtoo 技術團隊
- **緊急聯繫**：如有安全或成本問題
- **文件更新**：如發現手冊問題

### 相關文件

- `carrefour-validation-spec.md`：驗證規格文件
- `DEVELOPMENT.md`：開發狀態記錄
- `data_copy_strategy.md`：技術策略文件
- `permission_request.md`：權限申請文件

---

**文件版本**：1.0
**最後更新**：2025-08-15
**適用範圍**：家樂福離線事件資料複製專案
