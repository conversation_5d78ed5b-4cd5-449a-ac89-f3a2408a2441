# 家樂福離線資料系統 Terraform 部署指南

## 📋 部署前檢查清單

### 前置需求

- [x] ✅ 高效能 SQL 方法已驗證
- [x] ✅ Service Account 權限已配置
- [x] ✅ 目標表格結構已確認
- [x] ✅ main.py 支援 HTTP 觸發
- [x] ✅ Terraform 配置遵循模板標準

### 環境準備

```bash
# 1. 確認 gcloud 認證
gcloud auth list
gcloud config set project tagtoo-tracking

# 2. 確認 Terraform 版本
terraform version  # 需要 >= 1.12.0

# 3. 確認目錄結構
cd /path/to/carrefour-offline-data/terraform
ls -la  # 應該看到 main.tf, variables.tf, outputs.tf, environments/
```

## 🚀 部署步驟

### 步驟 1：初始化 Terraform

```bash
cd terraform/
terraform init
```

**預期輸出**：

- ✅ 成功連接到 GCS backend
- ✅ 下載必要的 provider
- ✅ 初始化完成

### 步驟 2：開發環境部署

```bash
# 規劃部署
terraform plan -var-file="environments/dev.tfvars"

# 執行部署
terraform apply -var-file="environments/dev.tfvars"
```

**開發環境配置**：

- 環境：dev
- 排程器：停用
- 記憶體：1024Mi
- 超時：8分鐘
- 成本限制：$0.5 USD

### 步驟 3：功能驗證

```bash
# 1. 檢查 Cloud Function 部署狀態
gcloud functions list --filter="name:carrefour-offline-data-dev"

# 2. 手動觸發測試
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  -H "Content-Type: application/json" \
  -d '{"execution_mode": "manual", "days_back": 1, "dry_run": true}' \
  [FUNCTION_URL]

# 3. 檢查執行日誌
gcloud functions logs read carrefour-offline-data-dev --limit=50
```

### 步驟 4：生產環境部署

```bash
# 規劃生產部署
terraform plan -var-file="environments/prod.tfvars"

# 執行生產部署
terraform apply -var-file="environments/prod.tfvars"
```

**生產環境配置**：

- 環境：prod
- 排程器：啟用（每日 02:00）
- 記憶體：1024Mi
- 超時：10分鐘
- 成本限制：$1.0 USD

## 🔧 架構特點

### HTTP 觸發機制

基於 [架構決策文檔](../architecture/deployment_architecture_comparison.md) 的建議：

```
Cloud Scheduler → HTTP Trigger → Cloud Run Functions 2nd Gen
```

**優勢**：

- ✅ 60分鐘執行時間限制（vs 9分鐘）
- ✅ 直接觸發，減少延遲
- ✅ OIDC 認證，安全性更高
- ✅ 成本更低（$0.60/月 vs $0.70/月）

### 安全配置

```yaml
認證機制：
  - Cloud Scheduler 使用 OIDC Token
  - Service Account: <EMAIL>
  - 僅允許內部觸發 (ALLOW_INTERNAL_ONLY)

權限配置（已驗證）：
  - BigQuery 讀取：tagtoo-tracking 專案 (roles/bigquery.dataEditor)
  - BigQuery 寫入：tagtoo-ml-workflow 專案 (資料集層級 WRITER)
  - BigQuery Job 執行：roles/bigquery.jobUser
  - Cloud Storage 存取：roles/storage.objectUser
  - Cloud Function 觸發：roles/cloudfunctions.invoker (Terraform 管理)

IAM 最小權限原則：
  - 使用現有專用 Service Account
  - 僅授予必要的最小權限
  - 透過 Terraform 管理 Cloud Function 觸發權限
  - 定期權限審查和驗證
```

## 📊 監控和維護

### 監控指標

```bash
# 檢查 Cloud Function 狀態
gcloud functions describe carrefour-offline-data-prod --region=asia-east1

# 檢查 Cloud Scheduler 狀態
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule --location=asia-east1

# 檢查執行日誌
gcloud functions logs read carrefour-offline-data-prod --limit=20
```

### 成本監控

```bash
# 檢查 BigQuery 查詢成本
bq query --dry_run --use_legacy_sql=false "
SELECT
  creation_time,
  total_bytes_processed,
  total_bytes_processed / 1024 / 1024 / 1024 / 1024 * 5 as estimated_cost_usd
FROM \`tagtoo-tracking.region-asia-east1.INFORMATION_SCHEMA.JOBS_BY_PROJECT\`
WHERE job_type = 'QUERY'
  AND creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
  AND query LIKE '%carrefour%'
ORDER BY creation_time DESC
LIMIT 10
"
```

## 🔄 故障排除

### 常見問題

**1. Terraform 初始化失敗**

```bash
# 檢查 GCS bucket 權限
gsutil ls gs://tagtoo-tracking-terraform-state/

# 重新初始化
rm -rf .terraform/
terraform init
```

**2. Cloud Function 部署失敗**

```bash
# 檢查 API 啟用狀態
gcloud services list --enabled --filter="name:cloudfunctions.googleapis.com"

# 檢查 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

**3. Cloud Scheduler 觸發失敗**

```bash
# 手動觸發測試
gcloud scheduler jobs run carrefour-offline-data-prod-schedule --location=asia-east1

# 檢查 OIDC Token 配置
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule --location=asia-east1
```

### 回滾程序

```bash
# 1. 停用 Cloud Scheduler
gcloud scheduler jobs pause carrefour-offline-data-prod-schedule --location=asia-east1

# 2. 回滾 Terraform 變更
terraform plan -destroy -var-file="environments/prod.tfvars"
terraform destroy -var-file="environments/prod.tfvars"

# 3. 恢復手動執行模式
cd ../
python -c "
import sys
sys.path.append('src/analysis')
from carrefour_audience_matching import entity_optimized_run
entity_optimized_run(execution_mode='manual', days_back=1, dry_run=False)
"
```

## 📈 效能基準

### 預期效能指標

```yaml
執行時間：
- 小規模 (100 記錄)：< 30 秒
- 中等規模 (1000 記錄)：< 60 秒
- 生產規模 (完整)：< 180 秒

成本控制：
- 開發環境：< $0.5 USD/天
- 生產環境：< $1.0 USD/天
- 月度總成本：< $30 USD

資源使用：
- 記憶體使用：< 800Mi
- CPU 使用：< 80%
- 網路流量：< 100MB
```

## 🔐 安全檢查

### 部署後安全驗證

```bash
# 1. 確認 Service Account 權限最小化
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 2. 確認 Cloud Function 僅允許內部觸發
gcloud functions describe carrefour-offline-data-prod \
  --region=asia-east1 \
  --format="value(serviceConfig.ingressSettings)"

# 3. 確認 OIDC Token 配置正確
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1 \
  --format="value(httpTarget.oidcToken.serviceAccountEmail)"
```

---

**文檔版本**：v1.0
**最後更新**：2025-08-27
**部署架構**：HTTP 觸發 + Cloud Run Functions 2nd Gen
**維護團隊**：資料工程團隊
