# 跨平台環境設定指南

## 📋 概述

本專案已完全支援跨平台使用，包括 macOS、Linux 和 Windows。所有硬編碼的使用者特定路徑都已移除，改為使用動態路徑檢測。

## 🔧 認證檔案路徑

### 自動路徑檢測

專案會自動檢測以下路徑中的 Google Cloud 認證檔案：

**macOS/Linux:**

```
~/.config/gcloud/application_default_credentials.json
```

**Windows:**

```
%USERPROFILE%\AppData\Roaming\gcloud\application_default_credentials.json
```

### 環境變數優先級

1. **最高優先級**: `GOOGLE_APPLICATION_CREDENTIALS` 環境變數
2. **次要優先級**: 系統標準路徑（如上所示）

### 設定方式

#### 方法 1: 使用 Service Account Impersonation（推薦）

```bash
# 直接使用 Service Account Impersonation，不影響全域設定
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

#### 方法 1b: 使用 gcloud configurations（隔離設定）

```bash
# 建立專案專用的 configuration
gcloud config configurations create carrefour-offline-data
gcloud config configurations activate carrefour-offline-data
gcloud config set project tagtoo-tracking
gcloud config set auth/impersonate_service_account <EMAIL>
gcloud auth application-default login

# 使用完畢後可切換回預設 configuration
gcloud config configurations activate default
```

#### 方法 2: 自定義環境變數

```bash
# 設定自定義認證檔案路徑
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/credentials.json"
```

#### 方法 3: 使用 .env 檔案

在專案根目錄建立 `.env` 檔案：

```bash
# 複製範本
cp .env.carrefour .env

# 編輯檔案，取消註解並設定路徑
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/credentials.json
```

## 🚀 快速開始

### 1. 安裝 Google Cloud CLI

**macOS:**

```bash
brew install google-cloud-sdk
```

**Linux:**

```bash
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
```

**Windows:**
下載並安裝 [Google Cloud CLI](https://cloud.google.com/sdk/docs/install)

### 2. 認證設定

**推薦方式（不影響全域設定）**：

```bash
# 登入 Google Cloud
gcloud auth login

# 直接使用 Service Account Impersonation
gcloud auth application-default login --impersonate-service-account=<EMAIL>
```

**替代方式（使用 configurations 隔離）**：

```bash
# 建立專案專用 configuration
gcloud config configurations create carrefour-offline-data
gcloud config configurations activate carrefour-offline-data
gcloud config set project tagtoo-tracking
gcloud config set auth/impersonate_service_account <EMAIL>
gcloud auth application-default login

# 完成後切換回預設 configuration
gcloud config configurations activate default
```

### 3. 驗證設定

```bash
# 啟動虛擬環境
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 執行環境檢查
python3 src/setup_environment.py
```

## 🔍 故障排除

### 認證檔案找不到

**錯誤訊息:**

```
⚠️ 認證檔案不存在於標準位置: ~/.config/gcloud/application_default_credentials.json
請確保已執行: gcloud auth application-default login
```

**解決方案:**

1. 執行 `gcloud auth application-default login`
2. 或設定 `GOOGLE_APPLICATION_CREDENTIALS` 環境變數

### 權限不足

**錯誤訊息:**

```
❌ 來源表格讀取權限失敗: 403 Forbidden
```

**解決方案:**

1. 確認 Service Account Impersonation 設定正確
2. 檢查是否有 BigQuery Job User 權限
3. 聯絡管理員開通必要權限

### 跨平台路徑問題

**Windows 使用者注意事項:**

- 使用正斜線 `/` 或雙反斜線 `\\` 作為路徑分隔符
- 避免使用單反斜線 `\`

**範例:**

```bash
# 正確
export GOOGLE_APPLICATION_CREDENTIALS="C:/Users/<USER>/credentials.json"
export GOOGLE_APPLICATION_CREDENTIALS="C:\\Users\\<USER>\\credentials.json"

# 錯誤
export GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\credentials.json"
```

## 📝 技術實作細節

### 動態路徑檢測函數

所有核心模組都包含 `get_gcloud_credentials_path()` 函數：

```python
def get_gcloud_credentials_path() -> str:
    """動態獲取 gcloud 認證檔案路徑，支援跨平台"""
    # 優先使用環境變數
    if 'GOOGLE_APPLICATION_CREDENTIALS' in os.environ:
        existing_path = os.environ['GOOGLE_APPLICATION_CREDENTIALS']
        if os.path.exists(existing_path):
            return existing_path

    # 使用 gcloud 標準路徑
    home_dir = os.path.expanduser('~')

    # 跨平台路徑處理
    if os.name == 'nt':  # Windows
        gcloud_dir = os.path.join(home_dir, 'AppData', 'Roaming', 'gcloud')
    else:  # macOS/Linux
        gcloud_dir = os.path.join(home_dir, '.config', 'gcloud')

    credentials_path = os.path.join(gcloud_dir, 'application_default_credentials.json')
    return credentials_path
```

### 修正的檔案清單

以下檔案已完成路徑通用化修正：

1. `src/setup_environment.py`
2. `src/copy_carrefour_data.py`
3. `src/data_validator.py`
4. `src/schema_validator.py`
5. `config/set_env.sh`
6. `.env.carrefour`

## ✅ 驗證清單

完成設定後，請確認以下項目：

- [ ] Google Cloud CLI 已安裝
- [ ] 已執行 `gcloud auth application-default login`
- [ ] Service Account Impersonation 已設定
- [ ] 環境檢查腳本執行成功
- [ ] 所有模組都能正常導入
- [ ] 認證檔案路徑自動檢測正常

---

**文檔版本**: 1.0
**最後更新**: 2025-08-15
**適用範圍**: 家樂福離線事件資料專案跨平台設定
