# 家樂福離線資料系統 - 部署策略和歷史回填計畫

## 📊 系統現況分析

### **mobile 功能上線時間確認**

- **正式上線日期**：2025-08-15 (星期四)
- **測試階段**：2025-08-13~14 (少量測試記錄)
- **穩定期**：2025-08-19 開始，mobile 記錄比例穩定在 23-31%
- **每日可媒合用戶**：約 4-6 萬個 mobile 記錄

### **歷史資料回填可行性**

- **可回填天數**：13 天 (2025-08-15 到 2025-08-27)
- **總 mobile 記錄數**：562,735 筆
- **平均每日記錄**：43,287 筆
- **預估可媒合用戶**：~337,641 個 (假設 60% 媒合率)

### **成本分析**

- **總查詢成本**：$4.81 USD (13 天 × $0.37/天)
- **儲存成本**：~$0.01 USD
- **總成本**：~$4.82 USD
- **總執行時間**：32.5 分鐘 (13 天 × 2.5 分鐘/天)

---

## 🔧 special_lta_temp_for_update 機制確認

### **Scheduled Query 配置**

1. **`create_daily_lta_temp_table`**：

   - **執行時間**：每日 14:01 (台灣時間)
   - **功能**：建立隔日的 `special_lta_temp_for_update_YYYYMMDD` 表格
   - **SQL 邏輯**：`CREATE TABLE IF NOT EXISTS` - **不會覆寫已存在表格**

2. **資料覆寫風險**：**無風險** ✅
   - 如果表格已存在，scheduled query 會跳過建立
   - 手動回填的歷史資料不會被自動化流程覆蓋

### **自動化執行時間軸**

```
每日 14:01 → 建立隔日的 temp 表格 (如果不存在)
每日 02:00 → 家樂福自動化執行 (寫入當日 temp 表格)
```

---

## 🚀 部署策略建議

### **階段 1: 歷史資料回填 (優先執行)**

#### **1.1 回填時間安排**

- **建議時間**：2025-08-28 上午 (避開自動化執行時間)
- **分批策略**：每批 5 天，共 3 批
  - 批次 1：2025-08-15 ~ 2025-08-19 (5 天)
  - 批次 2：2025-08-20 ~ 2025-08-24 (5 天)
  - 批次 3：2025-08-25 ~ 2025-08-27 (3 天)

#### **1.2 回填執行步驟**

```bash
# 批次 1 回填
for date in 2025-08-15 2025-08-16 2025-08-17 2025-08-18 2025-08-19; do
  python main.py --execution_mode=manual --target_date=$date --dry_run=false
  sleep 30  # 避免 BigQuery 配額限制
done

# 批次 2 回填 (隔 1 小時後執行)
for date in 2025-08-20 2025-08-21 2025-08-22 2025-08-23 2025-08-24; do
  python main.py --execution_mode=manual --target_date=$date --dry_run=false
  sleep 30
done

# 批次 3 回填 (隔 1 小時後執行)
for date in 2025-08-25 2025-08-26 2025-08-27; do
  python main.py --execution_mode=manual --target_date=$date --dry_run=false
  sleep 30
done
```

#### **1.3 回填驗證**

每批次完成後驗證：

```sql
-- 驗證回填資料完整性
SELECT
  DATE(created_at) as date,
  COUNT(*) as records,
  COUNT(DISTINCT permanent) as unique_users
FROM `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_YYYYMMDD`
WHERE source_type = 'carrefour_offline_purchase'
GROUP BY DATE(created_at)
ORDER BY date;
```

### **階段 2: 自動化系統部署**

#### **2.1 Terraform 部署**

```bash
# 測試環境部署
cd terraform
terraform plan -var="environment=test"
terraform apply -var="environment=test"

# 驗證測試環境
# 手動觸發測試執行

# 生產環境部署
terraform plan -var="environment=production"
terraform apply -var="environment=production"
```

#### **2.2 部署時間安排**

- **測試環境**：2025-08-28 下午 (歷史回填完成後)
- **生產環境**：2025-08-29 上午 (測試環境驗證通過後)
- **首次自動執行**：2025-08-30 02:00 (台灣時間)

### **階段 3: 監控和驗證**

#### **3.1 首週監控 (2025-08-30 ~ 2025-09-05)**

每日檢查項目：

- Cloud Function 執行狀況
- BigQuery 查詢成本
- 受眾標籤生成數量
- 執行時間和記憶體使用率

#### **3.2 監控指標**

```sql
-- 每日執行狀況監控
SELECT
  DATE(created_at) as execution_date,
  COUNT(*) as total_records,
  COUNT(DISTINCT permanent) as unique_users,
  source_entity,
  execution_id
FROM `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_*`
WHERE source_type = 'carrefour_offline_purchase'
  AND DATE(created_at) >= '2025-08-30'
GROUP BY DATE(created_at), source_entity, execution_id
ORDER BY execution_date DESC;
```

---

## ⚠️ 風險評估和緩解措施

### **風險 1: 歷史回填資料量過大**

- **風險等級**：低
- **影響**：BigQuery 查詢成本增加
- **緩解措施**：
  - 分批執行，控制單次查詢量
  - 使用 `--dry_run` 預估成本
  - 設定每日成本上限 $2 USD

### **風險 2: 自動化執行失敗**

- **風險等級**：中
- **影響**：每日受眾標籤生成中斷
- **緩解措施**：
  - Cloud Function 超時設定 600s (充足緩衝)
  - 記憶體配置 1024Mi (避免 OOM)
  - 執行時間監控和警告機制
  - 手動備援執行程序

### **風險 3: 資料品質問題**

- **風險等級**：低
- **影響**：受眾標籤準確性下降
- **緩解措施**：
  - 每日資料完整性驗證
  - 與歷史資料比較分析
  - 異常檢測和警報機制

### **風險 4: 成本超標**

- **風險等級**：低
- **影響**：BigQuery 成本超出預算
- **緩解措施**：
  - 設定 BigQuery 每日成本警報 ($1 USD)
  - 查詢優化和快取策略
  - 定期成本檢討和調整

---

## 📋 執行檢查清單

### **回填前檢查**

- [ ] 確認 BigQuery 權限正常
- [ ] 驗證程式碼邏輯正確
- [ ] 設定成本監控警報
- [ ] 準備回滾計畫

### **部署前檢查**

- [ ] Terraform 配置驗證通過
- [ ] Service Account 權限確認
- [ ] Cloud Function 資源配置適當
- [ ] 監控和警報機制設定

### **上線後檢查**

- [ ] 首次自動執行成功
- [ ] 資料完整性驗證通過
- [ ] 執行時間在預期範圍內
- [ ] 成本控制在預算內

---

## 🔄 回滾計畫

### **回填階段回滾**

如果回填過程中發現問題：

1. 立即停止回填程序
2. 檢查已回填資料的正確性
3. 如有錯誤，刪除錯誤的表格
4. 修正程式碼後重新回填

### **自動化部署回滾**

如果自動化執行出現問題：

1. 暫停 Cloud Scheduler 觸發
2. 檢查 Cloud Function 日誌
3. 修正配置問題
4. 手動執行驗證
5. 重新啟用自動化

### **緊急手動執行**

```bash
# 緊急手動執行當日受眾媒合
cd /Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data
source venv/bin/activate
python main.py --execution_mode=emergency --dry_run=false
```

---

## 📈 成功指標

### **短期指標 (第一週)**

- 歷史回填完成率：100%
- 自動化執行成功率：> 95%
- 每日受眾標籤生成數量：> 20,000
- 平均執行時間：< 180 秒

### **中期指標 (第一個月)**

- 系統穩定性：> 99%
- 成本控制：< $15 USD/月
- 資料品質：與手動執行結果一致性 > 99%
- 監控覆蓋率：100%

### **長期指標 (三個月)**

- 受眾媒合效果提升：> 10%
- 運維成本降低：> 50%
- 系統可擴展性：支援其他離線資料來源
- 團隊滿意度：> 90%

---

**文檔版本**：v1.0
**最後更新**：2025-08-27 19:30:00
**負責團隊**：資料工程團隊
**審核狀態**：✅ 準備部署
