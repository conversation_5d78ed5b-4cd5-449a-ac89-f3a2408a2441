# 最近變更記錄

## 📊 概覽

本文檔記錄家樂福離線資料系統最近的重要變更，包括權限修復、架構升級和功能改進。

## 🔄 最近變更

### 2025-09-08: 實現三重排程器混合架構 - 資料複製與受眾媒合系統

#### 🚨 **架構重大升級**

- **問題**: 原系統僅支援單一受眾媒合功能，缺乏家樂福資料的自動同步機制
- **目標**: 實現完整的資料處理流程，包含資料複製和受眾媒合兩大功能
- **架構**: 從單一 Cloud Function 升級為三重排程器混合架構
- **影響**: 系統具備完整的端對端資料處理能力

#### 🔧 **主要架構變更**

##### 1. 三重排程器設計

**新增資料複製排程器**:

```terraform
# 每日資料複製排程
resource "google_cloud_scheduler_job" "carrefour_daily_replication" {
  schedule = "30 10 * * *"  # 每日台灣時間 10:30
  table_name = "offline_transaction_day"
}

# 每月資料複製排程
resource "google_cloud_scheduler_job" "carrefour_monthly_replication" {
  schedule = "30 10 5 * *"  # 每月 5 號台灣時間 10:30
  table_name = "offline_transaction_month"
}
```

**保留受眾媒合排程器**:

```terraform
# 受眾媒合排程
resource "google_cloud_scheduler_job" "carrefour_audience_matching" {
  schedule = "0 2 * * *"   # 每日台灣時間凌晨 02:00
}
```

##### 2. 雙 Cloud Function 架構

**資料複製 Cloud Function**:

- **名稱**: `carrefour-data-replication-prod`
- **功能**: tw-eagle-prod → tagtoo-tracking 資料同步
- **支援表格**: offline_transaction_day, offline_transaction_month
- **執行時間**: 30-60 秒
- **成本**: ~$0.02 USD/天

**受眾媒合 Cloud Function**:

- **名稱**: `carrefour-offline-data-prod` (保持原名)
- **功能**: 受眾媒合和標籤生成
- **資料來源**: tagtoo-tracking.event_prod
- **資料目標**: tagtoo-ml-workflow
- **執行時間**: 2-3 分鐘
- **成本**: ~$0.37 USD/天

##### 3. 單表格複製模式

**核心類別重構**:

```python
class AutomatedDailyReplicator:
    def __init__(self, table_name: str = "offline_transaction_day"):
        self.table_configs = {
            "offline_transaction_day": {
                "source": f"{self.source_project}.rmn_tagtoo.offline_transaction_day",
                "target": f"{self.target_project}.event_prod.carrefour_offline_transaction_day",
                "schedule_type": "daily"
            },
            "offline_transaction_month": {
                "source": f"{self.source_project}.rmn_tagtoo.offline_transaction_month",
                "target": f"{self.target_project}.event_prod.carrefour_offline_transaction_month",
                "schedule_type": "monthly"
            }
        }
        self.current_table = table_name
        self.table_config = self.table_configs[table_name]
```

##### 4. 函數進入點適配

**資料複製函數**:

```python
def main(request):
    """Cloud Function 進入點 - 資料複製"""
    request_data = request.get_json() or {}
    table_name = request_data.get("table_name", "offline_transaction_day")

    replicator = AutomatedDailyReplicator(table_name=table_name)
    results = replicator.run_replication()
    return _serialize_datetime_objects(results)
```

#### 📊 **系統效能改進**

##### 執行時間優化

- **資料複製**: 30-60 秒 (單表格處理)
- **受眾媒合**: 2-3 分鐘 (保持原有效能)
- **總執行時間**: 分散到不同時間點，避免資源競爭

##### 成本優化

- **資料複製**: $0.02 USD/天 (新增成本)
- **受眾媒合**: $0.37 USD/天 (維持原有)
- **總成本**: $0.39 USD/天 (輕微增加)

##### 可靠性提升

- **職責分離**: 資料複製和受眾媒合獨立執行
- **錯誤隔離**: 一個功能失敗不影響另一個功能
- **監控細化**: 每個功能獨立監控和警報

#### 📁 **變更檔案總覽**

##### Terraform 配置

- `terraform/cloud_scheduler.tf` - 新增三重排程器定義
- `terraform/variables.tf` - 新增月複製排程變數
- `terraform/main.tf` - 保持原有 Cloud Function 配置

##### Python 程式碼

- `tools/automated_daily_replication.py` - 重構為單表格處理模式
- `tools/data_replication_function.py` - 新增資料複製進入點
- `main.py` - 保持受眾媒合邏輯

##### 文檔更新

- `README.md` - 更新為三重排程器混合架構說明
- `docs/deployment/recent-changes-log.md` - 新增本次變更記錄

#### 🎯 **部署驗證結果**

- ✅ 三個 Cloud Scheduler 成功部署
- ✅ 兩個 Cloud Function 獨立運作
- ✅ 日複製功能正常執行 (offline_transaction_day)
- ✅ 月複製功能配置完成 (offline_transaction_month)
- ✅ 受眾媒合功能維持正常 (保持原有邏輯)
- ✅ 所有排程時間配置正確

#### 🔄 **系統工作流程**

1. **10:30 AM**: 資料複製執行

   - 日表格: 每日自動同步
   - 月表格: 每月 5 號同步

2. **02:00 AM**: 受眾媒合執行

   - 使用前一天同步的資料
   - 生成受眾標籤和分析結果

3. **監控**: 兩套獨立的監控和警報系統

#### 📈 **架構優勢**

- **模組化設計**: 每個功能職責清晰，易於維護
- **彈性排程**: 不同資料表格按需求頻率同步
- **成本效益**: 資料複製成本低，受眾媒合價值高
- **可擴展性**: 未來可輕易新增更多資料表格或分析功能
- **故障恢復**: 獨立功能模組提升整體系統可靠性

---

### 2025-09-05: 修復家樂福資料複製 Cloud Function JSON 序列化錯誤

#### 🚨 **問題背景**

- **問題**: `carrefour-data-replication-prod` Cloud Scheduler 任務執行失敗
- **錯誤**: `Object of type datetime/date is not JSON serializable`
- **影響**: 資料複製系統無法正常返回結果，但複製功能本身正常
- **根本原因**: Cloud Function 回應中包含未序列化的 datetime/date 物件

#### 🔧 **主要修復**

##### 1. JSON 序列化處理

**新增函數**:

```python
def _serialize_datetime_objects(obj):
    """遞迴處理物件中的 datetime 和 date 物件，轉換為字串"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, __import__('datetime').date):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: _serialize_datetime_objects(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_serialize_datetime_objects(item) for item in obj]
    else:
        return obj
```

**應用範圍**: 在 `data_replication_function.py` 中處理所有回應資料

##### 2. 日誌改善

**新增清楚的來源/目標表格日誌**:

```python
logger.info("🔄 開始執行資料複製...")
logger.info(f"📥 來源表格: {self.source_table}")
logger.info(f"📤 目標表格: {self.target_table}")
```

**實際顯示**:

- 📥 來源表格: `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
- 📤 目標表格: `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`

##### 3. 架構一致性驗證

**確認腳本架構**:

- **Cloud Function 入口點**: `data_replication_function.py` (作為 main.py)
- **核心複製邏輯**: `automated_daily_replication.py`
- **localhost 測試**: 使用相同的 `automated_daily_replication.py` 核心邏輯

#### 📊 **測試結果**

- ✅ JSON 序列化錯誤完全修復 (最新執行 06:02 無錯誤)
- ✅ Cloud Scheduler 觸發正常 (HTTP 200)
- ✅ 日誌提供清楚的來源/目標資訊
- ✅ localhost 和 Cloud Function 使用一致的程式碼
- ✅ 複製邏輯正常運作 (資料為最新，跳過複製)

#### 📁 **變更檔案**

- `tools/data_replication_function.py` - 新增 JSON 序列化處理
- `tools/automated_daily_replication.py` - 改善日誌記錄
- `tools/carrefour_data_replication.py` - 優化跨專案存取日誌
- `terraform/cloud_scheduler.tf` - Service Account 配置優化

#### 🎯 **系統狀態**

- 家樂福資料複製系統完全正常運作
- JSON 序列化問題已解決
- 日誌記錄更加清楚，便於監控和除錯
- 準備長期穩定執行

---

### 2025-09-01: 修復 Cloud Scheduler 權限問題並採用共享基礎設施架構

### Commit bd0c7a2: 修復 Cloud Scheduler 權限問題並採用共享基礎設施架構

#### 🚨 **問題背景**

- **問題**: Cloud Scheduler 連續幾天執行失敗，返回 HTTP 403 PERMISSION_DENIED
- **影響**: 家樂福受眾媒合系統無法自動執行
- **根本原因**: Cloud Function Gen2 缺少 `roles/run.invoker` 權限

#### 🔧 **主要變更**

##### 1. Service Account 架構升級

```yaml
變更前: <EMAIL>
變更後: <EMAIL>
```

**選擇理由**:

- 統一權限管理，提升可維護性
- 減少重複的 Service Account 配置
- 符合企業級最佳實務
- 自動繼承完整的 BigQuery、Storage 權限

##### 2. IAM 權限修復

**新增權限**:

```hcl
# Cloud Run Service 觸發權限 (修復關鍵問題)
resource "google_cloud_run_service_iam_binding" "invoker" {
  project  = local.shared_outputs.project_id
  location = local.shared_outputs.region
  service  = google_cloudfunctions2_function.carrefour_offline_data.name
  role     = "roles/run.invoker"
  members  = ["serviceAccount:${local.carrefour_service_account_email}"]
}
```

##### 3. 環境變數修復

**新增環境變數**:

```hcl
environment_variables = merge(var.env_vars, {
  ENVIRONMENT          = var.environment
  PROJECT_ID           = local.shared_outputs.project_id
  GOOGLE_CLOUD_PROJECT = local.shared_outputs.project_id  # 新增
  TARGET_PROJECT       = var.target_project_id
  SERVICE_NAME         = var.service_name
})
```

#### 📊 **測試結果**

- ✅ Cloud Scheduler 權限問題已解決
- ✅ 從 HTTP 403 進步到 HTTP 500 (應用層錯誤)
- ⚠️ 需要修復應用程式內部邏輯錯誤

#### 📁 **變更檔案**

- `apps/carrefour-offline-data/terraform/iam.tf` (64 insertions, 23 deletions)
- `apps/carrefour-offline-data/terraform/main.tf` (9 insertions, 1 deletion)

---

### Commit 615fa09: 修復 Cloud Function 布林值參數處理錯誤

#### 🚨 **問題背景**

- **問題**: Cloud Function 執行時發生 `'bool' object has no attribute 'lower'` 錯誤
- **影響**: HTTP 500 INTERNAL ERROR，系統無法正常處理資料
- **根本原因**: Cloud Scheduler 發送的 JSON 中 `dry_run` 是布林值，但程式碼假設為字串

#### 🔧 **主要變更**

##### 程式邏輯修復

**修改前**:

```python
dry_run = request_data.get('dry_run', 'false').lower() == 'true'
```

**修改後**:

```python
# 修復 dry_run 參數處理，支援布林值和字串
dry_run_value = request_data.get('dry_run', False)
if isinstance(dry_run_value, bool):
    dry_run = dry_run_value
elif isinstance(dry_run_value, str):
    dry_run = dry_run_value.lower() in ('true', '1', 'yes')
else:
    dry_run = False
```

#### 📊 **測試結果**

- ✅ Cloud Scheduler 成功觸發 (HTTP 200)
- ✅ Cloud Function 正常執行 (18秒完成)
- ✅ 成功處理 32,969 個用戶的受眾媒合
- ✅ 寫入 1,682,763 個標籤實例到 BigQuery

#### 📁 **變更檔案**

- `apps/carrefour-offline-data/main.py` (11 insertions, 1 deletion)

#### 🎯 **系統狀態**

- 家樂福離線資料系統完全正常運作
- 權限問題已解決，應用邏輯錯誤已修復
- 準備投入生產環境自動化執行

## 🔐 權限配置總結

### 當前 Service Account 配置

**主要 Service Account**: `<EMAIL>`

#### tagtoo-tracking 專案權限

- `roles/bigquery.dataEditor` - BigQuery 資料編輯
- `roles/bigquery.jobUser` - BigQuery Job 執行
- `roles/cloudfunctions.invoker` - Cloud Function 觸發
- `roles/run.invoker` - Cloud Run 服務觸發 (新增)
- `roles/cloudtasks.enqueuer` - Cloud Tasks 佇列
- `roles/datastore.user` - Datastore 存取
- `roles/firestore.serviceAgent` - Firestore 服務代理
- `roles/logging.logWriter` - 日誌寫入
- `roles/monitoring.metricWriter` - 監控指標寫入

#### tagtoo-ml-workflow 專案權限

- `roles/bigquery.admin` - BigQuery 完整管理
- `roles/bigquery.jobUser` - BigQuery Job 執行
- `roles/cloudfunctions.admin` - Cloud Function 管理
- `roles/storage.admin` - Storage 完整管理

### 資源級別 IAM 權限

#### Cloud Function IAM

```yaml
Resource: carrefour-offline-data-prod
Role: roles/cloudfunctions.invoker
Member: serviceAccount:<EMAIL>
```

#### Cloud Run Service IAM

```yaml
Resource: carrefour-offline-data-prod (Cloud Run Service)
Role: roles/run.invoker
Member: serviceAccount:<EMAIL>
```

## 🚀 部署流程

### 標準部署步驟

1. **程式碼變更**

   ```bash
   # 修改相關檔案
   git add <changed-files>
   git commit -m "fix(carrefour): <description>"
   ```

2. **Terraform 部署**

   ```bash
   cd apps/carrefour-offline-data/terraform
   terraform plan -var-file="environments/prod.tfvars"
   terraform apply
   ```

3. **功能驗證**

   ```bash
   # 手動觸發測試
   gcloud scheduler jobs run carrefour-offline-data-prod-schedule \
     --location=asia-east1

   # 檢查執行結果
   gcloud logging read 'resource.type="cloud_scheduler_job" AND
     resource.labels.job_id="carrefour-offline-data-prod-schedule"' \
     --limit=2
   ```

### 回滾程序

如果部署出現問題，可以使用以下步驟回滾：

```bash
# 檢查 Terraform 狀態
terraform show

# 回滾到上一個版本
git revert <commit-hash>
terraform plan -var-file="environments/prod.tfvars"
terraform apply
```

## 📊 監控和警報

### 自動監控指標

1. **Cloud Scheduler 執行狀態**

   - 警報閾值: 連續 2 次失敗
   - 通知頻道: <EMAIL>

2. **Cloud Function 效能監控**

   - 執行時間: > 300 秒警報
   - 記憶體使用: > 90% 警報
   - 錯誤率: > 5% 警報

3. **BigQuery 成本監控**
   - 單次查詢成本: > $0.5 警報
   - 日總成本: > $1.0 警報

### 手動檢查指令

```bash
# 檢查系統健康狀態
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1 --format="yaml(status,lastAttemptTime)"

# 檢查處理結果
bq query --use_legacy_sql=false \
  "SELECT COUNT(*) as total_records,
   MAX(created_at) as latest_update
   FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`"
```

## 📞 支援聯絡

**系統負責人**: Frank Zheng (<EMAIL>)
**技術支援**: Data Team
**緊急聯絡**: 透過 Google Cloud Monitoring 警報系統

---

**文檔建立**: 2025-09-01
**最後更新**: 2025-09-05 (新增 JSON 序列化修復記錄)
**下次審查**: 2025-12-01
