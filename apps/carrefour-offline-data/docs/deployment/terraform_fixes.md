# Terraform 配置修正報告

## 🔧 修正的關鍵問題

### 1. BigQuery Labels 策略優化

#### **問題分析**

- ❌ **不一致**: 程式碼使用 `"carrefour-offline-data"`，Terraform 使用 `"carrefour-audience-matching"`
- ❌ **重複定義**: 兩處都定義 labels，可能造成衝突
- ❌ **缺乏靈活性**: Terraform 硬編碼無法根據執行模式動態調整

#### **解決方案**

✅ **移除 Terraform 中的 BigQuery labels 硬編碼**
✅ **完全依賴程式碼動態生成 labels**
✅ **保留 GCP 資源的 common_labels**

#### **程式碼中的動態 Labels**

```python
labels = {
    "project": "carrefour-offline-data",
    "repo": "Tagtoo/integrated-event",
    "env": "prod",
    "trigger": "manual" if execution_mode == "manual" else "auto",
    "user": username  # 動態偵測當前用戶
}
```

#### **優勢**

- 🎯 **精確追蹤**: 可區分手動執行 vs 自動執行
- 👤 **用戶追蹤**: 自動偵測執行用戶
- 🔄 **動態調整**: 根據執行模式自動調整
- 💰 **成本分析**: 支援更細緻的費用追蹤

### 2. Cloud Scheduler 時間設定修正

#### **問題分析**

- ❌ **時區混淆**: 設定了 `time_zone = "Asia/Taipei"` 但 cron 仍使用 UTC 時間
- ❌ **錯誤時間**: `"0 18 * * *"` 在台北時間是凌晨 2:00，但應該直接用台北時間

#### **解決方案**

✅ **修正 cron 表達式**: `"0 18 * * *"` → `"0 2 * * *"`
✅ **保持時區設定**: `time_zone = "Asia/Taipei"`

#### **修正前後對比**

```hcl
# 修正前 (錯誤)
schedule = "0 18 * * *"  # UTC 18:00 = 台北時間 02:00
time_zone = "Asia/Taipei"

# 修正後 (正確)
schedule = "0 2 * * *"   # 台北時間 02:00
time_zone = "Asia/Taipei"
```

## 📋 其他重要修正

### 3. Cloud Function 標籤添加

```hcl
resource "google_cloudfunctions2_function" "carrefour_audience_matching" {
  # ...
  labels = local.common_labels  # 新增
}
```

### 4. 原始碼打包路徑修正

```hcl
data "archive_file" "function_source" {
  source_dir = "${path.module}/.."  # 包含 main.py 和 requirements.txt

  excludes = [
    "__pycache__",
    "*.pyc",
    ".pytest_cache",
    "tests/",
    "terraform/",
    "docs/",
    "reports/",
    ".git/",
    "venv/",
    "*.md"
  ]
}
```

## 🎯 最佳實務建議

### BigQuery Labels 管理策略

#### **推薦做法**

1. **程式碼負責 BigQuery labels**: 動態生成，包含執行上下文
2. **Terraform 負責 GCP 資源 labels**: 靜態標籤，用於資源管理
3. **避免重複定義**: 防止標籤衝突和不一致

#### **未來擴展考量**

```python
# 支援更細緻的成本追蹤
def _generate_gcp_labels(self, execution_mode: str, operation_type: str = None) -> Dict[str, str]:
    labels = {
        "project": "carrefour-offline-data",
        "repo": "Tagtoo/integrated-event",
        "env": "prod",
        "trigger": "manual" if execution_mode == "manual" else "auto",
        "user": self._get_current_user(),
        "operation": operation_type or "audience-matching"  # 支援不同操作類型
    }
    return labels

# 使用範例
# 階段1查詢
job_config = bigquery.QueryJobConfig(
    labels=self._generate_gcp_labels(execution_mode, "stage1-entity-mapping")
)

# 階段2查詢
job_config = bigquery.QueryJobConfig(
    labels=self._generate_gcp_labels(execution_mode, "stage2-offline-data")
)
```

### 時區管理最佳實務

#### **Cloud Scheduler 時區設定**

```hcl
variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

variable "schedule_cron" {
  description = "排程執行的 Cron 表達式 (使用指定時區)"
  type        = string
  default     = "0 2 * * *"  # 台灣時間凌晨 2:00
}
```

#### **程式碼中的時間處理**

```python
# 確保時區一致性
def _format_created_at(self) -> datetime:
    """使用 UTC 時間，確保時區一致性"""
    return datetime.now(timezone.utc)

def _calculate_target_date(self) -> str:
    """計算目標表格日期後綴 (台灣時間)"""
    taipei_tz = pytz.timezone('Asia/Taipei')
    base_date = datetime.now(taipei_tz)
    # ...
```

## 🚀 部署準備檢查清單

### 執行前驗證

- [x] BigQuery labels 策略已優化
- [x] Cloud Scheduler 時間設定已修正
- [x] Cloud Function 標籤已添加
- [x] 原始碼打包路徑已修正
- [ ] Terraform 配置語法驗證
- [ ] 權限設定確認
- [ ] 成本預算設定

### 部署後驗證

- [ ] Cloud Scheduler 觸發時間正確
- [ ] BigQuery labels 正確生成
- [ ] Cloud Function 執行成功
- [ ] 費用追蹤功能正常

## 💡 總結

這些修正確保了：

1. **一致性**: 移除重複和衝突的標籤定義
2. **靈活性**: 支援動態標籤生成和細緻成本追蹤
3. **正確性**: 修正時區設定，確保排程時間準確
4. **可維護性**: 清晰的責任分工和最佳實務

**下一步**: 執行 `terraform plan` 驗證配置，然後進行實際部署。
