# Terraform 部署需求規格

## 📋 前置檢查結果

### ✅ 系統狀態確認

**高效能 SQL 方法**：

- ✅ `entity_optimized_run` 方法正常運作
- ✅ 預估查詢成本：$0.1806 USD (合理範圍)
- ✅ 權限配置正確
- ✅ 邏輯一致性已驗證

**當前 GCP 配置**：

- **專案**：tagtoo-tracking
- **區域**：asia-east1
- **可用區**：asia-east1-a
- **帳戶**：<EMAIL>

**Service Account**：

- **名稱**：carrefour-tagtoo-bigquery-view
- **Email**：<EMAIL>
- **狀態**：啟用

**目標表格**：

- **表格**：tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_YYYYMMDD
- **結構**：permanent, segment_id, created_at, source_type, source_entity, execution_id
- **叢集欄位**：source_type, segment_id
- **權限**：讀寫正常

## 🎯 部署目標架構

### Cloud Run Functions 2nd Gen 規格

```yaml
資源配置：
記憶體：1024Mi
CPU：1 vCPU
超時：600秒 (10分鐘)
最大實例數：1
最小實例數：0

觸發配置：
類型：HTTP 觸發器
來源：Cloud Scheduler

環境變數：
EXECUTION_MODE：scheduled
TARGET_PROJECT：tagtoo-ml-workflow
SOURCE_PROJECT：tagtoo-tracking
```

### Cloud Scheduler 規格

```yaml
排程配置：
  Cron：0 2 * * *
  時區：Asia/Taipei
  描述：家樂福離線資料每日受眾標籤生成

重試配置：
  最大重試次數：3
  重試間隔：5分鐘
  最大重試持續時間：15分鐘

有效負載：
  HTTP 方法：POST
  內容類型：application/json
  主體：{"execution_mode": "scheduled", "days_back": 1}
```

### IAM 權限需求

```yaml
Cloud Function Service Account 權限：
BigQuery 權限：
- roles/bigquery.dataViewer (tagtoo-tracking)
- roles/bigquery.dataEditor (tagtoo-ml-workflow)
- roles/bigquery.jobUser (兩個專案)

Cloud Logging 權限：
- roles/logging.logWriter

Cloud Monitoring 權限：
- roles/monitoring.metricWriter

Cloud Scheduler Service Account 權限：
- roles/cloudfunctions.invoker (觸發 Cloud Function)
```

## 📊 監控需求

### Cloud Function 監控

```yaml
執行監控：
  - 執行成功率 (目標: >95%)
  - 執行時間 (目標: <180秒)
  - 記憶體使用率 (目標: <80%)
  - 錯誤率 (目標: <5%)

警報設定：
  - 執行失敗警報 (立即)
  - 執行時間超過 300秒 (5分鐘延遲)
  - 記憶體使用率超過 90% (立即)
```

### BigQuery 成本監控

```yaml
成本監控：
  - 每日查詢成本 (目標: <$0.5 USD)
  - 每月總成本 (目標: <$15 USD)
  - 異常查詢量檢測

警報設定：
  - 單日成本超過 $1 USD (立即)
  - 單次查詢成本超過 $0.5 USD (立即)
  - 每月成本超過 $20 USD (立即)
```

### Cloud Scheduler 監控

```yaml
排程監控：
  - 觸發成功率 (目標: >99%)
  - 重試次數統計
  - 執行延遲監控

警報設定：
  - 觸發失敗警報 (立即)
  - 連續重試 3 次失敗 (立即)
  - 執行延遲超過 10 分鐘 (立即)
```

## 🔧 技術規格

### Terraform 版本需求

```yaml
Terraform：>= 1.0
Provider 版本：
google：~> 4.0
google-beta：~> 4.0
archive：~> 2.0
```

### 部署環境

```yaml
開發環境 (dev)：
專案：tagtoo-tracking
區域：asia-east1
函數名稱：carrefour-audience-matching-dev
排程：手動觸發

生產環境 (prod)：
專案：tagtoo-tracking
區域：asia-east1
函數名稱：carrefour-audience-matching-prod
排程：每日 02:00 (Asia/Taipei)
```

## 📁 檔案結構需求

```
terraform/
├── main.tf                    # 主要資源配置
├── variables.tf               # 變數定義
├── outputs.tf                 # 輸出值
├── versions.tf                # Provider 版本約束
├── iam.tf                     # IAM 角色和權限
├── monitoring.tf              # 監控和警報配置
├── scheduler.tf               # Cloud Scheduler 配置
├── function.tf                # Cloud Function 配置
└── environments/
    ├── dev.tfvars            # 開發環境變數
    └── prod.tfvars           # 生產環境變數
```

## 🔐 安全需求

### 最小權限原則

```yaml
Service Account 權限：
- 僅授予必要的最小權限
- 定期審查和更新權限
- 使用專用 Service Account

網路安全：
- Cloud Function 僅允許內部觸發
- 使用 VPC 連接器 (如需要)
- 啟用 Cloud Armor (如需要)
```

### 資料安全

```yaml
資料加密：
- 傳輸中加密 (HTTPS)
- 靜態資料加密 (預設)
- 敏感環境變數加密

存取控制：
- IAM 角色最小化
- 定期權限審查
- 存取日誌記錄
```

## 📋 驗證需求

### 功能驗證

```yaml
部署後驗證：
- Cloud Function 部署成功
- Cloud Scheduler 配置正確
- IAM 權限正常
- 監控警報設定

功能測試：
- 手動觸發測試
- 自動排程測試
- 錯誤處理測試
- 監控警報測試
```

### 效能驗證

```yaml
效能基準：
- 執行時間 < 180秒
- 記憶體使用 < 800Mi
- 查詢成本 < $0.2 USD
- 成功率 > 95%
```

## 🔄 回滾計畫

### 緊急回滾

```yaml
回滾觸發條件：
- 連續執行失敗 > 3次
- 成本異常 (超過 $2 USD/天)
- 資料品質問題
- 系統不可用

回滾步驟：
1. 暫停 Cloud Scheduler
2. 檢查錯誤日誌
3. 恢復手動執行模式
4. 修正問題後重新部署
```

### 災難恢復

```yaml
備份策略：
- Terraform state 備份
- 配置檔案版本控制
- 監控配置備份

恢復程序：
- 從備份恢復 Terraform state
- 重新部署基礎設施
- 驗證功能正常
- 恢復監控配置
```

---

**文檔版本**：v1.0
**建立日期**：2025-08-27
**負責團隊**：資料工程團隊
**審核狀態**：✅ 準備實施
