# 家樂福離線資料系統文檔

本目錄包含家樂福離線資料系統的完整文檔，按照功能和用途分類組織。

## 📁 目錄結構

```
docs/
├── README.md                    # 本文件 - 文檔導航
├── architecture/               # 系統架構相關文檔
├── deployment/                 # 部署和配置相關文檔
├── analysis/                   # 技術分析和比較文檔
├── operations/                 # 運維和操作指南
├── development/               # 開發過程和技術決策
└── archived/                  # 過時文檔歸檔
```

## 🏗️ 系統架構 (architecture/)

| 文檔                                                                                        | 描述                     | 狀態    |
| ------------------------------------------------------------------------------------------- | ------------------------ | ------- |
| [automation_architecture.md](architecture/automation_architecture.md)                       | 自動化系統架構設計       | ✅ 當前 |
| [deployment_architecture_comparison.md](architecture/deployment_architecture_comparison.md) | 部署架構方案比較分析     | ✅ 當前 |
| [service-account-permissions.md](architecture/service-account-permissions.md)               | Service Account 權限配置 | ✅ 當前 |

**適用對象**：系統架構師、技術主管、新團隊成員
**更新頻率**：架構變更時更新

## 🚀 部署配置 (deployment/)

| 文檔                                                          | 描述               | 狀態    |
| ------------------------------------------------------------- | ------------------ | ------- |
| [deployment_strategy.md](deployment/deployment_strategy.md)   | 完整部署策略和計畫 | ✅ 當前 |
| [deployment_checklist.md](deployment/deployment_checklist.md) | 部署檢查清單       | ✅ 當前 |
| [terraform_fixes.md](deployment/terraform_fixes.md)           | Terraform 配置修正 | ✅ 當前 |
| [cross_platform_setup.md](deployment/cross_platform_setup.md) | 跨平台環境設定     | ✅ 當前 |
| [recent-changes-log.md](deployment/recent-changes-log.md)     | 最近變更記錄       | ✅ 當前 |

**適用對象**：DevOps 工程師、部署團隊
**更新頻率**：部署流程變更時更新

## 📊 技術分析 (analysis/)

| 文檔                                                                | 描述               | 狀態    |
| ------------------------------------------------------------------- | ------------------ | ------- |
| [performance_comparison.md](analysis/performance_comparison.md)     | 效能比較分析報告   | ✅ 當前 |
| [backfill_comparison.md](analysis/backfill_comparison.md)           | 歷史回填方案比較   | ✅ 當前 |
| [logic_comparison.md](analysis/logic_comparison.md)                 | 邏輯一致性驗證報告 | ✅ 當前 |
| [cost_optimization_report.md](analysis/cost_optimization_report.md) | 成本優化分析報告   | ✅ 當前 |
| [architecture_assessment.md](analysis/architecture_assessment.md)   | 架構評估報告       | ✅ 當前 |

**適用對象**：技術團隊、決策者、架構師
**更新頻率**：重大技術變更時更新

## 🔧 運維操作 (operations/)

| 文檔                                                                          | 描述               | 狀態    |
| ----------------------------------------------------------------------------- | ------------------ | ------- |
| [operations_manual.md](operations/operations_manual.md)                       | 運維操作手冊       | ✅ 當前 |
| [execution_checklist.md](operations/execution_checklist.md)                   | 執行檢查清單       | ✅ 當前 |
| [authentication_guide.md](operations/authentication_guide.md)                 | 認證配置指南       | ✅ 當前 |
| [trigger-flow-and-permissions.md](operations/trigger-flow-and-permissions.md) | 觸發流程和權限詳解 | ✅ 當前 |

**適用對象**：運維團隊、值班工程師
**更新頻率**：運維流程變更時更新

## 💻 開發文檔 (development/)

| 文檔                                                                         | 描述               | 狀態    |
| ---------------------------------------------------------------------------- | ------------------ | ------- |
| [development.md](development/development.md)                                 | 開發指南和當前狀態 | ✅ 當前 |
| [technical_decisions.md](development/technical_decisions.md)                 | 技術決策記錄       | ✅ 當前 |
| [development_recommendations.md](development/development_recommendations.md) | 開發建議和最佳實務 | ✅ 當前 |
| [json_field_standards.md](development/json_field_standards.md)               | JSON 欄位標準規範  | ✅ 當前 |
| [carrefour_validation_spec.md](development/carrefour_validation_spec.md)     | 家樂福驗證規格     | ✅ 當前 |

**適用對象**：開發團隊、新成員
**更新頻率**：開發流程或標準變更時更新

## 📦 歷史歸檔 (archived/)

| 文檔                                                                    | 描述         | 歸檔原因     |
| ----------------------------------------------------------------------- | ------------ | ------------ |
| [data_sync_plan.md](archived/data_sync_plan.md)                         | 資料同步計畫 | 已完成實施   |
| [operation_manual_old.md](archived/operation_manual_old.md)             | 舊版運維手冊 | 已被新版取代 |
| [data_copy_strategy.md](archived/data_copy_strategy.md)                 | 資料複製策略 | 已完成實施   |
| [permission_request.md](archived/permission_request.md)                 | 權限申請記錄 | 已完成申請   |
| [script_reorganization_plan.md](archived/script_reorganization_plan.md) | 腳本重組計畫 | 已完成重組   |

**適用對象**：歷史參考、審計需求
**更新頻率**：不再更新

## 🎯 快速導航

### 新團隊成員入門

1. [系統架構概覽](architecture/automation_architecture.md)
2. [開發環境設定](deployment/cross_platform_setup.md)
3. [開發指南](development/development.md)
4. [運維手冊](operations/operations_manual.md)

### 部署相關

1. [部署策略](deployment/deployment_strategy.md)
2. [部署檢查清單](deployment/deployment_checklist.md)
3. [Terraform 配置](deployment/terraform_fixes.md)

### 技術決策參考

1. [效能比較分析](analysis/performance_comparison.md)
2. [架構比較](architecture/deployment_architecture_comparison.md)
3. [技術決策記錄](development/technical_decisions.md)

### 故障排除

1. [運維手冊](operations/operations_manual.md)
2. [執行檢查清單](operations/execution_checklist.md)
3. [認證問題](operations/authentication_guide.md)

## 📋 文檔維護

### 命名規範

- 檔案名稱：小寫字母，使用底線分隔
- 格式：`category_description.md`
- 避免特殊字元和空格

### 更新責任

- **架構文檔**：系統架構師負責
- **部署文檔**：DevOps 團隊負責
- **分析文檔**：技術團隊負責
- **運維文檔**：運維團隊負責
- **開發文檔**：開發團隊負責

### 審查週期

- **每季度**：檢查文檔有效性
- **重大變更後**：立即更新相關文檔
- **年度**：全面審查和歸檔過時文檔

### 歸檔標準

文檔符合以下條件時應歸檔：

- 相關功能已下線或替換
- 流程已廢棄或重新設計
- 技術方案已過時
- 超過 6 個月未更新且不再相關

## 🔍 搜尋指南

### 按主題搜尋

- **效能優化**：查看 `analysis/` 目錄
- **部署問題**：查看 `deployment/` 目錄
- **運維操作**：查看 `operations/` 目錄
- **開發規範**：查看 `development/` 目錄

### 按狀態搜尋

- **當前有效**：所有非 `archived/` 目錄的文檔
- **歷史參考**：`archived/` 目錄中的文檔
- **最新更新**：檢查各文檔的最後更新日期

---

**文檔結構版本**：v2.1
**最後更新**：2025-09-01
**維護團隊**：資料工程團隊
**聯絡方式**：透過 Git issue 或團隊 Slack 頻道
