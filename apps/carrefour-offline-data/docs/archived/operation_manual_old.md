# 家樂福受眾媒合系統操作手冊

## 📋 系統概述

家樂福受眾媒合系統是一個自動化工具，用於將家樂福線下購買資料與 Tagtoo 線上事件資料進行媒合，並根據購買商品分類生成階層式受眾標籤，最終輸出到 BigQuery 表格供 ML 工作流程使用。

## 🚀 快速開始

### 環境準備

1. **進入專案目錄**

```bash
cd /Users/<USER>/tagtoo/integrated-event/apps/carrefour-offline-data
```

2. **啟動虛擬環境**

```bash
source venv/bin/activate
```

3. **驗證環境**

```bash
python --version  # 應該顯示 Python 3.8+
pip list | grep google-cloud-bigquery  # 確認 BigQuery 套件已安裝
```

### 基本執行

#### 測試模式 (推薦首次使用)

```bash
# 執行完整的端到端測試
python src/analysis/end_to_end_test.py

# 或者單獨執行受眾媒合 (限制 100 筆資料)
python src/analysis/carrefour_audience_matching.py
```

#### 生產模式 (處理完整資料集)

```bash
# 執行生產模式 (需要適當權限)
python src/analysis/carrefour_audience_matching.py production
```

## 📊 系統功能模組

### 1. 商品分類分析 (`product_category_analysis.py`)

**功能**: 分析家樂福商品分類表，建立商品編號到階層式 segment_id 的對應關係

**執行**:

```bash
python src/analysis/product_category_analysis.py
```

**輸出**:

- `reports/product_category_analysis.json`: 完整分析結果
- 商品分類統計、階層關係驗證、對應表建立

### 2. 標籤生成測試 (`segment_generation_test.py`)

**功能**: 測試階層式 segment_id 生成邏輯的正確性

**執行**:

```bash
python src/analysis/segment_generation_test.py
```

**輸出**:

- `reports/segment_generation_test_report.json`: 測試結果
- 單一商品、多商品、邊界情況測試

### 3. 受眾媒合系統 (`carrefour_audience_matching.py`)

**功能**: 核心媒合系統，執行完整的資料處理流程

**執行選項**:

```bash
# 測試模式 (限制資料量)
python src/analysis/carrefour_audience_matching.py

# 生產模式 (完整資料集)
python src/analysis/carrefour_audience_matching.py production
```

**處理流程**:

1. 建立 `user.ph` → `permanent` 對應表
2. 查詢線下購買資料 (使用 UNNEST 展開 items)
3. 生成階層式受眾標籤
4. 寫入目標表格 (需要權限)

### 4. 資料品質驗證 (`data_quality_validator.py`)

**功能**: 驗證生成的受眾標籤資料品質

**執行**:

```bash
python src/analysis/data_quality_validator.py
```

**驗證項目**:

- permanent 欄位格式和唯一性
- segment_id 欄位格式和階層性
- 時間戳欄位有效性
- 元資料一致性

### 5. 端到端測試 (`end_to_end_test.py`)

**功能**: 執行完整的系統測試，驗證所有模組整合

**執行**:

```bash
python src/analysis/end_to_end_test.py
```

**測試階段**:

1. 商品分類分析
2. 標籤生成測試
3. 受眾媒合測試
4. 資料品質驗證
5. 整合功能測試

## 🔧 配置參數

### 系統配置 (`carrefour_audience_matching.py`)

```python
default_config = {
    "source_tables": {
        "tagtoo_event": "tagtoo-tracking.event_prod.tagtoo_event",
        "carrefour_offline": "tw-eagle-prod.rmn_tagtoo.offline_transaction_day"
    },
    "target_table": "tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{date}",
    "product_csv": "docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv",
    "batch_size": 10000,
    "max_cost_usd": 10.0,  # BigQuery 查詢成本限制
    "ec_id": 715           # 家樂福的 ec_id
}
```

### 可調整參數

- `days_back`: 用戶對應表回溯天數 (預設: 7 天，生產: 30 天)
- `limit`: 購買記錄查詢限制 (測試模式: 100 筆，生產模式: 無限制)
- `max_cost_usd`: BigQuery 查詢成本上限 (預設: $10 USD)

## 📈 監控與日誌

### 日誌級別

- `INFO`: 正常執行資訊
- `WARNING`: 警告訊息 (部分功能異常)
- `ERROR`: 錯誤訊息 (功能失敗)

### 關鍵指標監控

**媒合效能**:

- 用戶對應關係數量
- 購買記錄處理數量
- 受眾標籤生成數量
- 平均每用戶標籤數

**資料品質**:

- 資料完整性 (目標: 100%)
- 資料準確性 (目標: 100%)
- 資料一致性 (目標: 100%)
- 總體評分 (目標: A 級)

**系統效能**:

- 執行時間 (目標: < 5 分鐘)
- BigQuery 查詢成本 (目標: < $10 USD)
- 記憶體使用量

## 🚨 故障排除

### 常見問題

#### 1. BigQuery 權限錯誤

```
Error: 403 Forbidden
```

**解決方案**:

- 確認已設定正確的 service account
- 檢查 BigQuery 專案權限
- 使用 `gcloud auth application-default login` 重新認證

#### 2. 查詢成本超限

```
Error: 查詢成本 $XX.XX 超過限制 $10.00
```

**解決方案**:

- 調整 `max_cost_usd` 參數
- 減少查詢資料範圍 (調整 `days_back` 或 `limit`)
- 優化查詢條件

#### 3. 商品分類對應失敗

```
Error: 載入商品分類資料失敗
```

**解決方案**:

- 檢查 CSV 檔案路徑: `docs/家樂福商品分類表（塔圖內部用） - 所有商品分類.csv`
- 確認檔案格式正確 (UTF-8 編碼)
- 檢查必要欄位是否存在

#### 4. 目標表格寫入失敗

```
Error: 寫入目標表格失敗
```

**解決方案**:

- 確認對 `tagtoo-ml-workflow` 專案有寫入權限
- 檢查目標表格 schema 是否正確
- 驗證資料格式符合要求

### 除錯工具

#### 資料結構調試

```bash
python src/analysis/debug_items_structure.py
```

用於檢查線下資料中 items 欄位的實際結構。

#### 詳細日誌

```bash
# 啟用詳細日誌
export PYTHONPATH=$PYTHONPATH:$(pwd)/src
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
```

## 📋 檢查清單

### 執行前檢查

- [ ] 虛擬環境已啟動
- [ ] BigQuery 權限已設定
- [ ] 商品分類檔案存在且格式正確
- [ ] 網路連線正常

### 執行後驗證

- [ ] 所有測試階段通過
- [ ] 資料品質評分達到 A 級
- [ ] 生成的受眾標籤格式正確
- [ ] 執行時間在合理範圍內
- [ ] BigQuery 成本在預算內

### 生產部署檢查

- [ ] 目標表格寫入權限已取得
- [ ] 排程執行機制已設定
- [ ] 監控告警已建立
- [ ] 備份恢復機制已準備

## 📞 支援聯絡

**技術支援**: Tagtoo Data Team
**文檔版本**: v1.0.0
**最後更新**: 2025-08-25

---

**注意事項**:

1. 首次使用請先執行測試模式確認系統正常
2. 生產模式執行前請確認所有權限已設定
3. 定期檢查 BigQuery 成本使用情況
4. 建議設定自動化監控和告警機制
