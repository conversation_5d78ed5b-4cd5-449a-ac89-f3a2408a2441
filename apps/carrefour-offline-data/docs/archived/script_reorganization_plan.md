# 腳本重組計畫

> 計畫日期：2025-08-20
> 目標：簡化腳本依賴關係，建立清晰的功能分類，移除重複功能

## 📋 **當前腳本分析**

### **靜態網頁生成腳本（重複功能）**

- `create_all_in_one_presentation.py` - **主要腳本**，生成統一投影片
- `create_self_contained_html.py` - 重複功能，建立自包含 HTML
- `create_slide_presentation.py` - 重複功能，建立投影片
- `populate_web_reports.py` - 重複功能，填充網頁報告
- `create_web_architecture.py` - 重複功能，建立網頁架構

**決策**：保留 `create_all_in_one_presentation.py`，移除其他重複腳本

### **資料分析腳本（核心功能）**

- `offline_deep_analysis.py` - 離線資料深度分析
- `items_format_validation.py` - Items 格式驗證
- `null_ph_id_analysis.py` - NULL ph_id 分析
- `negative_values_investigation.py` - 負數資料調查
- `comprehensive_data_distribution_analysis.py` - 綜合資料分佈分析

**決策**：保留所有核心分析腳本

### **重疊率分析腳本（部分重複）**

- `create_corrected_analysis.py` - 修正分析報告
- `comprehensive_overlap_analysis.py` - 重複功能
- `full_overlap_analysis.py` - 重複功能
- `real_overlap_analysis.py` - 重複功能
- `validate_ph_id_overlap.py` - 重複功能

**決策**：保留 `create_corrected_analysis.py`，移除其他重複腳本

### **雜湊驗證腳本（重複功能）**

- `hash_validation_tools.py` - **主要腳本**
- `hash_reverse_analysis.py` - 重複功能
- `hash_verification_investigation.py` - 重複功能
- `quick_hash_validation.py` - 重複功能
- `simple_hash_reverse.py` - 重複功能
- `simple_hash_validation.py` - 重複功能
- `test_hash_validation.py` - 重複功能
- `sha256_security_assessment.py` - 重複功能

**決策**：保留 `hash_validation_tools.py`，移除其他重複腳本

### **工具和維護腳本**

- `generate_complete_report.py` - **主控腳本**
- `copy_to_target_dataset.py` - 資料複製工具
- `data_validator.py` - 資料驗證工具
- `schema_validator.py` - 結構驗證工具
- `monitoring_logger.py` - 監控日誌工具
- `upload_to_gcs.py` - GCS 上傳工具
- `web_maintenance.py` - 網頁維護工具

**決策**：保留所有工具腳本

### **舊版和測試腳本（可移除）**

- `run_full_pipeline.py` - 舊版主控腳本，被 `generate_complete_report.py` 取代
- `create_enhanced_report.py` - 舊版報告生成
- `create_interactive_report.py` - 舊版互動報告
- `detailed_data_analysis.py` - 舊版資料分析
- `diagnose_data_range.py` - 舊版診斷腳本
- `corrected_data_quality_analysis.py` - 舊版品質分析
- `update_comprehensive_analysis.py` - 舊版更新腳本
- `organize_gcs_reports.py` - 舊版 GCS 整理
- `copy_carrefour_data.py` - 舊版資料複製
- `quick_items_stats.py` - 快速統計（功能重複）
- `setup_environment.py` - 環境設定（一次性）

**決策**：移除所有舊版和測試腳本

## 🏗️ **重組後的目錄結構**

```
src/
├── analysis/           # 資料分析腳本
│   ├── offline_deep_analysis.py
│   ├── items_format_validation.py
│   ├── null_ph_id_analysis.py
│   ├── negative_values_investigation.py
│   ├── comprehensive_data_distribution_analysis.py
│   └── create_corrected_analysis.py
├── tools/              # 工具腳本
│   ├── copy_to_target_dataset.py
│   ├── hash_validation_tools.py
│   ├── data_validator.py
│   ├── schema_validator.py
│   ├── monitoring_logger.py
│   ├── upload_to_gcs.py
│   └── web_maintenance.py
├── presentation/       # 報告生成腳本
│   └── create_all_in_one_presentation.py
└── [已移除 archive 目錄 - 舊版腳本已完全刪除]
└── generate_complete_report.py  # 主控腳本（根目錄）
```

## 📊 **腳本數量對比**

| 分類         | 重組前 | 重組後 | 減少    |
| ------------ | ------ | ------ | ------- |
| 靜態網頁生成 | 5      | 1      | -4      |
| 重疊率分析   | 5      | 1      | -4      |
| 雜湊驗證     | 8      | 1      | -7      |
| 舊版/測試    | 11     | 0      | -11     |
| 核心分析     | 5      | 6      | +1      |
| 工具腳本     | 7      | 7      | 0       |
| **總計**     | **41** | **16** | **-25** |

**腳本數量減少 61%**，大幅簡化依賴關係

## 🔧 **實施步驟**

### **第一階段：建立新目錄結構**

1. 建立 `src/analysis/`、`src/tools/`、`src/presentation/`、`src/archive/` 目錄
2. 移動核心腳本到對應目錄
3. 更新主控腳本的路徑引用

### **第二階段：移除重複腳本**

1. ✅ 完全移除重複功能腳本（已從 Git 歷史中刪除）
2. ✅ 無需 archive 目錄（直接刪除舊版腳本）
3. ✅ 測試主控腳本執行正常

### **第三階段：更新文檔和配置**

1. 更新 README.md 說明新的目錄結構
2. 更新主控腳本的腳本路徑配置
3. 建立腳本功能說明文檔

## ✅ **預期效果**

1. **簡化依賴關係**：腳本數量減少 61%
2. **清晰功能分類**：按功能組織目錄結構
3. **統一入口點**：`generate_complete_report.py` 作為唯一執行入口
4. **唯一網頁生成**：`create_all_in_one_presentation.py` 作為唯一靜態網頁生成腳本
5. **易於維護**：清晰的目錄結構和功能分工

## 🎯 **與現有腳本的兼容性**

- **主控腳本**：`generate_complete_report.py` 將更新路徑引用
- **網頁生成**：確保 `create_all_in_one_presentation.py` 為唯一網頁生成腳本
- **核心功能**：所有核心分析功能保持不變
- **工具腳本**：所有必要工具保持可用

## 📊 **JSON 欄位標準化成果** _(新增 2025-08-21)_

### **標準化規範建立**

- **建立文檔**: `docs/json_field_standards.md` 統一命名標準
- **命名原則**: snake_case、語義清晰、一致性、英文命名
- **複數形式規則**: 計數欄位使用單數，集合欄位使用複數

### **核心欄位標準化**

| 舊欄位名稱            | 新欄位名稱               | 影響檔案                                                              |
| --------------------- | ------------------------ | --------------------------------------------------------------------- |
| `unique_ph_ids`       | `unique_ph_id_count`     | offline_deep_analysis.py, comprehensive_data_distribution_analysis.py |
| `total_records`       | `total_record_count`     | offline_deep_analysis.py, comprehensive_data_distribution_analysis.py |
| `execution_sec`       | `execution_time_seconds` | offline_deep_analysis.py                                              |
| `total_unique_ph_ids` | `unique_ph_id_count`     | create_corrected_analysis.py                                          |

### **實施範圍**

- **修改腳本**: 3 個分析腳本 + 1 個網頁生成腳本
- **標準化檔案**: 所有核心 JSON 輸出檔案
- **驗證結果**: 網頁顯示正常，數據一致性良好

### **標準結構範例**

```json
{
  "metadata": {
    "version": "1.0.0",
    "generated_at": "2025-08-21T10:30:00Z",
    "analysis_type": "data_distribution"
  },
  "basic_statistics": {
    "total_record_count": 15242556,
    "unique_ph_id_count": 2984689,
    "null_ph_id_percentage": 4.756,
    "execution_time_seconds": 2.308496
  }
}
```

---

_計畫制定日期：2025-08-20_
_JSON 標準化完成：2025-08-21_
_預計實施時間：1-2 小時_
