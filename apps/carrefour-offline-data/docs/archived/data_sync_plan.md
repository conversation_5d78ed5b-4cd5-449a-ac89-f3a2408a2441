# 家樂福離線資料同步執行計劃

## 📊 資料分析結果

### 來源表格 (tw-eagle-prod.rmn_tagtoo.offline_transaction_day)

- **記錄數**: 15,855,981 筆
- **唯一用戶數**: 3,039,606 個
- **唯一交易數**: 15,855,981 筆
- **總交易金額**: NT$ 10,609,344,298
- **大小**: 19.90 GB
- **狀態**: 最新資料

### 目標表格 (tagtoo-tracking.event_prod.carrefour_offline_transaction_day)

- **記錄數**: 15,242,556 筆
- **唯一用戶數**: 2,984,689 個
- **唯一交易數**: 15,242,556 筆
- **總交易金額**: NT$ 9,786,714,080
- **大小**: 18.63 GB
- **狀態**: 需要同步

### 📈 詳細資料差異分析

- **記錄差異**: 613,425 筆 (4.02% 增加)
- **用戶差異**: 54,917 個 (1.84% 增加)
- **交易差異**: 613,425 筆 (4.02% 增加)
- **金額差異**: NT$ 822,630,218 (8.41% 增加)
- **預估同步資料大小**: 0.77 GB
- **同步策略**: 增量複製

### 🎯 同步後預期狀況

- **目標表格記錄數**: 15,855,981 筆 (100% 與來源一致)
- **目標表格用戶數**: 3,039,606 個 (100% 與來源一致)
- **目標表格交易數**: 15,855,981 筆 (100% 與來源一致)
- **目標表格總金額**: NT$ 10,609,344,298 (100% 與來源一致)
- **資料完整性**: 完全同步，無遺失

## 🔄 執行策略

### 階段 1: 資料差異分析

```sql
-- 查詢來源表格中不存在於目標表格的記錄
WITH source_data AS (
  SELECT transaction_id, event_name, store_id, ph_id
  FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`
),
target_data AS (
  SELECT transaction_id, event_name, store_id, ph_id
  FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
)
SELECT COUNT(*) as missing_records
FROM source_data s
LEFT JOIN target_data t ON s.transaction_id = t.transaction_id
WHERE t.transaction_id IS NULL;
```

### 階段 2: 增量資料複製

```sql
-- 將差異資料插入目標表格
INSERT INTO `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
SELECT
  event_name,
  event_times,
  store_id,
  store_name,
  gender,
  member_id,
  ph_id,
  transaction_id,
  items,
  total_amount,
  payment_method
FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` source
WHERE NOT EXISTS (
  SELECT 1
  FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` target
  WHERE target.transaction_id = source.transaction_id
);
```

### 階段 3: 資料驗證

```sql
-- 驗證同步後的記錄數量
SELECT
  'source' as table_type,
  COUNT(*) as record_count
FROM `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`

UNION ALL

SELECT
  'target' as table_type,
  COUNT(*) as record_count
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`;
```

## 💰 成本估算

### 查詢成本分析

- **差異分析查詢**: 約 $0.50 USD (掃描兩個完整表格)
- **增量複製查詢**: 約 $0.30 USD (插入 613,425 筆記錄)
- **驗證查詢**: 約 $0.20 USD (計數查詢)
- **總預估成本**: 約 $1.00 USD

### 儲存成本

- **新增儲存**: 1.27 GB
- **月儲存成本**: 約 $0.03 USD

## ⚠️ 風險評估

### 潛在風險

1. **資料重複**: 如果 transaction_id 不是唯一鍵
2. **權限問題**: 跨專案資料存取權限
3. **查詢超時**: 大量資料處理可能超時
4. **成本超支**: 查詢成本可能超出預期

### 緩解措施

1. **分批處理**: 將大查詢分成多個小批次
2. **權限驗證**: 執行前確認所有必要權限
3. **成本監控**: 使用 dry-run 預估實際成本
4. **備份策略**: 執行前備份目標表格

## 📋 執行檢查清單

### 執行前準備

- [ ] 確認 Google Cloud 認證和權限
- [ ] 驗證來源和目標表格存取權限
- [ ] 執行 dry-run 查詢估算成本
- [ ] 備份目標表格 (如需要)

### 執行步驟

- [ ] 執行資料差異分析查詢
- [ ] 確認差異記錄數量合理
- [ ] 執行增量資料複製
- [ ] 監控複製進度和狀態
- [ ] 執行資料驗證查詢

### 執行後驗證

- [ ] 確認記錄數量一致
- [ ] 抽樣驗證資料品質
- [ ] 檢查複製過程中的錯誤日誌
- [ ] 更新文檔和執行記錄

## 🚀 執行指令

### 使用現有工具執行

```bash
cd apps/carrefour-offline-data
source venv/bin/activate

# 執行資料同步
python src/tools/copy_to_target_dataset.py --mode incremental --dry-run

# 確認無誤後執行實際同步
python src/tools/copy_to_target_dataset.py --mode incremental
```

### 手動 BigQuery 執行

```bash
# 使用 bq 命令行工具
bq query --use_legacy_sql=false --dry_run < sync_query.sql
bq query --use_legacy_sql=false < sync_query.sql
```

## 📊 預期結果

### 成功指標

- 目標表格記錄數達到 15,855,981 筆
- 資料品質檢查 100% 通過
- 無重複或遺失記錄
- 查詢成本控制在 $1.50 USD 以內

### 完成時間

- **預估執行時間**: 15-30 分鐘
- **包含驗證時間**: 45 分鐘
- **建議執行時段**: 非尖峰時間

## 📱 Slack 格式報告

以下是適合複製貼上到 Slack 的差異分析報告：

```markdown
🏪 **家樂福離線資料同步狀況報告**

📊 **當前資料差異**：
• 來源表格：15,855,981 筆記錄，3,039,606 個用戶
• 目標表格：15,242,556 筆記錄，2,984,689 個用戶
• **需要同步：613,425 筆記錄** (4.0% 增加)

💰 **交易金額統計**：
• 來源總金額：NT$ 10,609,344,298
• 目標總金額：NT$ 9,786,714,080
• 差異金額：NT$ 822,630,218

🎯 **同步計劃**：
• 預估同步時間：15-30 分鐘
• 預估查詢成本：$0.80-1.20 USD
• 同步後資料完整性：100% 與來源一致

📋 **下一步驟**：

1. 執行增量資料複製
2. 驗證資料完整性
3. 更新受眾媒合系統配置

_報告生成時間：2025-08-26 16:48:29_
```

---

**注意**: 此計劃基於當前資料分析結果，實際執行時請先進行 dry-run 驗證。
