# 家樂福 BigQuery 資料複製策略

## 📋 專案概述

**目標**：將家樂福離線事件資料從 `tw-eagle-prod.rmn_tagtoo.offline_transaction_day` 完整複製到 `tagtoo-tracking.event_prod.carrefour_offline_data`

**資料規模**：

- 總行數：15,242,556 筆
- 總大小：18.63 GB
- 欄位數量：11 個（包含複雜的 STRUCT 結構）

## ✅ 已完成項目

### 1. 目標表格建立

- ✅ 成功建立 `tagtoo-tracking.event_prod.carrefour_offline_data`
- ✅ 完整複製來源表格的 schema 結構
- ✅ 包含所有欄位定義、資料類型和巢狀結構

### 2. Schema 分析

**主要欄位**：

- `event_name` (STRING) - 事件名稱
- `event_times` (INTEGER) - 事件次數
- `store_id` (STRING) - 店舖 ID
- `store_name` (STRING) - 店舖名稱
- `gender` (STRING) - 性別
- `member_id` (BYTES) - 會員 ID (加密)
- `ph_id` (BYTES) - 電話 ID (加密)
- `transaction_id` (STRING) - 交易 ID
- `items` (ARRAY<STRUCT>) - 商品項目 (複雜結構)
- `total_amount` (NUMERIC) - 總金額
- `payment_method` (STRING) - 付款方式

**複雜結構 - items 欄位**：

```sql
ARRAY<STRUCT<
  item_id STRING,
  item_name STRING,
  GRP_CLASS_KEY STRING,
  GRP_CLASS_DESC STRING,
  CLASS_KEY STRING,
  CLASS_DESC STRING,
  SUB_CLASS_KEY STRING,
  SUB_CLASS_DESC STRING,
  quantity NUMERIC,
  unit_price NUMERIC,
  subtotal NUMERIC
>>
```

## 🔒 當前權限限制分析

### 我們擁有的權限

- ✅ `bigquery.datasets.get` - 可以讀取資料集資訊
- ✅ `bigquery.tables.get` - 可以讀取表格 metadata 和 schema

### 缺少的權限

- ❌ `bigquery.jobs.create` - 無法建立查詢 jobs
- ❌ `bigquery.readsessions.create` - 無法使用 Storage Read API

### 權限限制的影響

**無法執行的操作**：

1. 直接 SQL 查詢複製：`INSERT INTO ... SELECT FROM ...`
2. BigQuery Storage Read API 直接讀取
3. BigQuery Data Transfer Service
4. 任何需要建立 jobs 的操作

## 🎯 資料複製策略

### 策略 A：申請最小查詢權限 ⭐ **推薦**

**所需權限**：

```bash
gcloud projects add-iam-policy-binding tw-eagle-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

**實作方案**：

1. **分批複製**：避免單次查詢超過成本限制
2. **增量複製**：支援後續資料更新
3. **錯誤處理**：完整的重試和監控機制

### 策略 B：請對方協助匯出

**實作方案**：

1. 請對方將資料匯出到 Cloud Storage
2. 我們從 Cloud Storage 載入到目標表格
3. 定期同步更新

### 策略 C：第三方工具

**可能的工具**：

- Dataflow
- Cloud Composer (Airflow)
- 第三方 ETL 工具

## 💰 成本分析

### BigQuery 查詢成本估算

**基於 18.63 GB 資料量**：

- 查詢成本：約 $0.093 USD (18.63 GB × $5/TB)
- 儲存成本：約 $0.37 USD/月 (18.63 GB × $0.02/GB/月)
- **總計**：遠低於規格文件的 $10 USD 限制

### 網路傳輸成本

- 跨專案複製：免費（同區域內）
- 跨區域複製：需評估區域設定

## 🚀 實作計畫

### 階段 1：權限申請 (1-2 天)

1. 準備權限申請文件
2. 聯繫家樂福技術團隊
3. 說明最小權限需求和安全保證

### 階段 2：資料複製實作 (1 天)

1. 開發分批複製腳本
2. 實作成本監控機制
3. 建立錯誤處理和重試邏輯

### 階段 3：驗證和測試 (1 天)

1. 驗證資料完整性
2. 比對行數和資料樣本
3. 執行完整的資料驗證流程

## 📊 預期效益

### 完成後的優勢

1. **完全自主控制**：不受對方權限限制
2. **成本可控**：使用我們自己的 BigQuery 配額
3. **驗證完整性**：可執行所有規格要求的驗證
4. **後續維護**：建立定期同步機制

### 風險緩解

1. **資料安全**：在我們的環境中處理敏感資料
2. **成本控制**：預設查詢限制和監控
3. **合規性**：確保符合資料保護要求

## 📝 下一步行動

### 立即執行

1. ✅ 目標表格已建立
2. 🔄 準備權限申請文件
3. 🔄 開發資料複製腳本框架

### 等待權限後執行

1. 執行資料複製
2. 驗證資料完整性
3. 建立定期同步機制
4. 執行完整的資料驗證流程

---

**更新時間**：2025-08-15
**狀態**：目標表格已建立，等待權限申請結果
