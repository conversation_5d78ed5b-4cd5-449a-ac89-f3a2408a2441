# BigQuery 權限申請文件

## 📋 申請概述

**申請目的**：為了完成家樂福離線事件資料驗證和複製任務
**申請日期**：2025-08-15
**申請人**：Tagtoo 技術團隊

## 🎯 業務需求

根據雙方合作協議，我們需要對家樂福提供的離線事件資料進行完整驗證，包括：

1. **資料內容驗證**：確認資料時間範圍符合 D-62 ~ D-3 規格要求
2. **資料品質分析**：檢查資料完整性、一致性和準確性
3. **資料量統計**：分析每日資料分布和趨勢
4. **資料複製**：將驗證通過的資料複製到我們的分析環境

## 🔐 當前權限狀況

### ✅ 已有權限

- **BigQuery Data Viewer**：可以讀取表格結構和 metadata
- **表格存取**：可以存取 `tw-eagle-prod.rmn_tagtoo.offline_transaction_day`

### ❌ 缺少權限

- **BigQuery Job User**：無法建立查詢 jobs 來檢視資料內容

## 📝 權限申請詳情

### 申請的 Service Account

```
<EMAIL>
```

### 申請的權限角色

```
roles/bigquery.jobUser
```

### 具體權限內容

- `bigquery.jobs.create` - 建立查詢 jobs
- `bigquery.jobs.get` - 獲取 job 狀態
- `bigquery.jobs.list` - 列出 jobs（可選）

## 🛡️ 安全保證

### 1. 最小權限原則

- **僅申請必要權限**：只申請 Job User，不申請資料修改權限
- **唯讀操作**：所有查詢都是 SELECT 操作，不會修改或刪除資料
- **專案層級限制**：權限僅限於 `tw-eagle-prod` 專案

### 2. 成本控制機制

- **查詢成本限制**：單次查詢不超過 $10 USD
- **自動成本估算**：每次查詢前都會進行 dry run 成本估算
- **批次處理**：大型查詢會分批執行，避免高額費用

### 3. 操作透明度

- **查詢日誌**：所有查詢都會記錄在 BigQuery 日誌中
- **操作通知**：重要操作會提前通知貴方技術團隊
- **定期報告**：提供查詢使用情況和成本報告

### 4. 時間限制

- **臨時權限**：建議設定權限有效期（如 30 天）
- **任務完成後移除**：驗證和複製完成後可立即移除權限

## 💰 成本影響分析

### 預估查詢成本

**基於當前資料量（18.63 GB）**：

- 完整資料掃描：約 $0.093 USD
- 驗證查詢（10-15 次）：約 $0.50 USD
- 資料複製（1 次）：約 $0.093 USD
- **總預估成本**：< $1 USD

### 成本控制措施

1. **Dry Run 驗證**：每次查詢前估算成本
2. **分批處理**：大型操作分批執行
3. **成本監控**：設定成本警報和限制
4. **定期報告**：提供詳細的成本使用報告

## 🚀 實施計畫

### 階段 1：權限授予（預計 1 天）

```bash
gcloud projects add-iam-policy-binding tw-eagle-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

### 階段 2：驗證測試（預計 1 天）

1. 執行基本查詢測試
2. 驗證成本控制機制
3. 確認所有功能正常

### 階段 3：完整驗證（預計 2 天）

1. 執行完整的資料內容驗證
2. 進行資料品質分析
3. 生成完整的驗證報告

### 階段 4：資料複製（預計 1 天）

1. 將驗證通過的資料複製到我們的環境
2. 驗證複製結果的完整性
3. 建立後續同步機制

## 📊 預期效益

### 對家樂福的效益

1. **資料品質保證**：完整的資料驗證確保資料品質
2. **合規性確認**：驗證資料符合合作協議要求
3. **技術支援**：提供詳細的資料分析報告

### 對 Tagtoo 的效益

1. **完整驗證**：能夠執行所有規格要求的驗證項目
2. **自主分析**：在我們的環境中進行深度分析
3. **後續維護**：建立穩定的資料同步機制

## 🔄 權限移除計畫

### 自動移除條件

1. **任務完成**：所有驗證和複製任務完成後
2. **時間到期**：權限有效期到期後
3. **合作結束**：合作協議結束後

### 手動移除指令

```bash
gcloud projects remove-iam-policy-binding tw-eagle-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

## 📞 聯繫資訊

**技術聯繫人**：Tagtoo 技術團隊
**緊急聯繫**：如有任何安全疑慮，請立即聯繫我們
**監控報告**：每週提供權限使用情況報告

## 📋 檢查清單

### 申請前確認

- [x] Service Account 已建立
- [x] 目標表格已建立
- [x] 成本控制機制已實作
- [x] 安全措施已規劃

### 權限授予後確認

- [ ] 權限測試通過
- [ ] 成本控制正常
- [ ] 查詢功能正常
- [ ] 日誌記錄正常

### 任務完成後確認

- [ ] 所有驗證完成
- [ ] 資料複製完成
- [ ] 報告已提交
- [ ] 權限已移除（如需要）

---

**文件版本**：1.0
**最後更新**：2025-08-15
**審核狀態**：待審核
