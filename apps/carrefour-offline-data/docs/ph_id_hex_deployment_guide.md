# ph_id_hex 功能部署和驗證指南

> 版本: v8.1.0
> 更新日期: 2025-09-15
> 狀態: 🔄 準備部署

## 📋 部署概述

本指南說明如何將 ph_id_hex 輔助欄位功能部署到生產環境，並進行完整的驗證流程。

## 🚀 部署前檢查清單

### ✅ 程式碼準備

- [x] `automated_daily_replication.py` 已實作 ph_id_hex 功能
- [x] `automated_monthly_merge.py` 已實作 ph_id_hex 功能
- [x] 單元測試全部通過
- [x] 語法檢查通過
- [x] 文檔更新完成

### ✅ 環境準備

- [ ] 確認 Cloud Function 部署權限
- [ ] 確認 BigQuery 資料集存取權限
- [ ] 確認 Cloud Scheduler 觸發權限
- [ ] 備份當前 Cloud Function 版本

### ✅ 監控準備

- [ ] 確認 Cloud Logging 設定
- [ ] 確認 Cloud Monitoring 警報設定
- [ ] 準備部署後驗證查詢

## 📦 部署步驟

### 步驟 1: 備份當前版本

```bash
# 1. 備份當前 Cloud Function 程式碼
gcloud functions describe carrefour-data-replication-prod \
    --region=asia-east1 \
    --format="export" > backup_function_$(date +%Y%m%d_%H%M%S).yaml

# 2. 記錄當前版本資訊
gcloud functions describe carrefour-data-replication-prod \
    --region=asia-east1 \
    --format="value(versionId,updateTime)"
```

### 步驟 2: 部署新版本

```bash
# 在專案根目錄執行
cd /path/to/integrated-event/apps/carrefour-offline-data

# 確認在正確的 GCP 專案
gcloud config get-value project
# 應該顯示: tagtoo-tracking

# 部署 Cloud Function
gcloud functions deploy carrefour-data-replication-prod \
    --gen2 \
    --runtime=python311 \
    --region=asia-east1 \
    --source=. \
    --entry-point=main \
    --memory=2Gi \
    --timeout=540s \
    --max-instances=1 \
    --service-account=<EMAIL> \
    --set-env-vars=ENVIRONMENT=production
```

### 步驟 3: 驗證部署

```bash
# 檢查 Cloud Function 狀態
gcloud functions describe carrefour-data-replication-prod \
    --region=asia-east1 \
    --format="table(name,status,updateTime)"

# 檢查 Cloud Function 日誌
gcloud functions logs read carrefour-data-replication-prod \
    --region=asia-east1 \
    --limit=10
```

## 🧪 驗證流程

### 階段 1: Dry-Run 驗證

#### 1.1 手動觸發 Daily Replication (Dry-Run)

```bash
# 觸發日常複製 (dry-run 模式)
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth print-access-token)" \
    -H "Content-Type: application/json" \
    -d '{"operation_type": "daily_replication", "dry_run": true}' \
    https://carrefour-data-replication-prod-[HASH]-uc.a.run.app
```

**預期結果**:

- HTTP 200 回應
- 日誌顯示 "DRY RUN 模式"
- 沒有實際資料修改
- ph_id_hex 處理邏輯被跳過（dry-run 模式）

#### 1.2 手動觸發 Monthly Merge (Dry-Run)

```bash
# 觸發月度合併 (dry-run 模式)
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth print-access-token)" \
    -H "Content-Type: application/json" \
    -d '{"operation_type": "monthly_merge", "dry_run": true}' \
    https://carrefour-data-replication-prod-[HASH]-uc.a.run.app
```

**預期結果**:

- HTTP 200 回應
- 日誌顯示合併分析結果
- 沒有實際資料修改
- ph_id_hex 處理在 dry-run 模式被跳過

### 階段 2: 生產環境小規模驗證

#### 2.1 手動觸發單一表格複製

```bash
# 觸發日常複製 (生產模式，僅處理一個表格)
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth print-access-token)" \
    -H "Content-Type: application/json" \
    -d '{"operation_type": "daily_replication", "dry_run": false, "table_name": "offline_transaction_day"}' \
    https://carrefour-data-replication-prod-[HASH]-uc.a.run.app
```

#### 2.2 驗證 ph_id_hex 欄位

```sql
-- 檢查 ph_id_hex 欄位是否存在
SELECT
    column_name,
    data_type,
    is_nullable
FROM `tagtoo-tracking.event_prod.INFORMATION_SCHEMA.COLUMNS`
WHERE table_name = 'carrefour_offline_transaction_day'
    AND column_name = 'ph_id_hex';

-- 預期結果: 應該返回一行，data_type = 'STRING'
```

#### 2.3 驗證 ph_id_hex 內容正確性

```sql
-- 驗證 ph_id_hex 與 TO_HEX(ph_id) 的一致性
SELECT
    COUNT(*) as total_records,
    COUNT(ph_id_hex) as ph_id_hex_count,
    SUM(CASE WHEN ph_id_hex = TO_HEX(ph_id) THEN 1 ELSE 0 END) as consistent_count,
    SUM(CASE WHEN ph_id_hex = TO_HEX(ph_id) THEN 1 ELSE 0 END) / COUNT(*) as consistency_rate
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE DATE(TIMESTAMP_SECONDS(event_times)) = CURRENT_DATE();

-- 預期結果: consistency_rate = 1.0 (100% 一致)
```

#### 2.4 驗證查詢效能

```sql
-- 測試使用 ph_id_hex 的查詢效能
SELECT
    ph_id_hex,
    COUNT(*) as transaction_count
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE ph_id_hex IN ('ABC123', 'DEF456', 'GHI789')  -- 使用實際存在的值
GROUP BY ph_id_hex;

-- 比較原有查詢方式
SELECT
    TO_HEX(ph_id) as ph_id_hex,
    COUNT(*) as transaction_count
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE TO_HEX(ph_id) IN ('ABC123', 'DEF456', 'GHI789')  -- 使用實際存在的值
GROUP BY TO_HEX(ph_id);
```

### 階段 3: 完整生產驗證

#### 3.1 等待自動排程執行

- 等待下一次自動排程觸發（每日 10:30）
- 監控 Cloud Function 執行日誌
- 確認 ph_id_hex 處理成功

#### 3.2 驗證所有表格

```sql
-- 檢查所有相關表格的 ph_id_hex 欄位
SELECT
    table_name,
    COUNT(*) as column_count
FROM `tagtoo-tracking.event_prod.INFORMATION_SCHEMA.COLUMNS`
WHERE table_name IN (
    'carrefour_offline_transaction_day',
    'carrefour_offline_transaction_month',
    'carrefour_offline_transaction'
) AND column_name = 'ph_id_hex'
GROUP BY table_name;

-- 預期結果: 每個表格都應該有 ph_id_hex 欄位
```

## 📊 監控和警報

### 關鍵監控指標

#### 1. Cloud Function 執行狀態

```bash
# 檢查最近的執行狀態
gcloud functions logs read carrefour-data-replication-prod \
    --region=asia-east1 \
    --filter="severity>=WARNING" \
    --limit=50
```

#### 2. BigQuery 查詢成本

```sql
-- 監控 ph_id_hex 相關查詢的成本
SELECT
    job_id,
    creation_time,
    total_bytes_processed,
    total_bytes_processed / POW(1024, 4) * 5 as estimated_cost_usd
FROM `tagtoo-tracking.region-asia-east1.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
    AND query LIKE '%ph_id_hex%'
ORDER BY creation_time DESC;
```

#### 3. 資料一致性監控

```sql
-- 每日資料一致性檢查
SELECT
    DATE(TIMESTAMP_SECONDS(event_times)) as date,
    COUNT(*) as total_records,
    SUM(CASE WHEN ph_id_hex = TO_HEX(ph_id) THEN 1 ELSE 0 END) as consistent_records,
    SUM(CASE WHEN ph_id_hex = TO_HEX(ph_id) THEN 1 ELSE 0 END) / COUNT(*) as consistency_rate
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`
WHERE DATE(TIMESTAMP_SECONDS(event_times)) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
GROUP BY date
ORDER BY date DESC;
```

### 警報設定建議

#### 1. ph_id_hex 處理失敗警報

- **條件**: Cloud Function 日誌包含 "ph_id_hex 輔助欄位處理失敗"
- **頻率**: 立即通知
- **收件人**: <EMAIL>

#### 2. 資料一致性警報

- **條件**: consistency_rate < 0.99 (低於 99%)
- **頻率**: 每日檢查
- **收件人**: <EMAIL>

#### 3. 成本異常警報

- **條件**: ph_id_hex 相關查詢成本超過 $5.00 USD/天
- **頻率**: 每日檢查
- **收件人**: <EMAIL>

## 🔧 故障排除

### 常見問題和解決方案

#### 問題 1: Cloud Function 部署失敗

**症狀**: 部署過程中出現錯誤
**解決方案**:

1. 檢查 IAM 權限
2. 確認 requirements.txt 正確
3. 檢查程式碼語法錯誤
4. 回滾到備份版本

#### 問題 2: ph_id_hex 欄位未建立

**症狀**: 查詢時找不到 ph_id_hex 欄位
**解決方案**:

1. 檢查 Cloud Function 執行日誌
2. 手動執行 ALTER TABLE 語句
3. 重新觸發複製任務

#### 問題 3: ph_id_hex 內容不正確

**症狀**: ph_id_hex ≠ TO_HEX(ph_id)
**解決方案**:

1. 立即停止自動排程
2. 調查資料不一致原因
3. 手動修復資料
4. 重新啟動排程

### 回滾程序

如果需要回滾到舊版本：

```bash
# 1. 部署備份版本
gcloud functions deploy carrefour-data-replication-prod \
    --gen2 \
    --runtime=python311 \
    --region=asia-east1 \
    --source=backup_version/ \
    --entry-point=main \
    --memory=2Gi \
    --timeout=540s \
    --max-instances=1 \
    --service-account=<EMAIL>

# 2. 驗證回滾成功
gcloud functions describe carrefour-data-replication-prod \
    --region=asia-east1 \
    --format="value(versionId,updateTime)"

# 3. 清理 ph_id_hex 欄位（如果需要）
# 注意: 這會刪除欄位，請謹慎操作
# ALTER TABLE `table_name` DROP COLUMN ph_id_hex;
```

## ✅ 部署完成檢查清單

### 部署後驗證

- [ ] Cloud Function 部署成功
- [ ] Dry-run 測試通過
- [ ] 小規模生產測試通過
- [ ] ph_id_hex 欄位正確建立
- [ ] 資料一致性驗證通過
- [ ] 查詢效能測試通過
- [ ] 監控和警報設定完成

### 文檔更新

- [ ] README.md 版本號更新
- [ ] 功能說明文檔完成
- [ ] 部署指南完成
- [ ] 團隊成員通知完成

### 後續追蹤

- [ ] 第一週每日監控
- [ ] 第一個月週報追蹤
- [ ] 使用者回饋收集
- [ ] 效能指標分析

## 📞 支援聯絡

**部署負責人**: Frank Zheng
**聯絡方式**: <EMAIL>
**緊急聯絡**: 透過 Google Cloud Monitoring 警報系統
**技術支援**: Data Engineering Team
