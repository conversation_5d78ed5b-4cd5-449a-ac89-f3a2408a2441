# 家樂福 ph_id 直接處理功能

> 直接使用 ph_id 上傳到 Meta，避免透過 tagtoo_entity 媒合過程中的使用者流失

## 📋 功能概述

此功能實作了一個新的平行流程，能夠直接從家樂福離線交易資料中提取 ph_id 和商品分類標籤，生成符合 Meta 要求的 AVRO 檔案，並上傳到指定的 Cloud Storage 位置。

### 🎯 核心目標

1. **避免使用者流失**：直接使用 ph_id，不經過 `ph_id → tagtoo_entity.mobile → permanent` 的轉換
2. **保持資料精確度**：維持原始的使用者識別準確性
3. **平行處理**：不影響現有的 tagtoo_entity.phone 媒合流程
4. **相容現有架構**：重用現有的基礎設施和監控機制

## 🏗️ 系統架構

### 整體流程圖

```
每日 11:00 AM (新增)
    ↓
ph_id 直接處理流程
    ↓
1. 從 carrefour_offline_transaction_day 提取 ph_id + items
    ↓
2. 生成去重化的 ph_id 與 segment_id 對應關係
    ↓
3. 建立暫存對應表 (用於 debug)
    ↓
4. 生成 Meta AVRO 檔案
    ↓
5. 上傳到 GCS: gs://tagtoo-ml-workflow/carrefour_ph_id_direct/
    ↓
6. 觸發 Pub/Sub 通知
```

### 與現有流程的關係

- **資料複製流程** (10:30 AM): 複製 day 表資料到目標專案
- **現有流程** (02:00 AM): tagtoo_entity 媒合 → permanent + segment_id
- **新增流程** (11:00 AM): 直接處理 → ph_id + segment_id (在 day 表複製完成後執行)
- **資料隔離**: 使用不同的暫存表格和 GCS 路徑，不影響現有 special_lta 表格
- **監控整合**: 共享相同的監控和告警機制

## 📊 資料格式

### 輸入資料

**來源表格**: `tagtoo-tracking.event_prod.carrefour_offline_transaction_day`

```sql
SELECT
  ph_id,           -- BYTES: 32 bytes SHA256 雜湊
  items,           -- ARRAY: 商品資訊陣列
  event_times      -- INTEGER: 事件時間戳
FROM carrefour_offline_transaction_day
WHERE DATE(TIMESTAMP_SECONDS(event_times)) = '2025-09-11'
```

### 輸出格式

**AVRO 檔案結構**:

```json
{
  "group_id": null,
  "emails": [],
  "phones": [
    "68eaa7034f7cc4ba4101668d640eceb7feb7a7b395b6b21e967327e8322854ae"
  ],
  "fb_info": [{ "fbp_fbc_ip": ["", "", ""] }],
  "segment_id": ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"]
}
```

### 暫存對應表

**表格**: `tagtoo-tracking.event_prod.carrefour_ph_id_segment_mapping_{YYYYMMDD}`

```sql
CREATE TABLE carrefour_ph_id_segment_mapping_20250911 (
  ph_id_hex STRING,           -- TO_HEX(ph_id) 格式
  segment_ids ARRAY<STRING>,  -- 去重化的商品分類標籤陣列
  segment_count INTEGER,      -- 標籤數量
  created_at TIMESTAMP,       -- 建立時間
  execution_id STRING         -- 執行 ID
)
PARTITION BY DATE(created_at)
CLUSTER BY ph_id_hex;
```

## 🔧 技術實作

### 核心模組

1. **PhIdDirectProcessor** (`src/analysis/ph_id_direct_processor.py`)

   - 主要處理邏輯
   - 資料提取和轉換
   - AVRO 檔案生成

2. **GCSUploader** (`src/tools/gcs_uploader.py`)

   - Cloud Storage 上傳
   - 檔案管理和元數據

3. **PubSubNotifier** (`src/tools/pubsub_notifier.py`)
   - 事件通知
   - 下游流程觸發

### 配置管理

**配置檔案**: `config/ph_id_direct_config.json`

```json
{
  "environments": {
    "prod": {
      "source_table": "tagtoo-tracking.event_prod.carrefour_offline_transaction_day",
      "target_dataset": "tagtoo-tracking.event_prod",
      "gcs_bucket": "tagtoo-ml-workflow",
      "gcs_path_prefix": "carrefour_ph_id_direct",
      "pubsub_topic": "projects/tagtoo-ml-workflow/topics/carrefour-ph-id-processing",
      "max_cost_usd": 50.0
    }
  }
}
```

## 🚀 部署指南

### 前置需求

1. **權限設定**:

   - BigQuery 讀取權限 (`tagtoo-tracking.event_prod`)
   - Cloud Storage 寫入權限 (`tagtoo-ml-workflow`)
   - Pub/Sub 發布權限

2. **服務帳號**:
   - `<EMAIL>`
   - `<EMAIL>`

### 部署步驟

1. **執行部署腳本**:

   ```bash
   # 測試部署
   ./scripts/deploy_ph_id_direct.sh dev --dry-run

   # 生產部署
   ./scripts/deploy_ph_id_direct.sh prod
   ```

2. **驗證部署**:

   ```bash
   # 手動測試
   python3 -m src.analysis.ph_id_direct_processor \
     --target-date "2025-09-11" \
     --dry-run
   ```

3. **監控設定**:
   - Cloud Function 日誌
   - Cloud Scheduler 狀態
   - BigQuery 查詢成本
   - GCS 檔案上傳狀態

## 📈 監控和告警

### 關鍵指標

1. **執行成功率**: 每日排程執行成功率
2. **資料量**: 處理的 ph_id 數量
3. **執行時間**: 完整流程執行時間
4. **成本控制**: BigQuery 查詢成本
5. **檔案大小**: 生成的 AVRO 檔案大小

### 告警設定

- 執行失敗告警
- 成本超標告警 (> $50 USD)
- 執行時間過長告警 (> 15 分鐘)
- 資料量異常告警 (< 100 筆記錄)

## 🧪 測試策略

### 單元測試

```bash
# 執行單元測試
python3 -m pytest tests/test_ph_id_direct.py -v
```

### 整合測試

```bash
# 執行整合測試 (dry run)
python3 -m src.analysis.ph_id_direct_processor \
  --target-date "2025-09-11" \
  --dry-run \
  --config "config/ph_id_direct_config.json"
```

### 效能測試

- 測試不同資料量的處理時間
- 驗證 BigQuery 查詢成本
- 檢查 GCS 上傳速度

## 🔒 安全考量

1. **資料加密**: ph_id 已經是 SHA256 雜湊，無需額外加密
2. **存取控制**: 使用 IAM 控制資源存取權限
3. **稽核日誌**: 所有操作都有完整的日誌記錄
4. **資料保留**: 暫存表格 7 天後自動過期

## 📚 API 參考

### PhIdDirectProcessor

```python
from src.analysis.ph_id_direct_processor import PhIdDirectProcessor

# 初始化
processor = PhIdDirectProcessor(config_path="config/ph_id_direct_config.json")

# 執行完整流程
result = processor.run_complete_pipeline(
    target_date="2025-09-11",
    dry_run=True
)
```

### 主要方法

- `extract_ph_id_segments()`: 提取 ph_id 和商品分類
- `execute_avro_export()`: 執行 AVRO 檔案匯出
- `create_temp_mapping_table()`: 建立暫存對應表
- `publish_completion_notification()`: 發送完成通知

## 🐛 故障排除

### 常見問題

1. **權限錯誤**:

   ```
   解決方案: 檢查服務帳號權限設定
   ```

2. **成本超標**:

   ```
   解決方案: 調整 max_cost_usd 設定或優化查詢
   ```

3. **檔案上傳失敗**:
   ```
   解決方案: 檢查 GCS bucket 權限和網路連線
   ```

### 日誌查看

```bash
# Cloud Function 日誌
gcloud functions logs read carrefour-offline-data-prod --region=asia-east1

# Cloud Scheduler 日誌
gcloud scheduler jobs describe carrefour-ph-id-direct-prod
```

## 📝 變更記錄

### v1.0.0 (2025-09-12)

- 初始版本發布
- 實作核心 ph_id 直接處理功能
- 整合 Cloud Storage 和 Pub/Sub
- 建立監控和告警機制

---

**維護者**: AI Assistant
**最後更新**: 2025-09-12
**版本**: 1.0.0
