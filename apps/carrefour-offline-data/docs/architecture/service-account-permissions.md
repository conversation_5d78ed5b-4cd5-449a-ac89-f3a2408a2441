# Service Account 權限配置文檔

## 📊 概覽

家樂福離線資料系統使用統一的共享基礎設施 Service Account，實現權限集中管理和最佳安全實務。

**架構分離設計**：

- **基礎設施層**: `<EMAIL>`
- **專案執行層**: `<EMAIL>`

## 🏗️ 架構設計

### 權限架構選擇

經過詳細分析，我們採用 **架構分離設計**，結合基礎設施層和專案執行層的優勢：

| 評估項目       | 專案特定 SA   | 共享基礎設施 SA | 選擇 |
| -------------- | ------------- | --------------- | ---- |
| **管理複雜度** | 🟡 中等       | 🟢 低           | ✅   |
| **安全性隔離** | 🟢 高         | 🟡 中等         | -    |
| **權限控制**   | 🟢 精細       | 🟡 粗粒度       | -    |
| **可維護性**   | 🟡 分散管理   | 🟢 集中管理     | ✅   |
| **擴展性**     | 🔴 每專案建立 | 🟢 統一管理     | ✅   |
| **成本**       | 🟡 多個 SA    | 🟢 單一 SA      | ✅   |

**選擇理由**：

- **基礎設施層統一管理**：所有觸發和部署使用共享 Service Account
- **專案執行層隔離**：每個客戶專案使用專屬 Service Account，確保資料隔離
- **安全性最佳化**：符合多客戶合作場景的安全隔離需求
- **可擴展性強**：新客戶專案可快速建立專屬 Service Account

## 🔐 詳細權限配置

### 1. tagtoo-tracking 專案權限

| 角色                            | 權限範圍            | 用途                         | 來源           |
| ------------------------------- | ------------------- | ---------------------------- | -------------- |
| `roles/bigquery.dataEditor`     | BigQuery 資料編輯   | 讀取 integrated_event 資料表 | 共享基礎設施   |
| `roles/bigquery.jobUser`        | BigQuery Job 執行   | 執行查詢和資料處理           | 共享基礎設施   |
| `roles/cloudfunctions.invoker`  | Cloud Function 觸發 | Cloud Scheduler 觸發權限     | Terraform 管理 |
| `roles/run.invoker`             | Cloud Run 服務觸發  | Cloud Function Gen2 底層權限 | Terraform 管理 |
| `roles/cloudtasks.enqueuer`     | Cloud Tasks 佇列    | 任務排程和執行               | 共享基礎設施   |
| `roles/datastore.user`          | Datastore 存取      | 資料儲存和檢索               | 共享基礎設施   |
| `roles/firestore.serviceAgent`  | Firestore 服務代理  | 文檔資料庫操作               | 共享基礎設施   |
| `roles/logging.logWriter`       | 日誌寫入            | 應用程式日誌記錄             | 共享基礎設施   |
| `roles/monitoring.metricWriter` | 監控指標寫入        | 自定義監控指標               | 共享基礎設施   |

### 2. tagtoo-ml-workflow 專案權限

| 角色                         | 權限範圍            | 用途             | 來源         |
| ---------------------------- | ------------------- | ---------------- | ------------ |
| `roles/bigquery.admin`       | BigQuery 完整管理   | 寫入受眾媒合結果 | 共享基礎設施 |
| `roles/bigquery.jobUser`     | BigQuery Job 執行   | 跨專案查詢執行   | 共享基礎設施 |
| `roles/cloudfunctions.admin` | Cloud Function 管理 | 函數部署和管理   | 共享基礎設施 |
| `roles/storage.admin`        | Storage 完整管理    | 檔案上傳和管理   | 共享基礎設施 |

### 3. 資源級別 IAM 權限

#### Cloud Function IAM

```yaml
Resource: carrefour-offline-data-prod
Bindings:
  - role: roles/cloudfunctions.invoker
    members:
      - serviceAccount:<EMAIL>
```

#### Cloud Run Service IAM

```yaml
Resource: carrefour-offline-data-prod (Cloud Run Service)
Bindings:
  - role: roles/run.invoker
    members:
      - serviceAccount:<EMAIL>
```

## 🔄 執行流程中的權限使用

### 1. Cloud Scheduler 觸發階段

```
Cloud Scheduler Job
├── Service Account: <EMAIL>
├── OIDC Token 生成: ✅ (使用 roles/iam.serviceAccountTokenCreator)
├── HTTP POST 請求: ✅
└── 目標: Cloud Function HTTP 端點
```

### 2. Cloud Function 執行階段

```
Cloud Function (Gen2)
├── Runtime Service Account: <EMAIL>
├── Cloud Run Service 觸發: ✅ (基礎設施層 SA 觸發)
├── 應用程式執行: ✅ (專案執行層 SA 執行)
└── 資源存取權限: ✅
```

### 3. BigQuery 資料處理階段

```
BigQuery Operations (執行層 SA: carrefour-tagtoo-bigquery-view)
├── 來源資料讀取 (tagtoo-tracking)
│   ├── tagtoo_event: ✅ (roles/bigquery.dataEditor)
│   ├── carrefour_offline_transaction_day: ✅
│   └── tagtoo_entity: ✅
├── 查詢執行: ✅ (roles/bigquery.jobUser)
└── 目標資料寫入 (tagtoo-ml-workflow)
    └── special_lta_temp_for_update_*: ✅ (roles/bigquery.dataEditor + jobUser)
```

## 🛡️ 安全性最佳實務

### 1. 最小權限原則

- ✅ 只授予執行必要功能所需的最小權限
- ✅ 使用資源級別 IAM 綁定限制存取範圍
- ✅ 定期審查和清理不必要的權限

### 2. 認證和授權

- ✅ 使用 OIDC Token 進行服務間認證
- ✅ Cloud Function 設定為 `ALLOW_INTERNAL_ONLY`
- ✅ 所有 HTTP 請求都需要有效的認證

### 3. 審計和監控

- ✅ 所有權限變更記錄在 Terraform 狀態
- ✅ Cloud Audit Logs 記錄所有 API 呼叫
- ✅ 監控異常的權限使用模式

## 🔧 故障排除

### 常見權限問題

#### 1. HTTP 403 PERMISSION_DENIED

**原因**: Cloud Run Service 缺少 `roles/run.invoker` 權限
**解決**: 確認 Terraform 中的 `google_cloud_run_service_iam_binding.invoker` 資源

#### 2. BigQuery 存取被拒

**原因**: 跨專案權限配置不正確
**解決**: 檢查 tagtoo-ml-workflow 專案中的 Service Account 權限

#### 3. OIDC Token 生成失敗

**原因**: Service Account 缺少 Token Creator 權限
**解決**: 確認共享基礎設施中的 IAM 配置

### 權限驗證指令

```bash
# 檢查 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 檢查 Cloud Function IAM
gcloud functions get-iam-policy carrefour-offline-data-prod \
  --region=asia-east1 --gen2

# 檢查 Cloud Run Service IAM
gcloud run services get-iam-policy carrefour-offline-data-prod \
  --region=asia-east1
```

## 📝 變更歷史

| 日期       | 變更內容                           | Commit  |
| ---------- | ---------------------------------- | ------- |
| 2025-09-01 | 採用共享基礎設施 Service Account   | bd0c7a2 |
| 2025-09-01 | 添加 Cloud Run Service IAM 權限    | bd0c7a2 |
| 2025-09-01 | 修復 GOOGLE_CLOUD_PROJECT 環境變數 | bd0c7a2 |

---

**最後更新**: 2025-09-01
**維護者**: Frank Zheng (<EMAIL>)
**審查週期**: 每季度
