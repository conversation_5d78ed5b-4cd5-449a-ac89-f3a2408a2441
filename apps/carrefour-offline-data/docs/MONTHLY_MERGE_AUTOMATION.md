# 家樂福離線資料月度智慧合併自動化系統

## 系統概述

此文件描述了家樂福離線資料月度智慧合併自動化系統的實作細節、操作流程和監控指南。

### 核心功能

1. **智慧資料邊界分析** - 自動分析月表和完整表的資料邊界，識別需要合併的資料類型
2. **多策略合併執行** - 根據資料特性選擇最佳合併策略（直接插入、替換、跳過）
3. **自動化排程執行** - 每月5號自動執行資料複製和智慧合併
4. **成本優化設計** - 使用 DELETE+INSERT 替代 MERGE 以實現最佳成本效益

## 架構設計

### 資料流程

```
每月5號 10:30: 資料複製 (offline_transaction_month)
     ↓
每月5號 11:00: 智慧資料合併 (月表 → 完整表)
     ↓
自動檢測和處理:
- 月表專有日期 → 直接插入
- 月表主導日期 → 刪除+插入
- 重疊相同日期 → 跳過處理
```

### 技術元件

1. **Cloud Function**: `carrefour-data-replication-prod`

   - 支援 `operation_type = "monthly_merge"`
   - 整合 `automated_monthly_merge.py` 模組

2. **Cloud Scheduler**:

   - `carrefour-monthly-replication-prod` (10:30)
   - `carrefour-monthly-merge-prod` (11:00)

3. **BigQuery 表格**:
   - 來源表: `tagtoo-tracking.event_prod.carrefour_offline_transaction_month`
   - 目標表: `tagtoo-tracking.event_prod.carrefour_offline_transaction`

## 合併策略詳解

### 智慧邊界分析

系統自動分析每個日期的資料狀況：

```python
# 日期分類邏輯
if month_count > 0 and complete_count == 0:
    # 月表專有 → 直接插入
elif month_count > complete_count:
    # 月表主導 → 替換策略
elif month_count == complete_count:
    # 完全重疊 → 跳過處理
```

### 合併策略選擇

1. **直接插入策略** (month_only_dates)

   ```sql
   INSERT INTO complete_table
   SELECT * FROM month_table
   WHERE DATE(...) IN ('2025-09-02', '2025-09-03', ...)
   ```

2. **替換策略** (month_dominant_dates)

   ```sql
   -- 步驟1: 刪除完整表中的舊資料
   DELETE FROM complete_table WHERE DATE(...) = '2025-07-15'

   -- 步驟2: 插入月表中的新資料
   INSERT INTO complete_table
   SELECT * FROM month_table WHERE DATE(...) = '2025-07-15'
   ```

3. **跳過策略** (overlap_dates)
   - 資料完全相同，無需處理

## 操作指南

### 手動執行測試

1. **測試模式執行**:

   ```bash
   # 本地測試
   cd /path/to/carrefour-offline-data
   source venv/bin/activate
   python tools/automated_monthly_merge.py --dry-run
   ```

2. **Cloud Function 測試**:
   ```bash
   gcloud functions call carrefour-data-replication-prod \
     --region=asia-east1 \
     --data='{
       "operation_type": "monthly_merge",
       "dry_run": true,
       "max_days_back": 90
     }'
   ```

### 生產環境執行

**注意**: 生產模式會實際修改 BigQuery 資料，請謹慎操作。

1. **透過 Cloud Scheduler 自動執行** (建議):

   - 系統會在每月5號11:00自動執行

2. **手動觸發生產執行**:

   ```bash
   gcloud scheduler jobs run carrefour-monthly-merge-prod \
     --location=asia-east1
   ```

3. **直接呼叫 Cloud Function**:
   ```bash
   gcloud functions call carrefour-data-replication-prod \
     --region=asia-east1 \
     --data='{
       "operation_type": "monthly_merge",
       "dry_run": false,
       "max_days_back": 90
     }'
   ```

## 監控和警報

### 執行狀態檢查

1. **Cloud Scheduler 狀態**:

   ```bash
   gcloud scheduler jobs describe carrefour-monthly-merge-prod \
     --location=asia-east1
   ```

2. **Cloud Function 日誌**:

   ```bash
   gcloud logging read 'resource.type="cloud_function" AND
     resource.labels.function_name="carrefour-data-replication-prod" AND
     jsonPayload.operation_type="monthly_merge"' \
     --limit=50
   ```

3. **BigQuery 執行歷程**:
   - 在 BigQuery Console 查看 Jobs History
   - 搜尋專案: `tagtoo-tracking`，時間範圍: 每月5號前後

### 關鍵監控指標

1. **執行時間**: 一般 < 60 秒
2. **處理記錄數**: 依月份資料量而定
3. **成本**: 每月約 $0.05-0.15 USD
4. **錯誤率**: 應 < 1%

### 警報配置

系統已配置以下自動警報：

1. **執行失敗警報** - 立即發送至 <EMAIL>
2. **執行超時警報** - 超過 30 分鐘觸發
3. **成本異常警報** - 單次執行超過 $1 USD

## 故障排除

### 常見問題

1. **權限錯誤 (HTTP 403)**

   ```
   檢查 Service Account 權限:
   - bigquery.dataEditor
   - bigquery.jobUser
   ```

2. **資料不一致**

   ```bash
   # 驗證資料完整性
   python tools/automated_monthly_merge.py --dry-run
   ```

3. **執行超時**
   ```
   檢查資料量是否異常增長
   調整 Cloud Function timeout 設定
   ```

### 緊急恢復程序

如果合併過程出現問題：

1. **立即停止**: 透過 Cloud Console 停用相關 Cloud Scheduler
2. **資料備份**: BigQuery 有自動快照，可恢復至錯誤前狀態
3. **問題分析**: 查看 Cloud Function 日誌確定失敗原因
4. **手動修復**: 必要時執行手動 SQL 修正資料

## 成本分析

### 預估月度成本

| 項目                | 預估成本 (USD) | 備註            |
| ------------------- | -------------- | --------------- |
| Cloud Scheduler     | $0.10          | 每月1次執行     |
| Cloud Function 執行 | $0.01          | 約1分鐘執行時間 |
| BigQuery 查詢       | $0.05-0.15     | 依資料量而定    |
| BigQuery 儲存       | 包含在現有成本 | 無額外費用      |
| **月度總計**        | **$0.16-0.26** | 非常經濟實惠    |

### 成本優化特點

1. **DELETE+INSERT 設計**: 利用 BigQuery 分割區免費刪除
2. **智慧跳過邏輯**: 避免處理重複資料
3. **精確日期篩選**: 最小化掃描資料量
4. **單次執行**: 減少 Cloud Function 冷啟動成本

## 維護和升級

### 定期維護任務

1. **月度檢查** (每月6號)

   - 驗證前一天的合併執行結果
   - 檢查資料完整性和一致性
   - 確認成本在預期範圍內

2. **季度檢查**
   - 評估效能和成本趨勢
   - 檢討合併策略有效性
   - 更新文件和監控閾值

### 升級路徑

1. **功能增強**: 在 `automated_monthly_merge.py` 中新增功能
2. **部署更新**: 透過 Terraform 自動部署
3. **測試驗證**: 先在測試環境驗證後部署生產環境

## 技術參數配置

### Terraform 變數

```hcl
# terraform/variables.tf
variable "monthly_merge_schedule" {
  description = "每月智慧資料合併排程"
  default     = "0 11 5 * *" # 每月5號 11:00
}
```

### Cloud Function 環境變數

```yaml
ENVIRONMENT: prod
PROJECT_ID: tagtoo-tracking
TARGET_PROJECT: tagtoo-tracking
SOURCE_PROJECT: tw-eagle-prod
MAX_COST_USD: 1.0
LOG_LEVEL: INFO
```

### BigQuery 配置

- **來源專案**: `tw-eagle-prod`
- **複製目標**: `tagtoo-tracking.event_prod.carrefour_offline_transaction_month`
- **合併目標**: `tagtoo-tracking.event_prod.carrefour_offline_transaction`
- **分割區欄位**: `DATE(TIMESTAMP_SECONDS(event_times))`

---

## 版本資訊

- **創建日期**: 2025-09-12
- **版本**: v1.0
- **作者**: Claude Code
- **維護者**: Data Engineering Team
- **更新頻率**: 依需求更新

## 相關文件

- [CLAUDE.md](../CLAUDE.md) - 專案總體架構說明
- [README.md](../README.md) - 系統整體說明
- [smart_merge_monthly_to_complete.py](../tools/smart_merge_monthly_to_complete.py) - 原始合併工具
- [automated_monthly_merge.py](../tools/automated_monthly_merge.py) - 自動化合併模組
