# 家樂福離線資料系統監控和警報配置

# 建立 Email 通知頻道（專案隔離設計）
resource "google_monitoring_notification_channel" "email" {
  count        = length(var.notification_emails)
  display_name = "家樂福離線資料系統 - ${var.environment} - ${var.notification_emails[count.index]}"
  type         = "email"

  labels = {
    email_address = var.notification_emails[count.index]
  }

  # 標準化用戶標籤，便於管理和分類
  user_labels = {
    project     = "carrefour-offline-data"
    environment = var.environment
    purpose     = "project-specific-alerts"
    owner       = split("@", var.notification_emails[count.index])[0]
  }

  enabled = var.enable_monitoring
}

# Cloud Function 執行失敗率警報
resource "google_monitoring_alert_policy" "function_error_rate" {
  display_name = "${local.service_full_name}-function-error-rate"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Function 執行失敗 (任何錯誤立即通知)"

    condition_threshold {
      filter          = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_count\" AND metric.labels.status!=\"ok\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0      # 任何失敗執行都觸發警報
      duration        = "300s" # 平衡檢測速度與成本效益

      aggregations {
        alignment_period     = "300s" # 適中的檢查頻率，避免過度監控
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.label.function_name"]
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "1800s"
  }

  documentation {
    content = "Cloud Function ${local.service_full_name} 執行失敗，任何錯誤都會在 5 分鐘內觸發此警報。正常情況下每日凌晨 2:00 執行一次且應該成功完成。請檢查：1) Cloud Function 日誌 2) 執行錯誤訊息 3) 資料來源狀態 4) BigQuery 連線狀況。監控間隔已優化為 300 秒，平衡檢測速度與成本效益。"
  }
}

# Cloud Function 執行時間警報 - 暫時停用，避免異常值誤報
# 原因：指標數據異常，出現 16851977723.299019 秒（約 534 年）的錯誤執行時間數據
# TODO: 等待 Google Cloud Monitoring 修復執行時間指標異常後重新啟用
/*
resource "google_monitoring_alert_policy" "function_execution_time" {
  display_name = "${local.service_full_name}-execution-time"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Function 執行時間超過 ${var.alert_thresholds.execution_time_seconds} 秒"

    condition_threshold {
      # 修正過濾器：移除無效的 metric.value 語法，改用聚合函數處理異常值
      filter          = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_times\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.alert_thresholds.execution_time_seconds * 1000 # 轉換為毫秒 (正確單位)
      duration        = "300s"                                             # 增加持續時間，避免短暫異常觸發

      # 修正聚合策略：
      # - alignment_period: 300s 減少雜訊，提供更穩定的檢測
      # - ALIGN_DELTA: 適用於 DELTA DISTRIBUTION 指標，計算變化量
      # - REDUCE_PERCENTILE_95: 使用 95% 分位數，減少異常值影響但保持敏感度
      aggregations {
        alignment_period     = "300s"                 # 5分鐘窗口，減少雜訊
        per_series_aligner   = "ALIGN_DELTA"          # 適用於 DELTA 類型指標
        cross_series_reducer = "REDUCE_PERCENTILE_95" # 使用 95% 分位數，過濾異常值
        group_by_fields      = ["resource.label.function_name"]
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "1800s"
  }

  documentation {
    content = "【執行時間異常警報】Cloud Function ${local.service_full_name} 執行時間超過 ${var.alert_thresholds.execution_time_seconds} 秒閾值。\n\n正常執行時間：15-20 秒\n告警條件：使用 ALIGN_DELTA 聚合，取 300s 窗口內執行時間變化，當 95% 分位數超過 300 秒時觸發\n\n異常值處理：使用 REDUCE_PERCENTILE_95 聚合函數，自動過濾極端異常值（如 16M+ 秒）\n持續時間：需持續 300 秒才觸發，避免短暫異常和誤報\n指標類型：DELTA DISTRIBUTION - 適用於 Cloud Functions 執行時間監控\n\n檢查步驟：\n1. 查看 Cloud Function 日誌確認實際執行時間\n2. 檢查 BigQuery 查詢效能\n3. 檢查資料來源連線狀況\n4. 檢查記憶體使用率是否接近上限\n5. 確認是否為監控指標異常值（如 16M+ 秒的錯誤數據）"
  }
}
*/

# Cloud Function 記憶體使用率警報
resource "google_monitoring_alert_policy" "function_memory_usage" {
  display_name = "${local.service_full_name}-memory-usage"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Function 記憶體使用率超過 ${var.alert_thresholds.memory_usage_percentage}%"

    condition_threshold {
      filter     = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/user_memory_bytes\""
      comparison = "COMPARISON_GT"
      # 計算記憶體閾值：1024Mi = 1073741824 bytes，90% = 966367641.6 bytes
      threshold_value = 1073741824 * var.alert_thresholds.memory_usage_percentage / 100
      duration        = "300s"

      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_DELTA"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["resource.label.function_name"]
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "1800s"
  }

  documentation {
    content = "Cloud Function ${local.service_full_name} 記憶體使用率過高 (>${var.alert_thresholds.memory_usage_percentage}%)，可能需要增加記憶體配置或優化程式碼。當前配置：1024Mi"
  }
}

# Cloud Scheduler 失敗警報
resource "google_monitoring_alert_policy" "scheduler_failure" {
  count = var.enable_scheduler ? 1 : 0

  display_name = "${local.service_full_name}-scheduler-failure"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Scheduler 執行失敗"

    # 修正：使用 condition_matched_log 來監控日誌條目，而不是指標
    # 這是監控 Cloud Scheduler 失敗的正確方法
    condition_matched_log {
      # 篩選器邏輯說明：
      # - 監控 Cloud Scheduler 的錯誤日誌
      # - 包含 HTTP 錯誤狀態碼（4xx, 5xx）和 ERROR 級別日誌
      # - 針對實際失敗狀態進行監控，避免正常執行的 INFO 日誌觸發告警
      filter = "resource.type=\"cloud_scheduler_job\" AND resource.labels.job_id=\"${local.service_full_name}-schedule\" AND (severity=\"ERROR\" OR httpRequest.status>=400)"

      # 可選：提取標籤用於通知中的詳細資訊
      label_extractors = {
        "job_id"      = "EXTRACT(resource.labels.job_id)"
        "status_code" = "EXTRACT(httpRequest.status)"
        "error_msg"   = "EXTRACT(textPayload)"
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "3600s"

    # 修正：Log-based Alert Policy 必須指定 notification_rate_limit
    notification_rate_limit {
      period = "300s" # 最多每 5 分鐘一次通知，避免過度通知
    }
  }

  documentation {
    content = "【排程執行失敗警報】Cloud Scheduler ${local.service_full_name}-schedule 執行失敗\n\n✅ 修正說明：\n- 使用 condition_matched_log 替代 condition_threshold\n- 直接監控日誌條目而非指標，避免語法錯誤\n- 支援 HTTP 狀態碼和 severity 雙重過濾\n\n正常執行時間：每日台灣時間凌晨 2:00 (UTC 18:00)\n告警條件：監控 ERROR 級別日誌和 HTTP 4xx/5xx 狀態碼\n\n常見失敗原因：\n1. HTTP 500: Cloud Function 內部錯誤\n2. HTTP 403: 權限配置問題\n3. HTTP 404: Function 不存在或 URL 錯誤\n4. HTTP 408: 請求超時\n\n檢查步驟：\n1. 確認 Cloud Function 狀態：gcloud functions describe ${local.service_full_name} --region=asia-east1\n2. 檢查 Service Account 權限配置\n3. 查看詳細錯誤日誌：gcloud logging read 'resource.labels.job_id=\"${local.service_full_name}-schedule\"' --limit=5\n4. 檢查 Cloud Function 日誌：gcloud logging read 'resource.labels.function_name=\"${local.service_full_name}\"' --limit=10"
  }
}

# BigQuery 成本監控 - 基於 Log-based Metric 實現
# 由於 BigQuery 原生指標在此專案中不可用，我們使用 Log-based Metric 來監控查詢成本

# 自定義指標：BigQuery 查詢執行次數監控
resource "google_logging_metric" "bigquery_query_executions" {
  name   = "${local.service_full_name}-bigquery-query-executions"
  filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND (textPayload:\"BigQuery 查詢完成\" OR jsonPayload.message:\"BigQuery 查詢完成\")"

  metric_descriptor {
    metric_kind  = "DELTA"
    value_type   = "INT64"
    display_name = "BigQuery 查詢執行次數"
  }
}

# Cloud Function 執行頻率異常檢測 - 每日執行超過一次即為異常
resource "google_monitoring_alert_policy" "function_execution_frequency_alert" {
  display_name = "${local.service_full_name}-execution-frequency"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  conditions {
    display_name = "Cloud Function 每日執行次數異常 (超過 1 次)"

    condition_threshold {
      filter          = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_count\""
      comparison      = "COMPARISON_GT"
      threshold_value = 1 # 每日超過 1 次執行視為異常
      duration        = "300s"

      aggregations {
        alignment_period     = "86400s" # 24 小時
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.label.function_name"]
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "86400s" # 24 小時
  }

  documentation {
    content = "Cloud Function 每日執行次數異常，超過正常的 1 次執行。正常情況下每日只在凌晨 2:00 執行一次。請檢查：1) 是否有手動觸發 2) Cloud Scheduler 配置是否異常 3) 是否有重試機制被觸發。"
  }
}

# 自定義指標：家樂福受眾媒合成功率
resource "google_logging_metric" "audience_matching_success" {
  name   = "${local.service_full_name}-audience-matching-success"
  filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND (textPayload:\"✅ 家樂福受眾媒合執行完成\" OR jsonPayload.message:\"✅ 家樂福受眾媒合執行完成\")"

  metric_descriptor {
    metric_kind  = "DELTA"
    value_type   = "INT64"
    display_name = "家樂福受眾媒合成功次數"
  }
}

# 自定義指標：處理的用戶數量
resource "google_logging_metric" "processed_users_count" {
  name   = "${local.service_full_name}-processed-users"
  filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND textPayload:\"生成受眾標籤:\""

  metric_descriptor {
    metric_kind  = "DELTA"
    value_type   = "INT64"
    display_name = "處理的用戶數量"
  }
}

# 自定義指標：執行失敗次數
resource "google_logging_metric" "execution_failures" {
  name   = "${local.service_full_name}-execution-failures"
  filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.service_full_name}\" AND (severity=\"ERROR\" OR textPayload:\"❌\" OR jsonPayload.message:\"❌\")"

  metric_descriptor {
    metric_kind  = "DELTA"
    value_type   = "INT64"
    display_name = "執行失敗次數"
  }
}

# 基於自定義指標的執行失敗警報 - 暫時禁用直到 Log Metric 穩定
resource "google_monitoring_alert_policy" "custom_execution_failures" {
  count        = 0 # 暫時禁用，等待 Log Metric 穩定後再啟用
  display_name = "${local.service_full_name}-custom-execution-failures"
  combiner     = "OR"
  enabled      = var.enable_monitoring

  # 依賴於 Log Metric 建立完成
  depends_on = [google_logging_metric.execution_failures]

  conditions {
    display_name = "家樂福受眾媒合執行失敗"

    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${local.service_full_name}-execution-failures\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0
      duration        = "60s"

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }

  notification_channels = google_monitoring_notification_channel.email[*].name

  alert_strategy {
    auto_close = "3600s"
  }

  documentation {
    content = "家樂福受眾媒合系統執行失敗，請立即檢查 Cloud Function 日誌和錯誤訊息。"
  }
}
