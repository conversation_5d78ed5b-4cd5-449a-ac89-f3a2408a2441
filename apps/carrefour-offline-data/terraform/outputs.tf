# 家樂福離線資料受眾媒合系統 Terraform 輸出

output "function_name" {
  description = "Cloud Function 名稱"
  value       = google_cloudfunctions2_function.carrefour_offline_data.name
}

output "function_url" {
  description = "Cloud Function URL"
  value       = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri
}

# 移除 Pub/Sub 相關輸出 - 改用 HTTP 觸發

output "scheduler_job_name" {
  description = "Cloud Scheduler 任務名稱 (受眾媒合)"
  value       = google_cloud_scheduler_job.carrefour_audience_matching.name
}

output "storage_bucket" {
  description = "原始碼儲存 Bucket"
  value       = google_storage_bucket.function_source.name
}

output "service_account_email" {
  description = "使用的 Service Account Email"
  value       = local.carrefour_service_account_email
}

output "security_status" {
  description = "安全狀態檢查"
  value = {
    service_account_configured = local.carrefour_service_account_email != ""
    ingress_restricted         = "✅ 僅允許內部觸發"
    http_auth                  = "✅ HTTP 使用 OIDC 驗證"
    scheduler_enabled          = var.enable_scheduler
  }
}

output "deployment_info" {
  description = "部署資訊"
  value = {
    environment     = var.environment
    service_name    = var.service_name
    runtime         = var.runtime
    memory          = var.memory
    timeout_seconds = var.timeout
    max_instances   = var.max_instances
    schedule        = var.enable_scheduler ? var.schedule_expression : "disabled"
    timezone        = var.schedule_timezone
  }
}

output "monitoring_info" {
  description = "監控配置資訊"
  value = {
    monitoring_enabled  = var.enable_monitoring
    notification_emails = var.notification_emails
    alert_policies = var.enable_monitoring ? [
      "${local.service_full_name}-function-success-rate",
      "${local.service_full_name}-execution-time",
      "${local.service_full_name}-memory-usage",
      "${local.service_full_name}-bigquery-cost"
    ] : []
    scheduler_monitoring = var.enable_scheduler && var.enable_monitoring ? [
      "${local.service_full_name}-scheduler-failure"
    ] : []
    custom_metrics = var.enable_monitoring ? [
      "${local.service_full_name}-audience-matching-success",
      "${local.service_full_name}-processed-users"
    ] : []
    alert_thresholds = var.alert_thresholds
  }
}
