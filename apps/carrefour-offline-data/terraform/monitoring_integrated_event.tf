# 家樂福 integrated_event 轉換簡化監控配置
#
# 此檔案定義基本的監控和警報配置

locals {
  # 簡化的通知頻道配置
  integrated_event_notification_channels = [
    # 可以在部署後手動配置通知頻道
  ]

  # 監控標籤
  integrated_event_monitoring_labels = merge(local.integrated_event_labels, {
    component = "monitoring"
    alert_type = "carrefour-integrated-event"
  })
}

# ============================================================================
# Cloud Function 執行錯誤監控
# ============================================================================

resource "google_monitoring_alert_policy" "integrated_event_function_errors" {
  display_name = "家樂福 integrated_event 轉換 - Cloud Function 錯誤"
  combiner     = "OR"

  conditions {
    display_name = "Cloud Function 錯誤率 > 5%"

    condition_threshold {
      filter         = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.integrated_event_function_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_count\""
      duration       = "300s"
      comparison     = "COMPARISON_GT"
      threshold_value = 0.05

      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["resource.label.function_name"]
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = local.integrated_event_notification_channels

  alert_strategy {
    auto_close = "86400s"  # 24 小時自動關閉
  }

  user_labels = local.integrated_event_monitoring_labels
}

# ============================================================================
# Cloud Function 執行時間監控
# ============================================================================

resource "google_monitoring_alert_policy" "integrated_event_function_duration" {
  display_name = "家樂福 integrated_event 轉換 - 執行時間過長"
  combiner     = "OR"

  conditions {
    display_name = "Cloud Function 執行時間 > 8 分鐘"

    condition_threshold {
      filter         = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.integrated_event_function_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_times\""
      duration       = "300s"
      comparison     = "COMPARISON_GT"
      threshold_value = 480000  # 8 分鐘 (毫秒)

      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_PERCENTILE_95"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["resource.label.function_name"]
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = local.integrated_event_notification_channels

  alert_strategy {
    auto_close = "43200s"  # 12 小時自動關閉
  }

  user_labels = local.integrated_event_monitoring_labels
}

# ============================================================================
# Dashboard 配置
# ============================================================================

resource "google_monitoring_dashboard" "integrated_event_dashboard" {
  dashboard_json = jsonencode({
    displayName = "家樂福 integrated_event 轉換監控儀表板"

    mosaicLayout = {
      columns = 12
      tiles = [
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Function 執行次數"
            xyChart = {
              dataSets = [{
                timeSeriesQuery = {
                  timeSeriesFilter = {
                    filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.integrated_event_function_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_count\""
                    aggregation = {
                      alignmentPeriod    = "300s"
                      perSeriesAligner   = "ALIGN_RATE"
                      crossSeriesReducer = "REDUCE_SUM"
                    }
                  }
                }
                plotType = "LINE"
              }]
              timeshiftDuration = "0s"
              yAxis = {
                label = "執行次數/秒"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          xPos   = 6
          widget = {
            title = "Cloud Function 執行時間"
            xyChart = {
              dataSets = [{
                timeSeriesQuery = {
                  timeSeriesFilter = {
                    filter = "resource.type=\"cloud_function\" AND resource.labels.function_name=\"${local.integrated_event_function_name}\" AND metric.type=\"cloudfunctions.googleapis.com/function/execution_times\""
                    aggregation = {
                      alignmentPeriod    = "300s"
                      perSeriesAligner   = "ALIGN_PERCENTILE_95"
                      crossSeriesReducer = "REDUCE_MEAN"
                    }
                  }
                }
                plotType = "LINE"
              }]
              timeshiftDuration = "0s"
              yAxis = {
                label = "執行時間 (ms)"
                scale = "LINEAR"
              }
            }
          }
        }
      ]
    }
  })
}

# ============================================================================
# 輸出資訊
# ============================================================================

output "integrated_event_alert_policies" {
  description = "integrated_event 轉換警報策略 ID"
  value = {
    function_errors     = google_monitoring_alert_policy.integrated_event_function_errors.name
    function_duration   = google_monitoring_alert_policy.integrated_event_function_duration.name
  }
}

output "integrated_event_dashboard_url" {
  description = "integrated_event 轉換監控儀表板 URL"
  value       = "https://console.cloud.google.com/monitoring/dashboards/custom/${urlencode(google_monitoring_dashboard.integrated_event_dashboard.id)}?project=${var.project_id}"
}