# 家樂福 integrated_event 轉換 Cloud Function 基礎設施配置
#
# 此檔案定義：
# 1. carrefour-integrated-event-prod Cloud Function Gen2
# 2. Cloud Scheduler 排程（每日 11:30）
# 3. 相關 IAM 權限和 Service Account
# 4. 監控和警報配置

locals {
  # Cloud Function 配置
  integrated_event_function_name = "carrefour-integrated-event-prod"
  integrated_event_scheduler_name = "carrefour-integrated-event-prod"

  # 時區和排程配置
  timezone = "Asia/Taipei"
  schedule_time = "30 11 * * *"  # 每日 11:30

  # 環境標籤
  integrated_event_labels = merge(local.common_labels, {
    application = "carrefour-integrated-event"
    operation   = "daily-conversion"
    frequency   = "daily"
  })
}

# ============================================================================
# Cloud Function Source Archive
# ============================================================================

# 使用預先建立的 ZIP 檔案
# data "archive_file" "integrated_event_function_source" {
#   type        = "zip"
#   output_path = "${path.module}/../deployment/carrefour_integrated_event_function.zip"
#   source_dir = "${path.module}/../deployment/integrated_event_source"
# }

# 上傳 ZIP 檔案到 Google Cloud Storage
resource "google_storage_bucket_object" "integrated_event_function_source" {
  name   = "carrefour-integrated-event/${data.archive_file.integrated_event_function_source.output_md5}.zip"
  bucket = var.deployment_bucket
  source = data.archive_file.integrated_event_function_source.output_path

  lifecycle {
    replace_triggered_by = [
      data.archive_file.integrated_event_function_source
    ]
  }
}

# ============================================================================
# Cloud Function Gen2 - integrated_event 轉換
# ============================================================================

resource "google_cloudfunctions2_function" "integrated_event_converter" {
  name        = local.integrated_event_function_name
  location    = var.region
  description = "家樂福線下交易資料轉換為 integrated_event 格式"

  build_config {
    runtime     = "python311"
    entry_point = "main"

    source {
      storage_source {
        bucket = var.deployment_bucket
        object = google_storage_bucket_object.integrated_event_function_source.name
      }
    }

    environment_variables = {
      GOOGLE_FUNCTION_SOURCE = "carrefour-integrated-event"
    }
  }

  service_config {
    max_instance_count               = 2
    min_instance_count               = 0
    available_memory                 = "1Gi"
    timeout_seconds                  = 540  # 9 分鐘
    max_instance_request_concurrency = 1
    available_cpu                    = "1"

    environment_variables = {
      PROJECT_ID   = var.project_id
      ENVIRONMENT  = var.environment
      FUNCTION_TARGET = "main"
    }

    ingress_settings               = "ALLOW_INTERNAL_ONLY"
    all_traffic_on_latest_revision = true

    service_account_email = local.carrefour_service_account_email
  }

  labels = local.integrated_event_labels

  depends_on = [
    google_storage_bucket_object.integrated_event_function_source
  ]
}

# ============================================================================
# Cloud Scheduler - integrated_event 轉換排程
# ============================================================================

resource "google_cloud_scheduler_job" "integrated_event_converter" {
  name             = local.integrated_event_scheduler_name
  description      = "每日執行家樂福線下交易資料轉換為 integrated_event"
  schedule         = local.schedule_time
  time_zone        = local.timezone
  attempt_deadline = "600s"  # 10 分鐘
  region           = var.region

  retry_config {
    retry_count          = 3
    max_retry_duration   = "1800s"  # 30 分鐘
    min_backoff_duration = "60s"
    max_backoff_duration = "300s"
    max_doublings        = 3
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.integrated_event_converter.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      lookback_days  = 1
      dry_run        = false
      max_cost_usd   = 5.0
      operation_type = "daily_scheduled_conversion"
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.integrated_event_converter.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.integrated_event_converter
  ]
}

# ============================================================================
# IAM 權限配置
# ============================================================================

# 重用現有的 Service Accounts，遵循架構分離設計
# Service Account 定義已在 iam.tf 中設定

# Cloud Function 觸發權限
# 允許基礎設施層 Service Account 透過 OIDC 觸發 Cloud Function
resource "google_cloudfunctions2_function_iam_member" "scheduler_invoke_function" {
  project        = var.project_id
  location       = var.region
  cloud_function = google_cloudfunctions2_function.integrated_event_converter.name
  role           = "roles/cloudfunctions.invoker"
  member         = "serviceAccount:${local.infrastructure_service_account_email}"
}

# Cloud Run Service 觸發權限 (Cloud Function Gen2 底層使用 Cloud Run)
resource "google_cloud_run_service_iam_member" "scheduler_invoke_run_service" {
  project  = var.project_id
  location = var.region
  service  = google_cloudfunctions2_function.integrated_event_converter.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${local.infrastructure_service_account_email}"
}

# ============================================================================
# BigQuery Dataset 權限 (如果需要跨專案存取)
# ============================================================================

# 對 event_prod dataset 的存取權限（專案執行層 SA 已有此權限，但為 integrated_event 表格確保權限）
resource "google_bigquery_dataset_iam_member" "function_sa_event_prod_editor" {
  dataset_id = "event_prod"
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${local.carrefour_service_account_email}"
}

# ============================================================================
# 輸出資訊
# ============================================================================

output "integrated_event_function_url" {
  description = "integrated_event 轉換 Cloud Function URL"
  value       = google_cloudfunctions2_function.integrated_event_converter.service_config[0].uri
}

output "integrated_event_function_name" {
  description = "integrated_event 轉換 Cloud Function 名稱"
  value       = google_cloudfunctions2_function.integrated_event_converter.name
}

output "integrated_event_scheduler_name" {
  description = "integrated_event 轉換 Scheduler 名稱"
  value       = google_cloud_scheduler_job.integrated_event_converter.name
}

output "integrated_event_function_sa_email" {
  description = "integrated_event 轉換 Cloud Function Service Account Email"
  value       = local.carrefour_service_account_email
}

output "integrated_event_scheduler_sa_email" {
  description = "integrated_event 轉換 Scheduler Service Account Email"
  value       = local.infrastructure_service_account_email
}