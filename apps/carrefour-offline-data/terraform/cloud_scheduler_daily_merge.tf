# 家樂福每日增量合併排程配置
# 新增每日自動合併功能，將 day 表的資料增量更新到主表

# 每日增量合併排程 - 在每日資料複製完成後執行
resource "google_cloud_scheduler_job" "carrefour_daily_merge" {
  name             = "carrefour-daily-merge-${var.environment}"
  description      = "家樂福離線資料每日增量合併排程 - 從 day 表增量更新到完整表 (每日 12:00)"
  schedule         = var.daily_merge_schedule # 每日台灣時間 12:00
  time_zone        = "Asia/Taipei"
  region           = var.region
  project          = var.project_id
  attempt_deadline = "900s" # 15 分鐘超時

  retry_config {
    retry_count          = 3
    max_retry_duration   = "1800s" # 30 分鐘最大重試時間
    min_backoff_duration = "300s"  # 5 分鐘最小重試間隔
    max_backoff_duration = "900s"  # 15 分鐘最大重試間隔
    max_doublings        = 2
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.carrefour_daily_merge.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      operation_type = "daily_incremental_merge"
      target_date    = "auto" # 自動使用昨天的日期
      lookback_days  = 3      # 回溯3天處理延遲資料
      dry_run        = false  # 生產模式執行實際合併
      max_cost_usd   = 5.0    # 成本限制
      environment    = var.environment
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.carrefour_daily_merge.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_daily_merge,
    google_cloud_scheduler_job.carrefour_daily_replication
  ]
}

# 每日增量合併 Cloud Function
resource "google_cloudfunctions2_function" "carrefour_daily_merge" {
  name        = "carrefour-daily-merge-${var.environment}"
  location    = var.region
  description = "家樂福每日增量合併 Cloud Function - 從 day 表增量更新到主表"
  project     = var.project_id

  build_config {
    runtime     = "python311"
    entry_point = "main"

    source {
      storage_source {
        bucket = google_storage_bucket.carrefour_functions.name
        object = google_storage_bucket_object.carrefour_daily_merge_source.name
      }
    }
  }

  service_config {
    max_instance_count               = 1
    min_instance_count               = 0
    available_memory                 = "2Gi" # 2Gi 記憶體支援大量資料處理
    timeout_seconds                  = 900   # 15 分鐘超時
    max_instance_request_concurrency = 1
    available_cpu                    = "1"

    environment_variables = {
      ENVIRONMENT    = var.environment
      PROJECT_ID     = var.project_id
      TARGET_PROJECT = "tagtoo-tracking"
      SOURCE_PROJECT = "tagtoo-tracking"
      MAX_COST_USD   = "5.0"
      LOG_LEVEL      = "INFO"
      OPERATION_TYPE = "daily_incremental_merge"
    }

    ingress_settings               = "ALLOW_INTERNAL_ONLY"
    all_traffic_on_latest_revision = true

    service_account_email = local.carrefour_service_account_email
  }

  depends_on = [
    google_storage_bucket_object.carrefour_daily_merge_source
  ]
}

# 每日合併函數原始碼儲存
resource "google_storage_bucket_object" "carrefour_daily_merge_source" {
  name   = "carrefour-daily-merge-source-${var.environment}.zip"
  bucket = google_storage_bucket.carrefour_functions.name
  source = "${path.module}/../deployment/carrefour_daily_merge_function.zip"

  depends_on = [
    data.archive_file.carrefour_daily_merge_function
  ]
}

# 打包每日合併函數原始碼
data "archive_file" "carrefour_daily_merge_function" {
  type        = "zip"
  output_path = "${path.module}/../deployment/carrefour_daily_merge_function.zip"

  source {
    content  = file("${path.module}/../tools/daily_merge_function.py")
    filename = "main.py"
  }

  source {
    content  = file("${path.module}/../tools/automated_daily_merge.py")
    filename = "automated_daily_merge.py"
  }

  source {
    content  = file("${path.module}/../requirements.txt")
    filename = "requirements.txt"
  }
}

# 輸出新的每日合併排程資訊
output "daily_merge_scheduler" {
  description = "Daily merge Cloud Scheduler job information"
  value = {
    daily_merge = {
      name      = google_cloud_scheduler_job.carrefour_daily_merge.name
      schedule  = google_cloud_scheduler_job.carrefour_daily_merge.schedule
      timezone  = google_cloud_scheduler_job.carrefour_daily_merge.time_zone
      operation = "daily_incremental_merge"
    }
  }
}

output "daily_merge_cloud_function" {
  description = "Daily merge Cloud Function information"
  value = {
    daily_merge_function = {
      name = google_cloudfunctions2_function.carrefour_daily_merge.name
      uri  = google_cloudfunctions2_function.carrefour_daily_merge.service_config[0].uri
    }
  }
}
