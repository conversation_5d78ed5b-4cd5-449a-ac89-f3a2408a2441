# 家樂福離線資料系統監控配置 (最小化版本)
# 暫時移除複雜的監控警報，確保核心功能部署穩定性
# TODO: 後續需要重新設計和實作監控警報系統

# Email 通知頻道
resource "google_monitoring_notification_channel" "email_alerts" {
  display_name = "Carrefour Email Alerts - ${var.environment}"
  type         = "email"
  project      = var.project_id

  labels = {
    email_address = var.alert_email
  }

  enabled = true
}

# Slack 通知頻道 (可選)
resource "google_monitoring_notification_channel" "slack_alerts" {
  count = var.enable_slack_alerts ? 1 : 0

  display_name = "Carrefour Slack Alerts - ${var.environment}"
  type         = "slack"
  project      = var.project_id

  labels = {
    channel_name = var.slack_channel
    url          = var.slack_webhook_url
  }

  enabled = true
}

# 輸出
output "notification_channels" {
  description = "Created notification channels"
  value = {
    email = google_monitoring_notification_channel.email_alerts.name
    slack = var.enable_slack_alerts ? google_monitoring_notification_channel.slack_alerts[0].name : null
  }
}

# 註記：監控警報和儀表板已暫時移除
# 原因：
# 1. BigQuery 指標配置錯誤 (bigquery.googleapis.com/job/query/scanned_bytes 不存在)
# 2. Cloud Scheduler 指標配置錯誤 (cloudscheduler.googleapis.com/job/attempt_count 標籤問題)
# 3. 聚合函數配置錯誤 (ALIGN_MAX 不適用於 DELTA/DISTRIBUTION 類型)
# 4. 過濾器語法錯誤 (metric.value 不能與 metric 前綴一起使用)
#
# 後續 TODO:
# 1. 研究正確的 Google Cloud Monitoring 指標名稱和標籤
# 2. 重新設計監控警報策略
# 3. 建立測試環境驗證監控配置
# 4. 逐步重新引入監控警報
