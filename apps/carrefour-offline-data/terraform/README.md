# 家樂福受眾媒合系統 Terraform 配置

此目錄包含家樂福受眾媒合系統的 Terraform 基礎設施即程式碼 (Infrastructure as Code) 配置。

## 📁 檔案結構

```
terraform/
├── main.tf                    # 主要資源定義
├── variables.tf               # 變數定義
├── outputs.tf                 # 輸出值定義
├── versions.tf                # Provider 版本約束
├── README.md                  # 本檔案
├── environments/
│   ├── test.tfvars           # 測試環境配置
│   └── production.tfvars     # 生產環境配置
└── modules/                   # 自定義模組 (未來擴展)
```

## 🚀 快速開始

### 前置需求

1. **安裝 Terraform**

```bash
# macOS
brew install terraform

# 或下載二進位檔案
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_darwin_amd64.zip
```

2. **設定 GCP 認證**

```bash
# 使用 gcloud 認證
gcloud auth application-default login

# 或設定 service account key
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
```

3. **設定 GCP 專案**

```bash
gcloud config set project tagtoo-tracking
```

### 部署步驟

#### 測試環境部署

```bash
# 1. 初始化 Terraform
terraform init

# 2. 檢查計畫
terraform plan -var-file="environments/test.tfvars"

# 3. 執行部署
terraform apply -var-file="environments/test.tfvars"
```

#### 生產環境部署

```bash
# 1. 檢查計畫
terraform plan -var-file="environments/production.tfvars"

# 2. 執行部署 (需要確認)
terraform apply -var-file="environments/production.tfvars"
```

## 📋 管理的資源

### 核心資源

- **Cloud Function (Gen 2)**: 家樂福受眾媒合主程式
- **Service Account**: 專用服務帳號和 IAM 權限
- **Pub/Sub Topic**: 觸發器主題
- **Cloud Storage Bucket**: 函數原始碼儲存
- **Cloud Scheduler**: 排程執行 (僅生產環境)

### IAM 權限

- `roles/bigquery.dataViewer`: 讀取來源表格
- `roles/bigquery.jobUser`: 執行 BigQuery 查詢
- `roles/bigquery.dataEditor`: 寫入目標表格

### 監控資源 (未來擴展)

- Cloud Monitoring 告警
- Log-based Metrics
- Error Reporting 配置

## 🔧 配置說明

### 環境變數

| 變數名稱            | 描述         | 測試環境           | 生產環境           |
| ------------------- | ------------ | ------------------ | ------------------ |
| `PROJECT_ID`        | 來源專案 ID  | tagtoo-tracking    | tagtoo-tracking    |
| `TARGET_PROJECT_ID` | 目標專案 ID  | tagtoo-ml-workflow | tagtoo-ml-workflow |
| `EC_ID`             | 家樂福 EC ID | 715                | 715                |
| `MAX_COST_USD`      | 查詢成本上限 | 10.0               | 100.0              |
| `TEST_MODE`         | 測試模式     | true               | false              |
| `DAYS_BACK`         | 回溯天數     | 7                  | 30                 |

### 資源配置

| 資源       | 測試環境  | 生產環境      |
| ---------- | --------- | ------------- |
| 記憶體     | 2048Mi    | 4096Mi        |
| 逾時       | 300s      | 540s          |
| 最大實例數 | 1         | 3             |
| 排程       | 每 6 小時 | 每天凌晨 2 點 |

## 🛠️ 常用指令

### 檢查資源狀態

```bash
terraform show
terraform state list
```

### 更新特定資源

```bash
# 僅更新 Cloud Function
terraform apply -target=google_cloudfunctions2_function.carrefour_offline_data

# 僅更新 IAM 權限
terraform apply -target=google_project_iam_member.function_sa_roles
```

### 銷毀資源

```bash
# 測試環境
terraform destroy -var-file="environments/test.tfvars"

# 生產環境 (謹慎操作)
terraform destroy -var-file="environments/production.tfvars"
```

### 格式化和驗證

```bash
# 格式化程式碼
terraform fmt -recursive

# 驗證配置
terraform validate

# 檢查安全性 (需要 tfsec)
tfsec .
```

## 🔍 故障排除

### 常見問題

1. **API 未啟用**

```
Error: Error creating function: googleapi: Error 403: Cloud Functions API has not been used
```

**解決方案**: Terraform 會自動啟用必要的 API，等待幾分鐘後重試。

2. **權限不足**

```
Error: Error creating IAM member: googleapi: Error 403: Permission denied
```

**解決方案**: 確認當前帳號有 `roles/resourcemanager.projectIamAdmin` 權限。

3. **Service Account 已存在**

```
Error: Error creating service account: googleapi: Error 409: Service account already exists
```

**解決方案**: 使用 `terraform import` 匯入現有資源。

### 匯入現有資源

```bash
# 匯入現有 Service Account
terraform import google_service_account.function_sa projects/tagtoo-tracking/serviceAccounts/<EMAIL>

# 匯入現有 Cloud Function
terraform import google_cloudfunctions2_function.carrefour_offline_data projects/tagtoo-tracking/locations/asia-east1/functions/carrefour-offline-data-test
```

## 📊 監控和日誌

### 檢查 Cloud Function 狀態

```bash
# 使用 gcloud
gcloud functions describe carrefour-audience-matching-test --region=asia-east1

# 檢查日誌
gcloud functions logs read carrefour-audience-matching-test --region=asia-east1
```

### 檢查 Pub/Sub 訊息

```bash
# 手動發送測試訊息
gcloud pubsub topics publish carrefour-audience-matching-test --message='{"test_mode": true}'
```

## 🔄 CI/CD 整合

### GitHub Actions 範例

```yaml
name: Deploy Carrefour Audience Matching
on:
  push:
    branches: [main]
    paths: ["apps/carrefour-offline-data/terraform/**"]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: hashicorp/setup-terraform@v2
      - name: Terraform Init
        run: terraform init
        working-directory: apps/carrefour-offline-data/terraform
      - name: Terraform Plan
        run: terraform plan -var-file="environments/production.tfvars"
        working-directory: apps/carrefour-offline-data/terraform
      - name: Terraform Apply
        run: terraform apply -auto-approve -var-file="environments/production.tfvars"
        working-directory: apps/carrefour-offline-data/terraform
```

## 📞 支援

**技術支援**: Tagtoo Data Team
**文檔版本**: v1.0.0
**最後更新**: 2025-08-25
