# Cloud Scheduler 配置
# 家樂福離線資料自動化排程

# 1. 每日資料複製排程 - offline_transaction_day 表格
resource "google_cloud_scheduler_job" "carrefour_daily_replication" {
  name             = "carrefour-daily-replication-${var.environment}"
  description      = "家樂福離線資料每日複製排程 - offline_transaction_day 表格"
  schedule         = var.replication_schedule # 每日台灣時間 10:30
  time_zone        = "Asia/Taipei"
  region           = var.region
  project          = var.project_id
  attempt_deadline = "900s" # 15 分鐘超時

  retry_config {
    retry_count          = 3
    max_retry_duration   = "1800s" # 30 分鐘最大重試時間
    min_backoff_duration = "300s"  # 5 分鐘最小重試間隔
    max_backoff_duration = "900s"  # 15 分鐘最大重試間隔
    max_doublings        = 2
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      operation_type = "data_replication"
      table_name     = "offline_transaction_day"
      environment    = var.environment
      force_copy     = false
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_data_replication
  ]
}

# 2. 每月資料複製排程 - offline_transaction_month 表格
resource "google_cloud_scheduler_job" "carrefour_monthly_replication" {
  name             = "carrefour-monthly-replication-${var.environment}"
  description      = "家樂福離線資料每月複製排程 - offline_transaction_month 表格 (每月5號)"
  schedule         = var.monthly_replication_schedule # 每月 5 號台灣時間 10:30
  time_zone        = "Asia/Taipei"
  region           = var.region
  project          = var.project_id
  attempt_deadline = "1200s" # 20 分鐘超時（月表格較大）

  retry_config {
    retry_count          = 3
    max_retry_duration   = "2400s" # 40 分鐘最大重試時間
    min_backoff_duration = "600s"  # 10 分鐘最小重試間隔
    max_backoff_duration = "1200s" # 20 分鐘最大重試間隔
    max_doublings        = 2
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      operation_type = "data_replication"
      table_name     = "offline_transaction_month"
      environment    = var.environment
      force_copy     = false
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_data_replication
  ]
}

# 2a. 每月智慧資料合併排程 - 已停用，改用每日增量合併機制
# 註解：由於實施每日增量更新，月度合併已不再需要
# 新的每日增量合併會在 cloud_scheduler_daily_merge.tf 中定義
# resource "google_cloud_scheduler_job" "carrefour_monthly_merge" {
#   name             = "carrefour-monthly-merge-${var.environment}"
#   description      = "家樂福離線資料每月智慧合併排程 - 已停用，改用每日增量合併"
#   schedule         = var.monthly_merge_schedule
#   time_zone        = "Asia/Taipei"
#   region           = var.region
#   project          = var.project_id
#   attempt_deadline = "1800s"
#
#   retry_config {
#     retry_count          = 3
#     max_retry_duration   = "3600s"
#     min_backoff_duration = "600s"
#     max_backoff_duration = "1800s"
#     max_doublings        = 2
#   }
#
#   http_target {
#     http_method = "POST"
#     uri         = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri
#
#     headers = {
#       "Content-Type" = "application/json"
#     }
#
#     body = base64encode(jsonencode({
#       execution_mode = "scheduled"
#       operation_type = "monthly_merge"
#       dry_run        = false
#       max_days_back  = 90
#       environment    = var.environment
#     }))
#
#     oidc_token {
#       service_account_email = local.infrastructure_service_account_email
#       audience              = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri
#     }
#   }
#
#   depends_on = [
#     google_cloudfunctions2_function.carrefour_data_replication,
#     google_cloud_scheduler_job.carrefour_monthly_replication
#   ]
# }

# 2. 受眾媒合排程 - 每日台灣時間凌晨 02:00
resource "google_cloud_scheduler_job" "carrefour_audience_matching" {
  name             = "carrefour-audience-matching-${var.environment}"
  description      = "家樂福受眾媒合每日執行排程 - 凌晨執行"
  schedule         = var.audience_matching_schedule # 每日台灣時間凌晨 02:00
  time_zone        = "Asia/Taipei"
  region           = var.region
  project          = var.project_id
  attempt_deadline = "1800s" # 30 分鐘超時

  retry_config {
    retry_count          = 3
    max_retry_duration   = "3600s" # 1 小時最大重試時間
    min_backoff_duration = "600s"  # 10 分鐘最小重試間隔
    max_backoff_duration = "1800s" # 30 分鐘最大重試間隔
    max_doublings        = 2
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      days_back      = 1 # 暫時改回 1 天，避免記憶體超限 (1024 MiB)
      dry_run        = false
      environment    = var.environment
      max_cost_usd   = 1.0
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_offline_data
  ]
}

# 3. ph_id 直接處理排程 - 每日台灣時間 11:00 (在 day 表複製完成後執行)
resource "google_cloud_scheduler_job" "carrefour_ph_id_direct" {
  name             = "carrefour-ph-id-direct-${var.environment}"
  description      = "家樂福 ph_id 直接處理排程 - 直接從 day 表提取 ph_id 生成 AVRO 檔案"
  schedule         = var.ph_id_direct_schedule # 每日台灣時間 11:00
  time_zone        = "Asia/Taipei"
  region           = var.region
  project          = var.project_id
  attempt_deadline = "900s" # 15 分鐘超時

  retry_config {
    retry_count          = 3
    max_retry_duration   = "1800s" # 30 分鐘最大重試時間
    min_backoff_duration = "300s"  # 5 分鐘最小重試間隔
    max_backoff_duration = "900s"  # 15 分鐘最大重試間隔
    max_doublings        = 2
  }

  http_target {
    http_method = "POST"
    uri         = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      operation_type = "ph_id_direct"
      target_date    = "auto" # 自動使用昨天的日期
      dry_run        = false
      environment    = var.environment
      max_cost_usd   = 50.0
    }))

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
      audience              = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_offline_data,
    google_cloud_scheduler_job.carrefour_daily_replication
  ]
}

# 4. 週報排程已移除 - 功能尚未實作

# 4. 資料複製 Cloud Function
resource "google_cloudfunctions2_function" "carrefour_data_replication" {
  name        = "carrefour-data-replication-${var.environment}"
  location    = var.region
  description = "家樂福資料複製 Cloud Function - 支援單一表格複製模式 (day/month 表格)"
  project     = var.project_id

  build_config {
    runtime     = "python311"
    entry_point = "main"

    source {
      storage_source {
        bucket = google_storage_bucket.carrefour_functions.name
        object = google_storage_bucket_object.carrefour_replication_source.name
      }
    }
  }

  service_config {
    max_instance_count               = 1
    min_instance_count               = 0
    available_memory                 = "1Gi" # 維持 1Gi，多表格序列處理應足夠
    timeout_seconds                  = 900   # 增加到 15 分鐘支援多表格複製
    max_instance_request_concurrency = 1
    available_cpu                    = "1"

    environment_variables = {
      ENVIRONMENT    = var.environment
      PROJECT_ID     = var.project_id
      TARGET_PROJECT = "tagtoo-tracking"
      SOURCE_PROJECT = "tw-eagle-prod"
      MAX_COST_USD   = "1.0"
      LOG_LEVEL      = "INFO"
    }

    ingress_settings               = "ALLOW_INTERNAL_ONLY"
    all_traffic_on_latest_revision = true

    service_account_email = local.carrefour_service_account_email
  }

  depends_on = [
    google_storage_bucket_object.carrefour_replication_source
  ]
}

# 4. BigQuery Service Account
resource "google_service_account" "carrefour_bigquery_view" {
  account_id   = "carrefour-bigquery-view-${var.environment}"
  display_name = "Carrefour BigQuery Service Account"
  description  = "Service Account for Carrefour BigQuery operations"
  project      = var.project_id
}

# 5. Scheduler Service Account 已移除 - 使用共用的 integrated-event-prod Service Account
# 共用 Service Account 已具備 roles/cloudfunctions.invoker 和 roles/run.invoker 權限

# 6. Storage Bucket for Cloud Functions
resource "google_storage_bucket" "carrefour_functions" {
  name     = "carrefour-functions-${var.environment}-${random_id.bucket_suffix.hex}"
  location = var.region
  project  = var.project_id

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
}

resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# 7. 函數原始碼儲存
resource "google_storage_bucket_object" "carrefour_replication_source" {
  name   = "carrefour-replication-source-${var.environment}.zip"
  bucket = google_storage_bucket.carrefour_functions.name
  source = "${path.module}/../deployment/carrefour_replication_function.zip"

  depends_on = [
    data.archive_file.carrefour_replication_function
  ]
}

# 8. 打包函數原始碼
data "archive_file" "carrefour_replication_function" {
  type        = "zip"
  output_path = "${path.module}/../deployment/carrefour_replication_function.zip"

  source {
    content  = file("${path.module}/../tools/data_replication_function.py")
    filename = "main.py"
  }

  source {
    content  = file("${path.module}/../tools/automated_daily_replication.py")
    filename = "automated_daily_replication.py"
  }

  source {
    content  = file("${path.module}/../tools/automated_monthly_merge.py")
    filename = "automated_monthly_merge.py"
  }

  source {
    content  = file("${path.module}/../requirements.txt")
    filename = "requirements.txt"
  }
}

# 9. 監控警報已移除 - 暫時移除以確保部署穩定性

# 10. 成本監控警報已移除 - 暫時移除以確保部署穩定性

# 11. Email 通知頻道已移至 monitoring_alerts.tf

# 12. 輸出
output "scheduler_jobs" {
  description = "Created Cloud Scheduler jobs"
  value = {
    daily_replication = {
      name     = google_cloud_scheduler_job.carrefour_daily_replication.name
      schedule = google_cloud_scheduler_job.carrefour_daily_replication.schedule
      timezone = google_cloud_scheduler_job.carrefour_daily_replication.time_zone
      table    = "offline_transaction_day"
    }
    monthly_replication = {
      name     = google_cloud_scheduler_job.carrefour_monthly_replication.name
      schedule = google_cloud_scheduler_job.carrefour_monthly_replication.schedule
      timezone = google_cloud_scheduler_job.carrefour_monthly_replication.time_zone
      table    = "offline_transaction_month"
    }
    # monthly_merge 已停用，改用每日增量合併
    # monthly_merge = {
    #   name      = google_cloud_scheduler_job.carrefour_monthly_merge.name
    #   schedule  = google_cloud_scheduler_job.carrefour_monthly_merge.schedule
    #   timezone  = google_cloud_scheduler_job.carrefour_monthly_merge.time_zone
    #   operation = "monthly_data_merge"
    # }
    audience_matching = {
      name     = google_cloud_scheduler_job.carrefour_audience_matching.name
      schedule = google_cloud_scheduler_job.carrefour_audience_matching.schedule
      timezone = google_cloud_scheduler_job.carrefour_audience_matching.time_zone
    }
    ph_id_direct = {
      name     = google_cloud_scheduler_job.carrefour_ph_id_direct.name
      schedule = google_cloud_scheduler_job.carrefour_ph_id_direct.schedule
      timezone = google_cloud_scheduler_job.carrefour_ph_id_direct.time_zone
    }
  }
}

output "cloud_functions" {
  description = "Created Cloud Functions"
  value = {
    data_replication = {
      name = google_cloudfunctions2_function.carrefour_data_replication.name
      uri  = google_cloudfunctions2_function.carrefour_data_replication.service_config[0].uri
    }
  }
}
