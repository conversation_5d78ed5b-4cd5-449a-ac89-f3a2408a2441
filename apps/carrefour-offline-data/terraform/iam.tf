# 家樂福離線資料系統 IAM 權限配置

# 架構分離的 Service Account 配置
# 基礎設施層：使用共享 Service Account 進行觸發和部署
# 專案執行層：使用專案特定 Service Account 執行業務邏輯
locals {
  # 基礎設施層 Service Account (用於 Cloud Scheduler 觸發)
  # 為 dev 環境提供 fallback，避免 null 值問題
  infrastructure_service_account_email = var.environment == "dev" ? "<EMAIL>" : local.shared_outputs.service_account_email

  # 專案執行層 Service Account (用於 Cloud Function 執行和資料處理)
  carrefour_service_account_email = var.service_account_email
}

# 驗證專案特定 Service Account 存在並可用
resource "null_resource" "verify_service_account" {
  provisioner "local-exec" {
    command = <<-EOT
      echo "驗證專案特定 Service Account 權限..."
      gcloud iam service-accounts describe ${local.carrefour_service_account_email} > /dev/null
      if [ $? -eq 0 ]; then
        echo "✅ 專案特定 Service Account 存在且可存取"
        echo "📧 執行層 Service Account: ${local.carrefour_service_account_email}"
        echo "📧 基礎設施層 Service Account: ${local.infrastructure_service_account_email}"
      else
        echo "❌ 專案特定 Service Account 不存在或無權限存取"
        exit 1
      fi
    EOT
  }

  triggers = {
    carrefour_service_account_email      = local.carrefour_service_account_email
    infrastructure_service_account_email = local.infrastructure_service_account_email
    shared_outputs_hash                  = md5(jsonencode(local.shared_outputs))
  }
}

# Cloud Function 觸發權限
# 允許基礎設施層 Service Account 透過 OIDC 觸發 Cloud Function
resource "google_cloudfunctions2_function_iam_binding" "invoker" {
  project        = local.shared_outputs.project_id
  location       = local.shared_outputs.region
  cloud_function = google_cloudfunctions2_function.carrefour_offline_data.name
  role           = "roles/cloudfunctions.invoker"

  members = [
    "serviceAccount:${local.infrastructure_service_account_email}"
  ]

  depends_on = [
    google_cloudfunctions2_function.carrefour_offline_data,
    null_resource.verify_service_account
  ]
}

# Cloud Run Service 觸發權限 (Cloud Function Gen2 底層使用 Cloud Run)
# 這是修復 HTTP 403 PERMISSION_DENIED 錯誤的關鍵配置
resource "google_cloud_run_service_iam_binding" "invoker" {
  project  = local.shared_outputs.project_id
  location = local.shared_outputs.region
  service  = google_cloudfunctions2_function.carrefour_offline_data.name
  role     = "roles/run.invoker"

  members = [
    "serviceAccount:${local.infrastructure_service_account_email}"
  ]

  depends_on = [
    google_cloudfunctions2_function.carrefour_offline_data,
    null_resource.verify_service_account
  ]
}

# 專案特定 Service Account 的跨專案權限配置
# 確保家樂福專案可以寫入 ML Workflow 專案的目標表格
resource "google_project_iam_member" "carrefour_ml_workflow_access" {
  project = var.target_project_id
  role    = "roles/bigquery.dataEditor"
  member  = "serviceAccount:${local.carrefour_service_account_email}"
}

resource "google_project_iam_member" "carrefour_ml_workflow_job_user" {
  project = var.target_project_id
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${local.carrefour_service_account_email}"
}

# GCS 權限：允許執行層 Service Account 檢視 ML Workflow 專案的輸出 AVRO 檔案
# 需要 storage.objects.list 與 storage.objects.get，使用 roles/storage.objectViewer 滿足最小權限
resource "google_storage_bucket_iam_member" "ml_workflow_object_viewer" {
  bucket = "tagtoo-ml-workflow"
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${local.carrefour_service_account_email}"

  depends_on = [
    null_resource.verify_service_account
  ]
}

# Cloud Logging 權限（如果需要）
# 注意：通常 Cloud Function 會自動獲得基本的 logging 權限
resource "google_project_iam_member" "function_logs_writer" {
  count = var.enable_additional_logging ? 1 : 0

  project = local.shared_outputs.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${local.carrefour_service_account_email}"
}

# 輸出 Service Account 資訊供驗證
output "service_account_verification" {
  description = "架構分離的 Service Account 驗證資訊"
  value = {
    infrastructure_layer = {
      email        = local.infrastructure_service_account_email
      name         = local.shared_outputs.service_account_name
      project_id   = local.shared_outputs.project_id
      display_name = "Integrated Event Service Account (${local.shared_outputs.environment})"
      purpose      = "Cloud Scheduler 觸發和基礎設施管理"
    }
    execution_layer = {
      email        = local.carrefour_service_account_email
      project_id   = "tagtoo-tracking"
      display_name = "carrefour-tagtoo-bigquery-view"
      purpose      = "Cloud Function 執行和資料處理"
    }
    architecture = "separated-layers"
  }
}

# 權限檢查摘要
locals {
  required_permissions = [
    "BigQuery 資料讀取 (tagtoo-tracking)",
    "BigQuery 資料寫入 (tagtoo-ml-workflow)",
    "BigQuery Job 執行",
    "Cloud Storage 存取",
    "Cloud Function 觸發"
  ]
}

output "permissions_summary" {
  description = "架構分離的權限配置摘要"
  value = {
    architecture_type = "separated-layers"
    infrastructure_layer = {
      service_account = local.infrastructure_service_account_email
      purpose         = "Cloud Scheduler 觸發和基礎設施管理"
      permissions = [
        "Cloud Function 觸發權限 (roles/cloudfunctions.invoker)",
        "Cloud Run Service 觸發權限 (roles/run.invoker)"
      ]
    }
    execution_layer = {
      service_account = local.carrefour_service_account_email
      purpose         = "Cloud Function 執行和資料處理"
      permissions = [
        "BigQuery 資料編輯 (roles/bigquery.dataEditor) - 本專案",
        "BigQuery Job 執行 (roles/bigquery.jobUser) - 本專案",
        "Cloud Storage 物件使用 (roles/storage.objectUser) - 本專案",
        "Service Account 使用權限 (roles/iam.serviceAccountUser) - 本專案",
        "BigQuery 資料編輯 (roles/bigquery.dataEditor) - ML Workflow 專案",
        "BigQuery Job 執行 (roles/bigquery.jobUser) - ML Workflow 專案"
      ]
    }
    required_permissions = local.required_permissions
    security_notes = [
      "採用架構分離設計，基礎設施層和執行層使用不同 Service Account",
      "Cloud Scheduler 使用共享基礎設施 Service Account 進行觸發",
      "Cloud Function 使用專案特定 Service Account 執行業務邏輯",
      "確保客戶資料隔離和多客戶合作場景的安全需求",
      "Cloud Function 僅允許內部觸發 (ALLOW_INTERNAL_ONLY)"
    ]
  }
}
