# 家樂福離線資料受眾媒合系統 Terraform 變數

variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod。"
  }
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "carrefour-offline-data"
}

variable "deployment_version" {
  description = "部署版本 (通常是 git commit SHA)"
  type        = string
  default     = "latest"
}

# Cloud Function 配置
variable "runtime" {
  description = "Function 執行環境"
  type        = string
  default     = "python311"
}

variable "entry_point" {
  description = "Function 進入點"
  type        = string
  default     = "main"
}

variable "timeout" {
  description = "Function 執行逾時時間 (秒) - 多表格複製需要更長時間"
  type        = number
  default     = 900 # 15 分鐘，支援多表格複製
}

variable "memory" {
  description = "Function 記憶體限制"
  type        = string
  default     = "4096Mi" # 增加記憶體以支援 ph_id 直接處理（1天資料約201K ph_id，實際使用約2066Mi）
}

variable "cpu" {
  description = "Function CPU 限制"
  type        = string
  default     = "1"
}

variable "max_instances" {
  description = "最大實例數"
  type        = number
  default     = 1
}

variable "min_instances" {
  description = "最小實例數"
  type        = number
  default     = 0
}

# 排程配置
variable "enable_scheduler" {
  description = "是否啟用排程器"
  type        = bool
  default     = true
}

variable "schedule_expression" {
  description = "排程表達式 (cron 格式)"
  type        = string
  default     = "0 2 * * *" # 每日台灣時間凌晨 2:00
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

# 業務邏輯配置
variable "days_back" {
  description = "回溯天數"
  type        = number
  default     = 1
}

variable "project_id" {
  description = "Google Cloud 專案 ID"
  type        = string
  default     = "tagtoo-tracking"
}

variable "region" {
  description = "Google Cloud 區域"
  type        = string
  default     = "asia-east1"
}

variable "target_project_id" {
  description = "目標 BigQuery 專案 ID"
  type        = string
  default     = "tagtoo-ml-workflow"
}

variable "service_account_email" {
  description = "Service Account Email"
  type        = string
  default     = "<EMAIL>"
}

# 原始碼配置
variable "function_source_dir" {
  description = "Function 原始碼目錄路徑"
  type        = string
  default     = "../"
}

# 環境變數
variable "env_vars" {
  description = "Function 環境變數"
  type        = map(string)
  default = {
    EC_ID        = "715"
    MAX_COST_USD = "2.0" # 增加成本限制以支援多表格複製
  }
}

# IAM 配置
variable "enable_additional_logging" {
  description = "是否啟用額外的 Cloud Logging 權限"
  type        = bool
  default     = false
}

# 監控配置
variable "enable_monitoring" {
  description = "是否啟用監控和警報"
  type        = bool
  default     = true # 重新啟用監控系統
}

variable "notification_emails" {
  description = "接收警報通知的 Email 地址列表"
  type        = list(string)
  default     = ["<EMAIL>"]
}

variable "alert_thresholds" {
  description = "監控警報閾值配置"
  type = object({
    execution_time_seconds    = number # 執行時間閾值（秒）
    memory_usage_percentage   = number # 記憶體使用率閾值（百分比）
    error_rate_percentage     = number # 錯誤率閾值（百分比）
    daily_cost_usd            = number # 日總成本閾值
    query_cost_usd            = number # 單次查詢成本閾值
    execution_timeout_minutes = number # 執行超時閾值（分鐘）
    data_delay_hours          = number # 資料延遲閾值（小時）
    user_count_change_percent = number # 用戶數變化閾值（百分比）
  })
  default = {
    execution_time_seconds    = 300 # 5 分鐘
    memory_usage_percentage   = 90  # 90%
    error_rate_percentage     = 5   # 5%
    daily_cost_usd            = 2.0 # $2 USD (增加以支援多表格)
    query_cost_usd            = 1.0 # $1.0 USD (增加以支援多表格)
    execution_timeout_minutes = 30  # 30 分鐘
    data_delay_hours          = 2   # 2 小時
    user_count_change_percent = 20  # ±20%
  }
}

# Cloud Scheduler 優化配置
variable "alert_email" {
  description = "接收警報的 Email 地址"
  type        = string
  default     = "<EMAIL>"
}

variable "replication_schedule" {
  description = "每日資料複製排程 (cron 格式) - offline_transaction_day 表格"
  type        = string
  default     = "30 10 * * *" # 每日台灣時間 10:30 (tw-eagle 10:00更新後)
}

variable "monthly_replication_schedule" {
  description = "每月資料複製排程 (cron 格式) - offline_transaction_month 表格"
  type        = string
  default     = "30 10 5 * *" # 每月 5 號台灣時間 10:30
}

variable "monthly_merge_schedule" {
  description = "每月智慧資料合併排程 (cron 格式) - 在資料複製後執行合併"
  type        = string
  default     = "0 11 5 * *" # 每月 5 號台灣時間 11:00（資料複製後30分鐘）
}

variable "audience_matching_schedule" {
  description = "受眾媒合排程 (cron 格式)"
  type        = string
  default     = "0 2 * * *" # 每日台灣時間凌晨 02:00
}

variable "ph_id_direct_schedule" {
  description = "ph_id 直接處理排程 (cron 格式)"
  type        = string
  default     = "0 11 * * *" # 每日台灣時間 11:00 (在 day 表複製完成後執行)
}

variable "entity_lookback_days" {
  description = "tagtoo_entity 回溯天數 (基於成本分析優化)"
  type        = number
  default     = 30 # 30天回溯提供10.98x用戶覆蓋率，成本不變
}

variable "max_query_cost_usd" {
  description = "多表格複製成本上限 (USD)"
  type        = number
  default     = 2.0 # 增加以支援多表格複製
}

variable "enable_cost_monitoring" {
  description = "是否啟用成本監控警報"
  type        = bool
  default     = true
}

# 監控警報配置
variable "enable_slack_alerts" {
  description = "是否啟用 Slack 警報"
  type        = bool
  default     = false
}

variable "slack_channel" {
  description = "Slack 頻道名稱"
  type        = string
  default     = "#carrefour-alerts"
}

variable "slack_webhook_url" {
  description = "Slack Webhook URL"
  type        = string
  default     = ""
  sensitive   = true
}

# 生產環境額外配置變數
variable "max_cost_usd" {
  description = "最大成本限制 (USD)"
  type        = number
  default     = 100.0
}

variable "enable_logging" {
  description = "是否啟用日誌記錄"
  type        = bool
  default     = true
}

variable "retention_days" {
  description = "日誌保留天數"
  type        = number
  default     = 30
}

variable "schedule_cron" {
  description = "排程 cron 表達式"
  type        = string
  default     = "0 2 * * *"
}

variable "notification_email" {
  description = "通知電子郵件地址"
  type        = string
  default     = "<EMAIL>"
}

variable "daily_merge_schedule" {
  description = "每日增量合併排程 (cron 格式) - 從 day 表增量更新到主表"
  type        = string
  default     = "0 11 * * *" # 每日台灣時間 11:00 (與 ph_id_direct 同時執行，利用 max_instances=2)
}

variable "labels" {
  description = "資源標籤"
  type        = map(string)
  default = {
    managed_by = "terraform"
  }
}

# 通知頻道配置
variable "slack_notification_channel_id" {
  description = "Slack 通知頻道 ID"
  type        = string
  default     = "dummy-channel-id"
}

variable "email_notification_channel_id" {
  description = "Email 通知頻道 ID"
  type        = string
  default     = "dummy-channel-id"
}

variable "deployment_bucket" {
  description = "Cloud Function 部署用的 GCS Bucket"
  type        = string
  default     = "tagtoo-tracking-deployment-asia-east1"
}

# 警報閾值配置已在上面定義，移除重複定義
