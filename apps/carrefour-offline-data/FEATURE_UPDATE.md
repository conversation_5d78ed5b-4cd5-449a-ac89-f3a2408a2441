# 家樂福 ph_id 直接處理功能更新

> **功能版本**: v1.0.0
> **開發日期**: 2025-09-12
> **開發者**: AI Assistant
> **狀態**: ✅ 開發完成，準備部署

## 🎯 功能概述

新增家樂福 ph_id 直接處理功能，能夠直接從線下交易資料中提取 ph_id 並上傳到 Meta，避免透過 `tagtoo_entity.phone` 媒合過程中的使用者流失問題。

### 核心目標

- **避免使用者流失**: 直接使用 ph_id，不經過 `ph_id → tagtoo_entity.mobile → permanent` 的轉換
- **保持資料精確度**: 維持原始的使用者識別準確性
- **平行處理**: 不影響現有的 tagtoo_entity.phone 媒合流程
- **相容現有架構**: 重用現有的基礎設施和監控機制

## 🏗️ 技術架構

### 新增模組

1. **PhIdDirectProcessor** (`src/analysis/ph_id_direct_processor.py`)

   - 主要處理邏輯和流程控制
   - 資料提取、轉換和 AVRO 檔案生成
   - 成本估算和效能監控

2. **GCSUploader** (`src/tools/gcs_uploader.py`)

   - Cloud Storage 檔案上傳
   - 重試機制和進度追蹤
   - 元數據管理

3. **PubSubNotifier** (`src/tools/pubsub_notifier.py`)
   - 事件通知和下游觸發
   - 標準化訊息格式
   - 主題和訂閱管理

### 整合點

- **Cloud Function 入口點**: 更新 `main.py` 支援 `operation_type=ph_id_direct`
- **配置管理**: 新增 `config/ph_id_direct_config.json`
- **部署腳本**: 提供 `scripts/deploy_ph_id_direct.sh`

## 📊 資料流程

```
每日 11:00 AM (新增)
    ↓
ph_id 直接處理流程
    ↓
1. 從 carrefour_offline_transaction_day 提取 ph_id + items
    ↓
2. 生成去重化的 ph_id 與 segment_id 對應關係
    ↓
3. 建立暫存對應表 (用於 debug)
    ↓
4. 生成 Meta AVRO 檔案
    ↓
5. 上傳到 GCS: gs://tagtoo-ml-workflow/carrefour_ph_id_direct/
    ↓
6. 觸發 Pub/Sub 通知
```

### 資料格式

**AVRO 檔案結構**:

```json
{
  "group_id": null,
  "emails": [],
  "phones": [
    "68eaa7034f7cc4ba4101668d640eceb7feb7a7b395b6b21e967327e8322854ae"
  ],
  "fb_info": [{ "fbp_fbc_ip": ["", "", ""] }],
  "segment_id": ["tm:c_715_pc_10012", "tm:c_715_pc_1001", "tm:c_715_pc_100"]
}
```

## 🧪 測試覆蓋

### 測試統計

- **總測試數**: 29 個
- **通過率**: 100%
- **測試類型**: 單元測試、整合測試、資料驗證、效能測試

### 測試檔案

1. `tests/test_ph_id_direct.py` - 核心功能單元測試 (11 個測試)
2. `tests/test_data_validation.py` - 資料正確性驗證 (9 個測試)
3. `tests/test_performance.py` - 效能和成本測試 (9 個測試)

### 測試覆蓋範圍

- ✅ 模組初始化和配置載入
- ✅ 資料提取和轉換邏輯
- ✅ AVRO 查詢生成和格式驗證
- ✅ GCS 上傳和錯誤處理
- ✅ Pub/Sub 通知機制
- ✅ 成本估算和控制
- ✅ 效能基準測試
- ✅ 與參考實作的一致性

## 🚀 部署指南

### 前置需求

- BigQuery 讀取權限 (`tagtoo-tracking.event_prod`)
- Cloud Storage 寫入權限 (`tagtoo-ml-workflow`)
- Pub/Sub 發布權限

### 部署步驟

1. **測試部署**:

   ```bash
   ./scripts/deploy_ph_id_direct.sh dev --dry-run
   ```

2. **生產部署**:

   ```bash
   ./scripts/deploy_ph_id_direct.sh prod
   ```

3. **手動測試**:
   ```bash
   python3 main.py --operation-type ph_id_direct --target-date 2025-09-11 --dry-run
   ```

## 📈 監控和告警

### 關鍵指標

- 執行成功率: 每日排程執行成功率
- 資料量: 處理的 ph_id 數量
- 執行時間: 完整流程執行時間 (目標 < 15 分鐘)
- 成本控制: BigQuery 查詢成本 (限制 < $50 USD)

### 告警設定

- 執行失敗告警
- 成本超標告警
- 執行時間過長告警
- 資料量異常告警

## 🔒 安全考量

- **資料加密**: ph_id 已經是 SHA256 雜湊，無需額外加密
- **存取控制**: 使用 IAM 控制資源存取權限
- **稽核日誌**: 所有操作都有完整的日誌記錄
- **資料保留**: 暫存表格 7 天後自動過期

## 📝 使用方式

### Cloud Function 調用

**新功能 (ph_id 直接處理)**:

```json
{
  "operation_type": "ph_id_direct",
  "target_date": "2025-09-11",
  "dry_run": true,
  "execution_mode": "manual"
}
```

**現有功能 (受眾媒合)**:

```json
{
  "operation_type": "audience_matching",
  "days_back": 1,
  "dry_run": true,
  "execution_mode": "manual"
}
```

### 本地測試

```bash
# 測試 ph_id 直接處理
python3 main.py --operation-type ph_id_direct --target-date 2025-09-11 --dry-run

# 測試原有受眾媒合
python3 main.py --operation-type audience_matching --days-back 1 --dry-run
```

## 🔄 與現有流程的關係

- **資料複製流程** (10:30 AM): 複製 day 表資料到目標專案
- **現有流程** (02:00 AM): tagtoo_entity 媒合 → permanent + segment_id
- **新增流程** (11:00 AM): 直接處理 → ph_id + segment_id (在 day 表複製完成後執行)
- **資料隔離**: 使用不同的暫存表格和 GCS 路徑，不影響現有 special_lta 表格
- **監控整合**: 共享相同的監控和告警機制

## 📚 文檔資源

- **技術文檔**: `docs/ph_id_direct_processing.md`
- **API 參考**: 詳細的方法和參數說明
- **故障排除**: 常見問題和解決方案
- **架構圖**: 完整的系統架構和資料流程

## 🐛 已知限制

1. **資料依賴**: 依賴 `carrefour_offline_transaction_day` 表格的資料品質
2. **成本控制**: 單次查詢成本限制為 $50 USD
3. **執行時間**: 大量資料處理可能需要較長時間
4. **並發限制**: 同時只能執行一個 ph_id 直接處理任務

## 🔮 未來改進

1. **效能最佳化**: 進一步優化 BigQuery 查詢效能
2. **並發處理**: 支援多個日期的並發處理
3. **增量處理**: 支援增量資料更新
4. **自動化測試**: 整合到 CI/CD 流程中

---

**維護者**: AI Assistant
**最後更新**: 2025-09-12
**版本**: 1.0.0
**狀態**: ✅ 準備部署
