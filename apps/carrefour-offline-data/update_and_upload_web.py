#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新並上傳靜態網頁到 GCS

此腳本會：
1. 執行統一主控腳本生成最新的分析報告
2. 生成最新的靜態網頁 (包含 web/index.html)
3. 上傳所有靜態網頁到 Google Cloud Storage

使用方式：
    source venv/bin/activate
    python update_and_upload_web.py
"""

import os
import sys
import subprocess
import time
from datetime import datetime

# 可配置的超時設定
COMMAND_TIMEOUT = int(os.getenv('COMMAND_TIMEOUT', '600'))  # 預設 10 分鐘

def run_command(command: str, description: str) -> bool:
    """執行命令並顯示結果"""
    print(f"\n🔄 {description}")
    print("=" * 60)

    try:
        start_time = time.time()
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=COMMAND_TIMEOUT  # 可由環境變數 COMMAND_TIMEOUT 設定
        )

        execution_time = time.time() - start_time

        if result.returncode == 0:
            print(f"✅ {description} 完成 ({execution_time:.1f}s)")
            if result.stdout.strip():
                print("輸出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失敗")
            print("錯誤輸出:")
            print(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超時 (>10分鐘)")
        return False
    except Exception as e:
        print(f"💥 {description} 發生異常: {e}")
        return False

def main():
    """主執行函數"""
    print("🚀 更新並上傳靜態網頁到 GCS")
    print("=" * 60)
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 檢查虛擬環境
    if 'VIRTUAL_ENV' not in os.environ:
        print("❌ 請先啟動虛擬環境: source venv/bin/activate")
        sys.exit(1)

    success_count = 0
    total_steps = 3

    # 步驟1: 執行統一主控腳本生成最新分析報告
    if run_command(
        "python src/generate_complete_report.py",
        "步驟1: 執行統一主控腳本生成最新分析報告"
    ):
        success_count += 1
    else:
        print("❌ 分析報告生成失敗，停止執行")
        sys.exit(1)

    # 步驟2: 確認靜態網頁已生成
    web_files = [
        'web/index.html',
        'web/all_in_one_analysis.html'
    ]

    print(f"\n🔍 步驟2: 確認靜態網頁已生成")
    print("=" * 60)

    all_files_exist = True
    for file_path in web_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"✅ {file_path} ({file_size:.1f} KB)")
        else:
            print(f"❌ {file_path} 不存在")
            all_files_exist = False

    if all_files_exist:
        success_count += 1
        print("✅ 所有靜態網頁檔案已確認存在")
    else:
        print("❌ 部分靜態網頁檔案不存在，停止執行")
        sys.exit(1)

    # 步驟3: 上傳靜態網頁到 GCS
    if run_command(
        "python src/tools/upload_to_gcs.py",
        "步驟3: 上傳靜態網頁到 GCS"
    ):
        success_count += 1
    else:
        print("❌ 上傳到 GCS 失敗")
        sys.exit(1)

    # 顯示總結
    print(f"\n🎉 更新並上傳流程完成")
    print("=" * 60)
    print(f"完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"成功步驟: {success_count}/{total_steps}")

    if success_count == total_steps:
        print("✅ 所有步驟執行成功！")
        print("\n🌐 靜態網頁已更新並上傳到 GCS:")
        print("   📄 主頁面: gs://integrated_event/carrefour_offline_data/web/index.html")
        print("   📊 完整分析: gs://integrated_event/carrefour_offline_data/web/all_in_one_analysis.html")
        print("   📊 JSON 報告: gs://integrated_event/carrefour_offline_data/reports/")
        print("\n💡 提示: 靜態網頁和 JSON 報告已分別上傳到 web/ 和 reports/ 目錄")
    else:
        print("❌ 部分步驟執行失敗，請檢查上述錯誤訊息")
        sys.exit(1)

if __name__ == "__main__":
    main()
