-- 家樂福離線交易資料每日增量更新 SQL
-- 從 carrefour_offline_transaction_day 表增量更新到 carrefour_offline_transaction 表
--
-- 設計原則：
-- 1. 動態檢查重疊資料，避免重複插入
-- 2. 支援回填缺失的歷史資料
-- 3. 使用 transaction_id 作為唯一性檢查
-- 4. 提供詳細的執行報告和驗證
--
-- 參數說明：
-- @target_date: 目標日期 (格式: 'YYYY-MM-DD')，如果為 'auto' 則使用昨天的日期
-- @lookback_days: 回溯天數，用於處理延遲到達的資料 (預設: 3)
-- @dry_run: 是否為測試模式 (預設: true)

DECLARE target_date DATE;
DECLARE lookback_days INT64 DEFAULT 3;
DECLARE dry_run BOOL DEFAULT true;
DECLARE start_date DATE;
DECLARE end_date DATE;

-- 參數初始化
-- 如果 target_date 為 'auto'，使用昨天的日期
SET target_date = CASE
  WHEN @target_date = 'auto' THEN DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY)
  ELSE PARSE_DATE('%Y-%m-%d', @target_date)
END;

SET lookback_days = IFNULL(@lookback_days, 3);
SET dry_run = IFNULL(@dry_run, true);

-- 設定處理日期範圍 (包含回溯天數以處理延遲到達的資料)
SET start_date = DATE_SUB(target_date, INTERVAL lookback_days DAY);
SET end_date = target_date;

-- 1. 資料重疊檢查 - 檢查哪些日期的資料已經存在於主表中
CREATE TEMP TABLE overlap_check AS
SELECT
  DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
  COUNT(*) as existing_records_in_main,
  (
    SELECT COUNT(*)
    FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` day
    WHERE DATE(TIMESTAMP_SECONDS(day.event_times)) = DATE(TIMESTAMP_SECONDS(main.event_times))
  ) as available_records_in_day
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction` main
WHERE DATE(TIMESTAMP_SECONDS(event_times)) BETWEEN start_date AND end_date
GROUP BY DATE(TIMESTAMP_SECONDS(event_times));

-- 2. 新資料識別 - 找出 day 表中不在主表中的資料
CREATE TEMP TABLE new_records AS
SELECT day.*
FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` day
LEFT JOIN `tagtoo-tracking.event_prod.carrefour_offline_transaction` main
  ON day.transaction_id = main.transaction_id
WHERE DATE(TIMESTAMP_SECONDS(day.event_times)) BETWEEN start_date AND end_date
  AND main.transaction_id IS NULL;

-- 3. 資料完整性驗證
CREATE TEMP TABLE validation_summary AS
SELECT
  'data_validation' as check_type,
  target_date as processing_date,
  start_date,
  end_date,
  (SELECT COUNT(*) FROM new_records) as new_records_count,
  (SELECT COUNT(DISTINCT DATE(TIMESTAMP_SECONDS(event_times))) FROM new_records) as new_days_count,
  (SELECT COUNT(DISTINCT ph_id) FROM new_records) as new_unique_ph_ids,
  (SELECT SUM(existing_records_in_main) FROM overlap_check) as existing_records,
  (SELECT SUM(available_records_in_day) FROM overlap_check) as day_table_records;

-- 4. 執行報告
SELECT
  '=== 家樂福離線交易資料增量更新報告 ===' as report_header,
  CONCAT('處理日期: ', target_date) as target_info,
  CONCAT('日期範圍: ', start_date, ' 到 ', end_date) as date_range,
  CONCAT('回溯天數: ', lookback_days, ' 天') as lookback_info,
  CONCAT('執行模式: ', IF(dry_run, '測試模式 (DRY RUN)', '生產模式 (LIVE)')) as execution_mode
UNION ALL
SELECT
  '=== 資料分析結果 ===',
  CONCAT('待新增記錄數: ', new_records_count),
  CONCAT('影響天數: ', new_days_count),
  CONCAT('新增 ph_id 數: ', new_unique_ph_ids),
  CONCAT('已存在記錄數: ', existing_records),
  CONCAT('Day表可用記錄數: ', day_table_records)
FROM validation_summary;

-- 5. 詳細的日期分析
SELECT
  DATE(TIMESTAMP_SECONDS(event_times)) as transaction_date,
  COUNT(*) as new_records_to_add,
  COUNT(DISTINCT ph_id) as unique_ph_ids,
  MIN(TIMESTAMP_SECONDS(event_times)) as earliest_timestamp,
  MAX(TIMESTAMP_SECONDS(event_times)) as latest_timestamp
FROM new_records
GROUP BY DATE(TIMESTAMP_SECONDS(event_times))
ORDER BY transaction_date;

-- 6. 重疊資料分析（如果有的話）
SELECT
  transaction_date,
  existing_records_in_main,
  available_records_in_day,
  (available_records_in_day - existing_records_in_main) as missing_records,
  ROUND((existing_records_in_main / available_records_in_day * 100), 2) as completion_percentage
FROM overlap_check
ORDER BY transaction_date;

-- 7. 實際插入操作 (只在非 dry_run 模式下執行)
-- 注意：這個部分在實際執行時會被動態生成
SELECT
  CASE
    WHEN dry_run THEN
      CONCAT('DRY RUN: 將會插入 ', (SELECT COUNT(*) FROM new_records), ' 筆新記錄')
    ELSE
      '準備執行實際插入操作...'
  END as execution_status;

-- 如果不是 dry_run 模式，則執行實際插入
-- INSERT INTO `tagtoo-tracking.event_prod.carrefour_offline_transaction`
-- SELECT * FROM new_records;

-- 8. 執行後驗證 (如果執行了實際插入)
-- 這部分會在實際執行後提供驗證報告
