#!/usr/bin/env python3
"""
家樂福離線資料系統權限驗證腳本

此腳本驗證 Service Account 是否具備執行家樂福受眾媒合所需的所有權限。
"""

import sys
import os
import logging
from typing import Dict, List, Tuple

# 添加 src 目錄到 Python 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'analysis'))

from google.cloud import bigquery
from google.cloud.exceptions import Forbidden, NotFound

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PermissionVerifier:
    """權限驗證器"""

    def __init__(self):
        self.client = bigquery.Client(project="tagtoo-tracking")
        self.service_account_email = "<EMAIL>"

    def verify_bigquery_read_permissions(self) -> List[Tuple[str, bool, str]]:
        """驗證 BigQuery 讀取權限"""
        results = []

        # 測試來源表格讀取權限
        test_tables = [
            "tagtoo-tracking.event_prod.tagtoo_event",
            "tagtoo-tracking.event_prod.carrefour_offline_transaction_day"
        ]

        for table_id in test_tables:
            try:
                # 嘗試查詢表格 schema
                table = self.client.get_table(table_id)
                results.append((f"讀取 {table_id}", True, f"✅ 成功讀取表格 schema，共 {len(table.schema)} 個欄位"))
            except Forbidden:
                results.append((f"讀取 {table_id}", False, "❌ 權限不足"))
            except NotFound:
                results.append((f"讀取 {table_id}", False, "❌ 表格不存在"))
            except Exception as e:
                results.append((f"讀取 {table_id}", False, f"❌ 錯誤: {e}"))

        return results

    def verify_bigquery_write_permissions(self) -> List[Tuple[str, bool, str]]:
        """驗證 BigQuery 寫入權限"""
        results = []

        # 測試目標表格寫入權限
        target_table = "tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_20250828"

        try:
            # 嘗試查詢目標表格
            table = self.client.get_table(target_table)
            results.append((f"讀取 {target_table}", True, f"✅ 成功讀取目標表格"))

            # 測試寫入權限（dry run）
            query = f"""
            SELECT
                'test_permanent' as permanent,
                'tm:c_715_pc_test' as segment_id,
                CURRENT_TIMESTAMP() as created_at,
                'test' as source_type,
                'permission_test' as source_entity,
                'test_execution' as execution_id
            """

            job_config = bigquery.QueryJobConfig(
                destination=target_table,
                write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
                dry_run=True
            )

            query_job = self.client.query(query, job_config=job_config)
            results.append((f"寫入 {target_table}", True, "✅ 寫入權限驗證成功 (dry run)"))

        except Forbidden:
            results.append((f"寫入 {target_table}", False, "❌ 寫入權限不足"))
        except NotFound:
            results.append((f"寫入 {target_table}", False, "❌ 目標表格不存在"))
        except Exception as e:
            results.append((f"寫入 {target_table}", False, f"❌ 錯誤: {e}"))

        return results

    def verify_bigquery_job_permissions(self) -> List[Tuple[str, bool, str]]:
        """驗證 BigQuery Job 執行權限"""
        results = []

        try:
            # 測試簡單查詢執行權限
            query = "SELECT 1 as test_value"
            job_config = bigquery.QueryJobConfig(dry_run=True)
            query_job = self.client.query(query, job_config=job_config)

            results.append(("BigQuery Job 執行", True, "✅ 查詢執行權限正常"))

        except Forbidden:
            results.append(("BigQuery Job 執行", False, "❌ Job 執行權限不足"))
        except Exception as e:
            results.append(("BigQuery Job 執行", False, f"❌ 錯誤: {e}"))

        return results

    def verify_service_account_info(self) -> Dict[str, str]:
        """取得 Service Account 資訊"""
        try:
            # 透過 BigQuery 客戶端取得認證資訊
            credentials = self.client._credentials

            info = {
                "專案ID": self.client.project,
                "Service Account": getattr(credentials, 'service_account_email', 'Unknown'),
                "認證類型": type(credentials).__name__
            }

            return info

        except Exception as e:
            return {"錯誤": str(e)}

    def run_full_verification(self) -> bool:
        """執行完整權限驗證"""
        print("=" * 60)
        print("🔐 家樂福離線資料系統權限驗證")
        print("=" * 60)

        # Service Account 資訊
        print("\n📋 Service Account 資訊:")
        sa_info = self.verify_service_account_info()
        for key, value in sa_info.items():
            print(f"  {key}: {value}")

        # 權限測試
        all_tests = []

        print("\n📖 BigQuery 讀取權限測試:")
        read_tests = self.verify_bigquery_read_permissions()
        for test_name, success, message in read_tests:
            print(f"  {message}")
            all_tests.append(success)

        print("\n📝 BigQuery 寫入權限測試:")
        write_tests = self.verify_bigquery_write_permissions()
        for test_name, success, message in write_tests:
            print(f"  {message}")
            all_tests.append(success)

        print("\n⚙️ BigQuery Job 執行權限測試:")
        job_tests = self.verify_bigquery_job_permissions()
        for test_name, success, message in job_tests:
            print(f"  {message}")
            all_tests.append(success)

        # 總結
        total_tests = len(all_tests)
        passed_tests = sum(all_tests)

        print("\n" + "=" * 60)
        print(f"📊 權限驗證結果: {passed_tests}/{total_tests} 項測試通過")

        if all(all_tests):
            print("🎉 所有權限驗證通過！系統可以正常運行。")
            return True
        else:
            print("⚠️ 部分權限驗證失敗，請檢查 Service Account 配置。")
            return False

def main():
    """主函數"""
    verifier = PermissionVerifier()
    success = verifier.run_full_verification()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
