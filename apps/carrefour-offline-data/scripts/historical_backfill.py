#!/usr/bin/env python3
"""
家樂福離線資料歷史回填腳本

此腳本用於回填 2025-08-15 到 2025-08-27 期間的家樂福受眾標籤資料。
使用一次查詢獲取所有歷史 mobile 用戶，然後按日期分組處理，
最終寫入 special_lta_temp_for_update_20250828 表格。

成本優化：
- 單次查詢 vs 13 次獨立查詢
- 預估成本：~$0.5 USD vs $4.81 USD (節省 90%)
- 執行時間：3-5 分鐘 vs 32.5 分鐘
"""

import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

from google.cloud import bigquery

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# BigQuery 設定
PROJECT_ID = "tagtoo-tracking"  # 使用有權限的專案
DATASET_ID = "tagtoo_export_results"
TARGET_TABLE = "special_lta_temp_for_update_20250828"
TARGET_PROJECT = "tagtoo-ml-workflow"  # 目標專案

# 回填日期範圍
START_DATE = "2025-08-15"  # mobile 功能上線日期
END_DATE = "2025-08-27"    # 昨天


def get_historical_mobile_users() -> str:
    """
    獲取歷史 mobile 用戶的 SQL 查詢

    Returns:
        str: BigQuery SQL 查詢語句
    """
    return f"""
    WITH historical_mobile_users AS (
      SELECT DISTINCT
        permanent,
        mobile,
        DATE(latest_entity_time) as activity_date
      FROM `tagtoo-tracking.event_prod.tagtoo_entity`
      WHERE ec_id = 715
        AND mobile IS NOT NULL
        AND latest_entity_time >= '{START_DATE}'
        AND latest_entity_time < '{END_DATE}'
    ),
    carrefour_offline_data AS (
      SELECT DISTINCT
        TO_HEX(off.ph_id) as mobile,
        item.SUB_CLASS_KEY
      FROM `tagtoo-tracking.event_prod.carrefour_offline_transaction_day` off
      JOIN UNNEST(off.items) as item
      WHERE item.SUB_CLASS_KEY IS NOT NULL
        AND item.SUB_CLASS_KEY != ''
    ),
    hierarchical_segments AS (
      SELECT
        h.permanent,
        h.activity_date,
        c.SUB_CLASS_KEY
      FROM historical_mobile_users h
      INNER JOIN carrefour_offline_data c ON h.mobile = c.mobile
    ),
    audience_mapping AS (
      SELECT
        permanent,
        activity_date,
        ARRAY_AGG(DISTINCT segment_id) as segment_ids
      FROM (
        -- 小分類標籤 (完整 SUB_CLASS_KEY)
        SELECT
          permanent,
          activity_date,
          CONCAT('tm:c_715_pc_', SUB_CLASS_KEY) as segment_id
        FROM hierarchical_segments
        WHERE SUB_CLASS_KEY IS NOT NULL AND SUB_CLASS_KEY != ''

        UNION ALL

        -- 中分類標籤 (前4位數字)
        SELECT
          permanent,
          activity_date,
          CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 4)) as segment_id
        FROM hierarchical_segments
        WHERE SUB_CLASS_KEY IS NOT NULL
          AND SUB_CLASS_KEY != ''
          AND LENGTH(SUB_CLASS_KEY) >= 4

        UNION ALL

        -- 大分類標籤 (前3位數字)
        SELECT
          permanent,
          activity_date,
          CONCAT('tm:c_715_pc_', SUBSTR(SUB_CLASS_KEY, 1, 3)) as segment_id
        FROM hierarchical_segments
        WHERE SUB_CLASS_KEY IS NOT NULL
          AND SUB_CLASS_KEY != ''
          AND LENGTH(SUB_CLASS_KEY) >= 3
      )
      GROUP BY permanent, activity_date
    )
    SELECT
      permanent,
      activity_date,
      ARRAY_TO_STRING(ARRAY(SELECT * FROM UNNEST(segment_ids) ORDER BY 1), ',') as segment_id
    FROM audience_mapping
    WHERE ARRAY_LENGTH(segment_ids) > 0
    ORDER BY activity_date, permanent
    """


def create_target_table_if_not_exists(client: bigquery.Client) -> None:
    """
    建立目標表格（如果不存在）

    Args:
        client: BigQuery 客戶端
    """
    table_id = f"{TARGET_PROJECT}.{DATASET_ID}.{TARGET_TABLE}"

    # 檢查表格是否存在
    try:
        client.get_table(table_id)
        logger.info(f"表格 {table_id} 已存在")
        return
    except Exception:
        logger.info(f"表格 {table_id} 不存在，開始建立...")

    # 建立表格
    schema = [
        bigquery.SchemaField("permanent", "STRING", description="唯一的使用者識別碼"),
        bigquery.SchemaField("segment_id", "STRING", description="受眾標籤或區隔識別碼"),
        bigquery.SchemaField("created_at", "TIMESTAMP", description="受眾包生成時間"),
        bigquery.SchemaField("source_type", "STRING", description="資料來源類型"),
        bigquery.SchemaField("source_entity", "STRING", description="產生此筆資料的具體元件"),
        bigquery.SchemaField("execution_id", "STRING", description="該次執行的唯一識別碼"),
    ]

    table = bigquery.Table(table_id, schema=schema)
    table.clustering_fields = ["source_type", "segment_id"]
    table.description = "每日暫存的 LTA 受眾資料表，包含詳細的可追溯性欄位"

    table = client.create_table(table)
    logger.info(f"成功建立表格 {table_id}")


def execute_historical_backfill(dry_run: bool = True) -> Dict:
    """
    執行歷史回填

    Args:
        dry_run: 是否為測試模式

    Returns:
        Dict: 執行結果
    """
    start_time = datetime.now()
    execution_id = f"historical_backfill_{start_time.strftime('%Y%m%d_%H%M%S')}"

    logger.info(f"開始歷史回填 (dry_run={dry_run})")
    logger.info(f"執行 ID: {execution_id}")
    logger.info(f"回填日期範圍: {START_DATE} 到 {END_DATE}")

    # 初始化 BigQuery 客戶端
    client = bigquery.Client(project=PROJECT_ID)

    # 獲取歷史資料查詢
    query = get_historical_mobile_users()

    if dry_run:
        # 測試模式：只估算成本
        job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
        query_job = client.query(query, job_config=job_config)

        processed_bytes = query_job.total_bytes_processed
        estimated_cost = processed_bytes / (1024**4) * 5  # $5 per TB

        logger.info(f"查詢將處理 {processed_bytes:,} bytes")
        logger.info(f"預估成本: ${estimated_cost:.4f} USD")

        return {
            "dry_run": True,
            "processed_bytes": processed_bytes,
            "estimated_cost": estimated_cost,
            "execution_id": execution_id
        }

    # 實際執行
    logger.info("執行歷史資料查詢...")
    query_job = client.query(query)
    results = query_job.result()

    # 建立目標表格
    create_target_table_if_not_exists(client)

    # 準備插入資料
    table_id = f"{TARGET_PROJECT}.{DATASET_ID}.{TARGET_TABLE}"
    table = client.get_table(table_id)

    rows_to_insert = []
    daily_stats = {}

    for row in results:
        permanent = row.permanent
        activity_date = row.activity_date
        segment_id = row.segment_id

        # 統計每日資料
        date_str = activity_date.strftime('%Y-%m-%d')
        if date_str not in daily_stats:
            daily_stats[date_str] = {"count": 0, "unique_users": set()}

        daily_stats[date_str]["count"] += 1
        daily_stats[date_str]["unique_users"].add(permanent)

        # 準備插入的資料
        rows_to_insert.append({
            "permanent": permanent,
            "segment_id": segment_id,
            "created_at": start_time.isoformat(),  # 轉換為 ISO 格式字串
            "source_type": "carrefour_offline_purchase",
            "source_entity": "historical_backfill_script",
            "execution_id": execution_id
        })

    # 批次插入資料
    if rows_to_insert:
        logger.info(f"開始批次插入 {len(rows_to_insert)} 筆資料到 {table_id}")

        # 分批插入，每批 1000 筆
        batch_size = 1000
        total_batches = (len(rows_to_insert) + batch_size - 1) // batch_size

        for i in range(0, len(rows_to_insert), batch_size):
            batch_num = i // batch_size + 1
            batch_data = rows_to_insert[i:i + batch_size]

            logger.info(f"插入批次 {batch_num}/{total_batches} ({len(batch_data)} 筆資料)")

            errors = client.insert_rows_json(table, batch_data)

            if errors:
                logger.error(f"批次 {batch_num} 插入失敗: {errors}")
                raise Exception(f"批次 {batch_num} 資料插入失敗: {errors}")

        logger.info("所有資料批次插入成功")
    else:
        logger.warning("沒有找到可插入的資料")

    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()

    # 轉換統計資料
    for date_str in daily_stats:
        daily_stats[date_str]["unique_users"] = len(daily_stats[date_str]["unique_users"])

    result = {
        "dry_run": False,
        "execution_id": execution_id,
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "execution_time_seconds": execution_time,
        "total_records": len(rows_to_insert),
        "daily_stats": daily_stats,
        "target_table": table_id,
        "status": "completed"
    }

    logger.info(f"歷史回填完成，耗時 {execution_time:.1f} 秒")
    logger.info(f"總共處理 {len(rows_to_insert)} 筆記錄")

    return result


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="家樂福離線資料歷史回填")
    parser.add_argument("--dry_run", action="store_true", help="測試模式，只估算成本")
    parser.add_argument("--execute", action="store_true", help="實際執行回填")

    args = parser.parse_args()

    if not args.dry_run and not args.execute:
        print("請指定 --dry_run 或 --execute 參數")
        sys.exit(1)

    try:
        result = execute_historical_backfill(dry_run=args.dry_run)

        print("\n=== 執行結果 ===")
        if result["dry_run"]:
            print(f"預估查詢成本: ${result['estimated_cost']:.4f} USD")
            print(f"將處理資料量: {result['processed_bytes']:,} bytes")
        else:
            print(f"執行狀態: {result['status']}")
            print(f"總處理記錄: {result['total_records']:,}")
            print(f"執行時間: {result['execution_time_seconds']:.1f} 秒")
            print(f"目標表格: {result['target_table']}")

            print("\n每日統計:")
            for date, stats in result["daily_stats"].items():
                print(f"  {date}: {stats['count']} 記錄, {stats['unique_users']} 唯一用戶")

    except Exception as e:
        logger.error(f"執行失敗: {e}")
        sys.exit(1)
