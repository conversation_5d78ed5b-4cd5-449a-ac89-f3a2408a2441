#!/bin/bash
# 整理 Cloud Storage 資料夾結構
# 基於診斷結果和修正分析，建立清晰的階層結構

set -e

echo "🗂️  整理 Cloud Storage 資料夾結構"
echo "============================================================"

# 設定變數
BUCKET="integrated_event"
BASE_PATH="carrefour_offline_data"

# 建立新的目錄結構
echo "📁 建立新目錄結構..."
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/analysis/ || true
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/analysis/final/ || true
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/analysis/diagnostic/ || true
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/analysis/preliminary/ || true
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/archive/ || true
gsutil -m mkdir gs://$BUCKET/$BASE_PATH/legacy/ || true

echo "✅ 目錄結構建立完成"

# 移動最終分析報告到 analysis/final/
echo "📊 移動最終分析報告..."
gsutil mv gs://$BUCKET/$BASE_PATH/final_analysis/* gs://$BUCKET/$BASE_PATH/analysis/final/ || true
gsutil rmdir gs://$BUCKET/$BASE_PATH/final_analysis/ || true

# 移動診斷報告 (如果存在)
echo "🔍 移動診斷相關檔案..."
gsutil mv gs://$BUCKET/$BASE_PATH/reports/data_range_diagnostic_report.json gs://$BUCKET/$BASE_PATH/analysis/diagnostic/ 2>/dev/null || true

# 移動初步分析到 preliminary/
echo "📋 移動初步分析報告..."
gsutil mv gs://$BUCKET/$BASE_PATH/reports/comprehensive_overlap_analysis.json gs://$BUCKET/$BASE_PATH/analysis/preliminary/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/real_overlap_analysis_report.json gs://$BUCKET/$BASE_PATH/analysis/preliminary/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/ph_id_enhanced_interactive_report.json gs://$BUCKET/$BASE_PATH/analysis/preliminary/ 2>/dev/null || true

# 歸檔舊版本報告
echo "📦 歸檔舊版本報告..."
gsutil mv gs://$BUCKET/$BASE_PATH/reports/ph_id_interactive_report_v2.json gs://$BUCKET/$BASE_PATH/archive/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/ph_id_overlap_validation_report.json gs://$BUCKET/$BASE_PATH/archive/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/ph_id_analysis_interactive_data.json gs://$BUCKET/$BASE_PATH/archive/ 2>/dev/null || true

# 移動文檔到 legacy/
echo "📚 移動舊版文檔..."
gsutil mv gs://$BUCKET/$BASE_PATH/reports/README.md gs://$BUCKET/$BASE_PATH/legacy/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/ph_id_format_analysis_report.md gs://$BUCKET/$BASE_PATH/legacy/ 2>/dev/null || true
gsutil mv gs://$BUCKET/$BASE_PATH/reports/schema_contract_validation_report.md gs://$BUCKET/$BASE_PATH/legacy/ 2>/dev/null || true

# 刪除過時檔案
echo "🗑️  刪除過時檔案..."
gsutil rm gs://$BUCKET/$BASE_PATH/reports/carrefour_data_analysis_20250818_*.* 2>/dev/null || true
gsutil rm gs://$BUCKET/$BASE_PATH/reports/carrefour_data_analysis_dashboard*.html 2>/dev/null || true
gsutil rm gs://$BUCKET/$BASE_PATH/reports/schema_analysis_20250818_*.* 2>/dev/null || true
gsutil rm gs://$BUCKET/$BASE_PATH/reports/schema_validation_report_20250818_*.* 2>/dev/null || true
gsutil rm gs://$BUCKET/$BASE_PATH/reports/environment_setup_report.* 2>/dev/null || true

# 保留重要的當前檔案在 reports/ 根目錄
echo "📌 保留重要檔案在根目錄..."
# project_completion_summary.md, index.html, project_metadata.json 等保持在 reports/ 目錄

# 建立新的索引檔案
echo "📄 建立新的目錄索引..."
cat > /tmp/directory_structure.md << 'EOF'
# 家樂福離線資料分析 - 目錄結構

> 更新日期: 2025-08-19
> 狀態: 已完成資料範圍診斷和修正分析

## 📁 目錄結構

### `/analysis/final/` - 最終分析結果
- `corrected_overlap_analysis.json` - 修正後的重疊率分析 (基於資料範圍診斷)
- `corrected_analysis_summary.md` - 分析總結文檔
- `full_overlap_analysis_100_percent.json` - 100% 抽樣率分析結果
- `data_range_diagnostic_report.json` - 資料範圍診斷報告

### `/analysis/diagnostic/` - 診斷分析
- 資料範圍和品質診斷相關檔案

### `/analysis/preliminary/` - 初步分析
- `comprehensive_overlap_analysis.json` - 統計抽樣分析
- `real_overlap_analysis_report.json` - 小樣本真實分析
- `ph_id_enhanced_interactive_report.json` - 增強版互動報告

### `/archive/` - 歸檔檔案
- 舊版本的分析報告和資料檔案

### `/legacy/` - 舊版文檔
- 早期的文檔和說明檔案

### `/reports/` - 根目錄 (重要檔案)
- `project_completion_summary.md` - 專案完成總結
- `index.html` - 主要索引頁面
- `project_metadata.json` - 專案元資料

## 🎯 關鍵發現

**重要**: 家樂福線上資料僅包含最近 7天 的用戶活動資料，因此：
- ✅ 1-7天的重疊率分析是有效的
- ❌ 7天以上的分析結果是無效的 (資料重複)

**有效重疊率**: 2.379% (71,014 筆用戶，基於7天資料)

## 📊 建議使用的檔案

1. **業務決策**: `/analysis/final/corrected_analysis_summary.md`
2. **技術細節**: `/analysis/final/corrected_overlap_analysis.json`
3. **完整數據**: `/analysis/final/full_overlap_analysis_100_percent.json`
4. **診斷資訊**: `/analysis/final/data_range_diagnostic_report.json`

---
*目錄結構更新時間: 2025-08-19*
EOF

gsutil cp /tmp/directory_structure.md gs://$BUCKET/$BASE_PATH/

echo "✅ Cloud Storage 整理完成"
echo ""
echo "📊 新的目錄結構:"
echo "gs://$BUCKET/$BASE_PATH/"
echo "├── analysis/"
echo "│   ├── final/          # 最終分析結果"
echo "│   ├── diagnostic/     # 診斷分析"
echo "│   └── preliminary/    # 初步分析"
echo "├── archive/            # 歸檔檔案"
echo "├── legacy/             # 舊版文檔"
echo "└── reports/            # 重要檔案"
echo ""
echo "🌐 主要存取點:"
echo "📊 最終分析: gs://$BUCKET/$BASE_PATH/analysis/final/"
echo "📄 目錄說明: gs://$BUCKET/$BASE_PATH/directory_structure.md"

# 驗證結構
echo ""
echo "🔍 驗證新結構:"
gsutil ls gs://$BUCKET/$BASE_PATH/analysis/final/
