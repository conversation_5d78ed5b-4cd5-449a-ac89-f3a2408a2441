#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CI/CD Service Account 權限檢查腳本

檢查 GitHub Actions 中使用的 Service Account 是否具備部署所需的權限。
"""

import subprocess
import json
import sys


def run_command(cmd):
    """執行 gcloud 指令並返回結果"""
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return {
            "success": True,
            "output": result.stdout.strip(),
            "error": None
        }
    except subprocess.CalledProcessError as e:
        return {
            "success": False,
            "output": None,
            "error": e.stderr.strip()
        }


def check_service_account_permissions():
    """檢查 CI/CD Service Account 權限"""

    # CI/CD Service Account
    service_account = "<EMAIL>"
    project_id = "tagtoo-tracking"

    print(f"🔍 檢查 Service Account: {service_account}")
    print(f"📋 專案: {project_id}")
    print("=" * 80)

    # 需要的權限清單
    required_roles = [
        "roles/storage.objectViewer",
        "roles/cloudfunctions.admin",
        "roles/cloudscheduler.admin",
        "roles/monitoring.admin",
        "roles/iam.serviceAccountUser",
        "roles/resourcemanager.projectIamAdmin"
    ]

    # 檢查專案層級權限
    print("\n📊 檢查專案層級 IAM 權限...")
    cmd = [
        "gcloud", "projects", "get-iam-policy", project_id,
        "--flatten=bindings[].members",
        "--format=table(bindings.role)",
        f"--filter=bindings.members:serviceAccount:{service_account}"
    ]

    result = run_command(cmd)
    if result["success"]:
        current_roles = result["output"].split('\n')[1:]  # 跳過標題行
        current_roles = [role.strip() for role in current_roles if role.strip()]

        print(f"✅ 當前擁有的角色 ({len(current_roles)} 個):")
        for role in current_roles:
            print(f"   - {role}")

        print(f"\n🎯 需要的角色 ({len(required_roles)} 個):")
        missing_roles = []
        for role in required_roles:
            if role in current_roles:
                print(f"   ✅ {role}")
            else:
                print(f"   ❌ {role} (缺少)")
                missing_roles.append(role)

        if missing_roles:
            print(f"\n⚠️  缺少 {len(missing_roles)} 個必要權限:")
            print("\n🔧 修正指令:")
            for role in missing_roles:
                print(f"gcloud projects add-iam-policy-binding {project_id} \\")
                print(f"  --member=\"serviceAccount:{service_account}\" \\")
                print(f"  --role=\"{role}\"")
                print()
        else:
            print("\n✅ 所有必要的專案權限都已配置！")
    else:
        print(f"❌ 無法檢查專案權限: {result['error']}")

    # 檢查 Storage Bucket 權限
    print("\n📦 檢查 Terraform State Bucket 權限...")
    bucket_name = "tagtoo-tracking-terraform-state"

    cmd = [
        "gsutil", "iam", "get", f"gs://{bucket_name}"
    ]

    result = run_command(cmd)
    if result["success"]:
        try:
            bucket_iam = json.loads(result["output"])
            has_bucket_access = False

            for binding in bucket_iam.get("bindings", []):
                if f"serviceAccount:{service_account}" in binding.get("members", []):
                    role = binding.get("role", "")
                    if "storage" in role.lower():
                        print(f"   ✅ {role}")
                        has_bucket_access = True

            if not has_bucket_access:
                print(f"   ❌ 缺少 Storage Bucket 存取權限")
                print(f"\n🔧 修正指令:")
                print(f"gsutil iam ch serviceAccount:{service_account}:legacyBucketReader gs://{bucket_name}")
                print(f"gsutil iam ch serviceAccount:{service_account}:objectViewer gs://{bucket_name}")
            else:
                print("   ✅ Storage Bucket 權限已配置")

        except json.JSONDecodeError:
            print(f"❌ 無法解析 Bucket IAM 政策")
    else:
        print(f"❌ 無法檢查 Bucket 權限: {result['error']}")

    # 檢查 API 啟用狀態
    print("\n🔌 檢查必要的 API 啟用狀態...")
    required_apis = [
        "cloudfunctions.googleapis.com",
        "cloudscheduler.googleapis.com",
        "monitoring.googleapis.com",
        "storage.googleapis.com",
        "iam.googleapis.com"
    ]

    for api in required_apis:
        cmd = [
            "gcloud", "services", "list",
            "--enabled",
            f"--filter=name:{api}",
            "--format=value(name)"
        ]

        result = run_command(cmd)
        if result["success"] and result["output"]:
            print(f"   ✅ {api}")
        else:
            print(f"   ❌ {api} (未啟用)")

    print("\n" + "=" * 80)
    print("🎯 權限檢查完成！")
    print("💡 如果發現缺少權限，請執行上述修正指令")


if __name__ == "__main__":
    check_service_account_permissions()
