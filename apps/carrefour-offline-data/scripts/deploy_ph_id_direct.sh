#!/bin/bash
# 家樂福 ph_id 直接處理功能部署腳本
#
# 此腳本用於部署新的 ph_id 直接處理功能到 Cloud Function
#
# 使用方式:
#   ./deploy_ph_id_direct.sh [環境] [選項]
#
# 環境: dev, staging, prod
# 選項: --dry-run, --force, --skip-tests
#
# Author: AI Assistant
# Date: 2025-09-12
# Version: 1.0.0

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 預設值
ENVIRONMENT="dev"
DRY_RUN=false
FORCE=false
SKIP_TESTS=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 顯示使用說明
show_usage() {
    cat << EOF
家樂福 ph_id 直接處理功能部署腳本

使用方式:
    $0 [環境] [選項]

環境:
    dev      開發環境 (預設)
    staging  測試環境
    prod     生產環境

選項:
    --dry-run     測試模式，不實際部署
    --force       強制部署，跳過確認
    --skip-tests  跳過測試
    --help        顯示此說明

範例:
    $0 dev --dry-run
    $0 prod --force
    $0 staging

EOF
}

# 解析命令列參數
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|staging|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知參數: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 檢查必要工具
check_prerequisites() {
    log_info "檢查必要工具..."

    local tools=("gcloud" "python3" "pip" "jq")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done

    # 檢查 gcloud 認證
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "請先執行 gcloud auth login"
        exit 1
    fi

    log_success "必要工具檢查完成"
}

# 載入環境配置
load_environment_config() {
    log_info "載入 $ENVIRONMENT 環境配置..."

    local config_file="$PROJECT_ROOT/config/ph_id_direct_config.json"
    if [[ ! -f "$config_file" ]]; then
        log_error "配置檔案不存在: $config_file"
        exit 1
    fi

    # 使用 jq 解析配置
    if ! jq -e ".environments.$ENVIRONMENT" "$config_file" > /dev/null; then
        log_error "環境配置不存在: $ENVIRONMENT"
        exit 1
    fi

    # 設定環境變數
    export GCS_BUCKET=$(jq -r ".environments.$ENVIRONMENT.gcs_bucket" "$config_file")
    export PUBSUB_TOPIC=$(jq -r ".environments.$ENVIRONMENT.pubsub_topic" "$config_file")
    export SOURCE_TABLE=$(jq -r ".environments.$ENVIRONMENT.source_table" "$config_file")

    log_success "環境配置載入完成"
    log_info "  GCS Bucket: $GCS_BUCKET"
    log_info "  Pub/Sub Topic: $PUBSUB_TOPIC"
    log_info "  Source Table: $SOURCE_TABLE"
}

# 執行測試
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "跳過測試"
        return 0
    fi

    log_info "執行測試..."

    cd "$PROJECT_ROOT"

    # 檢查 Python 語法
    log_info "檢查 Python 語法..."
    python3 -m py_compile src/analysis/ph_id_direct_processor.py
    python3 -m py_compile src/tools/gcs_uploader.py
    python3 -m py_compile src/tools/pubsub_notifier.py

    # 執行單元測試 (如果存在)
    if [[ -f "tests/test_ph_id_direct.py" ]]; then
        log_info "執行單元測試..."
        python3 -m pytest tests/test_ph_id_direct.py -v
    fi

    # 執行 dry run 測試
    log_info "執行 dry run 測試..."
    python3 -m src.analysis.ph_id_direct_processor \
        --target-date "2025-09-11" \
        --dry-run \
        --config "config/ph_id_direct_config.json"

    log_success "測試完成"
}

# 建立 Cloud Function 部署包
create_deployment_package() {
    log_info "建立部署包..."

    local temp_dir=$(mktemp -d)
    local deployment_dir="$temp_dir/deployment"

    mkdir -p "$deployment_dir"

    # 複製必要檔案
    cp "$PROJECT_ROOT/main.py" "$deployment_dir/"
    cp "$PROJECT_ROOT/requirements.txt" "$deployment_dir/"
    cp -r "$PROJECT_ROOT/src" "$deployment_dir/"
    cp -r "$PROJECT_ROOT/config" "$deployment_dir/"

    # 建立 zip 檔案
    cd "$deployment_dir"
    zip -r "$PROJECT_ROOT/deployment/ph_id_direct_function.zip" .

    # 清理暫存目錄
    rm -rf "$temp_dir"

    log_success "部署包建立完成: deployment/ph_id_direct_function.zip"
}

# 部署 Cloud Function
deploy_cloud_function() {
    log_info "部署 Cloud Function..."

    local function_name="carrefour-offline-data-prod"
    local region="asia-east1"
    local project_id

    # 根據環境設定專案 ID
    case $ENVIRONMENT in
        dev)
            project_id="tagtoo-ml-workflow-dev"
            ;;
        staging)
            project_id="tagtoo-ml-workflow-staging"
            ;;
        prod)
            project_id="tagtoo-ml-workflow"
            ;;
    esac

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: 將部署到 $project_id/$function_name"
        return 0
    fi

    # 部署 Cloud Function
    gcloud functions deploy "$function_name" \
        --gen2 \
        --runtime=python311 \
        --region="$region" \
        --source="$PROJECT_ROOT/deployment/ph_id_direct_function.zip" \
        --entry-point=main \
        --trigger=http \
        --memory=4096MB \
        --timeout=900s \
        --max-instances=10 \
        --set-env-vars="ENVIRONMENT=$ENVIRONMENT" \
        --project="$project_id"

    log_success "Cloud Function 部署完成"
}

# 建立 Cloud Scheduler 任務
create_scheduler_job() {
    log_info "建立 Cloud Scheduler 任務..."

    local job_name="carrefour-ph-id-direct-$ENVIRONMENT"
    local schedule="0 11 * * *"  # 每日台灣時間 11:00
    local function_url

    # 根據環境設定 Function URL
    case $ENVIRONMENT in
        dev)
            function_url="https://asia-east1-tagtoo-ml-workflow-dev.cloudfunctions.net/carrefour-offline-data-prod"
            ;;
        staging)
            function_url="https://asia-east1-tagtoo-ml-workflow-staging.cloudfunctions.net/carrefour-offline-data-prod"
            ;;
        prod)
            function_url="https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/carrefour-offline-data-prod"
            ;;
    esac

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: 將建立排程任務 $job_name"
        return 0
    fi

    # 建立排程任務
    gcloud scheduler jobs create http "$job_name" \
        --schedule="$schedule" \
        --uri="$function_url" \
        --http-method=POST \
        --headers="Content-Type=application/json" \
        --message-body='{"operation_type":"ph_id_direct","execution_mode":"scheduled"}' \
        --time-zone="Asia/Taipei" \
        --description="家樂福 ph_id 直接處理排程任務"

    log_success "Cloud Scheduler 任務建立完成"
}

# 驗證部署
verify_deployment() {
    log_info "驗證部署..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: 跳過部署驗證"
        return 0
    fi

    # 測試 Cloud Function
    log_info "測試 Cloud Function..."
    # TODO: 實作 Cloud Function 測試

    # 檢查 Cloud Scheduler
    log_info "檢查 Cloud Scheduler..."
    # TODO: 實作 Scheduler 檢查

    log_success "部署驗證完成"
}

# 主要執行流程
main() {
    log_info "開始部署家樂福 ph_id 直接處理功能"
    log_info "環境: $ENVIRONMENT"
    log_info "模式: $([ "$DRY_RUN" == "true" ] && echo "DRY RUN" || echo "實際部署")"

    # 確認部署
    if [[ "$FORCE" != "true" && "$DRY_RUN" != "true" ]]; then
        echo -n "確定要部署到 $ENVIRONMENT 環境嗎? (y/N): "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi

    # 執行部署步驟
    check_prerequisites
    load_environment_config
    run_tests
    create_deployment_package
    deploy_cloud_function
    create_scheduler_job
    verify_deployment

    log_success "部署完成！"

    if [[ "$DRY_RUN" != "true" ]]; then
        log_info "下一步："
        log_info "1. 檢查 Cloud Function 日誌"
        log_info "2. 測試手動觸發"
        log_info "3. 監控排程執行"
    fi
}

# 解析參數並執行
parse_arguments "$@"
main
