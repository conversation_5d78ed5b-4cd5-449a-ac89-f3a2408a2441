INSERT INTO `tagtoo-tracking.event_prod.integrated_event`
(
  permanent,
  partner_id,
  ec_id,
  partner_source,
  event,
  event_time,
  create_time,
  value,
  currency,
  order_id,
  link,
  user,
  items,
  page,
  location,
  raw_json
)
WITH matched_events AS (
  SELECT
    t1,
    t2,
    ROW_NUMBER() OVER (
      PARTITION BY t1.created_time, t1.hashed_email, t1.hashed_phone
      ORDER BY t2.event_time DESC
    ) AS rn
  FROM `tagtoo-ml-workflow.tagtoo_export_results.2324_firestore_to_bq_prod` AS t1
  LEFT JOIN `tagtoo-tracking.event_prod.tagtoo_event` AS t2
    ON (t1.hashed_email = t2.user.em AND t2.user.em IS NOT NULL)
    OR (t1.hashed_phone = t2.user.ph AND t2.user.ph IS NOT NULL)
  WHERE
    t2.permanent IS NOT NULL
    AND t2.ec_id = 2324
    AND DATE(t2.event_time) = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
)

SELECT
  t2.permanent,
  NULL AS partner_id,
  t1.ec_id,
  'shopify' AS partner_source,
  t1.event_name AS event,
  t2.event_time,
  TIMESTAMP(t1.created_time) AS create_time,
  SAFE_CAST(t1.value AS FLOAT64) AS value,
  t1.currency,
  t2.event.custom_data.order_id AS order_id,
  t2.link,
  STRUCT(
    t2.user.em AS em,
    t2.user.ph AS ph,
    CAST(t1.partner_user_id AS STRING) AS partner_user_id
  ) AS user,
  ARRAY(
    SELECT AS STRUCT
      CAST(id AS STRING) AS id,
      name AS name,
      '' AS description,
      SAFE_CAST(price AS FLOAT64) AS price,
      SAFE_CAST(quantity AS FLOAT64) AS quantity
    FROM UNNEST(t1.item_id) AS id WITH OFFSET AS idx
    LEFT JOIN UNNEST(t1.item_names) AS name WITH OFFSET AS name_idx ON idx = name_idx
    LEFT JOIN UNNEST(t1.item_price) AS price WITH OFFSET AS price_idx ON idx = price_idx
    LEFT JOIN UNNEST(t1.item_quantity) AS quantity WITH OFFSET AS quantity_idx ON idx = quantity_idx
  ) AS items,
  STRUCT(
    '' AS title,
    '' AS description
  ) AS page,
  STRUCT(
    '' AS country_code,
    '' AS region_name,
    '' AS city_name
  ) AS location,
  STRUCT(
    '' AS schema_version,
    JSON '{}'
  ) AS raw_json

FROM matched_events
WHERE rn = 1 