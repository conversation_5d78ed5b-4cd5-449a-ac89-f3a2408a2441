import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any

from flask import Flask, request, jsonify
from google.cloud import firestore, bigquery
from google.cloud.exceptions import NotFound
from google.oauth2 import service_account
import pandas as pd
import pytz

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 環境變數
PROJECT_ID = os.environ.get('PROJECT_ID', 'tagtoo-tracking')
BQ_PROJECT = os.environ.get('BQ_PROJECT', 'tagtoo-ml-workflow')
DATASET_ID = os.environ.get('DATASET_ID', 'tagtoo_export_results')
TABLE_ID = os.environ.get('TABLE_ID', '2324_firestore_to_bq_prod')
COLLECTION_NAME = os.environ.get('COLLECTION_NAME', '2324-purchase-sha256')
KEY_PATH = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')

# 初始化 GCP Clients
db = None
bq_client = None
gcp_init_error = None  # 記錄初始化錯誤供健康檢查使用

def init_gcp_clients():
    """初始化 GCP 客戶端，支援重試"""
    global db, bq_client, gcp_init_error

    try:
        if KEY_PATH and os.path.exists(KEY_PATH):
            credentials = service_account.Credentials.from_service_account_file(KEY_PATH)
            db = firestore.Client(credentials=credentials, project=PROJECT_ID)
            bq_client = bigquery.Client(credentials=credentials, project=BQ_PROJECT)
        else:
            db = firestore.Client(project=PROJECT_ID)
            bq_client = bigquery.Client(project=BQ_PROJECT)

        gcp_init_error = None  # 清除錯誤狀態
        logger.info("GCP clients 初始化成功")
        return True
    except Exception as e:
        gcp_init_error = str(e)
        logger.warning(f"GCP client 初始化失敗: {e}")
        logger.info("將在首次使用時重新嘗試初始化 GCP clients")
        return False

# 只在非測試環境中初始化 GCP 客戶端
if not os.environ.get('TESTING'):
    init_gcp_clients()

try:
    tz_tw = pytz.timezone('Asia/Taipei')
except Exception as e:
    logger.error(f"時區初始化失敗: {e}")
    tz_tw = pytz.UTC  # fallback

class ShopifyWebhookProcessor:
    def __init__(self):
        self.collection_name = COLLECTION_NAME
        # 在測試環境中，bq_client 可能為 None
        if bq_client is not None:
            dataset_ref = bigquery.DatasetReference(BQ_PROJECT, DATASET_ID)
            self.table_ref = dataset_ref.table(TABLE_ID)
        else:
            self.table_ref = None

    def fetch_firestore_data(self, target_date: str):
        tz_tw = pytz.timezone('Asia/Taipei')
        # 解析 target_date 成為帶時區的 datetime
        try:
            day_start = datetime.strptime(target_date, "%Y-%m-%d").replace(tzinfo=tz_tw)
        except Exception:
            logger.warning("日期格式錯誤，應為 YYYY-MM-DD")
            return []
        
        day_end = day_start + timedelta(days=1)

        #logger.info(f"查詢 created_at >= {day_start.isoformat()}")
        #logger.info(f"查詢 created_at <  {day_end.isoformat()}")

        
        new_data = []
        try:
            if db is None:
                logger.warning("Firestore client 未初始化，返回空結果")
                return []
            docs = db.collection(self.collection_name) \
                .where("created_at", ">=", day_start.isoformat()) \
                .where("created_at", "<", day_end.isoformat()) \
                .stream()
            
        except Exception as e:
            logger.error(f"Firestore stream 發生錯誤: {e}")
            return []
        
        for doc in docs:
            doc_data = doc.to_dict()
            if not doc_data:
                continue
            created_at_str = doc_data.get("created_at")
            if not created_at_str:
                continue

            try:
                created_at = datetime.fromisoformat(created_at_str)
                created_at_tw = created_at.astimezone(tz_tw)
                #logger.info(f"created_at: {created_at_tw.isoformat()}")

            except Exception as e:
                logger.warning("無法解析時間: %s - %s", created_at_str, e)
                continue

            total_price_set = doc_data.get("total_price", {})
            value = total_price_set.get("shop_money", {}).get("amount")
            currency = total_price_set.get("shop_money", {}).get("currency_code")
            line_items = doc_data.get("products", [])
            item_titles = [item.get("title") for item in line_items if isinstance(item, dict)]
            item_ids = [item.get("product_id") for item in line_items if isinstance(item, dict)]
            item_quantity = [item.get("quantity") for item in line_items if isinstance(item, dict)]
            item_price = [item.get("price") for item in line_items if isinstance(item, dict)]
            flat_data = {
                "ec_id": doc_data.get("ec_id"),
                "partner_user_id": doc_data.get("customer_id"),
                "hashed_email": doc_data.get("email"),
                "hashed_phone": doc_data.get("phone"),
                "hashed_gphone": doc_data.get("gphone"),
                "value": value,
                "currency": currency,
                "item_names": item_titles,
                "item_id": item_ids,
                "item_quantity": item_quantity,
                "item_price": item_price,
                "event_name": "purchase",
                "created_time": created_at_tw.isoformat()
            }

            new_data.append(flat_data)
        logger.info(f"new_data length: {len(new_data)}")
        return new_data

    def sync_to_bigquery(self, data):
        df = pd.DataFrame(data)
        if df.empty:
            return 0
        df['event_name'] = 'purchase'
        list_columns = ['item_names', 'item_id', 'item_quantity', 'item_price']
        for col in list_columns:
            df[col] = df[col].apply(lambda x: [str(i) for i in x] if isinstance(x, list) else [])

        # 在測試環境中，bq_client 可能為 None
        if bq_client is None:
            logger.warning("BigQuery client 未初始化，返回資料長度")
            return len(df)
        
        full_table_id = f"{BQ_PROJECT}.{DATASET_ID}.{TABLE_ID}"
        logger.info(f"BigQuery full_table_id: {full_table_id}")

        try:
            bq_client.get_table(full_table_id)
            logger.info("BigQuery table 已存在: %s", full_table_id)
        except NotFound:
            logger.info("BigQuery table 不存在，將建立新的 table: %s", full_table_id)
            schema = []
            for col, dtype in df.dtypes.items():
                if pd.api.types.is_integer_dtype(dtype):
                    field_type = "INT64"
                elif pd.api.types.is_float_dtype(dtype):
                    field_type = "FLOAT64"
                elif pd.api.types.is_bool_dtype(dtype):
                    field_type = "BOOL"
                elif pd.api.types.is_datetime64_any_dtype(dtype):
                    field_type = "TIMESTAMP"
                elif pd.api.types.is_object_dtype(dtype):
                    sample_val = df[col].dropna().iloc[0] if not df[col].dropna().empty else ""
                    if isinstance(sample_val, list):
                        field_type = "STRING"
                        schema.append(bigquery.SchemaField(col, field_type, mode="REPEATED"))
                        continue
                    else:
                        field_type = "STRING"
                else:
                    field_type = "STRING"
                schema.append(bigquery.SchemaField(col, field_type))
            table = bigquery.Table(full_table_id, schema=schema)
            bq_client.create_table(table)
        job_config = bigquery.LoadJobConfig(write_disposition=bigquery.WriteDisposition.WRITE_APPEND)
        logger.info(f"同步資料到 BigQuery: {full_table_id}")
        job = bq_client.load_table_from_dataframe(df, full_table_id, job_config=job_config)
        job.result()
        return len(df)

processor = ShopifyWebhookProcessor()

@app.route('/health', methods=['GET'])
def health_check():
    """健康檢查端點 - 快速響應，不依賴外部服務"""
    try:
        # 基本狀態檢查
        status = {
            "status": "healthy",
            "service": "shopify-webhook",
            "version": "1.0.0",
            "timestamp": datetime.now(tz_tw).isoformat(),
            "environment": os.environ.get('ENVIRONMENT', 'unknown')
        }

        # 詳細的 GCP 客戶端狀態檢查
        if db is not None:
            status["firestore"] = "available"
        elif gcp_init_error:
            status["firestore"] = "unavailable"
            status["firestore_error"] = gcp_init_error
        else:
            status["firestore"] = "not_initialized"

        if bq_client is not None:
            status["bigquery"] = "available"
        elif gcp_init_error:
            status["bigquery"] = "unavailable"
            status["bigquery_error"] = gcp_init_error
        else:
            status["bigquery"] = "not_initialized"

        # 如果有 GCP 初始化錯誤，嘗試重新初始化一次
        if gcp_init_error and not os.environ.get('TESTING'):
            logger.info("健康檢查時嘗試重新初始化 GCP clients")
            if init_gcp_clients():
                status["gcp_reinit"] = "success"
            else:
                status["gcp_reinit"] = "failed"

        return jsonify(status), 200
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
        return jsonify({
            "status": "error",
            "service": "shopify-webhook",
            "error": str(e),
            "timestamp": datetime.now().isoformat()  # fallback 使用 UTC
        }), 500

@app.route('/sync', methods=['POST'])
def manual_sync():
    # 允許指定日期，預設為昨天
    target_date = request.json.get('date') if request.is_json else None
    if not target_date:
        target_date = (datetime.now(tz_tw) - timedelta(days=1)).strftime("%Y-%m-%d")

    logger.info(f"啟動同步程序，目標日期: {target_date}")  # 新增這行

    data = processor.fetch_firestore_data(target_date)
    logger.info(f"從 Firestore 擷取到 {len(data)} 筆資料")  # 再新增一行

    count = processor.sync_to_bigquery(data)
    logger.info(f"已同步 {count} 筆資料到 BigQuery")  # 新增 log

    return jsonify({
        "status": "success",
        "synced_count": count,
        "date": target_date
    })

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080)
