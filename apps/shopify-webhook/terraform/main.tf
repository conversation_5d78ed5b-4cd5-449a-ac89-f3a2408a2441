# shopify-webhook Terraform 配置
# Cloud Run 服務部署

terraform {
  required_version = ">= 1.12.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shopify-webhook"
  }
}

# 引用共用基礎設施的資料
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

# 變數定義
variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod"
  }
}

variable "deployment_version" {
  description = "部署版本"
  type        = string
  default     = "latest"
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "shopify-webhook"
}

variable "min_instances" {
  description = "最小實例數"
  type        = number
  default     = 0
}

variable "max_instances" {
  description = "最大實例數"
  type        = number
  default     = 10
}

variable "cpu_limit" {
  description = "CPU 限制"
  type        = string
  default     = "1000m"
}

variable "memory_limit" {
  description = "記憶體限制"
  type        = string
  default     = "512Mi"
}

# 本地變數
locals {
  shared_outputs = data.terraform_remote_state.shared.outputs

  service_full_name = "${var.service_name}-${var.environment}"

  common_labels = {
    service     = var.service_name
    environment = var.environment
    managed_by  = "terraform"
    team        = "data-team"
  }

  env_vars = {
    ENVIRONMENT     = var.environment
    PROJECT_ID      = local.shared_outputs.project_id
    BQ_PROJECT      = "tagtoo-ml-workflow"
    DATASET_ID      = "tagtoo_export_results"
    TABLE_ID        = "2324_firestore_to_bq_prod"
    COLLECTION_NAME = "2324-purchase-sha256"
  }
}

# Google Cloud Provider
provider "google" {
  project = local.shared_outputs.project_id
  region  = local.shared_outputs.region
}

# Cloud Run 服務
resource "google_cloud_run_service" "main" {
  name     = local.service_full_name
  location = local.shared_outputs.region

  template {
    metadata {
      labels = local.common_labels

      annotations = {
        "autoscaling.knative.dev/minScale"  = var.min_instances
        "autoscaling.knative.dev/maxScale"  = var.max_instances
        "run.googleapis.com/cpu-throttling" = "false"
      }
    }

    spec {
      service_account_name  = local.shared_outputs.service_account_email
      container_concurrency = 100
      timeout_seconds       = 300

      containers {
        image = "${local.shared_outputs.artifact_registry_repository_url}/${var.service_name}:${var.deployment_version}"

        resources {
          limits = {
            cpu    = var.cpu_limit
            memory = var.memory_limit
          }
        }

        dynamic "env" {
          for_each = local.env_vars
          content {
            name  = env.key
            value = env.value
          }
        }

        # 健康檢查
        liveness_probe {
          http_get {
            path = "/health"
            port = 8080
          }
          initial_delay_seconds = 30
          period_seconds        = 30
          timeout_seconds       = 5
        }

        # 準備檢查
        startup_probe {
          http_get {
            path = "/health"
            port = 8080
          }
          initial_delay_seconds = 0
          period_seconds        = 10
          timeout_seconds       = 5
          failure_threshold     = 10
        }

        ports {
          container_port = 8080
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  lifecycle {
    ignore_changes = [
      template[0].metadata[0].annotations["run.googleapis.com/operation-id"]
    ]
  }
}

# IAM 設定 - 允許未驗證存取 (開發環境)
resource "google_cloud_run_service_iam_member" "noauth" {
  count = var.environment == "dev" ? 1 : 0

  service  = google_cloud_run_service.main.name
  location = google_cloud_run_service.main.location
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Cloud Scheduler (定期同步工作)
resource "google_cloud_scheduler_job" "sync_job" {
  name             = "${local.service_full_name}-sync"
  description      = "shopify-webhook 定期同步工作"
  schedule         = "5 0 * * *" # 每天 00:05 (台灣時間)
  time_zone        = "Asia/Taipei"
  attempt_deadline = "300s"

  http_target {
    uri         = "${google_cloud_run_service.main.status[0].url}/sync"
    http_method = "POST"
    headers = {
      "Content-Type" = "application/json"
    }
    body = base64encode(jsonencode({
      source = "scheduler"
    }))

    oidc_token {
      service_account_email = local.shared_outputs.service_account_email
      audience              = google_cloud_run_service.main.status[0].url
    }
  }

  retry_config {
    retry_count = 3
  }
}

# BigQuery Scheduled Query for Shopify Webhook
resource "google_bigquery_data_transfer_config" "shopify_daily_transfer" {
  display_name         = "Shopify Webhook Daily Data Integration"
  location             = local.shared_outputs.region
  data_source_id       = "scheduled_query"
  schedule             = "every day 00:03" # UTC+8 = 08:03 (可依需求調整)
  service_account_name = local.shared_outputs.service_account_email

  params = {
    query = file("../sql/shopify_daily_query.sql")
  }

}

# 輸出
output "service_url" {
  description = "Cloud Run 服務 URL"
  value       = google_cloud_run_service.main.status[0].url
}

output "service_name" {
  description = "Cloud Run 服務名稱"
  value       = google_cloud_run_service.main.name
}

output "revision" {
  description = "目前部署的版本"
  value       = google_cloud_run_service.main.status[0].latest_created_revision_name
}
