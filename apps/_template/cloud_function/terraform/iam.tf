# {{SERVICE_NAME}} IAM 權限配置
# 實施 Service Account 架構分離設計

# 驗證專案特定 Service Account 存在並可用
resource "null_resource" "verify_service_account" {
  count = var.project_service_account_email != "" ? 1 : 0

  provisioner "local-exec" {
    command = <<-EOT
      echo "驗證專案特定 Service Account 權限..."
      gcloud iam service-accounts describe ${var.project_service_account_email} > /dev/null
      if [ $? -eq 0 ]; then
        echo "✅ 專案特定 Service Account 存在且可存取"
        echo "📧 執行層 Service Account: ${var.project_service_account_email}"
        echo "📧 基礎設施層 Service Account: ${local.infrastructure_service_account_email}"
      else
        echo "❌ 專案特定 Service Account 不存在或無權限存取"
        echo "請先建立 Service Account: ${var.project_service_account_email}"
        exit 1
      fi
    EOT
  }

  triggers = {
    project_service_account_email        = var.project_service_account_email
    infrastructure_service_account_email = local.infrastructure_service_account_email
    shared_outputs_hash                  = md5(jsonencode(local.shared_outputs))
  }
}

# Cloud Function 觸發權限
# 允許基礎設施層 Service Account 透過 OIDC 觸發 Cloud Function
resource "google_cloudfunctions2_function_iam_binding" "invoker" {
  project        = local.shared_outputs.project_id
  location       = local.shared_outputs.region
  cloud_function = "{{SERVICE_NAME_KEBAB}}-${var.environment}"
  role           = "roles/cloudfunctions.invoker"

  members = [
    "serviceAccount:${local.infrastructure_service_account_email}"
  ]

  depends_on = [
    null_resource.verify_service_account
  ]
}

# Cloud Run Service 觸發權限 (Cloud Function Gen2 底層使用 Cloud Run)
# 這是修復 HTTP 403 PERMISSION_DENIED 錯誤的關鍵配置
resource "google_cloud_run_service_iam_binding" "invoker" {
  project  = local.shared_outputs.project_id
  location = local.shared_outputs.region
  service  = "{{SERVICE_NAME_KEBAB}}-${var.environment}"
  role     = "roles/run.invoker"

  members = [
    "serviceAccount:${local.infrastructure_service_account_email}"
  ]

  depends_on = [
    null_resource.verify_service_account
  ]
}

# 跨專案權限配置 (可選)
# 確保專案可以存取目標專案的資源
resource "google_project_iam_member" "cross_project_bigquery_access" {
  count   = var.enable_cross_project_access && var.project_service_account_email != "" ? length(var.target_project_ids) : 0
  project = var.target_project_ids[count.index]
  role    = "roles/bigquery.dataEditor"
  member  = "serviceAccount:${var.project_service_account_email}"
}

resource "google_project_iam_member" "cross_project_bigquery_job_user" {
  count   = var.enable_cross_project_access && var.project_service_account_email != "" ? length(var.target_project_ids) : 0
  project = var.target_project_ids[count.index]
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${var.project_service_account_email}"
}

# 輸出 Service Account 資訊供驗證
output "service_account_verification" {
  description = "Service Account 架構分離驗證資訊"
  value = var.project_service_account_email != "" ? {
    architecture = "separated-layers"
    infrastructure_layer = {
      email        = local.infrastructure_service_account_email
      name         = local.shared_outputs.service_account_name
      project_id   = local.shared_outputs.project_id
      display_name = "Integrated Event Service Account (${var.environment})"
      purpose      = "Cloud Scheduler 觸發和基礎設施管理"
    }
    execution_layer = {
      email        = var.project_service_account_email
      project_id   = local.shared_outputs.project_id
      display_name = "{{SERVICE_NAME}} Service Account"
      purpose      = "Cloud Function 執行和資料處理"
    }
    } : {
    architecture = "shared-infrastructure"
    service_account = {
      email        = local.infrastructure_service_account_email
      name         = local.shared_outputs.service_account_name
      project_id   = local.shared_outputs.project_id
      display_name = "Integrated Event Service Account (${var.environment})"
      purpose      = "統一基礎設施和執行管理"
    }
    note = "建議實施架構分離以提升安全性"
  }
}

# 權限配置摘要
locals {
  required_permissions = [
    "BigQuery 資料讀取 (tagtoo-tracking)",
    "BigQuery 資料寫入 (目標專案)",
    "BigQuery Job 執行",
    "Cloud Storage 存取",
    "Cloud Function 觸發"
  ]
}

output "permissions_summary" {
  description = "權限配置摘要"
  value = var.project_service_account_email != "" ? {
    architecture_type = "separated-layers"
    infrastructure_layer = {
      service_account = local.infrastructure_service_account_email
      purpose         = "Cloud Scheduler 觸發和基礎設施管理"
      permissions = [
        "Cloud Function 觸發權限 (roles/cloudfunctions.invoker)",
        "Cloud Run Service 觸發權限 (roles/run.invoker)"
      ]
    }
    execution_layer = {
      service_account = var.project_service_account_email
      purpose         = "Cloud Function 執行和資料處理"
      permissions = [
        "BigQuery 資料編輯 (roles/bigquery.dataEditor) - 本專案",
        "BigQuery Job 執行 (roles/bigquery.jobUser) - 本專案",
        "Cloud Storage 物件使用 (roles/storage.objectUser) - 本專案",
        var.enable_cross_project_access ? "BigQuery 跨專案存取 (roles/bigquery.dataEditor) - 目標專案" : null,
        var.enable_cross_project_access ? "BigQuery 跨專案 Job (roles/bigquery.jobUser) - 目標專案" : null
      ]
    }
    required_permissions = local.required_permissions
    cross_project_access = var.enable_cross_project_access
    target_projects      = var.target_project_ids
    security_notes = [
      "採用架構分離設計，基礎設施層和執行層使用不同 Service Account",
      "Cloud Scheduler 使用共享基礎設施 Service Account 進行觸發",
      "Cloud Function 使用專案特定 Service Account 執行業務邏輯",
      "確保客戶資料隔離和多客戶合作場景的安全需求",
      "Cloud Function 僅允許內部觸發 (ALLOW_INTERNAL_ONLY)"
    ]
    } : {
    architecture_type      = "shared-infrastructure"
    service_account_used   = local.infrastructure_service_account_email
    service_account_source = "shared-infrastructure"
    required_permissions   = local.required_permissions
    terraform_managed_permissions = [
      "Cloud Function 觸發權限 (roles/cloudfunctions.invoker)",
      "Cloud Run Service 觸發權限 (roles/run.invoker)"
    ]
    shared_infrastructure_permissions = [
      "BigQuery 資料編輯 (roles/bigquery.dataEditor)",
      "BigQuery Job 執行 (roles/bigquery.jobUser)",
      "Cloud Storage 物件使用 (roles/storage.objectUser)",
      "BigQuery 資料集寫入權限 (integrated_event dataset)",
      "Storage Bucket 存取權限 (staging and dashboard buckets)"
    ]
    security_notes = [
      "使用共享基礎設施 Service Account，統一權限管理",
      "建議實施架構分離以提升客戶資料隔離",
      "Cloud Scheduler 使用 OIDC Token 進行安全認證",
      "Cloud Function 僅允許內部觸發 (ALLOW_INTERNAL_ONLY)"
    ]
  }
}
