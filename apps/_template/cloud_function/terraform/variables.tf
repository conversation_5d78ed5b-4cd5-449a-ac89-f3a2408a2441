# {{SERVICE_NAME}} Terraform 變數

variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
  default     = "tagtoo-tracking"
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod。"
  }
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "{{SERVICE_NAME_KEBAB}}"
}

# Cloud Function 配置
variable "function_timeout" {
  description = "Function 執行逾時時間 (秒)"
  type        = number
  default     = 300
}

variable "function_memory" {
  description = "Function 記憶體限制 (MB)"
  type        = number
  default     = 256
}

# 排程配置
variable "schedule_expression" {
  description = "排程表達式 (cron 格式)"
  type        = string
  default     = "0 */6 * * *" # 每 6 小時執行一次
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

# Service Account 架構分離配置
variable "project_service_account_email" {
  description = "專案執行層 Service Account Email (留空則使用共享基礎設施 SA)"
  type        = string
  default     = ""

  validation {
    condition     = var.project_service_account_email == "" || can(regex("^[a-z0-9-]+@[a-z0-9-]+\\.iam\\.gserviceaccount\\.com$", var.project_service_account_email))
    error_message = "Service Account Email 格式必須為 {name}@{project}.iam.gserviceaccount.com"
  }
}

variable "enable_cross_project_access" {
  description = "是否啟用跨專案存取權限"
  type        = bool
  default     = false
}

variable "target_project_ids" {
  description = "目標專案 ID 列表 (用於跨專案存取)"
  type        = list(string)
  default     = []
}
