# 向後相容性和遷移指南

## 📊 概覽

本文檔說明 Service Account 架構分離設計的向後相容性策略，以及現有專案的遷移指南，確保平滑過渡而不影響現有服務的穩定運作。

## 🔍 現有專案分析

### 當前 Service Account 使用模式

```yaml
現有專案配置分析:
  shopify-webhook:
    當前架構: 共享基礎設施 Service Account
    Service Account: <EMAIL>
    服務類型: Cloud Run + BigQuery Data Transfer
    風險等級: 中等 (單一客戶資料)
    遷移優先級: 高

  reurl:
    當前架構: 專項特定 Service Account
    Service Account: <EMAIL>
    服務類型: BigQuery Data Transfer
    風險等級: 低 (已實施隔離)
    遷移優先級: 低 (僅需文檔更新)

  carrefour-offline-data:
    當前架構: 架構分離設計 (已遷移)
    基礎設施層: <EMAIL>
    執行層: <EMAIL>
    風險等級: 低 (已符合標準)
    遷移優先級: 完成

  legacy-event-sync:
    當前狀態: 暫停開發
    說明: 專案已暫停開發，不納入遷移計畫
    遷移優先級: 不適用
```

## 🛡️ 向後相容性策略

### 1. 漸進式遷移原則

```yaml
遷移策略:
  階段 1 - 模板更新:
    - 更新專案模板支援架構分離
    - 保持現有專案配置不變
    - 新專案預設使用架構分離

  階段 2 - 高風險專案遷移:
    - 優先遷移 shopify-webhook 專案
    - 建立專案特定 Service Account
    - 測試和驗證功能正常

  階段 3 - 低風險專案標準化:
    - 標準化 reurl 專案配置
    - 更新文檔和操作手冊
    - 完成全面架構分離
```

### 2. 相容性保證

```yaml
相容性承諾:
  API 相容性:
    - 現有 API 端點保持不變
    - 認證機制向後相容
    - 回應格式維持一致

  功能相容性:
    - 所有現有功能正常運作
    - 資料處理邏輯不變
    - 監控和警報持續運作

  配置相容性:
    - 現有 Terraform 配置繼續有效
    - 環境變數保持一致
    - 部署流程不變
```

## 🔄 專案遷移指南

### Legacy Event Sync 專案狀態

#### 專案暫停說明

```yaml
legacy-event-sync 狀態:
  開發狀態: 暫停開發
  原因: 專案需求變更，暫停進一步開發
  當前配置: 維持現狀，不進行架構遷移
  影響: 不影響其他專案的架構分離實施
```

**注意**：由於 legacy-event-sync 專案已暫停開發，因此不納入架構分離的遷移計畫中。未來如果重新啟動開發，可參考本指南的標準流程進行架構分離實施。

### Shopify Webhook 遷移

#### 遷移步驟

1. **建立 Service Account**

   ```bash
   gcloud iam service-accounts create shopify-webhook-processor \
     --display-name="Shopify Webhook Processor Service Account" \
     --description="專案執行層 Service Account for shopify-webhook"
   ```

2. **配置權限**

   ```bash
   SA_EMAIL="<EMAIL>"

   # BigQuery 權限
   gcloud projects add-iam-policy-binding tagtoo-tracking \
     --member="serviceAccount:${SA_EMAIL}" \
     --role="roles/bigquery.dataEditor"

   # BigQuery Data Transfer 權限
   gcloud projects add-iam-policy-binding tagtoo-tracking \
     --member="serviceAccount:${SA_EMAIL}" \
     --role="roles/bigquerydatatransfer.serviceAgent"
   ```

### ReURL 專案標準化

#### 當前狀態

```yaml
reurl 現狀:
  架構: 已使用專案特定 Service Account
  Service Account: <EMAIL>
  需要: 標準化命名和文檔更新
```

#### 標準化步驟

1. **重新命名 Service Account（可選）**

   ```bash
   # 建立標準化命名的 Service Account
   gcloud iam service-accounts create reurl-data-processor \
     --display-name="ReURL Data Processor Service Account" \
     --description="專案執行層 Service Account for reurl"

   # 遷移權限配置
   # 更新 Terraform 配置
   ```

2. **更新文檔**
   - 更新專案 README
   - 記錄 Service Account 配置
   - 標準化操作手冊

## 📋 遷移檢查清單

### 遷移前準備

- [ ] **風險評估**

  - [ ] 分析當前權限範圍
  - [ ] 評估客戶資料隔離風險
  - [ ] 確定業務影響範圍
  - [ ] 制定回滾計畫

- [ ] **環境準備**
  - [ ] 建立測試環境
  - [ ] 準備監控和警報
  - [ ] 設定維護時間窗口
  - [ ] 通知相關團隊

### 遷移執行

- [ ] **Service Account 建立**

  - [ ] 建立專案特定 Service Account
  - [ ] 配置基礎權限
  - [ ] 配置跨專案權限（如需要）
  - [ ] 驗證權限配置

- [ ] **Terraform 更新**

  - [ ] 更新 Service Account 配置
  - [ ] 實施架構分離
  - [ ] 更新 IAM 權限綁定
  - [ ] 驗證配置正確性

- [ ] **測試驗證**
  - [ ] 在測試環境執行遷移
  - [ ] 驗證功能正常運作
  - [ ] 檢查權限隔離
  - [ ] 測試監控和警報

### 遷移後驗證

- [ ] **功能驗證**

  - [ ] 服務正常啟動
  - [ ] 資料處理流程正確
  - [ ] API 回應正常
  - [ ] 排程任務執行

- [ ] **安全驗證**

  - [ ] 權限隔離正確
  - [ ] 無法存取其他客戶資料
  - [ ] 審計日誌記錄完整
  - [ ] 監控警報正常

- [ ] **清理工作**
  - [ ] 移除舊的權限配置
  - [ ] 更新文檔和操作手冊
  - [ ] 進行安全性審查
  - [ ] 團隊培訓和知識轉移

## 🚨 回滾程序

### 緊急回滾

如果遷移過程中出現問題，可以快速回滾到原始配置：

```bash
# 1. 恢復原始 Service Account 配置
terraform apply -var="project_service_account_email=" -auto-approve

# 2. 驗證服務恢復正常
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  {service-url}/health

# 3. 檢查監控指標
gcloud logging read 'resource.type="cloud_run_revision" AND
  resource.labels.service_name="{service-name}"' --limit=10
```

### 計畫性回滾

如果需要計畫性回滾：

1. **評估回滾原因**
2. **制定回滾計畫**
3. **執行回滾步驟**
4. **驗證系統狀態**
5. **更新文檔和學習**

## 📊 遷移時間表

### 建議遷移順序

```yaml
遷移時間表:
  第 1 週:
    - 完成模板更新和文檔建立
    - 新專案開始使用架構分離

  第 2-3 週:
    - 遷移 shopify-webhook (高優先級)
    - 測試和驗證功能正常

  第 4 週:
    - 標準化 reurl 專案 (低優先級)
    - 完成文檔更新

  第 5 週:
    - 全面安全性審查
    - 團隊培訓和知識轉移

  備註:
    - legacy-event-sync 專案已暫停開發，不納入遷移計畫
```

## 📞 支援和聯絡

### 遷移支援團隊

- **技術負責人**: Data Engineering Team
- **安全顧問**: Security Team
- **專案經理**: DevOps Team

### 緊急聯絡

- **技術問題**: Data Engineering Team Slack 頻道
- **安全問題**: Security Team 緊急聯絡
- **業務影響**: 專案經理和相關業務團隊

---

**文檔版本**: v1.0
**建立日期**: 2025-09-01
**最後更新**: 2025-09-01
**維護者**: Data Engineering Team
**審查週期**: 每月
