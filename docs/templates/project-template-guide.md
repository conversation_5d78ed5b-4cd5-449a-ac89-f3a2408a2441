# 專案模板使用指南

## 📊 概覽

本指南說明如何使用 Integrated Event Platform 的專案模板，實施 Service Account 架構分離設計，確保多客戶合作場景的安全隔離。

## 🔔 **監控配置標準**

所有新專案必須遵循 [Tagtoo 企業級監控配置標準](../architecture/monitoring-standards.md)：

### **必要監控組件**

- ✅ 專案獨立通知頻道
- ✅ 標準化警報策略（執行時間、錯誤率、資源使用）
- ✅ BigQuery Labels 費用追蹤
- ✅ 監控文檔和操作手冊

### **參考實施**

參考 [家樂福離線資料系統](../../apps/carrefour-offline-data/README.md) 的完整監控配置實施。

## 🏗️ 模板架構

### 可用模板類型

```yaml
模板類型:
  cloud_function:
    用途: Cloud Function 服務部署
    適用場景: 事件處理、資料轉換、API 服務
    架構支援: 完整的架構分離設計

  cloud_run:
    用途: Cloud Run 容器化服務部署
    適用場景: Web 服務、長時間運行的服務
    架構支援: 完整的架構分離設計

  bigquery_only:
    用途: 純 BigQuery 資料處理
    適用場景: 資料分析、ETL 流程
    架構支援: 基本的權限配置

  common:
    用途: 共用配置和工具
    適用場景: 所有專案的基礎配置
    架構支援: 標準化配置模板
```

## 🚀 快速開始

### 1. 建立新專案

```bash
# 複製模板
cp -r apps/_template/cloud_function apps/your-project-name

# 進入專案目錄
cd apps/your-project-name

# 執行模板初始化腳本
./scripts/init-project.sh
```

### 2. 配置 Service Account 架構分離

#### 步驟 1: 建立專案特定 Service Account

```bash
# 設定專案變數
PROJECT_NAME="your-project-name"
PURPOSE="data-processor"  # 或其他用途描述
SA_NAME="${PROJECT_NAME}-${PURPOSE}"

# 建立 Service Account
gcloud iam service-accounts create ${SA_NAME} \
  --display-name="${PROJECT_NAME} ${PURPOSE} Service Account" \
  --description="專案執行層 Service Account for ${PROJECT_NAME}"

# 記錄 Service Account Email
SA_EMAIL="${SA_NAME}@tagtoo-tracking.iam.gserviceaccount.com"
echo "Service Account Email: ${SA_EMAIL}"
```

#### 步驟 2: 配置基礎權限

```bash
# 本專案基礎權限
gcloud projects add-iam-policy-binding tagtoo-tracking \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding tagtoo-tracking \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/bigquery.jobUser"

gcloud projects add-iam-policy-binding tagtoo-tracking \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/storage.objectUser"

gcloud projects add-iam-policy-binding tagtoo-tracking \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/logging.logWriter"
```

#### 步驟 3: 配置跨專案權限（如需要）

```bash
# 跨專案權限配置
TARGET_PROJECT="tagtoo-ml-workflow"

gcloud projects add-iam-policy-binding ${TARGET_PROJECT} \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding ${TARGET_PROJECT} \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/bigquery.jobUser"
```

### 3. 更新 Terraform 配置

#### 編輯 `terraform/environments/prod.tfvars`

```hcl
# 基本配置
environment = "prod"
service_name = "your-project-name"

# Service Account 架構分離配置
project_service_account_email = "<EMAIL>"

# 跨專案存取配置（如需要）
enable_cross_project_access = true
target_project_ids = ["tagtoo-ml-workflow"]

# Cloud Function 配置
function_timeout = 600
function_memory = 1024

# 排程配置
schedule_expression = "0 2 * * *"  # 每日凌晨 2 點
schedule_timezone = "Asia/Taipei"
```

### 4. 部署和驗證

```bash
# 進入 Terraform 目錄
cd terraform

# 初始化 Terraform
terraform init

# 檢查配置
terraform plan -var-file="environments/prod.tfvars"

# 部署
terraform apply -var-file="environments/prod.tfvars"

# 驗證部署
terraform output service_account_verification
terraform output permissions_summary
```

## 🔐 安全配置檢查清單

### 新專案建立檢查清單

- [ ] **Service Account 建立**

  - [ ] 建立專案特定 Service Account
  - [ ] 使用標準化命名規範
  - [ ] 設定適當的描述和顯示名稱

- [ ] **權限配置**

  - [ ] 配置本專案基礎權限
  - [ ] 配置跨專案權限（如需要）
  - [ ] 驗證權限最小化原則
  - [ ] 檢查權限隔離

- [ ] **Terraform 配置**

  - [ ] 更新 `project_service_account_email` 變數
  - [ ] 配置跨專案存取設定
  - [ ] 驗證 IAM 權限綁定
  - [ ] 檢查輸出配置

- [ ] **部署驗證**

  - [ ] 執行 `terraform plan` 檢查
  - [ ] 部署到測試環境
  - [ ] 驗證觸發流程
  - [ ] 檢查權限隔離
  - [ ] 測試跨專案存取

- [ ] **文檔更新**
  - [ ] 更新專案 README
  - [ ] 記錄 Service Account 配置
  - [ ] 更新操作手冊
  - [ ] 建立故障排除指南

### 安全性驗證檢查清單

- [ ] **權限隔離驗證**

  - [ ] 確認無法存取其他客戶資料
  - [ ] 驗證跨專案權限範圍
  - [ ] 檢查資料集存取限制
  - [ ] 測試權限邊界

- [ ] **觸發流程驗證**

  - [ ] Cloud Scheduler 成功觸發
  - [ ] Cloud Function 正常執行
  - [ ] 資料處理流程正確
  - [ ] 監控警報正常

- [ ] **審計和監控**
  - [ ] 審計日誌記錄完整
  - [ ] 監控指標正常收集
  - [ ] 警報配置正確
  - [ ] 通知頻道設定

## 🔄 現有專案遷移

### 遷移評估

1. **檢查當前配置**

   ```bash
   # 檢查當前使用的 Service Account
   gcloud functions describe {function-name} --region=asia-east1 --gen2 \
     --format="value(serviceConfig.serviceAccountEmail)"

   # 檢查當前權限
   gcloud projects get-iam-policy tagtoo-tracking \
     --flatten="bindings[].members" \
     --filter="bindings.members:{current-service-account}"
   ```

2. **風險評估**
   - 評估當前權限範圍
   - 分析客戶資料隔離風險
   - 確定遷移優先級
   - 制定回滾計畫

### 遷移步驟

1. **準備階段**

   - 建立新的專案特定 Service Account
   - 配置必要權限
   - 準備 Terraform 配置變更

2. **測試階段**

   - 在測試環境執行遷移
   - 驗證功能正常
   - 檢查權限隔離
   - 測試監控和警報

3. **生產遷移**

   - 選擇維護時間窗口
   - 執行 Terraform 配置更新
   - 驗證服務正常運作
   - 監控系統狀態

4. **清理階段**
   - 移除舊的權限配置
   - 更新文檔和操作手冊
   - 進行安全性審查

## 🚨 故障排除

### 常見問題

1. **Service Account 不存在**

   ```bash
   # 檢查 Service Account
   gcloud iam service-accounts describe {service-account-email}

   # 如果不存在，建立 Service Account
   gcloud iam service-accounts create {service-account-name}
   ```

2. **權限不足**

   ```bash
   # 檢查權限
   gcloud projects get-iam-policy {project-id} \
     --flatten="bindings[].members" \
     --filter="bindings.members:{service-account-email}"

   # 添加必要權限
   gcloud projects add-iam-policy-binding {project-id} \
     --member="serviceAccount:{service-account-email}" \
     --role="{required-role}"
   ```

3. **觸發失敗**

   ```bash
   # 檢查 Cloud Function IAM
   gcloud functions get-iam-policy {function-name} --region={region}

   # 檢查 Cloud Run Service IAM
   gcloud run services get-iam-policy {service-name} --region={region}
   ```

### 診斷工具

```bash
# 完整的權限診斷腳本
./scripts/diagnose-permissions.sh {project-name}

# 架構分離驗證腳本
./scripts/verify-architecture-separation.sh {project-name}

# 安全性檢查腳本
./scripts/security-check.sh {project-name}
```

## 📚 相關資源

- [Service Account 架構分離設計原則](../architecture/service-account-separation-design.md)
- [安全性最佳實務指南](../architecture/security-best-practices.md)
- [觸發流程和權限驗證指南](../operations/trigger-flow-and-permissions.md)
- [專案模板 API 文檔](template-api-reference.md)

---

**文檔版本**: v1.0
**建立日期**: 2025-09-01
**最後更新**: 2025-09-01
**維護者**: Data Engineering Team
**審查週期**: 每月
