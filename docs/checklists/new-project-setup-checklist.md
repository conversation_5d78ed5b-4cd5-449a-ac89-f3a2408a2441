# 新專案建立檢查清單

## 📊 概覽

本檢查清單確保新專案正確實施 Service Account 架構分離設計，符合安全隔離和多客戶合作的需求。

## 🔔 **監控配置檢查清單**

基於 [企業級監控配置標準](../architecture/monitoring-standards.md)：

### **通知頻道配置**

- [ ] 建立專案專用通知頻道
- [ ] 命名符合標準格式：`{專案名稱} - {環境} - {收件人}`
- [ ] 設定標準化用戶標籤（project, environment, purpose, owner）
- [ ] 測試通知頻道正常運作
- [ ] 多收件人需求時建立多個通知頻道

### **警報策略配置**

- [ ] 執行時間監控（閾值根據服務特性設定）
- [ ] 錯誤率監控（閾值：0，任何錯誤立即通知）
- [ ] 記憶體使用率監控（閾值：90%）
- [ ] 執行頻率異常監控
- [ ] 調度器失敗監控（如適用）
- [ ] 監控間隔設定為 300 秒（推薦）

### **BigQuery 費用追蹤**

- [ ] 所有 BigQuery 查詢包含標準 labels
- [ ] Labels 包含：project, repo, env, trigger, user
- [ ] 實施自動用戶偵測機制
- [ ] 費用報告可按 labels 分類查看
- [ ] 成本控制機制正常運作

### **文檔更新**

- [ ] README.md 包含監控配置說明
- [ ] 建立或更新操作手冊
- [ ] 記錄特殊監控配置和閾值設定
- [ ] 更新團隊聯絡資訊

## 🚀 專案初始化

### 1. 專案規劃階段

- [ ] **需求分析**

  - [ ] 確定專案用途和範圍
  - [ ] 識別資料來源和目標
  - [ ] 評估客戶資料隔離需求
  - [ ] 確定跨專案存取需求

- [ ] **架構設計**

  - [ ] 選擇適當的專案模板
  - [ ] 設計 Service Account 架構
  - [ ] 規劃權限配置
  - [ ] 設計監控和警報策略

- [ ] **命名規範**
  - [ ] 專案名稱符合命名規範
  - [ ] Service Account 名稱標準化
  - [ ] 資源命名一致性
  - [ ] 標籤和標記規範

### 2. 環境準備階段

- [ ] **開發環境**

  - [ ] 複製適當的專案模板
  - [ ] 設定本地開發環境
  - [ ] 配置 IDE 和工具
  - [ ] 建立版本控制倉庫

- [ ] **GCP 專案配置**
  - [ ] 確認 GCP 專案存取權限
  - [ ] 啟用必要的 API 服務
  - [ ] 配置計費和配額
  - [ ] 設定網路和安全配置

## 🔐 Service Account 配置

### 3. Service Account 建立

- [ ] **專案特定 Service Account**

  ```bash
  # 檢查清單項目
  PROJECT_NAME="your-project-name"
  PURPOSE="data-processor"  # 根據用途調整
  SA_NAME="${PROJECT_NAME}-${PURPOSE}"
  ```

  - [ ] 使用標準化命名規範
  - [ ] 設定適當的顯示名稱和描述
  - [ ] 記錄 Service Account Email
  - [ ] 驗證 Service Account 建立成功

- [ ] **命名驗證**
  - [ ] 格式：`{project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com`
  - [ ] 名稱長度不超過 30 字元
  - [ ] 使用小寫字母和連字號
  - [ ] 避免使用保留字和特殊字元

### 4. 權限配置

- [ ] **本專案基礎權限**

  - [ ] `roles/bigquery.dataEditor` - BigQuery 資料編輯
  - [ ] `roles/bigquery.jobUser` - BigQuery Job 執行
  - [ ] `roles/storage.objectUser` - Storage 物件存取
  - [ ] `roles/logging.logWriter` - 應用程式日誌
  - [ ] `roles/monitoring.metricWriter` - 應用程式監控

- [ ] **專案特定權限**

  - [ ] Secret Manager 存取（如需要）
  - [ ] Cloud Tasks 權限（如需要）
  - [ ] Pub/Sub 權限（如需要）
  - [ ] 其他服務特定權限

- [ ] **跨專案權限（如需要）**
  - [ ] 目標專案的 BigQuery 權限
  - [ ] 目標專案的 Storage 權限
  - [ ] 使用 IAM 條件限制存取範圍
  - [ ] 記錄跨專案存取理由

### 5. 權限驗證

- [ ] **權限檢查**

  ```bash
  # 驗證本專案權限
  gcloud projects get-iam-policy tagtoo-tracking \
    --flatten="bindings[].members" \
    --filter="bindings.members:{service-account-email}"

  # 驗證跨專案權限
  gcloud projects get-iam-policy {target-project} \
    --flatten="bindings[].members" \
    --filter="bindings.members:{service-account-email}"
  ```

  - [ ] 確認所有必要權限已授予
  - [ ] 驗證權限範圍正確
  - [ ] 檢查無多餘權限
  - [ ] 測試權限功能正常

## 🏗️ Terraform 配置

### 6. 基礎配置

- [ ] **變數配置**

  - [ ] 更新 `terraform/variables.tf`
  - [ ] 設定 `project_service_account_email`
  - [ ] 配置跨專案存取變數
  - [ ] 設定環境特定變數

- [ ] **環境檔案**
  - [ ] 建立 `environments/dev.tfvars`
  - [ ] 建立 `environments/staging.tfvars`
  - [ ] 建立 `environments/prod.tfvars`
  - [ ] 驗證變數值正確性

### 7. IAM 配置

- [ ] **架構分離實施**

  - [ ] 配置基礎設施層 Service Account
  - [ ] 配置專案執行層 Service Account
  - [ ] 實施 IAM 權限綁定
  - [ ] 配置跨專案權限（如需要）

- [ ] **安全配置**
  - [ ] Cloud Function/Run 設為 `ALLOW_INTERNAL_ONLY`
  - [ ] 使用 OIDC Token 認證
  - [ ] 實施最小權限原則
  - [ ] 配置資源級別 IAM

### 8. 部署驗證

- [ ] **Terraform 檢查**

  ```bash
  # 初始化和檢查
  terraform init
  terraform validate
  terraform plan -var-file="environments/dev.tfvars"
  ```

  - [ ] Terraform 初始化成功
  - [ ] 配置驗證通過
  - [ ] Plan 輸出符合預期
  - [ ] 無安全警告或錯誤

- [ ] **測試環境部署**
  - [ ] 部署到開發環境
  - [ ] 驗證資源建立成功
  - [ ] 檢查 Service Account 配置
  - [ ] 測試基本功能

## 🔍 功能測試

### 9. 觸發流程測試

- [ ] **Cloud Scheduler 測試**

  - [ ] 手動觸發 Cloud Scheduler
  - [ ] 驗證 OIDC Token 認證
  - [ ] 檢查 HTTP 回應狀態
  - [ ] 確認觸發成功

- [ ] **Cloud Function/Run 測試**
  - [ ] 驗證服務啟動正常
  - [ ] 檢查環境變數配置
  - [ ] 測試業務邏輯執行
  - [ ] 確認資料處理正確

### 10. 權限隔離測試

- [ ] **資料存取測試**

  - [ ] 驗證可存取指定資料
  - [ ] 確認無法存取其他客戶資料
  - [ ] 測試跨專案存取（如配置）
  - [ ] 檢查權限邊界

- [ ] **安全邊界測試**
  - [ ] 嘗試存取未授權資源
  - [ ] 驗證權限拒絕正確
  - [ ] 檢查審計日誌記錄
  - [ ] 確認安全隔離有效

## 📊 監控和警報

### 11. 監控配置

- [ ] **基礎監控**

  - [ ] Cloud Function/Run 效能監控
  - [ ] BigQuery 查詢監控
  - [ ] 錯誤率和延遲監控
  - [ ] 資源使用監控

- [ ] **自定義監控**
  - [ ] 業務邏輯監控指標
  - [ ] 資料處理量監控
  - [ ] 成本監控指標
  - [ ] 安全事件監控

### 12. 警報配置

- [ ] **系統警報**

  - [ ] 服務可用性警報
  - [ ] 錯誤率閾值警報
  - [ ] 效能異常警報
  - [ ] 資源耗盡警報

- [ ] **安全警報**

  - [ ] 異常存取模式警報
  - [ ] 權限變更警報
  - [ ] 跨客戶存取警報
  - [ ] 認證失敗警報

- [ ] **通知配置**
  - [ ] 設定通知頻道
  - [ ] 配置通知對象
  - [ ] 測試警報通知
  - [ ] 驗證警報觸發

## 📚 文檔和維護

### 13. 文檔建立

- [ ] **專案文檔**

  - [ ] 更新專案 README
  - [ ] 記錄架構設計
  - [ ] 說明 Service Account 配置
  - [ ] 建立操作手冊

- [ ] **技術文檔**
  - [ ] API 文檔（如適用）
  - [ ] 資料流程圖
  - [ ] 權限配置說明
  - [ ] 故障排除指南

### 14. 維護準備

- [ ] **運維準備**

  - [ ] 建立監控儀表板
  - [ ] 準備故障排除腳本
  - [ ] 設定備份和恢復程序
  - [ ] 建立維護時間表

- [ ] **團隊準備**
  - [ ] 團隊成員培訓
  - [ ] 權限和存取配置
  - [ ] 緊急聯絡清單
  - [ ] 知識轉移文檔

## ✅ 最終驗證

### 15. 生產就緒檢查

- [ ] **安全檢查**

  - [ ] 完整的安全性審查
  - [ ] 權限配置驗證
  - [ ] 資料隔離確認
  - [ ] 合規性檢查

- [ ] **效能檢查**

  - [ ] 負載測試執行
  - [ ] 效能基準建立
  - [ ] 容量規劃完成
  - [ ] 擴展性驗證

- [ ] **可靠性檢查**
  - [ ] 災難恢復測試
  - [ ] 備份恢復驗證
  - [ ] 故障轉移測試
  - [ ] 監控警報測試

### 16. 上線準備

- [ ] **部署準備**

  - [ ] 生產環境配置
  - [ ] 部署腳本準備
  - [ ] 回滾計畫制定
  - [ ] 維護時間安排

- [ ] **團隊準備**
  - [ ] 上線檢查清單
  - [ ] 緊急回應程序
  - [ ] 監控值班安排
  - [ ] 溝通計畫執行

## 📋 檢查清單摘要

### 關鍵檢查點

1. **Service Account 架構分離** ✅
2. **權限最小化和隔離** ✅
3. **安全配置和認證** ✅
4. **監控和警報完整** ✅
5. **文檔和維護準備** ✅

### 簽核確認

- [ ] **技術負責人簽核**: **\*\*\*\***\_**\*\*\*\*** 日期: \***\*\_\*\***
- [ ] **安全團隊簽核**: **\*\*\*\***\_**\*\*\*\*** 日期: \***\*\_\*\***
- [ ] **專案經理簽核**: **\*\*\*\***\_**\*\*\*\*** 日期: \***\*\_\*\***

---

**檢查清單版本**: v1.0
**建立日期**: 2025-09-01
**最後更新**: 2025-09-01
**維護者**: Data Engineering Team
**審查週期**: 每季度
