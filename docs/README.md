# Integrated Event Platform 文檔中心

## 📊 概覽

歡迎來到 Integrated Event Platform 的文檔中心。本平台採用 Service Account 架構分離設計，確保多客戶合作場景下的安全隔離和資料保護。

## 🏗️ 架構設計 (architecture/)

### 核心架構文檔

| 文檔                                                                                  | 描述                                     | 適用對象           | 狀態    |
| ------------------------------------------------------------------------------------- | ---------------------------------------- | ------------------ | ------- |
| [Service Account 架構分離設計原則](architecture/service-account-separation-design.md) | 定義基礎設施層與專案執行層的分離設計原則 | 架構師、技術主管   | ✅ 當前 |
| [安全性最佳實務指南](architecture/security-best-practices.md)                         | 多客戶合作場景的安全性最佳實務           | 安全團隊、開發團隊 | ✅ 當前 |

**核心原則**：

- **基礎設施層**：使用共享 Service Account 進行觸發和部署
- **專案執行層**：使用專案特定 Service Account 執行業務邏輯
- **安全隔離**：確保不同客戶間的資料完全隔離

## 📋 檢查清單 (checklists/)

### 標準化流程

| 文檔                                                            | 描述                               | 適用對象           | 狀態    |
| --------------------------------------------------------------- | ---------------------------------- | ------------------ | ------- |
| [新專案建立檢查清單](checklists/new-project-setup-checklist.md) | 新專案建立的完整檢查清單和驗證步驟 | 開發團隊、專案經理 | ✅ 當前 |

**檢查重點**：

- Service Account 架構分離實施
- 權限最小化和隔離驗證
- 安全配置和認證設定
- 監控和警報配置完整性

## 🔄 遷移指南 (migration/)

### 向後相容性

| 文檔                                                              | 描述                               | 適用對象              | 狀態    |
| ----------------------------------------------------------------- | ---------------------------------- | --------------------- | ------- |
| [向後相容性和遷移指南](migration/backward-compatibility-guide.md) | 現有專案的遷移策略和向後相容性保證 | DevOps 團隊、技術主管 | ✅ 當前 |

**遷移策略**：

- 漸進式遷移，確保服務穩定
- 風險評估和優先級排序
- 完整的回滾程序和支援

## 🛠️ 專案模板 (templates/)

### 標準化模板

| 文檔                                                    | 描述                         | 適用對象         | 狀態    |
| ------------------------------------------------------- | ---------------------------- | ---------------- | ------- |
| [專案模板使用指南](templates/project-template-guide.md) | 專案模板的使用方法和最佳實務 | 開發團隊、新成員 | ✅ 當前 |

**模板特色**：

- 內建架構分離設計
- 標準化 IAM 權限配置
- 完整的監控和警報設定
- 安全性最佳實務實施

## 🎯 快速導航

### 新團隊成員入門

1. **了解架構設計** → [Service Account 架構分離設計原則](architecture/service-account-separation-design.md)
2. **學習安全實務** → [安全性最佳實務指南](architecture/security-best-practices.md)
3. **建立新專案** → [專案模板使用指南](templates/project-template-guide.md)
4. **執行檢查清單** → [新專案建立檢查清單](checklists/new-project-setup-checklist.md)

### 現有專案維護

1. **評估遷移需求** → [向後相容性和遷移指南](migration/backward-compatibility-guide.md)
2. **實施安全措施** → [安全性最佳實務指南](architecture/security-best-practices.md)
3. **標準化配置** → [專案模板使用指南](templates/project-template-guide.md)

### 架構決策參考

1. **設計原則** → [Service Account 架構分離設計原則](architecture/service-account-separation-design.md)
2. **安全考量** → [安全性最佳實務指南](architecture/security-best-practices.md)
3. **實施指南** → [專案模板使用指南](templates/project-template-guide.md)

## 🔐 安全性重點

### 多客戶合作安全原則

```yaml
安全隔離層級:
  L1 - 基礎設施層:
    Service Account: integrated-event-{env}@tagtoo-tracking.iam.gserviceaccount.com
    職責: 觸發、部署、基礎設施管理
    資料存取: 無直接資料存取權限

  L2 - 專案執行層:
    Service Account: {client}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com
    職責: 特定客戶的資料處理
    資料存取: 僅限指定客戶的資料

  L3 - 跨專案層:
    權限範圍: 特定目標專案的有限權限
    資料存取: 僅限必要的寫入操作
```

### 關鍵安全措施

- ✅ **客戶資料隔離**：每個客戶專案使用獨立的執行層 Service Account
- ✅ **權限最小化**：只授予執行必要功能所需的最小權限
- ✅ **基礎設施統一**：所有觸發和部署使用共享基礎設施 Service Account
- ✅ **可審計性**：清楚的權限邊界和責任分工

## 📊 實施狀態

### 專案遷移狀態

```yaml
專案狀態總覽:
  已完成架構分離:
    - carrefour-offline-data ✅

  計畫遷移 (高優先級):
    - shopify-webhook (單一客戶資料)

  需要標準化:
    - reurl (已使用專案特定 SA，需標準化)

  暫停開發:
    - legacy-event-sync (專案暫停，不納入遷移計畫)

  新專案:
    - 預設使用架構分離設計 ✅
```

### 模板更新狀態

- ✅ **Cloud Function 模板**：已更新支援架構分離
- 🔄 **Cloud Run 模板**：計畫更新
- 🔄 **BigQuery Only 模板**：計畫更新
- ✅ **Common 模板**：標準化配置完成

## 🚀 最佳實務摘要

### 新專案建立

1. **使用標準化模板** → 確保架構分離設計
2. **建立專案特定 SA** → 實現客戶資料隔離
3. **配置最小權限** → 降低安全風險
4. **實施完整監控** → 確保系統可觀測性
5. **執行安全驗證** → 驗證權限隔離有效

### 現有專案維護

1. **評估安全風險** → 優先遷移高風險專案
2. **漸進式遷移** → 確保服務穩定性
3. **完整測試驗證** → 確保功能正常
4. **文檔同步更新** → 維護知識完整性
5. **團隊培訓** → 確保實施一致性

## 📞 支援和聯絡

### 技術支援

- **架構設計問題**：Data Engineering Team
- **安全性問題**：Security Team
- **模板使用問題**：DevOps Team
- **遷移支援**：Data Engineering Team + DevOps Team

### 緊急聯絡

- **技術問題**：Data Engineering Team Slack 頻道
- **安全事件**：Security Team 緊急聯絡
- **業務影響**：專案經理和相關業務團隊

### 文檔貢獻

歡迎團隊成員貢獻和改進文檔：

1. 發現問題或改進建議 → 建立 GitHub Issue
2. 文檔更新 → 提交 Pull Request
3. 新增最佳實務 → 與團隊討論後更新

## 📈 版本管理

### 文檔版本

- **當前版本**：v1.0
- **建立日期**：2025-09-01
- **最後更新**：2025-09-01
- **下次審查**：2025-12-01

### 更新頻率

- **架構文檔**：重大架構變更時更新
- **安全文檔**：每季度審查，安全事件後立即更新
- **模板文檔**：模板更新時同步更新
- **檢查清單**：每月審查，流程改進時更新

---

**文檔中心版本**：v1.0
**維護團隊**：Data Engineering Team
**聯絡方式**：透過 GitHub Issue 或團隊 Slack 頻道
