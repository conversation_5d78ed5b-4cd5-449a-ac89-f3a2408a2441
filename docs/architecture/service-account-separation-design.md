# Service Account 架構分離設計原則

## 📊 概覽

本文檔定義了 Integrated Event Platform 的 Service Account 架構分離設計原則，確保基礎設施層與專案執行層的安全隔離，符合企業級多客戶合作場景的安全需求。

## 🏗️ 架構設計原則

### 核心原則：雙層 Service Account 分離

```yaml
架構分離設計:
  基礎設施層 (Infrastructure Layer):
    Service Account: integrated-event-{environment}@tagtoo-tracking.iam.gserviceaccount.com
    職責:
      - Cloud Scheduler 觸發
      - Terraform 部署和基礎設施管理
      - OIDC Token 生成和認證
      - 跨服務的基礎設施操作

  專案執行層 (Project Execution Layer):
    Service Account: {project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com
    職責:
      - Cloud Function/Cloud Run 執行環境
      - 業務邏輯和資料處理
      - 客戶特定的資料存取
      - 跨專案資料操作
```

### 安全隔離目標

1. **客戶資料隔離**：每個客戶專案使用獨立的執行層 Service Account
2. **權限最小化**：只授予執行必要功能所需的最小權限
3. **基礎設施統一**：所有觸發和部署使用共享基礎設施 Service Account
4. **可審計性**：清楚的權限邊界和責任分工

## 🔐 權限配置標準

### 基礎設施層權限

```yaml
Service Account: integrated-event-{environment}@tagtoo-tracking.iam.gserviceaccount.com

專案級權限 (tagtoo-tracking):
  - roles/cloudfunctions.invoker # Cloud Function 觸發
  - roles/run.invoker # Cloud Run 服務觸發
  - roles/cloudscheduler.admin # Cloud Scheduler 管理
  - roles/storage.admin # 部署檔案管理
  - roles/logging.logWriter # 基礎設施日誌
  - roles/monitoring.metricWriter # 基礎設施監控

資源級權限:
  - Cloud Function IAM: roles/cloudfunctions.invoker
  - Cloud Run Service IAM: roles/run.invoker
  - Cloud Scheduler Job: 建立和管理權限
```

### 專案執行層權限

```yaml
Service Account: {project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com

本專案權限 (tagtoo-tracking):
  - roles/bigquery.dataEditor       # BigQuery 資料編輯
  - roles/bigquery.jobUser          # BigQuery Job 執行
  - roles/storage.objectUser        # Storage 物件存取
  - roles/logging.logWriter         # 應用程式日誌
  - roles/monitoring.metricWriter   # 應用程式監控

跨專案權限 (依需求配置):
  - tagtoo-ml-workflow: roles/bigquery.dataEditor
  - tagtoo-ml-workflow: roles/bigquery.jobUser
  - 其他目標專案的特定權限
```

## 🔄 實施流程

### 新專案建立流程

1. **建立專案特定 Service Account**

   ```bash
   gcloud iam service-accounts create {project-name}-{purpose} \
     --display-name="{Project Name} Service Account" \
     --description="專案執行層 Service Account for {project-name}"
   ```

2. **配置基礎權限**

   ```bash
   # 本專案基礎權限
   gcloud projects add-iam-policy-binding tagtoo-tracking \
     --member="serviceAccount:{project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com" \
     --role="roles/bigquery.dataEditor"

   # 跨專案權限（依需求）
   gcloud projects add-iam-policy-binding {target-project} \
     --member="serviceAccount:{project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com" \
     --role="roles/bigquery.dataEditor"
   ```

3. **Terraform 配置**

   ```hcl
   locals {
     # 基礎設施層 Service Account
     infrastructure_service_account_email = local.shared_outputs.service_account_email

     # 專案執行層 Service Account
     project_service_account_email = "{project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com"
   }
   ```

### 現有專案遷移流程

1. **評估當前配置**

   - 檢查現有 Service Account 使用情況
   - 分析權限需求和安全風險
   - 確定遷移優先級

2. **漸進式遷移**

   - 建立新的專案特定 Service Account
   - 配置必要權限
   - 更新 Terraform 配置
   - 測試和驗證
   - 清理舊配置

3. **向後相容性確保**
   - 保持現有專案的穩定運作
   - 提供遷移時間表和支援
   - 建立回滾機制

## 🛡️ 安全最佳實務

### 權限管理

1. **最小權限原則**

   - 只授予執行必要功能所需的最小權限
   - 定期審查和清理不必要的權限
   - 使用資源級別 IAM 綁定限制存取範圍

2. **權限隔離**

   - 不同客戶專案使用不同的執行層 Service Account
   - 避免跨客戶的資料存取權限
   - 實施嚴格的權限邊界

3. **審計和監控**
   - 所有權限變更記錄在 Terraform 狀態
   - Cloud Audit Logs 記錄所有 API 呼叫
   - 監控異常的權限使用模式

### 命名規範

```yaml
Service Account 命名規範:
  基礎設施層: integrated-event-{environment}@tagtoo-tracking.iam.gserviceaccount.com
  專案執行層: {project-name}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com

範例:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
```

## 📋 檢查清單

### 新專案建立檢查清單

- [ ] 建立專案特定 Service Account
- [ ] 配置本專案基礎權限
- [ ] 配置跨專案權限（如需要）
- [ ] 更新 Terraform 配置使用架構分離
- [ ] 配置 Cloud Scheduler 使用基礎設施層 SA
- [ ] 配置 Cloud Function/Run 使用執行層 SA
- [ ] 測試觸發和執行流程
- [ ] 驗證權限隔離
- [ ] 更新專案文檔

### 安全性驗證檢查清單

- [ ] 確認執行層 SA 無法存取其他客戶資料
- [ ] 驗證基礎設施層 SA 無法執行業務邏輯
- [ ] 檢查跨專案權限的必要性和範圍
- [ ] 確認所有權限都遵循最小權限原則
- [ ] 驗證審計日誌記錄完整
- [ ] 測試權限撤銷和恢復流程

## 🚨 故障排除

### 常見問題

1. **HTTP 403 PERMISSION_DENIED**

   - 檢查基礎設施層 SA 是否有 Cloud Function/Run 觸發權限
   - 確認 OIDC Token 配置正確
   - 驗證 Service Account 存在且啟用

2. **BigQuery 存取被拒**

   - 檢查執行層 SA 是否有必要的 BigQuery 權限
   - 確認跨專案權限配置正確
   - 驗證資料集和表格的存取權限

3. **Service Account 不存在**
   - 確認 Service Account 已建立
   - 檢查命名規範是否正確
   - 驗證專案 ID 和環境設定

### 診斷指令

```bash
# 檢查 Service Account 存在
gcloud iam service-accounts describe {service-account-email}

# 檢查專案級權限
gcloud projects get-iam-policy {project-id} \
  --flatten="bindings[].members" \
  --filter="bindings.members:{service-account-email}"

# 檢查資源級權限
gcloud functions get-iam-policy {function-name} --region={region}
gcloud run services get-iam-policy {service-name} --region={region}
```

## 📚 相關文檔

- [Service Account 權限配置詳解](service-account-permissions.md)
- [觸發流程和權限驗證指南](../operations/trigger-flow-and-permissions.md)
- [專案模板使用指南](../templates/project-template-guide.md)
- [安全性最佳實務](security-best-practices.md)

---

**文檔版本**: v1.0
**建立日期**: 2025-09-01
**最後更新**: 2025-09-01
**維護者**: Data Engineering Team
**審查週期**: 每季度
