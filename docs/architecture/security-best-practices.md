# 安全性最佳實務指南

## 📊 概覽

本文檔定義了 Integrated Event Platform 的安全性最佳實務，確保多客戶合作場景下的資料安全和隔離。

## 🛡️ 多客戶合作安全原則

### 核心安全目標

1. **資料隔離**：確保不同客戶的資料完全隔離
2. **存取控制**：實施嚴格的身份驗證和授權
3. **審計追蹤**：完整記錄所有資料存取和操作
4. **合規性**：符合資料保護法規要求

### 客戶資料分類

```yaml
資料敏感度分級:
  高敏感 (High Sensitive):
    - 個人識別資訊 (PII)
    - 交易記錄和財務資料
    - 客戶行為分析資料

  中敏感 (Medium Sensitive):
    - 聚合統計資料
    - 匿名化分析結果
    - 系統效能指標

  低敏感 (Low Sensitive):
    - 公開可用的資料
    - 系統配置資訊
    - 技術文檔
```

## 🔐 Service Account 安全配置

### 權限分離策略

```yaml
權限分離層級:
  L1 - 基礎設施層:
    Service Account: integrated-event-{env}@tagtoo-tracking.iam.gserviceaccount.com
    權限範圍: 觸發、部署、基礎設施管理
    資料存取: 無直接資料存取權限

  L2 - 專案執行層:
    Service Account: {client}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com
    權限範圍: 特定客戶的資料處理
    資料存取: 僅限指定客戶的資料

  L3 - 跨專案層:
    權限範圍: 特定目標專案的有限權限
    資料存取: 僅限必要的寫入操作
```

### Service Account 生命週期管理

1. **建立階段**

   ```bash
   # 使用標準化命名規範
   gcloud iam service-accounts create {client}-{purpose} \
     --display-name="{Client Name} {Purpose} Service Account" \
     --description="客戶專案執行層 Service Account"
   ```

2. **權限配置**

   ```bash
   # 最小權限原則
   gcloud projects add-iam-policy-binding tagtoo-tracking \
     --member="serviceAccount:{client}-{purpose}@tagtoo-tracking.iam.gserviceaccount.com" \
     --role="roles/bigquery.dataEditor" \
     --condition="expression=resource.name.startsWith('projects/tagtoo-tracking/datasets/{client}_')"
   ```

3. **定期審查**

   - 每季度檢查權限使用情況
   - 移除不必要的權限
   - 更新權限到最新需求

4. **停用和清理**

   ```bash
   # 停用 Service Account
   gcloud iam service-accounts disable {service-account-email}

   # 移除所有 IAM 綁定
   gcloud projects remove-iam-policy-binding {project-id} \
     --member="serviceAccount:{service-account-email}" \
     --role="{role}"
   ```

## 🔍 資料存取控制

### BigQuery 資料隔離

```sql
-- 資料集命名規範
{client_name}_{data_type}_{environment}

-- 範例
carrefour_offline_data_prod
shopify_webhook_events_prod
legacy_event_sync_staging
```

### 資料表級別權限

```yaml
權限配置策略:
  資料集級別:
    - 每個客戶使用獨立的資料集
    - 資料集名稱包含客戶識別碼
    - 實施資料集級別的存取控制

  資料表級別:
    - 敏感資料表使用額外的存取控制
    - 實施行級別安全性 (Row-Level Security)
    - 使用資料遮罩保護敏感欄位

  欄位級別:
    - PII 欄位使用加密或雜湊
    - 實施欄位級別的存取控制
    - 使用資料分類標籤
```

### 跨專案存取控制

```hcl
# Terraform 配置範例
resource "google_project_iam_member" "cross_project_access" {
  project = var.target_project_id
  role    = "roles/bigquery.dataEditor"
  member  = "serviceAccount:${local.project_service_account_email}"

  condition {
    title       = "限制資料集存取"
    description = "只允許存取特定客戶的資料集"
    expression  = "resource.name.startsWith('projects/${var.target_project_id}/datasets/${var.client_name}_')"
  }
}
```

## 📋 審計和監控

### 審計日誌配置

```yaml
Cloud Audit Logs 配置:
  Admin Activity:
    - 所有管理操作記錄
    - Service Account 建立/修改/刪除
    - IAM 權限變更

  Data Access:
    - BigQuery 資料存取記錄
    - Storage 檔案存取記錄
    - 敏感操作的詳細記錄

  System Events:
    - 系統自動化操作
    - 資源建立/刪除事件
```

### 監控警報

```yaml
安全監控警報:
  異常存取模式:
    - 非預期的跨客戶資料存取
    - 大量資料下載或匯出
    - 非工作時間的異常活動

  權限變更:
    - Service Account 權限修改
    - IAM 政策變更
    - 跨專案權限授予

  系統安全:
    - 認證失敗次數過多
    - 未授權的 API 呼叫
    - 安全政策違規
```

### 監控指標

```bash
# 建立自定義監控指標
gcloud logging metrics create cross_client_data_access \
  --description="跨客戶資料存取監控" \
  --log-filter='protoPayload.resourceName!~"projects/tagtoo-tracking/datasets/{client_name}_.*"'

# 建立警報政策
gcloud alpha monitoring policies create \
  --policy-from-file=security-alert-policy.yaml
```

## 🔒 網路安全

### VPC 和防火牆配置

```yaml
網路隔離策略:
  VPC 配置:
    - 使用私有 IP 範圍
    - 實施子網路隔離
    - 配置 VPC 防火牆規則

  Cloud Function/Run 網路:
    - 設定為 ALLOW_INTERNAL_ONLY
    - 使用 VPC Connector 進行內部通訊
    - 限制出站網路存取

  BigQuery 網路:
    - 使用私有 Google 存取
    - 配置授權網路
    - 實施 VPC Service Controls
```

### API 安全

```yaml
API 安全措施:
  認證:
    - 使用 OIDC Token 進行服務間認證
    - 實施 mTLS 進行敏感通訊
    - 定期輪換 API 金鑰

  授權:
    - 實施細粒度的 API 權限
    - 使用 IAM 條件進行動態授權
    - 限制 API 呼叫頻率

  加密:
    - 所有傳輸使用 TLS 1.2+
    - 敏感資料使用端到端加密
    - 實施金鑰管理最佳實務
```

## 🚨 事件回應

### 安全事件分類

```yaml
事件嚴重性分級:
  Critical (嚴重):
    - 客戶資料洩露
    - 未授權的跨客戶存取
    - 系統完全入侵

  High (高):
    - 權限提升攻擊
    - 異常的大量資料存取
    - 認證系統被破壞

  Medium (中):
    - 異常登入活動
    - 配置錯誤導致的安全風險
    - 非預期的權限變更

  Low (低):
    - 輕微的配置偏差
    - 非敏感資料的異常存取
    - 系統效能異常
```

### 回應流程

1. **檢測和警報**

   - 自動化監控系統檢測異常
   - 即時警報通知相關團隊
   - 初步影響評估

2. **隔離和控制**

   - 立即隔離受影響的系統
   - 撤銷可疑的存取權限
   - 啟動事件回應團隊

3. **調查和分析**

   - 收集和分析審計日誌
   - 確定攻擊向量和影響範圍
   - 保存證據進行後續分析

4. **修復和恢復**

   - 修補安全漏洞
   - 恢復正常服務運作
   - 實施額外的安全措施

5. **後續行動**
   - 更新安全政策和程序
   - 進行安全意識培訓
   - 改進監控和檢測能力

## 📚 合規性要求

### 資料保護法規

```yaml
合規性框架:
  GDPR (歐盟一般資料保護規範):
    - 資料主體權利保護
    - 資料處理合法性
    - 資料外洩通知義務

  CCPA (加州消費者隱私法):
    - 消費者權利保護
    - 資料透明度要求
    - 選擇退出機制

  SOC 2 Type II:
    - 安全性控制
    - 可用性保證
    - 處理完整性
```

### 合規性檢查清單

- [ ] 實施資料分類和標籤
- [ ] 建立資料保留政策
- [ ] 實施資料主體權利回應流程
- [ ] 進行定期安全評估
- [ ] 維護合規性文檔
- [ ] 實施員工安全培訓
- [ ] 建立第三方風險管理

---

**文檔版本**: v1.0
**建立日期**: 2025-09-01
**最後更新**: 2025-09-01
**維護者**: Security Team & Data Engineering Team
**審查週期**: 每季度
