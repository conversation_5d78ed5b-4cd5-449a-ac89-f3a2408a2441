# 🏛️ Tagtoo 企業級監控配置標準

> **Google Cloud Monitoring 統一標準**
> 基於家樂福離線資料系統最佳實務
> 最後更新：2025-09-01

## 📋 **標準概述**

本文檔定義 Tagtoo 所有 Google Cloud 專案的監控配置標準，確保一致性、可維護性和成本效益。

## 🎯 **核心原則**

### **1. 專案隔離原則**

- 每個專案建立專用通知頻道
- 避免跨專案警報混淆
- 支援精確的權限控制

### **2. 成本最優化原則**

- 利用 Google Cloud Monitoring 免費配額
- 通知頻道：無限制免費
- 警報策略：前 100 個免費
- Email 通知：完全免費

### **3. 可擴展性原則**

- 標準化命名規範
- 模組化 Terraform 配置
- 支援團隊成長和專案擴展

## 🏗️ **通知頻道標準**

### **命名規範**

```
格式: "{專案名稱} - {環境} - {收件人}"
範例:
  生產環境: "ReURL整合系統 - prod - <EMAIL>"
  測試環境: "ReURL整合系統 - dev - <EMAIL>"
  共用服務: "共用基礎設施 - prod - <EMAIL>"
```

### **用戶標籤標準**

```yaml
必要標籤:
  project: 專案名稱 (kebab-case)
  environment: 環境 (prod/dev/staging)
  purpose: project-specific-alerts
  owner: 主要負責人 (email 前綴)

選用標籤:
  team: 負責團隊
  cost-center: 成本中心
  criticality: 重要性等級 (high/medium/low)
```

### **Terraform 標準模組**

```hcl
# modules/monitoring/notification-channels.tf
resource "google_monitoring_notification_channel" "project_channels" {
  count        = length(var.notification_emails)
  display_name = "${var.project_name} - ${var.environment} - ${var.notification_emails[count.index]}"
  type         = "email"

  labels = {
    email_address = var.notification_emails[count.index]
  }

  user_labels = {
    project     = var.project_name
    environment = var.environment
    purpose     = "project-specific-alerts"
    owner       = split("@", var.notification_emails[count.index])[0]
    team        = var.team_name
  }

  enabled = var.enable_monitoring
}
```

## 📊 **警報策略標準**

### **必要監控項目**

所有專案必須包含以下監控：

1. **執行時間監控**

   - 閾值：根據服務特性設定
   - 檢測間隔：300 秒（推薦）

2. **錯誤率監控**

   - 閾值：0（任何錯誤立即通知）
   - 檢測間隔：300 秒

3. **資源使用監控**

   - 記憶體：90% 使用率
   - CPU：80% 使用率（如適用）

4. **業務邏輯監控**
   - 執行頻率異常
   - 資料品質檢查

### **監控間隔最佳化**

```yaml
推薦配置:
  檢測間隔: 300 秒 (5 分鐘)
  理由:
    - 平衡檢測速度與成本
    - 避免 Cloud Function 冷啟動誤報
    - 符合大多數業務需求的時效性要求

特殊情況:
  關鍵服務: 60 秒 (需要即時監控)
  批次作業: 600 秒 (10 分鐘，降低成本)
```

## 💰 **費用追蹤標準**

### **BigQuery Labels 標準**

所有 BigQuery 查詢必須包含以下 labels：

```python
# 標準 labels 配置
standard_labels = {
    "project": project_name,           # 專案識別
    "repo": "integrated-event",        # 代碼庫
    "env": environment,                # 環境 (prod/dev/staging)
    "trigger": trigger_type,           # 觸發方式 (auto/manual)
    "user": current_user,              # 執行用戶
    "cost-center": cost_center,        # 成本中心 (選用)
}

# 使用範例
job_config = bigquery.QueryJobConfig(labels=standard_labels)
query_job = client.query(query, job_config=job_config)
```

### **費用追蹤實施**

1. **自動用戶偵測**

   ```python
   def detect_current_user():
       try:
           result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE', '--format=value(account)'])
           return result.stdout.strip().split('@')[0]
       except:
           return os.getenv('USER', 'unknown')
   ```

2. **成本報告查詢**
   ```sql
   SELECT
     labels.key as label_key,
     labels.value as label_value,
     SUM(cost) as total_cost
   FROM `project.dataset.gcp_billing_export_v1_XXXXXX`
   WHERE labels.key IN ('project', 'env', 'user')
   GROUP BY labels.key, labels.value
   ORDER BY total_cost DESC
   ```

## 🔧 **實施檢查清單**

### **新專案設置**

- [ ] **通知頻道配置**

  - [ ] 命名符合標準格式
  - [ ] 用戶標籤完整設定
  - [ ] 測試通知正常運作

- [ ] **警報策略配置**

  - [ ] 涵蓋必要監控項目
  - [ ] 閾值設定合理
  - [ ] 監控間隔最佳化

- [ ] **費用追蹤配置**

  - [ ] BigQuery 查詢包含 labels
  - [ ] 自動用戶偵測機制
  - [ ] 費用報告可按標籤分類

- [ ] **文檔更新**
  - [ ] README.md 包含監控配置說明
  - [ ] 技術文檔記錄特殊配置
  - [ ] 操作手冊更新

### **定期審查**

- [ ] **季度檢查**（每 3 個月）

  - [ ] 檢查未使用的通知頻道
  - [ ] 審查警報策略有效性
  - [ ] 分析費用追蹤數據

- [ ] **年度檢查**（每 12 個月）
  - [ ] 更新監控標準
  - [ ] 評估新的 Google Cloud 功能
  - [ ] 團隊培訓和知識分享

## 📚 **參考資源**

### **範例專案**

- [家樂福離線資料系統](../apps/carrefour-offline-data/README.md) - 完整實施範例
- [監控最佳實務指南](../apps/carrefour-offline-data/docs/operations/monitoring-best-practices.md)

### **Terraform 模組**

- [通知頻道模組](../apps/carrefour-offline-data/terraform/monitoring.tf)
- [警報策略範例](../apps/carrefour-offline-data/terraform/monitoring.tf)

### **Google Cloud 文檔**

- [Cloud Monitoring 概述](https://cloud.google.com/monitoring/docs)
- [通知頻道配置](https://cloud.google.com/monitoring/support/notification-options)
- [BigQuery 費用最佳化](https://cloud.google.com/bigquery/docs/best-practices-costs)

## 🔄 **版本歷史**

| 版本 | 日期       | 變更內容                     | 作者        |
| ---- | ---------- | ---------------------------- | ----------- |
| v1.0 | 2025-09-01 | 初始版本，基於家樂福專案經驗 | Frank Zheng |

---

**維護者**：Data Engineering Team
**審核者**：Tech Lead
**下次審查**：2025-12-01
